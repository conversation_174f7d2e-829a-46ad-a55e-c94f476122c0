import { Injectable } from '@angular/core';

import { interval, Observable, of, queueScheduler, scheduled, Subject } from 'rxjs';
import { concatAll, filter, map, switchMap, takeUntil, tap } from 'rxjs/operators';
import { v4 as uuid } from 'uuid';

import { IReference } from '../../core/models';
import { ICommentSettings } from '../comments.settings';
import { CommentParams } from '../models/comment-params.model';
import { Comments, IAttachment, IComment, ICommentTree } from '../models/comment.model';
import { CommentApiService, IComments } from './comment-api.service';
import { Actions } from './comment.actions';
import { CommentStore } from './comment.store';

@Injectable()
export class CommentService {
  private readonly destroyed = new Subject<void>();

  private readonly store = new CommentStore(this.destroyed.asObservable());

  constructor(
    private commentApiService: CommentApiService,
  ) { }

  init(reference: IReference, settings: ICommentSettings): Observable<Comments> {
    if (settings.autoUpdate) {
      this.autoUpdate(reference, settings.autoUpdateInterval);
    }

    const params = new CommentParams({
      count: settings.count,
      depth: settings.depth,
      depthCount: settings.depthCount,
    });

    return this.commentApiService.get(reference, params)
      .pipe(
        tap(({ totalCount, comments, draft }) => this.store.dispatch(new Actions.Add(reference, totalCount, comments, draft))),
        switchMap(() => this.store.comments$(reference)),
      );
  }

  destroy() {
    this.destroyed.next();
  }

  loadMore(reference: IReference, params: CommentParams): Observable<IComments> {
    return this.commentApiService.get(reference, params)
      .pipe(
        tap(comments => this.store.dispatch(new Actions.Insert(reference, comments.comments, false, false))),
      );
  }

  create(reference: IReference): Observable<ICommentTree> {
    return this.commentApiService.create(reference)
      .pipe(
        tap(comment => this.store.dispatch(new Actions.CreateDraft(reference, comment))),
      );
  }

  publish(comment: IComment) {
    return this.commentApiService.publish(comment)
      .pipe(
        tap(c => {
          if (c.publicationDate?.getTime() === c.lastPublicationDate?.getTime()) {
            this.store.dispatch(new Actions.RemoveDraft(c.reference));
            this.store.dispatch(new Actions.Insert(c.reference, c, c.reference.collection !== 'Comment'));
          }
          else {
            this.store.dispatch(new Actions.Update(c.reference, c));
          }
        }),
      );
  }

  discardDraft(comment: IComment): Observable<ICommentTree | null> {
    return this.commentApiService.discard(comment.id)
      .pipe(
        tap((c) => this.store.dispatch(
          c
            ? new Actions.Update(c.reference, c)
            : new Actions.RemoveDraft(comment.reference)
        )),
      );
  }

  remove(comment: IComment): Observable<void> {
    return this.commentApiService.remove(comment.id)
      .pipe(
        map(() => this.store.dispatch(new Actions.Remove(comment.reference, comment))),
      );
  }

  updateWorkingBody(reference: IReference, commentId: string, body: string): Observable<void> {
    return this.commentApiService.update(commentId, body)
      .pipe(
        map(data => this.store.dispatch(new Actions.UpdateWorkingBody(reference, commentId, data))),
      );
  }

  attachFiles(reference: IReference, commentId: string, fileList: FileList) {
    const files: File[] = [];

    for (let i = 0; i < fileList.length; i++) {
      const file = fileList.item(i);

      if (file) {
        files.push(file);
      }
    }

    return scheduled(
      files.map(file => this.postAttachment(reference, commentId, file)),
      queueScheduler
    )
      .pipe(concatAll());
  }

  removeAttachment(reference: IReference, commentId: string, attachment: IAttachment) {
    return this.commentApiService.removeAttachment(commentId, attachment.id)
      .pipe(
        tap(() => this.store.dispatch(new Actions.RemoveAttachment(reference, commentId, attachment))),
      );
  }

  private postAttachment(reference: IReference, commentId: string, input: File) {
    const trackingId = uuid();

    return of(input)
      .pipe(
        tap((file) => this.store.dispatch(
          new Actions.AddAttachment(reference, commentId, trackingId, file.name)
        )),
        switchMap((file) => this.commentApiService.postAttachment(commentId, file, trackingId)),
        tap(attachment => this.store.dispatch(
          new Actions.AddAttachmentComplete(reference, commentId, trackingId, attachment)
        )),
      );
  }

  private autoUpdate(reference: IReference, autoUpdateInterval: number) {
    let fromDate = new Date();

    interval(autoUpdateInterval * 1000)
      .pipe(
        filter(() => document.visibilityState === 'visible'),
        switchMap(() => this.commentApiService.changes(reference, fromDate)),
        takeUntil(this.destroyed),
      )
      .subscribe(comments => {
        fromDate = new Date();
        this.store.dispatch(new Actions.AddOrUpdate(comments));
      });
  }
}
