@mixin font-size($step, $line-height: false) {
  @include small {
    font-size: font-size-value($step, true);

    @if $line-height {
      line-height: line-height-value($step, true);
    }
  }

  font-size: font-size-value($step);

  @if $line-height {
    line-height: line-height-value($step);
  }
}

@mixin line-height($step) {
  @include small {
    line-height: line-height-value($step, true);
  }

  line-height: line-height-value($step);
}

@mixin button-focus() {
  &:focus {
    border-color: $pzh-button-focus-border-color;
    outline: 2px auto $pzh-button-focus-outline-color;
    outline-offset: 3px;
  }
}
