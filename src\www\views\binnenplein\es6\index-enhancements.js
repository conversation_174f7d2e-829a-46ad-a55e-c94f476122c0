(function ($) {
  'use strict';

  let lastAjaxRequest = {};
  let selectedFacet = undefined;
  let selectedPage = undefined;

  const initResultsWhenTyping = function () {
    $('.z-searchbar input[name = \'zoeken_term\']')
      .unlessProcessed('resultsWhenTyping')
      .each(function () {
        const $form = $(this).closest('form');
        $(this)
          .on('keyup', _.debounce(function () {
            setPaging(0);
            pushForm($form);
          }, 500));
      });
  };

  const initFacetAutoSubmit = function () {
    $('fieldset.zoekfacetten')
      .unlessProcessed('facetAutoSubmit')
      .each(function () {
        if (selectedFacet !== undefined) {
          $(`#${selectedFacet}`).focus();
          selectedFacet = undefined;
        }

        $(this)
          .closest('.type-facetzoeken')
          .find('.knoppen')
          .hide();

        $('.facet input', this)
          .on('click', function () {
            selectedFacet = $(this).attr('id');

            $(this)
              .closest('.type-facetzoeken')
              .find('.facet input[type = \'checkbox\']')
              .prop('readonly', true)
              .css({
                'cursor': 'wait'
              });
            setPaging(0);
            pushForm();
          });
      });
  };

  const initSortAutoSubmit = function () {
    $('#zoeken_sortering')
      .unlessProcessed('sortAutoSubmit')
      .on('change', function () {
        setPaging(0);
        setSort($(this).val());
        pushForm();
      });
  };

  const setPaging = function (pageNumber) {
    $('.z-facet form, .z-searchbar form')
      .find('input[name = \'pager_page\']')
      .val(pageNumber);
  }

  const setSort = function (sortOrder) {
    $('.z-facet form, .z-searchbar form')
      .find('input[name = \'zoeken_sortering\']')
      .val(sortOrder);
  }

  const initForms = function () {
    $('.z-results')
      .closest('.grid-container')
      .unlessProcessed('ariaAttributes')
      .attr('aria-busy', 'false');

    $('.z-facet form, .z-searchbar form')
      .unlessProcessed('ajaxSubmit')
      .on('submit', function (event) {
        event.preventDefault();
        pushForm($(this));
        return false;
      });
  }

  const initPaging = function () {
    const $pagerLinks = $('.z-results .pager a');
    if (selectedPage !== undefined && !isNaN(parseInt(selectedPage, 10))) {
      // 0, 1, 2, etc.
      $pagerLinks.filter(`a[data-page = '${selectedPage - 1}'], a[data-page = '${selectedPage + 1}']`).last().focus();
    }
    else if (selectedPage !== undefined) {
      // 'volgende' of 'vorige'
      $pagerLinks.filter(`a[data-page = '${selectedPage}']`).focus();
    }
    selectedPage = undefined;

    $pagerLinks
      .unlessProcessed('ajaxPager')
      .on('click', function (event) {
        event.preventDefault();
        const pageNumber = new URLSearchParams($(this).attr('href').split('?')[1]).get('pager_page');
        setPaging(pageNumber);
        selectedPage = $(this).data('page');
        pushForm();
        return false;
      });
  }

  const pushForm = function ($form) {
    if (!$form) {
      $form = $('.z-facet form').length > 0
        ? $('.z-facet form')
        : $('.z-searchbar form');
    }

    const searchParams = new URLSearchParams($form.serialize());

    window.history.pushState(null, null, window.location.toString().replace(window.location.hash, '').replace(window.location.search, '') + `?${searchParams.toString()}`);
    popForm();
  };

  const popForm = function () {
    const $form = $('.z-facet form').length > 0
            ? $('.z-facet form')
            : $('.z-searchbar form');
    const url = $form.attr('action');
    const searchParams = new URLSearchParams(window.location.search);

    if (lastAjaxRequest.abort) {
      lastAjaxRequest.abort();
    }

    if (!searchParams.has('ajax')) {
      searchParams.append('ajax', true);
    }

    $('.z-results')
      .closest('.grid-container')
      .attr('aria-busy', 'true');

    lastAjaxRequest = $.ajax({
      data: searchParams.toString(),
      type: 'get',
      url
    })
      .done((html) => {
        const $html = $(html);
        if (window.applyAngularJs) {
          window.applyAngularJs($html.find('div[data-ipng-workgroup-data]'));
        }

        $('.z-results')
          .replaceWith($html.find('.z-results'));
        $('.z-facet')
          .replaceWith($html.find('.z-facet'));
      })
      .always(function () {
        $('.z-results')
          .closest('.grid-container')
          .attr('aria-busy', 'false')
          .parent()
          .initPageLoadFunctions(true);
      });
  };

  window.pageLoadFunctions.push(function (context) {
    $('.z-searchbar form')
      .each(function () {
        const options = $(this).data('searchconfig');
        if (options.resultsWhileTyping) {
          initResultsWhenTyping();
        }

        if (options.facetAutoSubmit) {
          initFacetAutoSubmit();
        }

        if (options.sortAutoSubmit) {
          initSortAutoSubmit();
        }

        initPaging();
        initForms();
      });
  });

  window.onpopstate = popForm;

})(jQuery);
