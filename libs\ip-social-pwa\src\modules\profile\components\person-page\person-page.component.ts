import { Component } from '@angular/core';
import { ActivatedRoute } from '@angular/router';

import { IPerson, User, UserService } from '@ip/social-core';
import { Observable } from 'rxjs';

import { PersonMenuService } from '../../services/person-menu.service';

@Component({
  selector: 'ips-person-page',
  templateUrl: './person-page.component.html',
  styleUrls: ['./person-page.component.scss']
})
export class PersonPageComponent {
  data$: Observable<IProfilePageData>;

  user$: Observable<User>;

  constructor(
    public menu: PersonMenuService,
    userService: UserService,
    activatedRoute: ActivatedRoute,
  ) {
    this.data$ = activatedRoute.data as Observable<IProfilePageData>;
    this.user$ = userService.currentUser$;
  }
}

interface IProfilePageData {
  person: IPerson;
  isOwnProfile: boolean;
}
