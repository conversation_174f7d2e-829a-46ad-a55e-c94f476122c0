<?xml version="1.0" encoding="utf-8"?>
<node name="product">
  <node name="iconen">
    <node name="icoon">
      <table name="LibCatTab" alias="LibCat" />
      <where condition="LibCat.Nam = 'Product iconen'" />
      <table name="LibTab" alias="Lib" joincondition="LibCat.LibCatIdt = Lib.LibCatIdt" />
      <where condition="Lib.Src IS NOT NULL" />
      <where condition="Lib.Txt IS NOT NULL" />
      <where condition="Lib.Typ = 'img'" />
      <field table="Lib" name="LibCatIdt" type="attribute" />
      <field table="Lib" name="Wrd" type="attribute" />
      <field table="Lib" name="Src" type="attribute" />
      <field table="Lib" name="Txt" type="tag" />
    </node>
  </node>
</node>
