import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { RouterModule } from '@angular/router';

import { IonicModule } from '@ionic/angular';
import { IpPwaHeaderModule } from '@ip/pwa';
import { UtilitiesModule } from '@ip/social-core';

import { BlogModule } from '../blog/blog.module';
import { MetaModule } from '../meta/meta.module';
import { PersonModule } from '../person/person.module';
import { SharedModule } from '../shared/shared.module';
import { EmailComponent } from './components/email/email.component';
import { PersonFollowBtnComponent } from './components/follow-btn/follow-btn.component';
import { PersonBlogListPageComponent } from './components/person-blog-list-page/person-blog-list-page.component';
import { PersonHeaderComponent } from './components/person-header/person-header.component';
import { PersonMenuBtnComponent } from './components/person-menu-btn/person-menu-btn.component';
import { PersonMenuComponent } from './components/person-menu/person-menu.component';
import { PersonPageComponent } from './components/person-page/person-page.component';
import { PhoneNumbersComponent } from './components/phone-numbers/phone-numbers.component';
import { PropertyListComponent } from './components/property-list/property-list.component';
import { SocialsComponent } from './components/socials/socials.component';
import { WorkdaysComponent } from './components/workdays/workdays.component';
import { IncludesPipe } from './pipes/includes.pipe';
import { IsOwnProfileResolver } from './resolver/is-own-profile.resolver';
import { PersonResolver } from './resolver/person.resolver';
import { PersonMenuService } from './services/person-menu.service';

@NgModule({
  declarations: [
    EmailComponent,
    PersonFollowBtnComponent,
    PersonHeaderComponent,
    PersonMenuBtnComponent,
    PersonMenuComponent,
    PersonPageComponent,
    PersonBlogListPageComponent,
    PhoneNumbersComponent,
    PropertyListComponent,
    SocialsComponent,
    WorkdaysComponent,
    IncludesPipe,
  ],
  providers: [
    IsOwnProfileResolver,
    PersonResolver,
    PersonMenuService,
  ],
  imports: [
    CommonModule,
    IonicModule,
    RouterModule,
    SharedModule,
    UtilitiesModule,
    MetaModule,
    PersonModule,
    BlogModule,
    IpPwaHeaderModule,
  ],
  exports: [
    PersonPageComponent,
  ]
})
export class ProfileModule { }
