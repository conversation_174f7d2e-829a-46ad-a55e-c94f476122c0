<?xml version="1.0" encoding="utf-8"?>
<xsl:stylesheet
  version="1.0"
  xmlns="http://www.w3.org/1999/xhtml"
  xmlns:xsl="http://www.w3.org/1999/XSL/Transform"
  xmlns:formatter="urn:formatter"
  extension-element-prefixes="formatter"
>

  <xsl:include href="/views/pagetypes/googletagmanager/xsl/feature.xsl" />
  <xsl:include href="/views/pagetypes/ga4/xsl/feature.xsl" />

  <xsl:template match="content" mode="css_wrapper_stijlset_after">
    <link rel="stylesheet" href="{$view_folder}/css/binnenplein.css" />
  </xsl:template>

  <xsl:template match="content" mode="css_stijlset_baseline_grid">
    <link rel="stylesheet" href="{$view_folder}/css/baseline-grid.css" />
  </xsl:template>

  <xsl:template match="content" mode="js_wrapper_stijlset_after">
    <script src="{$view_folder}/js/binnenplein.js"></script>
    <script src="{$view_folder}/js/media.js"></script>
  </xsl:template>

  <xsl:template match="entry" mode="list-entry-elt-order">
    <xsl:param name="show-trefwoord" select="false()" />
    <xsl:param name="show-selitem" select="false()" />
    <xsl:text>title</xsl:text>
    <xsl:if test="not(ancestor::content[1]/index/selectie/properties/display/@zoeken_weergave = 'galerij')">,img</xsl:if>
    <xsl:text>,date,summary,list,source</xsl:text>
    <xsl:if test="$show-trefwoord">,trefwoord</xsl:if>
    <xsl:if test="$show-selitem">,selitem</xsl:if>
    <xsl:text>,publisher,author</xsl:text>
  </xsl:template>

  <xsl:template match="cluster" mode="servicebalk-zoeken-insideform-plug">
    <input type="hidden" name="selectedFacets">
      <xsl:attribute name="value">
        <xsl:value-of select="/data/content/site/search/item/cluster[veld[Nam = 'Pre-selectie' and Wrd = 1]][1]/veld[Nam = 'Facetwaarde']/Wrd" />
      </xsl:attribute>
    </input>
  </xsl:template>

  <!--
    ////////////////////////////////////////////////////////
    plug om social-element in 'backbuttons'-zone te plaatsen
    ////////////////////////////////////////////////////////
  -->

  <xsl:template match="layout[formatter:CsvContainsValue('loket,workgroup,persons', @Aka)]/zone[@Aka = 'backbuttons']" mode="empty-grid-zone">
    <xsl:apply-templates select="." mode="grid-element-wrapper">
      <xsl:with-param name="zone" select="." />
      <xsl:with-param name="blok-rol" select="'backbuttons'" />
      <xsl:with-param name="args">
        <xsl:choose>
          <xsl:when test="parent::layout/@Aka = 'loket' and key('item_alias', 'loket')">
            <xsl:value-of select="key('item_alias', 'loket')/@Url" />
          </xsl:when>
          <xsl:when test="parent::layout/@Aka = 'workgroup' and key('item_alias', 'groepen')">
            <xsl:value-of select="key('item_alias', 'groepen')/@Url" />
          </xsl:when>
          <xsl:when test="parent::layout/@Aka = 'persons'">
            <xsl:variable name="personsRoute" select="//data/content/site/functions/routes/item[@entity = 'persons']" />
            <xsl:value-of select="concat($personsRoute/@Url, $personsRoute/@slug, '/')" />
          </xsl:when>
        </xsl:choose>
      </xsl:with-param>
      <xsl:with-param name="extended-template" select="document('')/*/xsl:template[@name = 'backbuttons']" />
    </xsl:apply-templates>
  </xsl:template>

  <xsl:template match="xsl:template[@name = 'backbuttons']" name="backbuttons">
    <xsl:param name="context" select="null" />
    <xsl:param name="args" select="null" />

    <div class="grid-box">
      <ips-back-buttons data-url="'{$args}'" data-context="{$context/parent::layout/@Aka}" />
    </div>
  </xsl:template>

  <!--
    ///////////////////////////////////////////////////////////////////////////
    plug om tags-element in 'tags'-zone te plaatsen (ipv. in "aside" of "content")
    ///////////////////////////////////////////////////////////////////////////
  -->

  <xsl:template match="content/page[@pagetype = 'artikel']//zone[@Aka = 'content']" mode="grid-row-end-plug-two" />
  <xsl:template match="content/page[@pagetype = 'artikel']//zone[@Aka = 'aside']" mode="grid-aside-plug" />

  <xsl:template match="/data/content/page//layout/zone[@Aka = 'tags']" mode="empty-grid-zone">
    <xsl:apply-templates select="/data/content/page//cluster[Nam = 'Meta']/veld[@GgvTyp = '17' and item[Trf]][1]" mode="grid-content-wrap">
      <xsl:with-param name="zone" select="self::zone" />
      <xsl:with-param name="columns" select="@columns" />
    </xsl:apply-templates>
    <xsl:if test="/data/content/page//cluster[Nam = 'Meta']/veld[@GgvTyp = '17' and item[Trf]][1]">
      <hr />
    </xsl:if>
  </xsl:template>

  <xsl:template match="cluster[Nam = 'Meta']/veld[@GgvTyp = '17']" mode="grid-element">
    <div class="grid-edge">
      <div class="grid-inside">
        <ul class="iprox-content tags horizontal">
          <xsl:for-each select="parent::cluster[Nam = 'Meta']/veld[@GgvTyp = '17']/item[Trf]">
            <xsl:sort select="Trf" />

            <li class="tag">
              <xsl:value-of select="Trf" />
            </li>
          </xsl:for-each>
        </ul>
      </div>
    </div>
  </xsl:template>

  <!--
    ///////////////////////////////////////////////////////////////////////////
    plug om in product-entries of workgroup-entries, GEEN pennetje te krijgen
    ///////////////////////////////////////////////////////////////////////////
  -->
  <xsl:template match="entry[@pagetype='product' or @pagetype='workgroup']" mode="grid-extra-attribs" />

  <!--
    ///////////////////////////////////////////////////////////////////////////
    afbeeldingen in entries van index altijd decoratief
    ///////////////////////////////////////////////////////////////////////////
  -->
  <xsl:template match="cluster[ProTypNam = 'Index']/feed/entry/img/@alt" mode="xcopy">
    <xsl:attribute name="alt" />
  </xsl:template>

</xsl:stylesheet>
