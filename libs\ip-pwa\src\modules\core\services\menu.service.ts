import { Injectable } from '@angular/core';

import { Observable, Subject } from 'rxjs';
import { filter } from 'rxjs/operators';

@Injectable()
export class MenuService {
  private subject = new Subject<IMenuClick>();

  click(event: Event, tab: string) {
    const selected = (event.target as HTMLElement).closest('ion-tab-button')?.classList.contains('tab-selected') ?? false;

    this.subject.next({ tab, selected });
  }

  get(tab: string): Observable<IMenuClick> {
    return this.subject.pipe(
      filter(value => value.tab === tab)
    );
  }
}

export interface IMenuClick {
  tab: string;
  selected: boolean;
}
