import { ComponentFactoryResolver, Injectable } from '@angular/core';

import { TimelineContentFactory } from '../content-factory';
import { TimelineArticleComponent } from './article.component';

@Injectable()
export class ArticleFactory extends TimelineContentFactory {
  component = TimelineArticleComponent;

  type = 'article';

  constructor(componentFactoryResolver: ComponentFactoryResolver) {
    super(componentFactoryResolver);
  }
}
