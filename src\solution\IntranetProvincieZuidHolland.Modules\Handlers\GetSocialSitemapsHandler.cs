﻿namespace IntranetProvincieZuidHolland.Modules.Handlers {
  using System;
  using System.Collections.Generic;
  using System.IO;
  using System.Linq;
  using System.Xml;
  using System.Xml.Linq;

  using InfoProjects.Dxe.Cache;
  using InfoProjects.Dxe.Linq;
  using InfoProjects.Dxe.Sql;
  using InfoProjects.Dxe.Util;
  using InfoProjects.Dxe.Xdl;
  using InfoProjects.Dxe.Xml;
  using InfoProjects.Iprox.Modules.Frontend.Util;

  public class GetSocialSitemapsHandler {
    public static XdlBuilder.XdlHandler XdlHandler => Process;

    private static XmlNode Process(XmlElement node, XdlContext context) {
      var socialFeedsConfig = SocialFeedsConfig();
      var feedType = context.GetAttribute(node, "name");

      switch (feedType) {
        case "socialindex": {
          foreach (var socialFeed in socialFeedsConfig) {
            var feedDocument = SocialFeedNode(context, socialFeed);
            var xFeedDocument = StripNamespaces(feedDocument.DocumentElement.ToXElement());
            var lastModifieds = xFeedDocument.DescendantsAndSelf("LastModifiedDate")
              .Concat(xFeedDocument.DescendantsAndSelf("LastActivityDate"));
            var lastModifiedItemDate = string.Empty;
            var xElements = lastModifieds.Where(d => !string.IsNullOrEmpty(d.Value)).ToList();

            if (xElements.Count != 0) {
              lastModifiedItemDate = xElements
                .Select(dateElement => DateTime.Parse(dateElement.Value))
                .OrderByDescending(date => date.Ticks)
                .First()
                .ToString("yyyy-MM-dd");
            }

            var item = node.OwnerDocument.CreateElement("socialitem");
            item.SetAttribute("socialfeed", (string)socialFeed.Attribute("alias"));
            item.SetAttribute("lastmod", lastModifiedItemDate);
            node.AppendChild(item);
          }

          break;
        }
        case "socialurls": {
          var sitemapsource = context.GetProp("sitemapsource");
          var socialFeed = socialFeedsConfig.First(x => (string)x.Attribute("alias") == string.Concat("social.", sitemapsource));
          var feedDocument = SocialFeedNode(context, socialFeed);
          var feedDocumentStripped = StripNamespaces(feedDocument.DocumentElement.ToXElement()).ToXmlElement();
          var feedElements = feedDocumentStripped.ChildNodes;
          var socialfeedNode = node.OwnerDocument.CreateElement("socialfeed");

          foreach (XmlNode feedElement in feedElements) {
            var elementUrl = feedElement.SelectSingleNode("Url");
            
            if (elementUrl == null) {
              continue;
            }

            var item = node.OwnerDocument.CreateElement("url");
            item.SetAttribute("loc", elementUrl.InnerText);
            var lastMod = feedElement.SelectNodes(".//LastModifiedDate|.//LastActivityDate")
              .Cast<XmlNode>()
              .Select(n => n.InnerText)
              .Where(s => !string.IsNullOrEmpty(s))
              .OrderByDescending(s => s)
              .FirstOrDefault();
            if (lastMod != null) {
              item.SetAttribute("lastmod", DateTime.Parse(lastMod).ToString("yyyy-MM-dd"));
            }

            socialfeedNode.AppendChild(item);
          }

          node.AppendChild(socialfeedNode);
          break;
        }
      }

      return node;
    }

    private static IEnumerable<XElement> SocialFeedsConfig() {
      if (!VirtualPaths.FileSystem.TryGetFile("/apps/search.xml", out var data)) {
        Logger.Error("SocialFeeds config file not found");
        return Enumerable.Empty<XElement>();
      }

      return from source in XDocument.Load(XmlReader.Create(new MemoryStream(data))).Descendants("source")
             let enabledProp = $"sitemap_{source.Attribute("alias").Value}_enabled"
             where Context.DefaultContext.GetProp(enabledProp, false)
             select source;
    }

    private static XmlDocument SocialFeedNode(XdlContext context, XElement socialFeed) {
      var baseUrl = SqlConnection
        .GetSqlConnection()
        .DetermineFieldValue("EnvTab", "BasUrl", "EnvIdt", context.GetProp("EnvIdt"))
        .TrimEnd('/');

      if (string.IsNullOrEmpty(baseUrl)) {
        Logger.Error("No baseUrl found");
        return new XmlDocument();
      }

      var feedUrl = $"{baseUrl}/{(string)socialFeed.Attribute("api")}/{(string)socialFeed.Attribute("feed")}";
      var apiKey = (string)socialFeed.Attribute("apikey");

      if (!string.IsNullOrEmpty(apiKey)) {
        feedUrl = new Formatter().SetPropInHref(feedUrl, "apikey", apiKey);
      }

      var key = $"SocialSitemaps:{feedUrl}";

      ICacheItem Producer() {
        try {
          Logger.Debug("Retrieving {0}", feedUrl);
          
          var xmlFeedTimeout = new TimeSpan(0, 0, Parser.ParseInt(Context.Current.GetProp("getsocialsitemaps_timeout"), 10));
          var doc = new WebXmlDocument(feedUrl, new WebRequestOptions(xmlFeedTimeout));

          Logger.Debug("Retrieved {0}", feedUrl);
          Logger.Trace("Retrieved document {0}", doc.OuterXml);

          return new CacheItem(doc, new WallClockCacheDependency { EndDateTime = DateTime.Now + CacheService.GetDefaultExpiry(key, context) });
        }
        catch (Exception e) {
          Logger.Exception(e, "Could not retrieve {0}", feedUrl);
          return new CacheItem(null, new WallClockCacheDependency { EndDateTime = DateTime.Now + TimeSpan.FromMinutes(1) });
        }
      }

      var feedDocument = (XmlDocument)CacheService.Default.GetValue(key, Producer);
      return feedDocument;
    }

    private static XElement StripNamespaces(XElement root) {
      foreach (var xe in root.DescendantsAndSelf()) {
        // Stripping the namespace by setting the name of the element to it's localname only
        xe.Name = xe.Name.LocalName;

        // replacing all attributes with attributes that are not namespaces and their names are set to only the localname
        xe.ReplaceAttributes(xe.Attributes()
          .Where(xa => !xa.IsNamespaceDeclaration)
          .Select(xattrib => new XAttribute(xattrib.Name.LocalName, xattrib.Value)));
      }

      return root;
    }
  }
}
