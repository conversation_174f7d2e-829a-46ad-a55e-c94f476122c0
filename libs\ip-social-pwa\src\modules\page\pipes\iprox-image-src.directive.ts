import { Directive, HostBinding, Input, OnChanges, SimpleChanges } from '@angular/core';

import { CoreSettings, ResourceTokenService } from '@ip/social-core';
import { map } from 'rxjs/operators';

@Directive({
  // eslint-disable-next-line @angular-eslint/directive-selector
  selector: 'img[ips-iprox-image-src]',
})
export class IproxImageSrcDirective implements OnChanges {
  // eslint-disable-next-line @angular-eslint/no-input-rename
  @Input('ips-iprox-image-src')
  filePath!: string;

  @HostBinding('src')
  securedSrc?: string;

  constructor(private resourceTokenService: ResourceTokenService, private settings: CoreSettings) { }

  ngOnChanges(changes: SimpleChanges) {
    if (changes.filePath.currentValue !== changes.filePath.previousValue) {
      this.resourceTokenService.token$
        .pipe(map(token => `${this.settings.apiUrl}iprox/content?file=${this.filePath}&access-token=${token}`))
        .subscribe(src => this.securedSrc = src);
    }
  }
}
