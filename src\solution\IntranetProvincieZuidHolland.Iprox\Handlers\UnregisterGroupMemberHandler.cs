﻿namespace IntranetProvincieZuidHolland.Iprox.Handlers {
  using System.Collections.Generic;
  using System.Linq;

  using InfoProjects.Dxe.Linq;
  using InfoProjects.Dxe.Process;
  using InfoProjects.Dxe.Process.Handler;

  /// <summary>
  /// Handler to deauthorize group member.
  /// </summary>
  public class UnRegisterGroupMemberHandler : IContextProcessHandler {
    #region Public Methods and Operators

    /// <summary>
    /// Processes the unit.
    /// </summary>
    /// <param name="unit">
    /// Process unit.
    /// </param>
    /// <param name="context">
    /// Process context.
    /// </param>
    /// <returns>
    /// Result props with Success = true.
    /// </returns>
    public ResultProps Process(ProcessUnit unit, ProcessContext context) {
      var itmIdt = RegisterGroupMemberHandler.ParseUnitProperty<int>(unit, "GroupItemId", true);
      var log = RegisterGroupMemberHandler.ParseUnitProperty<string>(unit, "Login", true);
      var dmn = RegisterGroupMemberHandler.ParseUnitProperty<string>(unit, "Domain");

      RegisterGroupMemberHandler.ValidateItemId(itmIdt);
      var userId = GetUserId(log, dmn, context);

      var result = new ResultProps();
      if (!userId.HasValue) {
        result["Success"] = "false";
        return result;
      }

      var functionIds = RegisterGroupMemberHandler.GetFunctionIds(itmIdt, context).Values;
      result["Success"] = DeAuthorizeUser(userId.Value, functionIds, context);
      return result;
    }

    #endregion

    #region Methods

    /// <summary>
    /// Gets the ProcessUnit to authorize the user.
    /// </summary>
    /// <param name="userId">
    /// The user ID proxy.
    /// </param>
    /// <param name="functionIds">
    /// The possibly related function IDs.
    /// </param>
    /// <param name="context">
    /// The process context.
    /// </param>
    /// <returns>
    /// A string indicating whether something has been removed (true) or not (false)
    /// </returns>
    internal static string DeAuthorizeUser(int userId, IEnumerable<string> functionIds, ProcessContext context) {
      var existingQuery = context.Sql.GetSqlWriter();
      existingQuery.AddTable("GebFunTab");
      existingQuery.AddCondition("GebIdt", userId);
      existingQuery.AddCondition("FunIdt", functionIds);
      existingQuery.AddField("FunIdt");
      functionIds = existingQuery.GetValues();
      if (!functionIds.Any()) {
        return "false";
      }

      foreach (var functionId in functionIds) {
        var unit = new ProcessUnit("GebFunTab") { Action = Action.REPLACE };
        unit[Reserved.KEY] = "GebIdt";
        unit[Reserved.ID] = userId.ToIproxString();
        unit[Reserved.GROUP] = "FunIdt";
        unit["FunIdt"] = functionId;
        context.Schedule(unit);
      }

      return "true";
    }

    /// <summary>
    /// Gets the user ID.
    /// </summary>
    /// <param name="log">
    /// The user login name.
    /// </param>
    /// <param name="dmn">
    /// The user domain.
    /// </param>
    /// <param name="context">
    /// The process context.
    /// </param>
    /// <returns>
    /// The user ID or null if not found.
    /// </returns>
    private static int? GetUserId(string log, string dmn, ProcessContext context) {
      return context.Sql.DetermineFieldValue("GebTab", "GebIdt", "Log", log, "Dmn", dmn).To<int?>();
    }

    #endregion
  }
}