﻿namespace IntranetProvincieZuidHolland.Iprox.Events.EventModel {
  using System;
  using System.Diagnostics.CodeAnalysis;

  [SuppressMessage("StyleCop.CSharp.DocumentationRules", "SA1600:ElementsMustBeDocumented", 
    Justification = "Reviewed. Suppression is OK here.")]
  public class Item {
    #region Public Properties

    public int Id { get; set; }

    public string Label { get; set; }

    public DateTime LatestPublicationDateTime { get; set; }

    public Pagetype Pagetype { get; set; }

    #endregion

    /// <summary>
    /// Returns a string that represents the current object.
    /// </summary>
    /// <returns>
    /// A string that represents the current object.
    /// </returns>
    public override string ToString() {
      return string.Format(
        "{0}:{1} ({2}) @ {3:HH:mm}",
        this.Id,
        this.Label,
        this.Pagetype.Name,
        this.LatestPublicationDateTime);
    }
  }
}