﻿namespace IntranetProvincieZuidHolland.Modules.Triggers {
  using InfoProjects.Iprox.Modules.Frontend.Util;
  using IntranetProvincieZuidHolland.Modules.Events;
  using IntranetProvincieZuidHolland.Modules.Handlers;

  /// <summary>
  /// DxeInit class
  /// </summary>
  public static class DxeInit {
    /// <summary>
    /// Initialize DxeInit
    /// </summary>
    public static void Init() {
      LightboxStartpunten.Register(prefix: "Idx", pagetype: "artikel");
      LightboxStartpunten.Register(prefix: "Faq", pagetype: "faq");
      LightboxStartpunten.Register(prefix: "Evt", pagetype: "evenement");
      FrontendAddToFotoalbum.Register();
      FrontendEditArtikel.Register();
      FrontendEditEvenement.Register();
      FrontendEditFaq.Register();
      FrontendEditFoto.Register();
      FrontendEditInhoud.Register();
      LuceneEvents.Init();
      XdlEvents.Init();
    }
  }
}