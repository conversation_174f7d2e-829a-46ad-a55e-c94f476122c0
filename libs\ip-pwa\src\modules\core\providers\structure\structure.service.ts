import { Type } from '@angular/core';
import { Route, Router, Routes } from '@angular/router';

import { Observable, ReplaySubject } from 'rxjs';
import { map } from 'rxjs/operators';

import { PageComponent } from '../../components/page/page.component';
import { MenuItem } from '../../models/menu-item.model';
import { Menu } from '../../models/menu.model';
import { ModuleRoute } from '../../models/module-route.model';
import { Structure } from '../../models/structure.model';

export abstract class StructureService {
  protected routes: ModuleRoute[] = [];

  protected structure = new ReplaySubject<Structure>(1);

  structure$ = this.structure.asObservable();

  menu$ = this.structure$.pipe(
    map(s => new Menu(
      s.roots.map(r => new MenuItem(
        r.path,
        r.label,
        this.resolveIcon(r.type),
        this.resolveComponent(r.type),
      )))
    ),
  );

  routes$: Observable<Routes> = this.structure$.pipe(
    map(s => s.roots.map<Route>(r => this.resolve(r.path, r.type)))
  );

  defaultRoute$: Observable<MenuItem | undefined> = this.menu$.pipe(
    map(menu => menu.items[0])
  );

  constructor(private router: Router) { }

  protected registerRoute(moduleRoute: ModuleRoute) {
    this.routes.push(moduleRoute);
  }

  protected configureRouter(structure: Structure, moduleRoutes: ModuleRoute[]) {
    this.router.routeReuseStrategy.shouldReuseRoute = (_future, _current) => false;

    const routes: Route[] = [
      ...moduleRoutes.filter(r => r.applicationRoute).map(r => r.route),
      ...structure.roots.map<Route>(r => this.resolve(r.path, r.type))
    ];

    if (!routes.some(r => r.path === '')) {
      const r = [
        ...routes,
        {
          path: '',
          redirectTo: routes[0].path,
          pathMatch: 'full'
        }
      ];

      return this.router.resetConfig(r);
    }

    return this.router.resetConfig(routes);
  }

  private resolveIcon(type: string): string | undefined {
    return this.routes.find(r => r.type === type)?.icon;
  }

  private resolveComponent(type: string): Type<unknown> | undefined {
    return this.routes.find(r => r.type === type)?.component;
  }

  private resolve(path: string, type: string): Route {
    const route: Route = {
      path
    };

    const moduleRoute = this.routes.find(i => i.type === type);

    if (moduleRoute) {
      Object.assign<Route, Route>(route, moduleRoute.route);
    }
    else {
      Object.assign<Route, Route>(route, {
        component: PageComponent
      });
    }

    return route;
  }

  abstract init(): Promise<void>;
}
