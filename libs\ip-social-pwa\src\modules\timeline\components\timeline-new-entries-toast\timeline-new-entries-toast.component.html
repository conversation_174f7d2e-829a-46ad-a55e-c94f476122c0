<ng-container *ngIf="unseenEntries$ | async as unseenEntries">
  <div class="ips-timeline-toast-container" [class.ips-timeline-toast-hidden]="unseenEntries.length === 0">
    <div class="ips-timeline-toast">
      <ion-button
        class="ips-timeline-toast-btn"
        color="dark"
        size="small"
        (click)="refresh()"
      >
        <ion-text>{{ (unseenEntries.length === 1 ? 'timeline.newEntriesToastSingle' : 'timeline.newEntriesToast') | transloco:{ count: unseenEntries.length } }}</ion-text>
      </ion-button>
      <ion-button
        class="ips-timeline-toast-btn ips-ion-text-button"
        color="dark"
        size="small"
        (click)="dismiss()"
      >
        <ion-icon name="close"></ion-icon>
      </ion-button>
    </div>
  </div>
</ng-container>
