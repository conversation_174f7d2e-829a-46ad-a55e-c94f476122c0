export interface IPagination {
  count: number;
  start: number;
  totalCount: number;
  itemCount: number;
  pages: IPaginationPage[];
}

export interface IPaginatedResults<T> extends IPagination {
  items: T[];
}

/* eslint-disable @typescript-eslint/naming-convention */
export enum PaginationType {
  first = 'first',
  prev = 'prev',
  page = 'page',
  next = 'next',
  last = 'last',
}
/* eslint-enable @typescript-eslint/naming-convention */

export interface IPaginationPage {
  type: PaginationType;
  label: string;
  start: number;
  end: number;
  page: number;
  selected: boolean;
}
