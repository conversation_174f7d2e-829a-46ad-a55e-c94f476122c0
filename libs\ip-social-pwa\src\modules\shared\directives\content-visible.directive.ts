import { Directive, ElementRef, Renderer2 } from '@angular/core';

@Directive({ selector: '[ipsContentVisible]' })
export class ContentVisibleDirective {
  constructor(
    private renderer: Renderer2,
    private el: ElementRef,
  ) { }

  set show(value: boolean) {
    if (value) {
      this.renderer.removeStyle(this.el.nativeElement, 'display');
    }
    else {
      this.renderer.setStyle(this.el.nativeElement, 'display', 'none');
    }
  }
}
