<?xml version="1.0" encoding="utf-8"?>
<xsl:stylesheet xmlns:xsl="http://www.w3.org/1999/XSL/Transform" version="1.0" xmlns:resources="urn:resources" extension-element-prefixes="resources">

  <xsl:import href="include/StartPage.xsl"/>

  <xsl:param name="PublishFaq.done"/>

  <!-- abstract variables -->
  <xsl:variable name="done" select="$PublishFaq.done" />
  <xsl:variable name="pagetype">faq</xsl:variable>

  <xsl:template match="*" mode="body_inside_form">
    <xsl:if test="string($error = '')">
      <xsl:call-template name="editor"/>
      <form enctype="multipart/form-data">
        <xsl:apply-templates select="." mode="form_lib">
          <xsl:with-param name="form_AppIdt" select="$exitAppIdt"/>
        </xsl:apply-templates>
        <input type="hidden" cluster="{$handler}" name="{$handler}.$action" value="run"/>
        <xsl:call-template name="startpage-inputs" />
        <table class="list">
          <xsl:call-template name="veld">
            <xsl:with-param name="Nam" select="resources:GetText('labels', 'Vraag')"/>
            <xsl:with-param name="curFieldName" select="concat($handler, '.Titel')"/>
            <xsl:with-param name="curClusterName" select="$handler"/>
            <xsl:with-param name="GgvTyp" select="'2'"/>
            <xsl:with-param name="Req" select="'1'"/>
          </xsl:call-template>
          <xsl:call-template name="startpage-velden" />
        </table>
      </form>
    </xsl:if>
  </xsl:template>

  <xsl:template name="startpage-velden">
    <xsl:variable name="faq-veld" select="/data/clusterdefinition[Nam='Vraag en antwoord']/*/veld[site]" />
    <xsl:apply-templates select="$faq-veld[Nam = 'Antwoord']" mode="startpage-veld" />
  </xsl:template>

</xsl:stylesheet>