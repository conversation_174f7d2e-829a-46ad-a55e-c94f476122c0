﻿<p class="ips-loading grid-realign" ng-if="CalendarEventViewCtrl.isLoading"><i class="fa-solid fa-spin fa-spinner"></i></p>

<div class="grid-zone grid_12 z-calendar-event-view z-calendar-event-not-found" ng-if="!CalendarEventViewCtrl.isLoading && CalendarEventViewCtrl.calendarEvent === undefined">
  <div class="grid-blok grid_12 rol-social-group-components has-elt-breakpoints">
    <div class="grid-element">
      <div class="grid-edge">
        <div class="grid-box">
          <ips-component data-component="not-found">
            <content>
              <ips-not-found></ips-not-found>
            </content>
          </ips-component>
        </div>
      </div>
    </div>
  </div>
</div>

<div class="grid-zone grid_12 z-backbuttons z-backbuttons-static" ng-if=":: CalendarEventViewCtrl.calendarEvent">
  <div class="grid-blok grid_12 rol-backbuttons type-backbuttons has-elt-breakpoints">
    <div class="grid-element">
      <div class="grid-edge">
        <div class="grid-box">
          <button type="button" class="btn ips-button ips-primary" ng-click="CalendarEventViewCtrl.back()">
            <i class="fa-solid fa-angle-double-left" aria-hidden="true"></i>
            <span>{{:: 'view.calendar.calendarEvent.back' | ipRefText }}</span>
          </button>
        </div>
      </div>
    </div>
  </div>
</div>

<div class="grid-zone grid_12 z-calendar-event-view" ng-if=":: CalendarEventViewCtrl.calendarEvent">
  <div class="grid-row single-elt-row has-fullwidth-elt rol-rij-rol-social-group-components has-elt-breakpoints has-no-list-icons has-no-link-icons has-no-button-icons">
    <div class="grid-blok grid_12 rol-social-group-components type-social-files has-elt-breakpoints has-no-list-icons has-no-link-icons has-no-button-icons">
      <div class="grid-element">
        <div class="grid-edge">
          <div class="grid-box">
            <ips-component data-component="calendar">
              <content>
                <ips-calendar-event
                  class="ips-component-item"
                  ng-if="CalendarEventViewCtrl.calendarEvent"
                  data-calendar-event="CalendarEventViewCtrl.calendarEvent"
                  data-settings="CalendarEventViewCtrl.settings"
                ></ips-calendar-event>
              </content>
            </ips-component>
          </div>
        </div>
      </div>
    </div>
  </div>

  <div class="grid-row single-elt-row has-fullwidth-elt rol-rij-rol-social-group-components has-elt-breakpoints has-no-list-icons has-no-link-icons has-no-button-icons" ng-if="CalendarEventViewCtrl.comments">
    <div class="grid-blok grid_12 rol-social-group-components type-social-files has-elt-breakpoints has-no-list-icons has-no-link-icons has-no-button-icons">
      <div class="grid-element">
        <div class="grid-edge">
          <div class="grid-box">
            <ips-comments data-owner="{{ CalendarEventViewCtrl.comments.owner }}" data-config="{{ CalendarEventViewCtrl.comments.config }}"></ips-comments>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
