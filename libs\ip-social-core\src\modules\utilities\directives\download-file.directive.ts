import { Directive, HostBinding, HostListener, Input } from '@angular/core';

import { ResourceService } from '../../core/services/resource.service';

@Directive({
  // eslint-disable-next-line @angular-eslint/directive-selector
  selector: 'a[ips-download-file]',
})
export class DownloadFileDirective {
  @HostBinding('href')
  @Input()
  href!: string;

  @Input()
  openInNewTab = false;

  // eslint-disable-next-line @angular-eslint/no-input-rename
  @Input('ips-download-file')
  fileName!: string;

  constructor(private resourceService: ResourceService) { }

  @HostListener('click', ['$event'])
  download(event: Event) {
    event.preventDefault();
    event.stopImmediatePropagation();

    if (this.href) {
      this.resourceService.downloadFile(this.href, this.openInNewTab ? this.fileName : undefined);
    }
  }
}
