import { Pipe, PipeTransform } from '@angular/core';

import { CoreSettings } from '../../core/core.settings';
import { IIntranetFile } from '../models/comment.model';

@Pipe({
  name: 'attachmentDownloadHref',
})
export class AttachmentDownloadHrefPipe implements PipeTransform {
  constructor(private coreSettings: CoreSettings) { }

  transform(file: IIntranetFile, formatSize?: string): string {
    const imageFormatSize = formatSize && file.contentType !== 'image/gif' ? formatSize + '/' : '';
    return `${this.coreSettings.apiUrl}comment/${file.owner?.id}/attachment/${file.id}/${imageFormatSize}${file.fileName}`;
  }
}
