import { Injectable } from '@angular/core';

import { ISocialConfig, MetaComponent, SocialConfigProvider } from '@ip/social-core';

export interface IBlogSettings {
  enableAuthors: <AUTHORS>
  metaComponents: MetaComponent[];
}

@Injectable()
export class BlogSettings implements IBlogSettings {
  private module: keyof ISocialConfig = 'blog';

  // TODO: Default tinymceSettings?
  // private tinymceSettings

  enableAuthors = true;

  metaComponents: MetaComponent[] = [MetaComponent.Visits, MetaComponent.Comments, MetaComponent.Likes];

  constructor(settings: SocialConfigProvider) {
    const blogSettings = settings.get<Partial<IBlogSettings>>(this.module);

    if (blogSettings) {
      if (blogSettings.enableAuthors !== undefined) {
        this.enableAuthors = blogSettings.enableAuthors;
      }
    }
  }
}
