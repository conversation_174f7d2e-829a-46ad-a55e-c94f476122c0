﻿namespace IntranetProvincieZuidHolland.Iprox.Handlers {
  using InfoProjects.Dxe.Process;
  using InfoProjects.Iprox.Model;
  using InfoProjects.Iprox.Model.Util;

  using IntranetProvincieZuidHolland.Iprox.Model;

  /// <summary>
  /// Handles publish Artikel
  /// </summary>
  public class PublishEvenementHandler : PublishNewPageHandler<Evenement> {
    #region Methods

    /// <summary>
    /// Sets content of page.
    /// </summary>
    /// <param name="unit">
    /// The process unit.
    /// </param>
    /// <param name="page">
    /// The iprox page.
    /// </param>
    protected override void SetContent(ProcessUnit unit, Evenement page) {
      unit.SetValueOf(page.Inhoud.Startdatum, "Startdatum");
      unit.SetValueOf(page.Inhoud.Starttijd, "Starttijd");
      unit.SetValueOf(page.Inhoud.Eindtijd, "Eindtijd");
      unit.SetValueOf(page.Inhoud.Locatie, "Locatie");
      unit.SetValueOf(page.Inhoud.Omschrijving, "Omschrijving");
      SharedRepublish.SetValueIfSupplied(unit, page.Inhoud.Doelgroep, "Doelgroep");
      SharedRepublish.SetValueIfSupplied(unit, page.Inhoud.MeerInformatie, "Meer_informatie");
      SharedRepublish.SetValueIfSupplied(unit, page.Inhoud.Aanmelden, "Aanmelden");
    }

    #endregion
  }
}