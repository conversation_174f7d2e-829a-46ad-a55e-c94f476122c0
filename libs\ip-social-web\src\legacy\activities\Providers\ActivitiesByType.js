(function (angular) {
  'use strict';

  angular.module('Intranet.Activities').factory('ActivitiesByType', ['$http', '$route', '$routeParams', '$q', '$window', 'config', function ($http, $route, $routeParams, $q, $window, config) {
    return function (type, params) {
      switch (type) {
        case 'debug':
          return $http({
            method: 'GET',
            url: config.socialApiUrl + 'ActivityStream/',
            params: params
          });
        case 'person':
          if (!$routeParams.id) {
            return $q.reject({ data: [] });
          }

          return $http({
            method: 'GET',
            url: config.socialApiUrl + 'ActivityStream/Person/' + $routeParams.id,
            params: params
          });
        case 'userFollowingPersons':
          return $http({
            method: 'GET',
            url: config.socialApiUrl + 'ActivityStream/UserFollowingPersons',
            params: params
          });
        case 'userGroupsAndFollowingPersons':
          return $http({
            method: 'GET',
            url: config.socialApiUrl + 'ActivityStream/User',
            params: params
          });
        case 'group':
          return $http({
            method: 'GET',
            url: config.socialApiUrl + 'ActivityStream/Group/' + $window.itmIdt,
            params: params
          });
        case 'allGroups':
          return $http({
            method: 'GET',
            url: config.socialApiUrl + 'ActivityStream/AllGroups',
            params: params
          });
        case 'userGroups':
          return $http({
            method: 'GET',
            url: config.socialApiUrl + 'ActivityStream/UserGroups',
            params: params
          });
        default:
          return $q.reject({ data: [] });
      }
    };
  }]);
})(angular);
