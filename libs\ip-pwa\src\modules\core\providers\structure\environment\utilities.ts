import { Root } from '../../../models/structure.model';

/* eslint-disable @typescript-eslint/naming-convention */
export const TimelineRoot = () => new Root('tijdlijn', 'social-pwa-timeline', 'Tijdlijn');

export const PersonsRoot = () => new Root('persons', 'social-pwa-persons', 'Collega\'s');

export const ProfileRoot = () => new Root('profiel', 'social-pwa-profile', 'Profiel');

export const UserRoot = () => new Root('you', 'social-pwa-user', '');
