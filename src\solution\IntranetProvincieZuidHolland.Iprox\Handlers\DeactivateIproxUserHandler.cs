﻿namespace IntranetProvincieZuidHolland.Iprox.Handlers {
  using InfoProjects.Dxe.Process;
  using InfoProjects.Dxe.Process.Handler;

  /// <summary>
  /// Handler to deactivate IPROX user.
  /// </summary>
  public class DeactivateIproxUserHandler : IContextProcessHandler {
    /// <summary>
    /// Processes the unit.
    /// </summary>
    /// <param name="unit">
    /// Process unit.
    /// </param>
    /// <param name="context">
    /// Process context.
    /// </param>
    /// <returns>
    /// Result props with Success = true (or: false when user is not found)
    /// </returns>
    public ResultProps Process(ProcessUnit unit, ProcessContext context) {
      var log = RegisterGroupMemberHandler.ParseUnitProperty<string>(unit, "Login", true);
      var dmn = RegisterGroupMemberHandler.ParseUnitProperty<string>(unit, "Domain");
      var result = new ResultProps();

      var gebIdt = context.Sql.DetermineFieldValue("GebTab", "GebIdt", "Log", log, "Dmn", dmn);
      if (string.IsNullOrEmpty(gebIdt)) {
        result["Success"] = "false";
      }
      else {
        var gebUnit = new ProcessUnit("GebTab") {
          Action = Action.EDIT
        };
        gebUnit[Reserved.KEY] = "GebIdt";
        gebUnit[Reserved.ID] = gebIdt;
        gebUnit["Vrs"] = "-1";
        gebUnit["Act"] = "0";
        context.Schedule(gebUnit);
        result["Success"] = "true";
      }

      return result;
    }
  }
}