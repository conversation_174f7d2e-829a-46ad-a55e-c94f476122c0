{"name": "@ip/social-pwa-bootstrapper", "version": "3.3.2522", "publishConfig": {"registry": "https://npm.iphq.nl"}, "peerDependencies": {"@angular/common": "11.2.14", "@angular/core": "11.2.14"}, "dependencies": {"@angular/cdk": "11.2.12", "@azure/msal-angular": "2.0.2", "@azure/msal-browser": "2.16.1", "@ip/pwa": "3.3.2522", "@ip/social-core": "3.3.2522", "@ip/social-pwa": "3.3.2522", "@ngneat/transloco": "^2.20.1", "@ngxs/store": "^3.7.1", "tslib": "^2.0.0"}}