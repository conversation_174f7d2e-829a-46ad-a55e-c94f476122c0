import { Platform } from '@angular/cdk/platform';
import { Injectable } from '@angular/core';

import { ReplaySubject } from 'rxjs';
import { map, take } from 'rxjs/operators';

import { IPlatform } from '../models/platform.model';
import { IUserAgent } from '../models/user-agent.model';

@Injectable()
export class UserAgentService {
  private userAgent = new ReplaySubject<IUserAgent>();

  public userAgent$ = this.userAgent.asObservable();

  constructor(private platform: Platform) {
    this.userAgent.next(this.getUserAgent());
  }

  updateNotificationPermission(permission: NotificationPermission): void {
    this.userAgent$
      .pipe(
        take(1),
        map(device => ({
          ...device,
          notificationPermission: permission
        }))
      )
      .subscribe(updatedDevice => this.userAgent.next(updatedDevice));
  }

  getUserAgent(): IUserAgent {
    return {
      platform: this.getPlatform(),
      notificationPermission: this.getPlatform().pushSupported ? Notification.permission : null
    };
  }

  private getPlatform(): IPlatform {
    return {
      pushSupported: 'PushManager' in window && 'Notification' in window,
      isPwa: window.matchMedia('(display-mode: standalone)').matches,
      isBrowser: this.platform.isBrowser,
      android: this.platform.ANDROID,
      blink: this.platform.BLINK,
      edge: this.platform.EDGE,
      firefox: this.platform.FIREFOX,
      ios: this.platform.IOS,
      safari: this.platform.SAFARI,
      trident: this.platform.TRIDENT,
      webkit: this.platform.WEBKIT,
    };
  }
}
