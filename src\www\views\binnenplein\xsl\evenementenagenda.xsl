<?xml version="1.0" encoding="utf-8"?>

<xsl:stylesheet xmlns:xsl="http://www.w3.org/1999/XSL/Transform" xmlns="http://www.w3.org/1999/xhtml" version="1.0">
  <xsl:import href="../../baseline/xsl/evenementenagenda.xsl" />
  <xsl:import href="plugs.xsl" />

  <!-- In publicatieomgeving ivm. frontend-editing: hanteer `EdtEvtItm_` ipv. ID 'Itm_' , om een maatwerk-bewerkingsdialoog te kunnen bieden -->
  <xsl:template match="content[page/@pagetype = 'evenementenagenda']/feed/entry[@type = 'site' and @pagetype = 'evenement' and number(id) &gt; 0]" mode="grid-extra-attribs">
    <xsl:choose>
      <xsl:when test="$EnvPrv">
        <xsl:apply-imports />
      </xsl:when>
      <xsl:otherwise>
        <xsl:attribute name="id">
          <xsl:text>EdtEvtItm_</xsl:text>
          <xsl:value-of select="id" />
        </xsl:attribute>
      </xsl:otherwise>
    </xsl:choose>
  </xsl:template>

</xsl:stylesheet>
