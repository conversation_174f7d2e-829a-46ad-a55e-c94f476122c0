import { IPersonProperty } from './person-property.model';
import { Socials } from './person-socials.model';
import { IPhoneNumber } from './phone-numbers.model';
import { Workdays } from './workdays.model';

// TODO: Map this instead of using API response.
// TODO: Check if model is correct in PWA.
export interface IPerson {
  id: string;
  active: boolean;
  birthDate: Date;
  status?: string;
  slug: string;
  fullName: string;
  loginName: string;
  profileImage: string | null;
  phoneNumbers: IPhoneNumber[];
  roomNumber: string;
  locationId?: string;
  dataFields?: { name: string; value: string; }[];
  jobFunctions: IPersonProperty[];
  department?: IPersonProperty;
  departments: IPersonProperty[];
  organisation?: IPersonProperty;
  locations: IPersonProperty[];
  propertyList1: IPersonProperty[];
  propertyList2: IPersonProperty[];
  weblinks?: IPersonProperty[];
  skills?: IPersonProperty[];
  workHistory?: IPersonProperty[];
  memberships?: IPersonProperty[];
  email: string;
  workDays: Workdays;
  social: Socials;
}
