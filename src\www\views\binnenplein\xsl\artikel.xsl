<?xml version="1.0" encoding="utf-8"?>

<xsl:stylesheet
  xmlns:xsl="http://www.w3.org/1999/XSL/Transform"
  xmlns="http://www.w3.org/1999/xhtml"
  xmlns:baselinehelper="urn:baselinehelper"
  extension-element-prefixes="baselinehelper"
  version="1.0">

  <xsl:import href="../../baseline/xsl/artikel.xsl" />
  <xsl:import href="plugs.xsl" />

  <xsl:template match="content[page/cluster[ProTypAka = 'social-enhancements' and .//veld[Nam = 'enabled' and Wrd = '1']]]//zone" mode="grid-row-end-plug-three">
    <xsl:variable name="layout" select="/data/content/page//layout" />

    <xsl:choose>
      <xsl:when test="$layout/@Nam = 'Volledige breedte'">
        <xsl:apply-templates select="self::zone[@Aka = $layout/zone[last()]/@Aka]" mode="social_enhancements_zone" />
      </xsl:when>
      <xsl:when test="$social_enhancements_zones != ''">
        <xsl:variable name="social_enhancements_zone" select="baselinehelper:GetBestMatchingZone($layout/zone|/data/content/site/functions//layout/zone, $social_enhancements_zones)" />
        <xsl:apply-templates select="self::zone[@Aka = $social_enhancements_zone]" mode="social_enhancements_zone" />
      </xsl:when>
    </xsl:choose>
  </xsl:template>
</xsl:stylesheet>
