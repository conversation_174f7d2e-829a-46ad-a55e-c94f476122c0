import { Component, EventEmitter, Input, OnDestroy, OnInit, Output } from '@angular/core';
import { AbstractControl, FormControl, FormGroup, Validators } from '@angular/forms';

import { Observable, of, Subject, Subscription } from 'rxjs';
import { debounceTime, filter, map, mergeAll, mergeMap, shareReplay, switchMap, takeUntil, tap } from 'rxjs/operators';

import { FileTypes } from '../../../../utilities/file-types';
import { FileValidators } from '../../../../utilities/file-validators';
import { IReference } from '../../../core/models';
import { ICommentSettings } from '../../comments.settings';
import { IAttachment, IComment } from '../../models/comment.model';
import { CommentService } from '../../services/comment.service';

@Component({
  selector: 'ips-comment-form-base',
  template: '',
})
export class CommentFormBaseComponent implements OnInit, OnDestroy {
  private subscription?: Subscription;

  private publishTriggered$ = new Subject();

  @Input()
  settings!: ICommentSettings;

  @Input()
  reference!: IReference;

  @Input()
  comment?: IComment;

  @Input()
  reply = false;

  /** Outputs commentId */
  @Output()
  saved = new EventEmitter<string>();

  @Output()
  cancel = new EventEmitter();

  // eslint-disable-next-line @angular-eslint/no-output-native
  @Output()
  change = new EventEmitter<string>();

  inProgress = false;

  focused = false;

  form!: FormGroup;

  fileAccept = '';

  get body(): AbstractControl {
    return this.form.get('body') as AbstractControl;
  }

  constructor(private commentService: CommentService) { }

  ngOnInit() {
    this.init(this.comment);
  }

  ngOnDestroy() {
    this.subscription?.unsubscribe();
  }

  publish() {
    if (this.inProgress || !this.comment) {
      return;
    }

    this.inProgress = true;
    this.publishTriggered$.next();

    this.comment.workingBody = this.form.value.body;

    this.commentService.publish(this.comment)
      .subscribe(({ id }) => {
        this.inProgress = false;
        this.saved.emit(id);
        this.init();
      });
  }

  discard() {
    if (this.comment && this.comment.hasChanges) {
      this.commentService.discardDraft(this.comment)
        .subscribe(() => {
          this.cancel.emit();
          this.init();
        });
    }
    else {
      this.cancel.emit();
    }
  }

  removeAttachment(comment: IComment, attachment: IAttachment) {
    this.commentService.removeAttachment(comment.reference, comment.id, attachment)
      .subscribe();
  }

  private init(comment?: IComment) {
    const commentId$ = this.commentIdObservable(comment);
    const fileTypeCodes = FileTypes.get(this.settings.attachmentFileTypes);

    this.fileAccept = fileTypeCodes.join(', ');
    this.form = this.createForm(this.settings, fileTypeCodes, comment);

    this.subscription?.unsubscribe();
    this.subscription = this.formListener(this.form, commentId$);
  }

  /* eslint-disable @typescript-eslint/no-non-null-assertion */
  private formListener(form: FormGroup, commentId$: Observable<string>): Subscription {
    const attachments$ = form.get('attachments')!.valueChanges
      .pipe(
        filter(() => form.get('attachments')!.valid),
        switchMap(files => commentId$.pipe(
          mergeMap(commentId => this.commentService.attachFiles(this.reference, commentId, files))
        ))
      );

    const body$ = form.get('body')!.valueChanges
      .pipe(
        debounceTime(400),
        tap(body => this.change.emit(body)),
        switchMap(body => commentId$.pipe(
          mergeMap(commentId => this.commentService.updateWorkingBody(this.reference, commentId, body))
        ))
      );

    return of(attachments$, body$)
      .pipe(
        mergeAll(),
        takeUntil(this.publishTriggered$),
      )
      .subscribe();
  }
  /* eslint-enable @typescript-eslint/no-non-null-assertion */

  private createForm(settings: ICommentSettings, fileTypeCodes: string[], comment?: IComment) {
    const bodyValidators = [Validators.required, Validators.minLength(settings.minLength)];

    if (settings.maxLength) {
      bodyValidators.push(Validators.maxLength(settings.maxLength));
    }

    return new FormGroup({
      body: new FormControl(comment?.workingBody ?? '', bodyValidators),
      attachments: new FormControl(null, [FileValidators.type(fileTypeCodes)]),
    });
  }

  private commentIdObservable(comment?: IComment) {
    if (comment) {
      return of(comment.id)
        .pipe(shareReplay(1));
    }
    else {
      return this.commentService.create(this.reference)
        .pipe(
          map(newComment => newComment.id),
          filter((commentId): commentId is string => commentId !== undefined),
          shareReplay(1),
        );
    }
  }
}
