import { AbstractControl, ValidationErrors, ValidatorFn } from '@angular/forms';

export class FileValidators {
  static type(type: string | string[]): ValidatorFn {
    return (control: AbstractControl): ValidationErrors | null => {
      const fileList: FileList = control.value;
      const validTypes = typeof type === 'string' ? [type] : type;

      if (!fileList) {
        return null;
      }

      for (let i = 0; i < fileList.length; i++) {
        const file = fileList.item(i);

        if (file && !validTypes.includes(file.type.toLowerCase())) {
          return { invalidType: true };
        }
      }

      return null;
    };
  }

  static maxFileSize(maxSize: number): ValidatorFn {
    return (control: AbstractControl): ValidationErrors | null => {
      const fileList: FileList = control.value;

      if (!fileList) {
        return null;
      }

      for (let i = 0; i < fileList.length; i++) {
        const file = fileList.item(i);

        if (file && (file.size / 1024 / 1024) >= maxSize) {
          return { maxFileSizeExceeded: true };
        }
      }

      return null;
    };
  }

  static maxFileCount(count: number): ValidatorFn {
    return (control: AbstractControl): ValidationErrors | null => {
      const fileList: FileList = control.value;

      return fileList?.length > count ? { maxFileCountExceeded: true } : null;
    };
  }
}
