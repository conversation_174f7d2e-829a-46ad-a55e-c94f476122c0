@import "../../../../scss/variables";
@import "../../../../scss/mixins";

$ips-comment-entry-padding: $ips-unit * 2;

:host {
  @include cancel-grid-padding();

  display: block;
  margin-bottom: .5rem;
  margin-left: -.5rem;
  margin-right: -.5rem;
}

ion-card {
  border-radius: 0;
  box-shadow: none;
  margin: 0;
  touch-action: pan-y !important;
}

ion-card-content {
  @include font-size(0);

  padding-bottom: 0;
}

ips-meta {
  @include cancel-grid-padding();

  border-top: 1px solid var(--ion-color-light-shade);
  height: $ips-meta-height;
}

.ips-comment-date {
  @include font-size(-2);

  @media(min-width: 720px) {
    text-align: right;
  }
}

.ips-comment-body {
  color: #000;
  padding-bottom: 16px;
  padding-top: 16px;
  white-space: pre-wrap;
}

.ips-attachments {
  display: flex;
  flex-wrap: wrap;
  gap: .5rem;
  list-style: none;
  padding-left: 0;

  &.ips-edit-mode {
    padding: 0;
    background-color: transparent;
  }

  .ips-attachments-divider {
    width: 120px;
    height: 3px;
    margin-bottom: 6px;
    margin-top: 6px;
  }
}

.ips-comment-header {
  @include cancel-grid-padding();

  align-items: center;
  border-bottom: 1px solid var(--ion-color-light-shade);
  display: flex;
  flex-wrap: wrap;
  padding-left: 80px;
  padding-bottom: $ips-comment-entry-padding;
  padding-right: $ips-comment-entry-padding;
  padding-top: $ips-comment-entry-padding;
}

.ips-comment-footer {
  @include cancel-grid-padding();

  border-top: 1px solid var(--ion-color-light-shade);
  display: flex;
  justify-content: flex-end;

  .ips-comment-actions:not(:only-child) {
    align-self: center;
    margin-right: .5rem;
  }

  .ips-meta-container {
    height: 3rem;

    > ips-meta {
      display: contents;
    }
  }
}

.ips-comment-has-changes {
  font-size: 0.825rem;
  font-weight: 700;
}

@media(max-width: 720px) {
  ips-person,
  .ips-comment-date {
    flex: 0 0 100%;
  }
}

@media(min-width: 720px) {
  .ips-comment-header {
    justify-content: space-between;
  }
}

::ng-deep {
  ips-comment {
    ips-person ion-avatar {
      left: $ips-unit * 2;
      position: absolute;
      top: 16px;
    }
  }
}
