@import "../../../../scss/mixins";

:host {
  align-items: flex-start;
  display: flex;
  flex-wrap: wrap;

  &.ips-full-image {
    .ips-timeline-image-holder {
      flex: 0 0 100%;
      max-width: 100%;
    }
  }

  &:not(.ips-full-image) .ips-timeline-entry-content-block {
    flex: 1 1;
  }
}

h2 {
  @include font-size(1);

  color: var(--ion-color-primary);
  font-weight: 600;
  margin-bottom: 0;
  margin-top: 0;
}

ips-timeline-page-link {
  flex: 1 1;
}

.ips-timeline-image-holder {
  display: flex;
  flex: 0 0 33.33333%;

  .ips-timeline-image-ratio {
    width: 100%;
  }

  .ips-timeline-image-ratio {
    padding-bottom: 56.25%;
    position: relative;

    .ips-timeline-image {
      height: 100%;
      position: absolute;
      object-fit: cover;
    }
  }

  .ips-timeline-image {
    width: 100%;
  }
}
