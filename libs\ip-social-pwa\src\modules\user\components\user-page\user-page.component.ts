import { Component } from '@angular/core';

import { AppInstallService, AuthenticationService, DeviceService, UserService } from '@ip/social-core';

@Component({
  selector: 'ips-user-page',
  templateUrl: './user-page.component.html',
  styleUrls: ['./user-page.component.scss'],
})
export class UserPageComponent {
  constructor(
    public userService: UserService,
    public appInstall: AppInstallService,
    private auth: AuthenticationService,
    private deviceService: DeviceService,
  ) { }

  logout() {
    this.deviceService
      .unsubscribe()
      .subscribe(() => this.auth.logout());
  }
}
