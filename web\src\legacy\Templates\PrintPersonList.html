﻿<div data-autoscroll="false">
  <div class="grid-zone grid_12 z-results">
    <div class="grid-blok grid_12 rol-resultaten has-elt-breakpoints">
      <div class="grid-element">
        <div class="grid-edge">
          <div class="grid-box bs-personlist-print">
            <div class="row">
              <div class="col-xs-24">
                <button type="button" class="btn btn-primary hidden-print" data-ng-click="PersonListCtrl.closeWindow()">{{ 'print.personen.zoeken.sluiten' | ipRefText }}</button>
              </div>
            </div>
            <div class="row">
              <div class="col-xs-24">
                <div data-ng-repeat="person in PersonListCtrl.persons">
                  <div class="bs-personlist-print-item">
                    <div class="bs-personlist-print-avatar"><person-avatar data-person="person" data-count="{{person.changesSinceLastVisit}}"></person-avatar></div>
                    <div><b>{{ person.fullName }}</b></div>
                    <div data-ng-if="person.jobFunctions"><span data-ng-repeat="jobFunction in person.jobFunctions">{{ jobFunction.label }}</span></div>
                    <div data-ng-if="person.telephone">{{ person.telephone }}</div>
                    <div data-ng-if="person.subDepartment.label">{{ person.subDepartment.label }}</div>
                    <div data-ng-if="person.email">{{ person.email }}</div>
                  </div>
                  <div style="clear: both;" data-ng-if="($index + 1) % 3 === 0"></div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>