<ng-container *ngIf="anniversaryEvent$ | async as anniversaryEvent">
  <ion-grid>
    <ips-cake-svg></ips-cake-svg>

    <ion-row>
      <ion-col size="12">
        <div class="ips-anniversary-content-container">
          <h2>{{ 'anniversary.hooray' | transloco }}</h2>
          <p>{{ 'anniversary.birthday' | transloco : { name: anniversaryEvent.users && anniversaryEvent.users[0]?.fullName } }}</p>
        </div>
      </ion-col>
    </ion-row>
  </ion-grid>

  <ips-comments [reference]="reference"></ips-comments>
</ng-container>
