import { of } from 'rxjs';

import { IReference, User } from '../../core/models';
import { ICommentSettings } from '../comments.settings';
import { CommentAuthorizer } from '../models/authorizer.model';
import { IComment } from '../models/comment.model';
import { ICommentAuthorizerFactory } from '../services/comment-authorizer.service';

export class PostCommentAuthorizerFactory implements ICommentAuthorizerFactory {
  entities = ['Post'];

  constructor(private legacyPostAuthorizer: unknown) { }

  authorizer$ = (reference: IReference, settings: ICommentSettings, user: User) => {
    return of(new PostCommentAuthorizer(user, reference, settings, this.legacyPostAuthorizer));
  };
}

export class PostCommentAuthorizer extends CommentAuthorizer {
  // TODO nalopen iedere permission
  fn = {
    canView: (_comment: IComment | undefined): boolean => {
      return this.settings.allowCommentAsUser || this.legacyPostAuthorizer.canCreateComment(this.user, this.reference);
    },
    canCreate: (_comment: IComment | undefined): boolean => {
      return this.settings.allowCommentAsUser || this.legacyPostAuthorizer.canCreateComment(this.user, this.reference);
    },
    canEdit: (comment: IComment | undefined): boolean => {
      return this.legacyPostAuthorizer.canEditComment(this.user, comment, this.reference);
    },
    canRemove: (comment: IComment | undefined): boolean => {
      return this.legacyPostAuthorizer.canDeleteComment(this.user, comment, this.reference);
    },
    canAddAttachment: (_comment: IComment | undefined): boolean => {
      return true;
    },
    canRemoveAttachment: (_comment: IComment | undefined): boolean => {
      return true;
    },
  };

  constructor(
    private user: User,
    private reference: IReference,
    private settings: ICommentSettings,
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    private legacyPostAuthorizer: any,
  ) {
    super();
  }
}
