<?xml version="1.0" encoding="utf-8"?>
<xsl:stylesheet
  xmlns:xsl="http://www.w3.org/1999/XSL/Transform"
  xmlns:xhtml="http://www.w3.org/1999/xhtml"
  xmlns="http://www.w3.org/1999/xhtml"
  xmlns:formatter="urn:formatter"
  extension-element-prefixes="formatter"
  version="1.0"
>

  <xsl:template match="xhtml:div[formatter:HasClass(., 'rich-textarea')]/xhtml:div/xhtml:textarea[formatter:HasClass(., 'input')]" mode="page">
    <xsl:element name="{local-name()}">
      <xsl:apply-templates select="@*" mode="page" />
    </xsl:element>

    <pzh-social-mailer-editor>
      <xsl:attribute name="textarea-id">
        <xsl:value-of select="@id" />
      </xsl:attribute>
    </pzh-social-mailer-editor>
  </xsl:template>

  <xsl:template match="xhtml:div[formatter:HasClass(., 'rich-textarea') and xhtml:div/xhtml:textarea]/xhtml:div[formatter:HasClass(., 'uitkomst')]/xhtml:p" mode="page">
    <xsl:element name="{local-name()}">
      <xsl:apply-templates select="@*" mode="page" />
      <xsl:value-of select="preceding-sibling::xhtml:textarea/text()" disable-output-escaping="yes" />
    </xsl:element>
  </xsl:template>

</xsl:stylesheet>
