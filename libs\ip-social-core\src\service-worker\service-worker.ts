/// <reference lib="es2018" />
/// <reference lib="webworker" />

import { clientsClaim } from 'workbox-core';

import { PushActivitySetting } from '../modules/core/models/activity-type.model';
import { IPushSettings } from '../modules/core/models/push-settings.model';

declare const self: ServiceWorkerGlobalScope;

type TitleFn = (name: string) => string;

const actionMap = new Map<string, TitleFn | null>([
  ['IproxPublishedArticle', (name) => `${name} heeft een nieuwsartikel gepubliceerd.`],
  ['IproxUpdatedArticle', (name) => `${name} heeft een nieuwsartikel aangepast.`],
  ['IproxPublishedEvent', (name) => `${name} heeft een evenement gepubliceerd.`],
  ['IproxUpdatedEvent', (name) => `${name} heeft een evenement aangepast.`],
  ['PublishBlog', (name) => `${name} heeft een blog gepubliceerd.`],
  ['CommentOnBlog', (name) => `${name} heeft gereageerd op je blog.`],
  ['Birthday', null],
  ['BirthdayPreview', null],
  ['CongratulationsOnBirthday', null],
]);

const createTitle = (action: string, name: string): string | null => {
  const fn = actionMap.get(action);

  return fn ? fn(name) : null;
};

interface INotificationCallbackBody {
  action: 'received' | 'showed' | 'activated';
  notificationId: string;
}

const notificationCallback = async (apiUrl: string, subscriptionId: string, body: INotificationCallbackBody): Promise<IPushSettings> => {
  const received = await fetch(
    `${apiUrl}/Subscription/${subscriptionId}/Notification`,
    {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(body)
    }
  );

  return await received.json();
};

const socialPublicApiUrl = async (): Promise<string> => {
  const settings = await fetch(SocialServiceWorker.settings.url, SocialServiceWorker.settings.requestInit);
  const settingsJson = await settings.json();

  return settingsJson.serviceWorker.apiUrl;
};

const isOutsideOfficeHours = (now: Date): boolean => {
  if (now.getDay() === 0 || now.getDay() === 6) {
    return true;
  }

  const start = new Date();
  start.setHours(9, 0, 0, 0);

  const end = new Date();
  end.setHours(17, 30, 0, 0);

  return now < start || now > end;
};

const shouldShowPush = (push: ISocialPush, settings: IPushSettings) => {
  if (!settings.enabled) {
    return false;
  }

  if (settings.outsideOfficeHours === false && isOutsideOfficeHours(new Date())) {
    return false;
  }

  const pushActivitySetting = lowerCaseFirst(push.Action.replace(/Published|Updated|Publish/i, ''));

  if (!Object.keys(settings.activities).includes(pushActivitySetting)) {
    return true;
  }

  return settings.activities[pushActivitySetting as PushActivitySetting];
};

function lowerCaseFirst(value: string): string {
  return value.charAt(0).toLocaleLowerCase() + value.slice(1);
}

const handlePush = async (event: PushEvent) => {
  console.log('[social-pwa-sw] PushEvent ', event, event.data?.json());

  const push = event.data?.json();

  return self.clients.matchAll({ type: 'window' }).then(async clientList => {
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    const focusedClient = clientList.find((client: any) => client.focused === true) as unknown as WindowClient | undefined;

    if (!isSocialPush(push)) {
      return;
    }

    const apiUrl = await socialPublicApiUrl();

    const userPushSettings = await notificationCallback(
      apiUrl, push.SubscriptionId, { action: 'received', notificationId: push.NotificationId }
    );

    if (focusedClient) {
      return focusedClient.postMessage({ socialPush: true, data: push });
    }

    if (!shouldShowPush(push, userPushSettings)) {
      return;
    }

    const title = push.Title ?? createTitle(push.Action, push.User?.FullName);

    if (title) {
      const data: ISocialPushData = {
        action: push.Action,
        reference: {
          id: push.Reference.Id,
          collection: push.Reference.Collection,
        },
        subscriptionId: push.SubscriptionId,
        notificationId: push.NotificationId,
      };

      const notificationOptions: NotificationOptions = {
        body: push.Body ?? '',
        badge: 'assets/icons/icon-96x96.png',
        icon: 'assets/icons/icon-96x96.png',
        data,
      };

      return self.registration.showNotification(title, notificationOptions)
        .then(() => notificationCallback(apiUrl, push.SubscriptionId, { action: 'showed', notificationId: push.NotificationId }));
    }
  });
};

const handleNotificationClick = async (event: NotificationEvent) => {
  const data: ISocialPushData = event.notification.data;
  console.log('[sw] notificationClick', event);
  console.log('[sw] notificationClick data', data);

  event.notification.close();

  const apiUrl = await socialPublicApiUrl();

  await notificationCallback(
    apiUrl, data.subscriptionId, { action: 'activated', notificationId: data.notificationId }
  );

  const client: Client = (await self.clients.matchAll({
    includeUncontrolled: true,
    type: 'window'
  }))[0];

  if (isSupportedAction(data.action) && !!data.reference) {
    // TODO: SR: in toekomst actionMap uitbreiden met urlFn.
    let url = `tijdlijn/${data.reference.collection}/${data.reference.id}${data.action === 'CommentOnBlog' ? '?scrollTo=comments' : ''}`;

    if (data.action === 'BirthdayPreview') {
      url = 'tijdlijn';
    }

    if (client && 'focus' in client && 'navigate' in client) {
      await (client as WindowClient).focus();
      await (client as WindowClient).navigate(url);
    }
    else if ('openWindow' in self.clients) {
      const openedClient = await self.clients.openWindow(url);
      await (openedClient as WindowClient).focus();
    }
  }
};

const isSupportedAction = (action: string) => actionMap.get(action) !== undefined;

export class SocialServiceWorker {
  public static settings: IServiceWorkerSettings;

  constructor(settings: IServiceWorkerSettings) {
    this.updateServiceWorker();
    this.initPush();

    SocialServiceWorker.settings = settings;
  }

  private initPush() {
    self.addEventListener('push', event => event.waitUntil(handlePush(event)));
    self.addEventListener('notificationclick', event => event.waitUntil(handleNotificationClick(event)), false);
  }

  private updateServiceWorker() {
    self.skipWaiting();
    clientsClaim();
  }
}

export interface IServiceWorkerSettings {
  url: string;
  requestInit?: RequestInit;
}

/* eslint-disable @typescript-eslint/naming-convention */
interface ISocialPush {
  Action: string;
  DateTime: string;
  MessageId: string;
  NotificationId: string;
  Reference: {
    Id: string;
    Collection: string;
  };
  SubscriptionId: string;
  Title: string;
  Body: string;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  User?: any;
}

interface ISocialPushData {
  action: string;
  reference: {
    id: string;
    collection: string;
  };
  subscriptionId: string;
  notificationId: string;
}

// eslint-disable-next-line @typescript-eslint/no-explicit-any
const isSocialPush = (value: any): value is ISocialPush => value.Action !== undefined && value.Reference !== undefined;
