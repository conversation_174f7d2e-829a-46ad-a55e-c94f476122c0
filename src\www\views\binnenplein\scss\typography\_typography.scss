/////////////////////////////////
// Headings
/////////////////////////////////
$bl-heading-font-size-h1: font-size-value(7);
$bl-heading-font-size-h2: font-size-value(6);
$bl-heading-font-size-h3: font-size-value(5);
$bl-heading-font-size-h4: font-size-value(4);
$bl-heading-font-size-h5: font-size-value(4);
$bl-heading-font-size-h6: font-size-value(4);

$bl-line-height-h1: line-height-value(7);
$bl-line-height-h2: line-height-value(6);
$bl-line-height-h3: line-height-value(5);
$bl-line-height-h4: line-height-value(4);
$bl-line-height-h5: line-height-value(4);
$bl-line-height-h6: line-height-value(4);

/////////////////////////////////
// Text
/////////////////////////////////
$bl-font-size: font-size-value(4);
$bl-line-height: line-height-value(4);

$bl-paragraph-margin-bottom: 1.25rem;
$bl-paragraph-margin-top: 1.25rem;

$bl-heading-margin-bottom: 1rem;
$bl-heading-margin-top: 1.25rem;

.rol-inleiding {
  p {
    @include font-size(5, true);

    margin-bottom: 0.9375rem;
    margin-top: 0.9375rem;
  }
}

.iprox-content.iprox-date {
  @include font-size(3, true);
}

/////////////////////////////////
// Table
/////////////////////////////////
$bl-table-caption-color: $tekstkleur;

table thead {
  border-bottom: 2px solid $kopkleur;
}

/////////////////////////////////
// Artikel
/////////////////////////////////

.artikel {
  .type-titel h1 {
    margin-bottom: 0.9375rem; // Distance between title and subtitle.
  }

  .type-meta {
    margin-bottom: 1.5625rem;
  }

  .z-tags {
    display: none;
  }
}

/////////////////////////////////
// Loket
/////////////////////////////////

$pzh-loket-margin-left-large: 5.5rem;
$pzh-loket-margin-left-small: 2.5rem;
$pzh-loket-icon-size-small: 1.5rem;

.product {
  .z-backbuttons {
    margin-top: 1rem;

    .type-backbuttons {
      > .grid-element > .grid-edge > .grid-box {
        @include small-up {
          padding-left: $pzh-inner-padding;
        }

        @include small {
          padding-left: #{$pzh-inner-padding + $pzh-outer-padding};
        }
      }
    }

    @include small {
      .btn {
        margin-right: 10px;
        margin-top: 0.5rem;

        + .btn {
          margin-left: 0;
        }
      }
    }
  }

  h1.grid-title {
    @include small-up {
      margin-left: $pzh-loket-margin-left-large;
    }

    @include small {
      margin-left: $pzh-loket-margin-left-small;
    }

    margin-bottom: 0;
  }

  .library-icon {
    @include small-up {
      left: $pzh-inner-padding;
      top: 2rem;
    }

    @include small {
      height: $pzh-loket-icon-size-small;
      left: #{$pzh-inner-padding + $pzh-outer-padding};
      top: 1.6rem;
      width: $pzh-loket-icon-size-small;
    }

    position: absolute;
  }

  .type-trefwoorden {
    > .grid-element > .grid-edge > .grid-inside {
      padding-top: 0 !important;
    }

    ul.iprox-content.tags {
      @include small-up {
        display: flex;
        margin-left: $pzh-loket-margin-left-large;
      }

      @include small {
        margin-left: $pzh-loket-margin-left-small;
      }

      padding-bottom: 0.75rem;

      li.tag {
        margin: 0 0.5rem 0 0;

        .li-content {
          width: auto;
        }

        + .tag::before {
          content: "|";
          display: inline-block;
          width: 1rem;
        }
      }
    }

    + hr {
      display: none;
    }
  }
}

/////////////////////////////////
// Buttons
/////////////////////////////////
button,
form button {
  @include button-focus();

  border-radius: $pzh-border-radius;
  font-size: 1.375rem;
  font-weight: 500;
  line-height: $pzh-button-line-height; // SR: een vaste waarde omdat afleiden van 1.375rem met bv 1.25 altijd comma getallen opleverd. Hiermee valt beter te rekenen voor de hoogte.
}

@mixin secondary-button {
  @include button-focus();

  background-color: $pzh-blue;
  border: 1px solid transparent;
  border-radius: $pzh-border-radius;
  color: $pzh-white;
  display: inline-block;
  font-size: 1.25rem;
  line-height: 1.375rem;
  padding: 0.5rem 0.75rem;
  text-decoration: none;

  &:hover {
    background-color: $pzh-green;
    color: $pzh-white;
    text-decoration: none;
  }

  &:visited {
    color: $pzh-white;
  }
}

.product .type-productacties .regel-nu a,
.iprox-content.more > a,
.type-uitgelicht .entry a {
  @include secondary-button;
}

.iprox-content.more,
.type-uitgelicht .entry .title,
.type-uitgelicht .entry a {
  text-align: center;
}

/////////////////////////////////
// Facetzoeken
/////////////////////////////////

.type-facetzoeken {
  .zoekfacetten {
    legend {
      font-weight: 500;
    }
  }
}

/////////////////////////////////
// Forms
/////////////////////////////////
form {
  .rij {
    .label + .invoer,
    .label + .tooltip + .invoer {
      input,
      textarea,
      select {
        background-color: $pzh-input-background-color;
        margin-top: $bl-form-input-margin-top; // SR: Overbodig na baseline styling fix.
      }
    }
  }

  input {
    border-radius: $pzh-border-radius;
    height: $pzh-input-height; // SR: Onbekend waarom inputs een extra 1.5px krijgen.
  }

  select,
  textarea {
    border-radius: $pzh-border-radius;
    min-height: $pzh-input-height;
  }

  input[type="checkbox"],
  input[type="radio"] {
    &:disabled {
      + label::before {
        background-color: $bl-select-disabled-input-bg-color;
        color: $bl-select-disabled-input-bg-color;
      }

      &:hover {
        + label::before {
          color: $bl-select-disabled-input-bg-color;
        }
      }
    }
  }

  input[type="checkbox"] {
    & + label {
      &::before {
        border-radius: $pzh-border-radius;
      }

      &::after {
        border-width: 0 0 3px 3px;
      }
    }

    &:hover {
      & + label::before {
        color: $bl-form-checkbox-border-color;
      }
    }

    &:disabled:checked + label::after {
      border-color: $pzh-form-checkbox-disabled-check-color;
    }
  }

  input[type="radio"] + label::after {
    background-color: $bl-form-radio-checked-check-color;
    border: 7px solid;
    left: 0.25em;
  }
}

/////////////////////////////////
// Iconen
/////////////////////////////////
.iprox-content.iprox-rich-content {
  span {
    // sass-lint:disable-block class-name-format
    &.Email,
    &.Telefoon,
    &.Locatie {
      &::before {
        color: $pzh-green;
        display: inline-block;
        height: 1em;
        margin-right: 0.5rem;
        width: 1em;
      }
    }

    &.Email { // sass-lint:disable-line class-name-format
      @include icon($bl-icon-mail, $bl-icon-type: solid);
    }

    &.Telefoon { // sass-lint:disable-line class-name-format
      @include icon($pzh-icon-phone, $bl-icon-type: solid);
    }

    &.Locatie { // sass-lint:disable-line class-name-format
      @include icon($pzh-icon-location, $bl-icon-type: solid);
    }
  }
}

/////////////////////////////////
// Typography spacing
/////////////////////////////////

// SR: Paginatitel. Een hogere margin-top is hierdoor niet nodig.
h1 {
  &.grid-title {
    margin-bottom: 2.5rem;
    margin-top: 1.25rem;
  }
}

body { // Zwaarder worden dan baseline styling
  .grid-title {
    h1,
    h2,
    h3,
    h4,
    h5,
    h6,
    .h1,
    .h2,
    .h3,
    .h4,
    .h5,
    .h6 {
      margin-bottom: 0;
      margin-top: 0;
    }
  }

  .grid-blok {
    .iprox-rich-content {
      h1,
      .h1 {
        margin-bottom: 2rem;
        margin-top: 2.5rem;
      }

      h2,
      .h2 {
        margin-bottom: 2rem;
        margin-top: 2.5rem;
      }

      h3,
      .h3 {
        margin-bottom: 1rem;
        margin-top: 1.5rem;
      }

      h4,
      h5,
      h6,
      .h4,
      .h5,
      .h6 {
        margin-bottom: $bl-heading-margin-bottom;
        margin-top: $bl-heading-margin-top;
      }

      a {
        &,
        &:visited {
          color: $linkkleur;
          text-decoration: underline;
        }
      }
    }

    .iprox-content {
      a {
        text-decoration-thickness: $pzh-link-richcontent-hover-text-decoration-thickness;
      }
    }
  }

  @include small {
    h1,
    .h1 {
      font-size: font-size-value(7, true);
      line-height: line-height-value(7, true);
    }

    h2,
    .h2 {
      font-size: font-size-value(6, true);
      line-height: line-height-value(6, true);
    }

    h3,
    .h3 {
      font-size: font-size-value(5, true);
      line-height: line-height-value(5, true);
    }

    h4,
    .h4 {
      font-size: font-size-value(4, true);
      line-height: line-height-value(4, true);
    }

    h5,
    .h5 {
      font-size: font-size-value(4, true);
      line-height: line-height-value(4, true);
    }

    h6,
    .h6 {
      font-size: font-size-value(4, true);
      line-height: line-height-value(4, true);
    }
  }

  p + p {
    margin-top: $bl-paragraph-margin-top;
  }
}
