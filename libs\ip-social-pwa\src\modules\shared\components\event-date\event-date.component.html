<div *transloco="let t;">
  <dt>
    <ion-icon name="calendar-outline" [title]="t('event.date.label')"></ion-icon>
  </dt>
  <dd>
    <span *ngIf="!date.startHasTime && !date.end">
      {{ t('event.date.startDate', { date: (date.start | date: 'longDate') }) }}
    </span>
    <span *ngIf="date.startHasTime && !date.end || date.startHasTime && date.endsOnSameDay && !date.endHasTime">
      {{ t('event.date.startDateTime', { date: (date.start | date: 'longDate'), time: (date.start | date: 'shortTime') }) }}
    </span>

    <span *ngIf="date.endHasTime && date.endsOnSameDay">
      {{ t('event.date.startDateTimespan', { date: (date.start | date: 'longDate'), startTime: (date.start | date: 'shortTime'), endTime: (date.end | date: 'shortTime') }) }}
    </span>

    <span *ngIf="date.start && date.end && !date.startHasTime && !date.endHasTime">
      {{ t('event.date.multiDay', { startDate: (date.start | date: 'longDate'), endDate: (date.end | date: 'longDate') }) }}
    </span>

    <span *ngIf="date.start && date.end && date.startHasTime && date.endHasTime && !date.endsOnSameDay">
      {{ t('event.date.multiDayDates', { startDate: (date.start | date: 'longDate'), endDate: (date.end | date: 'longDate') }) }}
    </span>
  </dd>
  <dd *ngIf="date.start && date.end && date.startHasTime && date.endHasTime && !date.endsOnSameDay">
    <span>
      {{ t('event.date.multiDayTimespan', { startTime: (date.start | date: 'shortTime'), endTime: (date.end | date: 'shortTime') }) }}
    </span>
  </dd>
</div>
