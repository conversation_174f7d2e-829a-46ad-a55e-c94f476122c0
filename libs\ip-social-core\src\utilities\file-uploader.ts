import { EventEmitter } from '@angular/core';

import { SocialHttpClient } from '../modules/core/services/api-client';
import { reportUploadProgress } from './file-progress.service';

export interface IFileUploaderConfig {
  id: string;
  uploadUrl: string;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  responseFn: (response: any) => any;
  progressId?: string;
}

export class FileUploader {
  private input?: HTMLInputElement;

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  onSuccess = new EventEmitter<any>();

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  onError = new EventEmitter<any>();

  constructor(private socialApi: SocialHttpClient, private config: IFileUploaderConfig) { }

  open() {
    this.input ??= this.createInput();
    // TODO: check if already has value, then clear;
    this.input.click();
  }

  private createInput(): HTMLInputElement {
    const input = document.createElement('input');
    input.setAttribute('type', 'file');
    input.setAttribute('id', this.config.id);

    input.onchange = (event) => this.onChange(event);

    return input;
  }

  private onChange(event: Event) {
    const files = (event.target as HTMLInputElement).files;
    if (!files) {
      return;
    }

    const file = files[0];

    // TODO: Validate file
    // const reader = new FileReader();
    const formData = new FormData();

    formData.append('file', file, file.name);

    if (this.config.progressId) {
      this.socialApi.post(this.config.uploadUrl, formData, { reportProgress: true, observe: 'events' })
        .pipe(reportUploadProgress(this.config.progressId))
        .subscribe(
          response => this.onSuccess.emit(this.config.responseFn(response)),
          error => this.onError.emit(error),
        );
    }
    else {
      this.socialApi.post(this.config.uploadUrl, formData)
        .subscribe(
          response => this.onSuccess.emit(this.config.responseFn(response)),
          error => this.onError.emit(error),
        );
    }
  }
}
