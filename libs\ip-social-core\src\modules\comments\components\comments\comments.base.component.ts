import { Component, Input, On<PERSON>hanges, <PERSON><PERSON><PERSON>roy, SimpleChanges } from '@angular/core';

import { Observable } from 'rxjs';
import { take } from 'rxjs/operators';

import { IReference } from '../../../core/models';
import { CommentConfig, CommentSettings, ICommentSettings } from '../../comments.settings';
import { CommentAuthorizer } from '../../models/authorizer.model';
import { CommentParams } from '../../models/comment-params.model';
import { Comments, IComment } from '../../models/comment.model';
import { CommentAuthorizerService } from '../../services/comment-authorizer.service';
import { CommentService } from '../../services/comment.service';

@Component({
  selector: 'ips-comments-base',
  template: '',
})
export class CommentsBaseComponent implements OnChanges, OnDestroy {
  /** This is the top-level reference, eg: Post/:id or Microblog/:id */
  @Input()
  reference!: IReference;

  @Input()
  config: CommentConfig = {};

  @Input()
  overrideAuthorizer$?: Observable<CommentAuthorizer>;

  settings!: ICommentSettings;

  authorizer$?: Observable<CommentAuthorizer>;

  comments$?: Observable<Comments>;

  openDraft = false;

  inProgress = false;

  constructor(
    protected commentService: CommentService,
    protected commentAuthorizerService: CommentAuthorizerService,
    protected commentSettings: CommentSettings,
  ) { }

  ngOnChanges(changes: SimpleChanges) {
    if (changes.reference?.currentValue?.id !== changes.reference?.previousValue?.id || changes.reference?.isFirstChange()) {
      this.init(this.reference);
    }
  }

  ngOnDestroy() {
    this.commentService.destroy();
  }

  trackByFn(index: number, comment: IComment): string {
    return comment.id;
  }

  loadMore(commentCount: number): void {
    this.commentService.loadMore(this.reference, new CommentParams({
      start: commentCount,
      depth: this.settings.depth,
      depthCount: this.settings.depthCount,
    }))
      .subscribe();
  }

  protected init(reference: IReference) {
    this.settings = this.commentSettings.get(this.config);

    this.authorizer$ = this.overrideAuthorizer$ ?? this.commentAuthorizerService.authorizer$(this.reference, this.settings);

    this.authorizer$
      .pipe(take(1))
      .subscribe((authorizer) => {
        if (authorizer.fn.canView()) {
          this.comments$ = this.commentService.init(reference, this.settings);
        }
      });
  }
}
