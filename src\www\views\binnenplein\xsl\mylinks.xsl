<?xml version="1.0" encoding="utf-8"?>

<xsl:stylesheet xmlns:xsl="http://www.w3.org/1999/XSL/Transform" xmlns="http://www.w3.org/1999/xhtml" version="1.0">
  <xsl:import href="../../baseline/xsl/include/global.xsl" />
  <xsl:import href="../../baseline/xsl/include/root.xsl" />
  <xsl:import href="plugs.xsl" />

  <!-- dit paginatype is niet bedoeld om 'getoond' te worden, maar de ingestelde data kan opgevraagd worden met ?AppIdt=mylinks-json achter de URL -->
  <xsl:template match="content[page/@pagetype = 'mylinks']" mode="grid-content-wrap">
    <xsl:param name="zone" select="page//zone[@Aka = 'content']" />
    <xsl:param name="columns" select="$zone/@columns" />
    <xsl:param name="prefix"/>
    <xsl:param name="suffix"/>

    <!-- toon 'externe link' velden -->
    <xsl:apply-templates select=".//veld[@GgvTyp = '6']" mode="grid-content-wrap">
      <xsl:with-param name="zone" select="$zone"/>
      <xsl:with-param name="columns" select="$columns"/>
    </xsl:apply-templates>
  </xsl:template>

</xsl:stylesheet>

