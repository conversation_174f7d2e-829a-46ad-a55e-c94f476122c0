import { Injectable } from '@angular/core';

import { Animation<PERSON><PERSON><PERSON><PERSON>, ModalController } from '@ionic/angular';
import { PersonMenuComponent } from '../components/person-menu/person-menu.component';

@Injectable()
export class PersonMenuService {
  constructor(
    private modalController: <PERSON><PERSON><PERSON>ontroller,
    private animation: Animation<PERSON>ontroller,
  ) { }

  async open() {
    const enterAnimation = (baseEl: HTMLElement) => {
      const backdropAnimation = this.animation.create()
        // eslint-disable-next-line @typescript-eslint/no-non-null-assertion
        .addElement(baseEl.querySelector('ion-backdrop')!)
        .fromTo('opacity', '0.01', 'var(--backdrop-opacity)');

      const wrapperAnimation = this.animation.create()
        // eslint-disable-next-line @typescript-eslint/no-non-null-assertion
        .addElement(baseEl.querySelector('.modal-wrapper')!)
        .beforeStyles({ opacity: 1 })
        .fromTo('transform', 'translateX(100vw)', 'translateY(0vw)');

      return this.animation.create()
        .addElement(baseEl)
        .easing('cubic-bezier(0.4, 0.0, 0.2, 1)')
        .duration(300)
        .addAnimation([backdropAnimation, wrapperAnimation]);
    };

    const leaveAnimation = (baseEl: HTMLElement) => enterAnimation(baseEl).direction('reverse');

    const modal = await this.modalController.create({
      component: PersonMenuComponent,
      cssClass: 'ips-side-menu',
      enterAnimation,
      leaveAnimation
    });

    return await modal.present();
  }
}
