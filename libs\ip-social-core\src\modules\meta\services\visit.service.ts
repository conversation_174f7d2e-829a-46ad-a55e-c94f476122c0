import { Injectable } from '@angular/core';

import { Subject } from 'rxjs';
import { tap } from 'rxjs/operators';

import { IReference } from '../../core/models';
import { SocialHttpClient } from '../../core/services/api-client';

@Injectable()
export class VisitService {
  private personVisitSuccess = new Subject();

  onPersonVisitSuccess$ = this.personVisitSuccess.asObservable();

  constructor(private socialApi: SocialHttpClient) { }

  registerVisit(reference: IReference) {
    return this.socialApi.post('user/visit', reference);
  }

  registerPersonVisit(id: string) {
    const reference: IReference = {
      collection: 'Person',
      id
    };

    // TODO: API referenceId -> id. It's already obviously a reference.
    return this.socialApi.post('user/updateLastVisit', reference, { params: {
      collection: reference.collection,
      referenceId: reference.id,
    } })
      .pipe(
        tap(() => this.personVisitSuccess.next())
      );
  }
}
