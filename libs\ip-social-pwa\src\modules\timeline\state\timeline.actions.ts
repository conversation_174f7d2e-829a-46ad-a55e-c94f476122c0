import { ITimelineEntry } from '../models';

export namespace Timeline {
  export class Refresh {
    static readonly type = '[Timeline] refresh';
  }

  export class LoadMore {
    static readonly type = '[Timeline] load more entries';
  }

  export class Poll {
    static readonly type = '[Timeline] poll for updates';
  }

  export class PollResult {
    static readonly type = '[Timeline] poll result';

    constructor(public payload: ITimelineEntry[]) { }
  }

  export class DismissToast {
    static readonly type = '[Timeline] dismiss toast';
  }

  export class SetProps {
    static readonly type = '[Timeline] set entry props';

    constructor(public payload: { entryId: string; props: { [key: string]: unknown } }) { }
  }
}
