import { HttpClient } from '@angular/common/http';
import { Inject, Injectable } from '@angular/core';
import { Router } from '@angular/router';

import { map, tap } from 'rxjs/operators';

import { ModuleRoute } from '../../../models/module-route.model';
import { Root, Structure } from '../../../models/structure.model';
import { MODULE_ROUTE } from '../module-route';
import { StructureService } from '../structure.service';
import { ApiNav, NavigationItem } from './iprox-app-idt.models';
import { IPROX_STRUCTURE_APP_IDT } from './iprox-structure-app-idt';

@Injectable()
export class IproxAppIdtStructure extends StructureService {
  constructor(
    @Inject(MODULE_ROUTE) private moduleRoutes: ModuleRoute[],
    @Inject(IPROX_STRUCTURE_APP_IDT) private appIdtUrl: string,
    private http: HttpClient,
    router: Router,
  ) {
    super(router);

    moduleRoutes.forEach(r => this.registerRoute(r));
  }

  init(): Promise<void> {
    return this.http.get<ApiNav>(this.appIdtUrl)
      .pipe(
        map(n => new Structure(
          n.nav.map(item => this.mapNavigationItemToRoot(item))
        )),
        tap(structure => this.configureRouter(structure, this.moduleRoutes)),
        tap(this.structure),
        map(() => void 0)
      )
      .toPromise();
  }

  private mapNavigationItemToRoot(item: NavigationItem) {
    return new Root(
      item.relUrl,
      item.pagetype,
      item.Lbl
    );
  }
}
