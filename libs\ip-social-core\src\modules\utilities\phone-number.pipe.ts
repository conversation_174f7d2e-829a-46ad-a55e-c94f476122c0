import { Pipe, PipeTransform } from '@angular/core';

@Pipe({
  name: 'phoneNumber'
})
export class PhoneNumberPipe implements PipeTransform {
  private static typeSeparator = '\u2009-\u2009';

  private static groupSeparator = '\u2009';

  private static base = `0$2${PhoneNumberPipe.typeSeparator}$3${PhoneNumberPipe.groupSeparator}$4${PhoneNumberPipe.groupSeparator}$5`;

  private static formatters: IFormatter[] = [
    {
      filter: /^(\+31|0)6\d{8}$/,
      regex: /^(\+31|0)(.{1})(.{2})(.{2})(.{2})(.{2})$/,
      template: `${PhoneNumberPipe.base}${PhoneNumberPipe.groupSeparator}$6`,
    },
    {
      filter: /^(\+31|0)(10|13|14|15|20|23|24|26|30|33|35|36|38|40|43|45|46|50|53|55|58|70|71|72|73|74|75|76|77|78|79|85|88)\d{7}$/,
      regex: /^(\+31|0)(.{2})(.{3})(.{2})(.{2})$/,
      template: PhoneNumberPipe.base,
    },
    {
      filter: /^(\+31|0)\d{9}$/,
      regex: /^(\+31|0)(.{3})(.{2})(.{2})(.{2})$/,
      template: PhoneNumberPipe.base,
    },
  ];

  transform(input: string): string {
    if (!input) {
      return input;
    }

    if (input[0] === '0' || input.substr(0, 3) === '+31') {
      // digits and + only
      const phoneNumber = input.replace(/[^\d+]/g, '');

      const matchingFormatter = PhoneNumberPipe.formatters.find(formatter => phoneNumber.match(formatter.filter));

      return matchingFormatter
        ? phoneNumber.replace(matchingFormatter.regex, matchingFormatter.template)
        : input;
    }

    return input;
  }
}

interface IFormatter {
  filter: RegExp | string;
  regex: RegExp | string;
  template: string;
}
