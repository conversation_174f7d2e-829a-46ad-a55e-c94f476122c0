import { Injectable } from '@angular/core';

import { SocialHttpClient } from '@ip/social-core';
import { Observable } from 'rxjs';

import { IAnniversaryEvent } from '../models/anniversary.model';
import { AnniversaryApi } from '../models/api.models';

@Injectable()
export class AnniversaryApiService {
  constructor(private socialApi: SocialHttpClient) { }

  get(id: string): Observable<IAnniversaryEvent> {
    return this.socialApi.get<AnniversaryApi.IAnniversaryResponse>(`anniversary/${id}`);
  }
}
