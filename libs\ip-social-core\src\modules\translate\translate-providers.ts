import { APP_INITIALIZER, Provider } from '@angular/core';

import { TRANSLOCO_CONFIG, TranslocoConfig, translocoConfig, TranslocoService } from '@ngneat/transloco';

import { translationHttpLoader } from './translation-http-loader';

export function preloadLanguage(transloco: TranslocoService, config: TranslocoConfig) {
  return function() {
    transloco.setActiveLang(config.defaultLang);
    return transloco.load(config.defaultLang).toPromise();
  };
}

export function translateProviders(languages: string[]): Provider[] {
  if (languages.length === 0) {
    throw new Error('Specify at least one language.');
  }

  return [
    {
      provide: TRANSLOCO_CONFIG,
      useValue: translocoConfig({
        availableLangs: languages,
        defaultLang: languages[0]
      })
    },
    {
      provide: APP_INITIALIZER,
      multi: true,
      useFactory: preloadLanguage,
      deps: [TranslocoService, TRANSLOCO_CONFIG]
    },
    translationHttpLoader,
  ];
}
