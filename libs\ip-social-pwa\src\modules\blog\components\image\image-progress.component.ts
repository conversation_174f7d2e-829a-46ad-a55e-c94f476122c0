import { Component, Input, OnInit } from '@angular/core';

import { FileProgressService, isUpload, Upload } from '@ip/social-core';
import { Observable } from 'rxjs';
import { filter } from 'rxjs/operators';

@Component({
  selector: 'ips-blog-image-upload-progress',
  styleUrls: ['./image-progress.component.scss'],
  template: `<ng-container *ngIf="progress$ | async as progress" >
      <ion-progress-bar [value]="progress.progress / 100"></ion-progress-bar>
    </ng-container>
  `,
})
export class BlogImageUploadProgressComponent implements OnInit {
  @Input()
  blogId!: string;

  progress$?: Observable<Upload<unknown>>;

  constructor(private service: FileProgressService) { }

  ngOnInit() {
    this.progress$ = this.service.get$(`blog-${this.blogId}-banner`)
      .pipe(filter(isUpload));
  }
}
