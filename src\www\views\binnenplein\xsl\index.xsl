<?xml version="1.0" encoding="utf-8"?>
<xsl:stylesheet
  version="1.0"
  xmlns="http://www.w3.org/1999/xhtml"
  xmlns:xsl="http://www.w3.org/1999/XSL/Transform"
  xmlns:formatter="urn:formatter"
  extension-element-prefixes="formatter"
>
  <xsl:import href="../../baseline/xsl/index.xsl" />
  <xsl:import href="plugs.xsl" />

  <xsl:param name="zoeken_sortering" />

  <xsl:template match="content[page/@pagetype = 'index' and .//layout[@Aka = 'catalog' or @Aka = 'catalog-wide']]" mode="js_pagetype">
    <script src="/js/lib/underscore-min.js"></script>
    <script src="{$view_folder}/js/index-enhancements.js"></script>
  </xsl:template>

  <!-- fill 'searchbar' zone -->
  <xsl:template match="content/page[@pagetype = 'index']//layout[@Aka = 'catalog' or @Aka = 'catalog-wide']/zone[@Aka = 'searchbar']" mode="empty-grid-zone">
    <xsl:apply-templates select="." mode="grid-element-wrapper">
      <xsl:with-param name="zone" select="." />
      <xsl:with-param name="blok-rol" select="'formulier'" />
      <xsl:with-param name="inside-template" select="document('')/*/xsl:template[@name = 'searchbar_inside']" />
    </xsl:apply-templates>
  </xsl:template>

  <!-- fill 'searchbar' zone > callback after wrapping of search form input -->
  <xsl:template match="xsl:template[@name = 'searchbar_inside']" name="searchbar_inside">
    <xsl:param name="context" select="null" />

    <form class="formulier">
      <xsl:attribute name="data-searchconfig">
        <xsl:text>{</xsl:text>
          <xsl:text>"facetAutoSubmit": true, </xsl:text>
          <xsl:text>"sortAutoSubmit": true, </xsl:text>
          <xsl:text>"resultsWhileTyping": </xsl:text>
          <xsl:value-of select="$context/ancestor::content[1]/page/cluster[Nam = 'Selectie']/cluster[Nam = 'Selectie instellingen']/cluster[Nam = 'Zoekopties']/veld[Nam = 'Filteren bij typen']/Wrd = '1'" />
        <xsl:text>}</xsl:text>
      </xsl:attribute>

      <xsl:apply-templates select="." mode="formulier_submit">
        <xsl:with-param name="submit_method" select="'get'" />
      </xsl:apply-templates>

      <div class="verborgen">
        <input type="hidden" name="zoeken" value="true" />
        <input type="hidden" name="zoeken_sortering" value="{$zoeken_sortering}" />
        <input type="hidden" name="pager_page" value="{$pager_page}" />
      </div>

      <fieldset>
        <legend class="visuallyhidden">
          <xsl:call-template name="find_label">
            <xsl:with-param name="value" select="'Zoeken in'" />
          </xsl:call-template>
          <xsl:text>&#32;</xsl:text>
          <xsl:value-of select="formatter:ToLower($page/title)" />
        </legend>

        <xsl:apply-templates select="$context/ancestor::content[1]/page//cluster[ProTypNam = 'Selectie']" mode="formulierregel">
          <xsl:with-param name="fieldName" select="'zoeken_term'" />
          <xsl:with-param name="fieldRequired" select="'false'" />
          <xsl:with-param name="fieldLabel">
            <xsl:choose>
              <xsl:when test="$pathitem/@Aka = 'loket'">
                <xsl:call-template name="find_label">
                  <xsl:with-param name="value" select="'Zoek naar producten of diensten'" />
                </xsl:call-template>
              </xsl:when>
              <xsl:when test="$pathitem/@Aka = 'groepen'">
                <xsl:call-template name="find_label">
                  <xsl:with-param name="value" select="'Zoek naar groepen'" />
                </xsl:call-template>
              </xsl:when>
            </xsl:choose>
          </xsl:with-param>
          <xsl:with-param name="fieldValue" select="$zoeken_term" />
        </xsl:apply-templates>
      </fieldset>
    </form>
  </xsl:template>

  <!-- Button for formulierregel 'zoeken_term' -->
  <xsl:template match="page[@pagetype = 'index' and .//layout[@Aka = 'catalog' or @Aka = 'catalog-wide']]//cluster[ProTypNam = 'Selectie']" mode="formulierregel_after_plug">
    <xsl:param name="fieldName" />

    <xsl:if test="$fieldName = 'zoeken_term'">
      <xsl:apply-templates select="self::cluster" mode="formulierbuttons">
        <xsl:with-param name="add" select="'false'" />
        <xsl:with-param name="delete" select="'false'" />
        <xsl:with-param name="edit" select="'false'" />
        <xsl:with-param name="custom" select="'zoeken'" />
      </xsl:apply-templates>
    </xsl:if>
  </xsl:template>

  <!-- fill 'facet' zone -->
  <xsl:template match="content/page[@pagetype = 'index']//layout[@Aka = 'catalog']/zone[@Aka = 'facet']" mode="empty-grid-zone">
    <xsl:apply-templates select="." mode="grid-element-wrapper">
      <xsl:with-param name="zone" select="." />
      <xsl:with-param name="blok-rol" select="'formulier'" />
      <xsl:with-param name="blok-type" select="'facetzoeken'" />
      <xsl:with-param name="inside-template" select="document('')/*/xsl:template[@name = 'facet_inside']" />
    </xsl:apply-templates>
  </xsl:template>

  <!-- fill 'facet' zone > callback after wrapping of facet lists -->
  <xsl:template match="xsl:template[@name = 'facet_inside']" name="facet_inside">
    <xsl:param name="context" select="null" />

    <form class="formulier">
      <xsl:apply-templates select="." mode="formulier_submit">
        <xsl:with-param name="submit_method" select="'get'" />
      </xsl:apply-templates>

      <div class="verborgen">
        <input type="hidden" name="zoeken" value="true" />
        <input type="hidden" name="zoeken_term" value="{$zoeken_term}" />
        <input type="hidden" name="zoeken_sortering" value="{$zoeken_sortering}" />
        <input type="hidden" name="pager_page" value="{$pager_page}" />
      </div>

      <xsl:apply-templates select="$context/ancestor::content[1]/page//cluster[ProTypNam = 'Selectie']" mode="zoek_facetten">
        <xsl:with-param name="clusterdata" select="$context/ancestor::content[1]/*/selectie" />
      </xsl:apply-templates>

      <xsl:apply-templates select="$context/ancestor::content[1]/page//cluster[ProTypNam = 'Selectie']" mode="formulierbuttons">
        <xsl:with-param name="add" select="'false'" />
        <xsl:with-param name="delete" select="'false'" />
        <xsl:with-param name="edit" select="'false'" />
        <xsl:with-param name="custom" select="'zoeken'" />
      </xsl:apply-templates>
    </form>
  </xsl:template>

  <!-- fill 'results' zone -->
  <xsl:template match="content/page[@pagetype = 'index']//layout[@Aka = 'catalog' or @Aka = 'catalog-wide']/zone[@Aka = 'results']" mode="empty-grid-zone">
    <!-- sort -->
    <xsl:apply-templates select="." mode="grid-element-wrapper">
      <xsl:with-param name="zone" select="." />
      <xsl:with-param name="blok-rol" select="'resultatenheader'" />
      <xsl:with-param name="nesting-template" select="document('')/*/xsl:template[@name = 'results_header']" />
    </xsl:apply-templates>

    <!-- results/pagers -->
    <xsl:call-template name="feed-grid-content-wrap">
      <xsl:with-param name="feed" select="ancestor::content[1]/feed[entry]|ancestor::content[1][not(feed/entry)]/feed[1]" />
      <xsl:with-param name="zone" select="." />
      <xsl:with-param name="columns" select="@columns" />
      <xsl:with-param name="nested-columns" select="number(@columns) div 3" />
      <xsl:with-param name="listid" select="'results'" />
      <xsl:with-param name="pager_above" select="false()" />
      <xsl:with-param name="paging-element" select="ancestor::content[1]/*/selectie/lucene" />
      <xsl:with-param name="niets-gevonden" select="ancestor::content[1]/page//veld[Nam = 'Tekst bij niets gevonden']" />
    </xsl:call-template>
  </xsl:template>

  <!-- fill 'results' zone > callback after wrapping of sort form -->
  <xsl:template match="xsl:template[@name = 'results_header']" name="results_header">
    <xsl:param name="context" select="null" />
    <xsl:param name="zone" select="null" />

    <xsl:apply-templates select="$context" mode="grid-element-wrapper">
      <xsl:with-param name="zone" select="$zone" />
      <xsl:with-param name="columns" select="number($zone/@columns)" />
      <xsl:with-param name="blok-rol" select="'resultsortering'" />
      <xsl:with-param name="inside-template" select="document('')/*/xsl:template[@name = 'header_resultsort']" />
    </xsl:apply-templates>

    <xsl:apply-templates select="$context" mode="grid-element-wrapper">
      <xsl:with-param name="zone" select="$zone" />
      <xsl:with-param name="columns" select="number($zone/@columns)" />
      <xsl:with-param name="blok-rol" select="'resultcount'" />
      <xsl:with-param name="inside-template" select="document('')/*/xsl:template[@name = 'header_resultcount']" />
    </xsl:apply-templates>
  </xsl:template>

  <xsl:template match="xsl:template[@name = 'header_resultcount']" name="header_resultcount">
    <xsl:param name="context" select="null" />

    <xsl:choose>
      <xsl:when test="$context/ancestor::content[1]/*/selectie/lucene/@count = 0">
        <span role="alert" aria-live="polite">
          <xsl:call-template name="find_label">
            <xsl:with-param name="value" select="'Geen resultaten gevonden'" />
          </xsl:call-template>
        </span>
      </xsl:when>
      <xsl:when test="$context/ancestor::content[1]/*/selectie/lucene/@pages &gt; 1">
        <span role="status" aria-live="polite">
          <xsl:call-template name="find_label">
            <xsl:with-param name="value" select="'Zoekresultaten'" />
          </xsl:call-template>
          <xsl:text>&#32;(</xsl:text>
            <xsl:value-of select="$context/ancestor::content[1]/*/selectie/lucene/@start" />
            <xsl:text>&#32;</xsl:text>
            <xsl:call-template name="find_label">
              <xsl:with-param name="value" select="'t/m'" />
            </xsl:call-template>
            <xsl:text>&#32;</xsl:text>
            <xsl:value-of select="$context/ancestor::content[1]/*/selectie/lucene/@end" />
            <xsl:text>&#32;</xsl:text>
            <xsl:call-template name="find_label">
              <xsl:with-param name="value" select="'van'" />
            </xsl:call-template>
            <xsl:text>&#32;</xsl:text>
            <xsl:value-of select="$context/ancestor::content[1]/*/selectie/lucene/@count" />
          <xsl:text>)</xsl:text>
        </span>
      </xsl:when>
      <xsl:otherwise>
        <span role="status" aria-live="polite">
          <xsl:call-template name="find_label">
            <xsl:with-param name="value" select="'Zoekresultaten'" />
          </xsl:call-template>
          <xsl:text>&#32;(</xsl:text>
          <xsl:value-of select="count($context/ancestor::content[1]/feed/entry)" />
          <xsl:text>)</xsl:text>
        </span>
      </xsl:otherwise>
    </xsl:choose>
  </xsl:template>

  <xsl:template match="xsl:template[@name = 'header_resultsort']" name="header_resultsort">
    <xsl:param name="context" select="null" />

    <xsl:apply-templates select="$context/ancestor::content[1]/page//cluster[ProTypNam = 'Selectie']" mode="zoeken_sortering">
      <xsl:with-param name="clusterdata" select="$context/ancestor::content[1]/*/selectie" />
    </xsl:apply-templates>
  </xsl:template>

  <xsl:template match="feed[ancestor::content[1]/index_before/current_alias/@Aka = 'loket']" mode="feed-resultlist-nesting-attribs">
    <xsl:attribute name="class">
      <xsl:text>grid-nesting product-card-container</xsl:text>
    </xsl:attribute>
  </xsl:template>

  <xsl:template match="feed[ancestor::content[1]/index_before/current_alias/@Aka = 'groepen']" mode="feed-resultlist-nesting-attribs">
    <xsl:attribute name="class">
      <xsl:text>grid-nesting group-card-container</xsl:text>
    </xsl:attribute>
  </xsl:template>

  <!-- geen default entry-markup voor producten in het 'loket',
    maar markup die overeenkomt met de persoonkaartjes zoals die ook in het Angular/MVC framework zijn -->
  <xsl:template match="entry[@pagetype = 'product' and ancestor::content[1]/index_before/current_alias/@Aka = 'loket']" mode="grid-element-classes-plug">
    <xsl:text> product-card</xsl:text>
  </xsl:template>

  <xsl:template match="entry[@pagetype = 'product' and ancestor::content[1]/index_before/current_alias/@Aka = 'loket']" mode="grid-element">
    <div class="grid-edge product-card-height">
      <a href="{link/@href}">
        <xsl:attribute name="class">
          <xsl:text>grid-inside card-wrapper</xsl:text>
          <xsl:if test="summary/p">
            <xsl:text> linking</xsl:text>
          </xsl:if>
        </xsl:attribute>
        <img class="product-card-image" src="{img/@src}" alt="" />
        <span class="card-title">
          <xsl:value-of select="title" />
        </span>
      </a>
      <div class="product-card-follow-container" data-ipx-id="{id}">
        <xsl:apply-templates select="summary/p[@class = 'regel-nu']" mode="xcopy" />
      </div>
    </div>
  </xsl:template>

  <xsl:template match="p[@class = 'regel-nu']/a" mode="xcopy">
    <a>
      <xsl:attribute name="title">
        <xsl:call-template name="find_label">
          <xsl:with-param name="value" select="'Ga naar het formulier'" />
        </xsl:call-template>
        <xsl:text>&#32;</xsl:text>
        <xsl:value-of select="ancestor::entry/title" />
      </xsl:attribute>
      <xsl:apply-templates select="@*" mode="xcopy" />
      <xsl:call-template name="find_label">
        <xsl:with-param name="value" select="'Ga naar het formulier'" />
      </xsl:call-template>
      <xsl:text>&#32;</xsl:text>
      <xsl:value-of select="ancestor::entry/title" />
    </a>
  </xsl:template>

  <xsl:template match="feed//entry[@pagetype = 'product' and ancestor::content[1]/index_before/current_alias/@Aka = 'loket']/title" mode="display-in-grid">
    <h3>
      <a title="{.}">
        <xsl:apply-templates select="parent::entry" mode="link-attributes" />
        <xsl:apply-templates select="parent::entry" mode="link-inner-html" />
      </a>
    </h3>
  </xsl:template>

  <!--
    GRID-entry-blokken anders tonen (en van rol-groupcard voorzien ipv. 'entry') als kaartjes als het de 'catalog'-layout betreft en type is workgroup
  -->
  <xsl:template match="content[page/@pagetype = 'index' and page//layout[@Aka = 'catalog' or @Aka = 'catalog-wide']]/feed/entry[@pagetype = 'workgroup']" mode="grid-element-classes">
    <xsl:call-template name="add-grid-element-classes">
      <xsl:with-param name="blok-rol" select="'social-white'" />
    </xsl:call-template>

    <xsl:if test="@class">
      <xsl:value-of select="concat(' ', @class)" />
    </xsl:if>
    <xsl:if test="@pagetype">
      <xsl:value-of select="concat(' ipx-pt-', @pagetype)" />
    </xsl:if>
    <xsl:apply-templates select="." mode="grid-element-classes-plug" />
  </xsl:template>

  <!-- In indexen met gekozen 'catalog' layout de entries niet a la baseline tonen, maar met een json eiland waarmee de Angular laag de kaartjes kan tekenen -->
  <xsl:template match="content[page/@pagetype = 'index' and page//layout[@Aka = 'catalog' or @Aka = 'catalog-wide']]/feed/entry[@pagetype = 'workgroup']" mode="grid-element">
    <div class="grid-edge">
      <div class="grid-box">
        <xsl:attribute name="data-ipng-workgroup-data">
          {
            "pagetype":"<xsl:value-of select="@pagetype" />",
            "ItmIdt":"<xsl:value-of select="id" />",
            "Lbl":"<xsl:value-of select="title" />",
            "icon": { <xsl:apply-templates select="self::entry" mode="entry-icon-img" /> }
          }
        </xsl:attribute>
      </div>
    </div>
  </xsl:template>

  <xsl:template match="entry[img/@src]" mode="entry-icon-img">
    <xsl:text>"src":"</xsl:text>
    <xsl:value-of select="img/@src" />
    <xsl:text>","alt":"</xsl:text>
    <xsl:value-of select="img/@alt" />
    <xsl:text>"</xsl:text>
  </xsl:template>

  <xsl:template match="entry[not(img/@src)]" mode="entry-icon-img">
    <xsl:text>"src":"</xsl:text>
    <xsl:value-of select="concat($protocol_name, '://', $server_name, ':', $protocol_port, '/views/intranet/images/icons/dummy.png')" />
    <xsl:text>","alt":"</xsl:text>
    <xsl:value-of select="'dummy'" />
    <xsl:text>"</xsl:text>
  </xsl:template>

  <!-- In publicatieomgeving ivm. frontend-editing: hanteer `EdtIdxItm_` ipv. ID 'Itm_' , om een maatwerk-bewerkingsdialoog te kunnen bieden -->
  <xsl:template match="content[page/@pagetype = 'index']/feed/entry[@type = 'site' and @pagetype = 'artikel' and number(id) &gt; 0]" mode="grid-extra-attribs">
    <xsl:choose>
      <xsl:when test="$EnvPrv">
        <xsl:apply-imports />
      </xsl:when>
      <xsl:otherwise>
        <xsl:attribute name="id">
          <xsl:text>EdtIdxItm_</xsl:text>
          <xsl:value-of select="id" />
        </xsl:attribute>
      </xsl:otherwise>
    </xsl:choose>
  </xsl:template>

  <xsl:template match="content[page/@pagetype = 'index']/feed/entry/img/@alt" mode="xcopy">
    <xsl:attribute name="alt" />
  </xsl:template>

</xsl:stylesheet>
