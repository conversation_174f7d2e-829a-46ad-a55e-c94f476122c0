import { Observable } from 'rxjs';
import { map, tap } from 'rxjs/operators';
import { IPerson } from '../../persons/models';
import { PersonApiService } from '../../persons/services/person-api.service';

export type UserAuthorization =
  'like/DeleteLike' |
  'like/PostLike' |
  'person/AddEndorsement' |
  'person/AddSkill' |
  'person/AddWeblink' |
  'person/Delete' |
  'person/DeleteEndorsement' |
  'person/DeleteImage' |
  'person/DeleteSkill' |
  'person/DeleteWeblink' |
  'person/EditProfile' |
  'person/FollowGroup' |
  'person/FollowPerson' |
  'person/FollowProduct' |
  'person/GetExperts' |
  'person/GetRecentlyVisitedPersons' |
  'person/IgnoreProduct' |
  'person/LinkPerson' |
  'person/ListFeed' |
  'person/SuggestSkill' |
  'person/ToggleHiddenField' |
  'person/UpdateBirthDate' |
  'person/UpdateDashboardBlock' |
  'person/UpdateDataField' |
  'person/UpdateDepartments' |
  'person/UpdateEmail' |
  'person/UpdateImage' |
  'person/UpdateJobFunctions' |
  'person/UpdateLocations' |
  'person/UpdateMailerInterval' |
  'person/UpdateOrganisation' |
  'person/UpdatePhone' |
  'person/UpdateProjects' |
  'person/UpdatePropertyList1' |
  'person/UpdatePropertyList2' |
  'person/UpdateRoomNumber' |
  'person/UpdateSocial' |
  'person/UpdateSpot' |
  'person/UpdateTeams' |
  'person/UpdateWorkDays' |
  'person/UpdateWorkHistory' |
  'person/ViewImage' |
  'person/ViewProfile';

export interface IUserConstructor {
  authorizations: Record<UserAuthorization, boolean>;
  id: string;
  fullName: string;
  followPersons: IPerson[];
  profileImage: string;
  role: string;
  loginName: string;
}

export class User {
  id: string;

  authorizations: Record<UserAuthorization, boolean>;

  fullName: string;

  followPersons: string[];

  profileImage: string;

  role: string;

  loginName: string;

  constructor(private personApiService: PersonApiService, data: IUserConstructor) {
    this.id = data.id;
    this.authorizations = data.authorizations;
    this.fullName = data.fullName;
    this.followPersons = data.followPersons.map(p => p.id);
    this.profileImage = data.profileImage;
    this.role = data.role;
    this.loginName = data.loginName;
  }

  isAuthorized(key: UserAuthorization): boolean {
    return this.authorizations[key];
  }

  followPerson(personId: string): Observable<void> {
    return this.personApiService.followPerson(this.id, personId)
      .pipe(
        tap(() => this.followPersons = [...this.followPersons, personId]),
        map(() => void 0), // TODO: Combine tap and map operators.
      );
  }

  unfollowPerson(personId: string): Observable<void> {
    return this.personApiService.unfollowPerson(this.id, personId)
      .pipe(
        tap(() => this.followPersons = this.followPersons.filter(id => id !== personId)),
        map(() => void 0),
      );
  }

  isFollowingPerson(personId: string): boolean {
    return this.followPersons.includes(personId);
  }

  isCommunityManager(): boolean {
    return this.role === 'communityManager';
  }
}
