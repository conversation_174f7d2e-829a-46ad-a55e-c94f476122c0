import { Component, EventEmitter, Input, Output } from '@angular/core';

import { MetaComponent } from '../../../meta/models';
import { ICommentSettings } from '../../comments.settings';
import { CommentAuthorizer } from '../../models/authorizer.model';
import { IComment } from '../../models/comment.model';

@Component({
  selector: 'ips-comment-base',
  template: '',
})
export class CommentBaseComponent {
  @Input()
  settings!: ICommentSettings;

  @Input()
  canReply = false;

  @Input()
  authorizer!: CommentAuthorizer;

  @Input()
  comment!: IComment;

  @Output()
  reply = new EventEmitter<string>();

  @Output()
  remove = new EventEmitter<string>();

  metaComponents = [MetaComponent.Likes];

  editMode = false;
}
