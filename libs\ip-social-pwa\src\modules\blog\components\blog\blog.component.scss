@import "../../../../scss/variables";
@import "../../../../scss/mixins";

$ips-blog-edit-background-color: $ion-content-background;

ion-grid.ips-edit-mode {
  background-color: $ips-blog-edit-background-color;
  padding-bottom: $ips-unit * 2;
}

ips-meta {
  @include cancel-grid-padding();

  border-top: 1px solid var(--ion-color-light-shade);
  height: $ips-meta-height;
}

ion-grid {
  padding-bottom: 0;
}
