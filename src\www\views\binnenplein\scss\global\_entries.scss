$pzh-entry-image-width: 220px;

span.iprox-content.iprox-date {
  display: block;
}

.type-lijst {
  .type-entry {
    margin-bottom: $pzh-inner-padding;

    .grid-box {
      margin-top: 1rem;
    }

    &.elt-small .grid-box {
      padding-bottom: $pzh-unit;
    }

    .omschrijving {
      ul,
      ol {
        padding-left: 2rem;
      }
    }

    ul.media_downloads {
      padding-left: $pzh-unit * 2.5;

      a {
        color: $pzh-green;
        text-decoration: underline;
        text-decoration-thickness: $pzh-link-richcontent-hover-text-decoration-thickness;

        &:hover {
          color: $pzh-dark-green;
        }
      }
    }
  }

  &:not(.has-bgcolor) .type-entry.entry-has-image:not(.elt-small) .grid-inside {
    > .entry > .iprox-content.image {
      top: 20px;
    }
  }

  .type-entry.entry-has-image:not(.elt-small) {
    @include small {
      .grid-title,
      .grid-box {
        padding-left: $pzh-inner-padding;
        padding-right: $pzh-inner-padding;
      }
    }

    .grid-edge {
      min-height: $pzh-entry-image-width * (9 / 16);
    }

    .grid-title {
      margin-left: $pzh-entry-image-width;
      width: calc(100% - #{$pzh-entry-image-width});
    }

    .grid-inside {
      padding-left: 0;
      padding-right: 0;

      > .entry {
        display: block;

        > .iprox-content.image {
          margin-right: 0;
          margin-top: 0;
          position: absolute;
          top: 0;
          width: $pzh-entry-image-width !important;

          img {
            display: block;
          }
        }

        > .iprox-content:not(.image) {
          margin-left: $pzh-entry-image-width + $pzh-inner-padding !important;
        }
      }

      &:not(:last-child) {
        padding-bottom: 3rem;
      }
    }

    .grid-box {
      bottom: $pzh-unit;
      margin-left: $pzh-entry-image-width;
      position: absolute;
    }
  }
}
