import { DatePipe } from '@angular/common';
import { Component, Input, OnInit } from '@angular/core';

import { IAnniversaryPreview, IAnniversaryPreviewPeriod } from '../../models/anniversary-preview.model';

@Component({
  selector: 'ips-anniversary-preview',
  templateUrl: './anniversary-preview.component.html',
  styleUrls: ['./anniversary-preview.component.scss']
})
export class AnniversaryPreviewComponent implements OnInit {
  @Input()
  anniversaryPreview!: Array<IAnniversaryPreview>;

  @Input()
  startDay!: Date;

  @Input()
  endDay?: Date;

  anniversaryPeriod!: IAnniversaryPreviewPeriod;

  startDayString!: string;

  endDayString!: string;

  constructor(private datePipe: DatePipe) { }

  ngOnInit() {
    this.anniversaryPeriod = {
      period: {
        startDay: this.startDay,
        endDay: this.endDay ?? new Date(this.startDay.getFullYear(), this.startDay.getMonth(), this.startDay.getDate() + 6)
      },
      anniversaries: this.anniversaryPreview,
    };

    this.startDayString = this.datePipe.transform(this.anniversaryPeriod.period.startDay, 'd MMMM') ?? '';
    this.endDayString = this.datePipe.transform(this.anniversaryPeriod.period.endDay, 'd MMMM') ?? '';
  }
}
