import { ICommentSettings } from '../../comments/comments.settings';
import { IMetaSettings } from '../../meta/meta.settings';
import { IMicroblogSettings } from '../../microblog/microblog.settings';
import { IPersonConfig } from '../../persons/person.settings';
import { ICoreSettings } from '../core.settings';

export interface ISocialConfig {
  core: ICoreSettings;
  meta?: Partial<IMetaSettings>;
  comments?: Partial<ICommentSettings>;
  microblog?: Partial<IMicroblogSettings>;
  blog?: unknown; // TODO: SR: Pull settings to @ip/social-core
  person?: IPersonConfig;
}

export interface IWebSocialConfig {
  config: {
    downloadUrl: string;
    enableImpersonation: boolean;
    enableManualVisitTrigger: boolean;
    socialApiUrl: string;
    socialAssetsUrl?: string;
  };
  groupMeta: {
    enableEditLayout: boolean;
  };
  msalConfig: {
    authority: string;
    clientId: string;
    postLogoutRedirectUri: string;
    redirectUri: string;
  };
}
