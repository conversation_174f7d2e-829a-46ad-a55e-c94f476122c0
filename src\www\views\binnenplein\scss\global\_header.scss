.type-logo {
  a {
    @include nav-expanding-up {
      margin-left: -60px;
    }

    width: 280px;
  }
}

.type-zoeken {
  .zoekveld form {
    > .zoek {
      padding: 1.5rem;
      position: absolute;

      > .zoek {
        left: 50%;
        position: absolute;
        top: 50%;
        transform: translate(-50%, -50%);
      }
    }

    input {
      background-color: $pzh-input-background-color;
      box-shadow: inset 0 0 0 1px $pzh-border-color;
      padding-left: 3.5rem;

      &::placeholder {
        color: $pzh-placeholder-text-color;
        opacity: 1;
      }
    }
  }
}

@include nav-expanding {
  .type-logo {
    > .grid-element > .grid-edge {
      height: $pzh-nav-expanding-logo-height;
      position: relative;
    }

    a {
      width: 220px;

      @media screen and (min-width: #{$bl-mq-small + 1px}) and (max-width: $bl-mq-large) {
        margin-left: -48px;
      }

      @media screen and (max-width: $bl-mq-small) {
        margin-left: 30px;
      }
    }
  }

  .type-zoeken {
    height: inherit;
    width: 100% !important;

    .zoekveld {
      margin-top: 0.5rem;
    }
  }

  .type-primaire-navigatie {
    .elt-hidden-large {
      display: block;
    }
  }

  .z-navigatie {
    display: none;
  }

  // Social usermenu.
  .grid-blok.type-social-user-menu {
    margin-left: 0 !important;
    width: 100% !important;

    .grid-box {
      .ips-menu {
        margin-bottom: 1.5rem;
        margin-top: 1.5rem;
      }

      ips-person-avatar {
        right: 60px; // SR: avatar size.
        top: 50%;
        transform: translateY(-50%);

        @media screen and (max-width: $bl-mq-small) {
          right: 25px;
        }
      }
    }
  }
}

// START - Vertical alignments of header blocks.
.type-logo .iprox-content a {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
}

@include nav-expanding-up {
  $pzh-header-height: 120px;

  .z-header {
    .type-logo .grid-element > .grid-edge,
    .type-zoeken .grid-element > .grid-edge,
    .type-social-user-menu .grid-box {
      height: $pzh-header-height;
      position: relative;
    }

    .type-zoeken .zoekveld,
    .type-social-user-menu .grid-box > ips-user-menu {
      position: absolute;
      top: 50%;
      transform: translateY(-50%);
    }

    .type-zoeken .zoekveld {
      width: 90%;
    }

    .type-social-user-menu .grid-box {
      position: relative;

      > ips-user-menu {
        right: 0;
      }
    }
  }
}
// END - Vertical alignments of header blocks.

@include nav-expanding-up {
  .grid-zone.z-header {
    .type-primaire-navigatie {
      display: none;
    }

    .row-clear {
      display: none;
    }

    .grid-blok.type-social-user-menu {
      margin-left: 0;

      // SR: This is needed so that the usermenu will always fit with these massive gutters.
      .grid-box {
        margin-left: -2rem;
      }
    }
  }
}
