import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { RouterModule } from '@angular/router';

import { IonicModule } from '@ionic/angular';
import { IpPwaHeaderModule } from '@ip/pwa';

import { CommentsModule } from '../comments/comments.module';
import { SocialFormsModule } from '../forms/forms.module';
import { MetaModule } from '../meta/meta.module';
import { PersonModule } from '../person/person.module';
import { SharedModule } from '../shared/shared.module';
import { BlogSettings } from './blog.settings';
import { BlogAuthorComponent } from './components/author/author.component';
import { BlogFabComponent } from './components/blog-fab/blog-fab.component';
import { BlogListComponent } from './components/blog-list/blog-list.component';
import { BlogPageComponent } from './components/blog-page/blog-page.component';
import { BlogComponent } from './components/blog/blog.component';
import { BlogBodyComponent } from './components/body/body.component';
import { BlogEditorComponent } from './components/editor/editor.component';
import { BlogImageUploadProgressComponent } from './components/image/image-progress.component';
import { BlogImageComponent } from './components/image/image.component';
import { BlogPublishedComponent } from './components/published/published.component';
import { BlogTitleComponent } from './components/title/title.component';
import { BlogImageSrcDirective } from './directives/image-src.directive';
import { CanEditBlogPipe } from './pipes/can-edit.pipe';
import { BlogApiService } from './services/blog-api.service';
import { BlogResolver } from './services/blog.resolver';
import { NewBlogResolver } from './services/new-blog.resolver';

@NgModule({
  declarations: [
    BlogPageComponent,
    BlogAuthorComponent,
    BlogBodyComponent,
    BlogComponent,
    BlogEditorComponent,
    BlogFabComponent,
    BlogListComponent,
    BlogImageUploadProgressComponent,
    BlogImageComponent,
    BlogPublishedComponent,
    BlogTitleComponent,
    BlogImageSrcDirective,
    CanEditBlogPipe,
  ],
  providers: [
    BlogSettings,
    BlogApiService,
    BlogResolver,
    NewBlogResolver,
  ],
  imports: [
    CommonModule,
    RouterModule,
    SocialFormsModule,
    PersonModule,
    MetaModule,
    CommentsModule,
    SharedModule,
    IonicModule,
    IpPwaHeaderModule,
  ],
  exports: [
    BlogListComponent,
  ]
})
export class BlogModule { }
