import { Component, Input, OnInit } from '@angular/core';
import { FormControl, FormGroup } from '@angular/forms';

import { PersonSelectionService } from '../../../forms/components/person-selection/person-selection.service';

@Component({
  selector: 'ips-blog-author',
  templateUrl: './author.component.html',
  styleUrls: ['./author.component.scss'],
})
export class BlogAuthorComponent implements OnInit {
  @Input()
  enableAuthors!: boolean;

  @Input()
  editMode!: boolean;

  @Input()
  userId!: string;

  @Input()
  formGroup!: FormGroup;

  formControl!: FormControl;

  constructor(private personSelectionService: PersonSelectionService) { }

  ngOnInit() {
    this.formControl = this.formGroup.get('authors') as FormControl;

    if (this.formControl === null) {
      throw new Error('[blog] authors formcontrol not found.');
    }
  }

  openModal() {
    this.personSelectionService.open(this.formControl, 'blog.authors.label');
  }

  removeAuthor(personId: string) {
    this.formControl.patchValue(this.formControl.value.filter((p: string) => p !== personId));
  }
}
