﻿<p class="ips-loading grid-realign" ng-if="PostViewCtrl.isLoading"><i class="fa-solid fa-spin fa-spinner"></i></p>

<div class="grid-zone grid_12 z-post-view z-post-not-found" ng-if="!PostViewCtrl.isLoading && PostViewCtrl.post === undefined">
  <div class="grid-blok grid_12 rol-social-group-components has-elt-breakpoints">
    <div class="grid-element">
      <div class="grid-edge">
        <div class="grid-box">
          <ips-component data-component="not-found">
            <content>
              <ips-not-found></ips-not-found>
            </content>
          </ips-component>
        </div>
      </div>
    </div>
  </div>
</div>

<div class="grid-zone grid_12 z-backbuttons z-backbuttons-static" ng-if=":: PostViewCtrl.post">
  <div class="grid-blok grid_12 rol-backbuttons type-backbuttons has-elt-breakpoints">
    <div class="grid-element">
      <div class="grid-edge">
        <div class="grid-box">
          <button type="button" class="btn ips-button ips-primary" ng-click="PostViewCtrl.back()">
            <i class="fa-solid fa-angle-double-left" aria-hidden="true"></i>
            <span>{{:: 'view.posts.post.back' | ipRefText }}</span>
          </button>
        </div>
      </div>
    </div>
  </div>
</div>

<div class="grid-zone grid_12 z-post-view" ng-if=":: PostViewCtrl.post">
  <div class="grid-row single-elt-row has-fullwidth-elt rol-rij-social-group-components has-no-list-icons has-no-link-icons has-no-button-icons">
    <div class="grid-blok grid_12 rol-social-group-components has-elt-breakpoints">
      <div class="grid-element">
        <div class="grid-edge">
          <div class="grid-box">
            <ips-component data-component="posts">
              <content>
                <ips-post
                  class="ips-component-item"
                  data-post="PostViewCtrl.post"
                  data-settings="PostViewCtrl.settings"
                ></ips-post>
              </content>
            </ips-component>
          </div>
        </div>
      </div>
    </div>
  </div>

  <div class="grid-row single-elt-row has-fullwidth-elt rol-rij-social-group-components has-elt-breakpoints has-no-list-icons has-no-link-icons has-no-button-icons" ng-if="PostViewCtrl.commentsConfig">
    <div class="grid-blok grid_12 rol-social-group-components type-social-posts has-elt-breakpoints has-no-list-icons has-no-link-icons has-no-button-icons">
      <div class="grid-element">
        <div class="grid-edge">
          <div class="grid-box">
            <ips-comments data-owner="{{ PostViewCtrl.commentOwner }}" data-config="{{ PostViewCtrl.commentsConfig }}"></ips-comments>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
