import { Component, Inject, Input, OnInit } from '@angular/core';

import { TINYMCE_CONFIG } from '@ip/social-web/src/modules/core/settings/tinymce-config';

@Component({
  selector: 'pzh-social-mailer-editor',
  template: '<editor *ngIf="init" [init]="init" [(ngModel)]="value" (ngModelChange)="onChange($event)"></editor>',
  styles: [`
    :host {
      display: block;
      max-width: 35em;
    }
  `],
})
export class SocialMailerEditorComponent implements OnInit {
  private config = {
    block_formats: 'Paragraaf=p;Kop=h2',
    valid_elements: 'a[!href|target:_blank|title],h2,strong,em,b,i,ul,ol,li,p',
    toolbar: 'formatselect | bold italic | bullist numlist | link | help',
    plugins: [
      'link',
      'lists',
      'paste',
      'autoresize',
      'help'
    ],
  };

  @Input() textareaId: string;

  init: object;

  value: string;

  textAreaElement: HTMLTextAreaElement;

  constructor(@Inject(TINYMCE_CONFIG) private baseConfig: any) { }

  ngOnInit() {
    this.init = { ...this.baseConfig, ...this.config };
    this.textAreaElement = document.getElementById(this.textareaId) as HTMLTextAreaElement;
    this.textAreaElement.hidden = true;
  }

  onChange(newValue: string) {
    this.textAreaElement.value = newValue;
  }
}
