import template from '!html-loader?minimize=true&conservativeCollapse=false&collapseInlineTagWhitespace=true!./File.html';

(function (angular) {
  'use strict';

  angular.module('Intranet.Files').component('ipsFile', {
    bindings: {
      file: '<',
      config: '&',
      fileTarget: '<',
      listModeFn: '&?listMode'
    },
    template: template,
    controller: ['$location', '$ngRedux', 'DialogsService', 'FileActions', 'FilesAuthorizer', 'FilesMover', 'IproxPageService', 'VisitCountService', 'Settings', 'User',
    function ($location, $ngRedux, DialogsService, FileActions, FilesAuthorizer, FilesMover, IproxPageService, VisitCountService, Settings, User) {
      var self = this;
      var file = self.file;

      self.$onInit = function () {
        var authorizer = FilesAuthorizer(file.owner);
        self.settings = Settings.get('Files', self.config);
        self.listMode = !!self.listModeFn && self.listModeFn();

        User.user.$promise.then(function (user) {
          self.permissions = {
            canEdit: (self.settings.enableTitle || self.settings.enableDescription || self.settings.enableTagging) && authorizer.canEditFile(user, file, self.fileTarget),
            canMove: authorizer.canMoveFile(user, file, self.fileTarget),
            canDelete: authorizer.canDeleteFile(user, file, self.fileTarget)
          };
        });

        var disconnect = $ngRedux.connect(null, FileActions)(function (state, actions) {
          self.store = actions;

          if (!self.listMode && !self.file.$$open) {
            self.store.openFile(self.fileTarget, self.file.id);
          }
        });

        self.$onDestroy = disconnect;
      };

      self.toggleOpen = function () {
        if (!self.listMode) {
          return;
        }

        if (!self.file.$$open) {
          VisitCountService.registerInlineVisit('Files', self.file.id);
        }
        else {
          VisitCountService.cancelRegisterInlineVisit('Files', self.file.id);
        }

        self.file.$$open
          ? self.store.closeFile(self.fileTarget, self.file.id)
          : self.store.openFile(self.fileTarget, self.file.id);

        if (self.settings.documentCallback) {
          IproxPageService.initPageLoadFunctions('Files');
        }
      };

      self.edit = function () {
        self.store.editFile(self.fileTarget, file.id);

        if (self.settings.documentCallback) {
          IproxPageService.initPageLoadFunctions('Files');
        }
      };

      self.cancelEdit = function () {
        self.store.cancelEditFile(self.fileTarget, file.id);

        if (self.settings.documentCallback) {
          IproxPageService.initPageLoadFunctions('Files');
        }
      };

      self.update = function (draft) {
        self.store.updateFile(self.fileTarget, self.file.id, draft.title, draft.description);
      };

      self.move = function () {
        FilesMover(self.fileTarget)(self.file).then(function (fileTarget) {
          self.store.moveFile(self.file, fileTarget);

          if (!self.listMode) {
            $location.path('/');
          }
          else if (self.settings.documentCallback) {
            IproxPageService.initPageLoadFunctions('Files');
          }
        });
      };

      self.delete = function () {
        DialogsService.confirm('files.files.delete', 'files.file.delete.confirmMessage').then(function () {
          self.store.deleteFile(self.fileTarget, file.id);

          if (!self.listMode) {
            $location.path('/');
          }
        });
      };
    }],
    controllerAs: 'FileCtrl'
  });
})(angular);
