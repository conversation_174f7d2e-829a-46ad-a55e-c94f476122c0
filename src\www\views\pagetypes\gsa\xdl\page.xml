<?xml version="1.0" encoding="utf-8"?>
<node name="gsa">
  <!--
    INTRANET GSA : binnen e-net: *********** / vanaf server: *********
    INTERNET GSA : binnen e-net: *********** / vanaf server: *********
   -->
  <node name="settings">
    <node name="host" alias="defined" nparam="gsa_search_host" collapse="@gsa_search_host">
      <field xpath="ancestor::content[1]/page/cluster[Nam='Instellingen']/veld[Nam='Host']/Src/text()" alias="gsa_search_host" type="set,attribute" />
    </node>

    <node name="proxystylesheet" alias="defined" nparam="gsa_search_proxystylesheet" collapse="@gsa_search_proxystylesheet">
      <!-- veld 'Stylesheet' is ingevuld -->
      <if xpath="ancestor::content[1]/page/cluster[Nam='Instellingen']/veld[Nam='Stylesheet']/Wrd/text()" expr="." />
      <field xpath="ancestor::content[1]/page/cluster[Nam='Instellingen']/veld[Nam='Stylesheet']/Wrd/text()" alias="gsa_search_proxystylesheet" type="set,attribute" />
    </node>
    <node name="proxystylesheet" alias="undefined" nparam="gsa_search_proxystylesheet" collapse="@gsa_search_proxystylesheet">
      <!-- veld 'Stylesheet' is NIET ingevuld : default 'amsterdam_nl'-->
      <if xpath="ancestor::content[1]/page/cluster[Nam='Instellingen']/veld[Nam='Stylesheet']/Wrd/text()" nexpr="." />
      <field name="'iprox_frontend'" alias="gsa_search_proxystylesheet" type="set,attribute" />
    </node>

    <node name="gsa_site" alias="defined" nparam="gsa_search_site" collapse="@gsa_search_site">
      <!-- veld 'site' is ingevuld -->
      <if xpath="ancestor::content[1]/page/cluster[Nam='Instellingen']/veld[Nam='Site']/Wrd/text()" expr="." />
      <field xpath="ancestor::content[1]/page/cluster[Nam='Instellingen']/veld[Nam='Site']/Wrd/text()" alias="gsa_search_site" type="set,attribute" />
    </node>
    <node name="gsa_site" alias="undefined" nparam="gsa_search_site" collapse="@gsa_search_site">
      <!-- veld 'site' is NIET ingevuld : default 'amsterdam_nl' -->
      <if xpath="ancestor::content[1]/page/cluster[Nam='Instellingen']/veld[Nam='Site']/Wrd/text()" nexpr="." />
      <field name="'pzh_alles'" alias="gsa_search_site" type="set,attribute" />
    </node>

    <node name="gsa_default_search_args" collapse="@gsa_default_search_args">
      <field name="'btnG=Google+zoeken&amp;orig=Gemeente&amp;filter=0&amp;proxyreload=1&amp;output=xml_no_dtd&amp;sort=date%3AD%3AL%3Ad1&amp;wc=200&amp;wc_mc=1&amp;oe=UTF-8&amp;ie=UTF-8&amp;ud=1&amp;exclude_apps=1&amp;s=0&amp;emmain=/gsa.html&amp;emdstyle=true&amp;emsingleres=/static/gsa'" alias="gsa_default_search_args" type="set,attribute" />
    </node>

    <!-- als je schoon binnenkomt (zonder modus) -->
    <node name="ZoeUrl" alias="start">
      <field name="'$$*gsa_search_host$$/search?$$*gsa_default_search_args$$&amp;proxystylesheet=$$gsa_search_proxystylesheet$$&amp;client=$$gsa_search_proxystylesheet$$&amp;site=$$gsa_search_site$$'" alias="ZoeUrl" type="set,attribute"/>
    </node>
    <!-- als je via de zoekbox in de header binnenkomt -->
    <node name="ZoeUrl" alias="append" param="Zoe,q,requiredfield" nparam="proxystylesheet">
      <field name="('$$ZoeUrl$$&amp;q=$$Zoe$$$$q$$&amp;requiredfield=$$requiredfield$$&amp;requiredfields=$$requiredfield$$')" alias="ZoeUrl" type="set,attribute"/>
    </node>
    <!-- als je vanuit een zoekopdracht doornavigeert -->
    <node name="ZoeUrl" alias="start" param0="proxystylesheet" param1="QUERY_STRING">
      <field name="'$$*gsa_search_host$$/search?$$QUERY_STRING$$'" alias="ZoeUrl" type="set,attribute"/>
    </node>
  </node>

  <node
    name="output"
    run="RetrieveGsa"
    collapse="*"
    gsa_ZoeUrl="$$*ZoeUrl$$"
    >

  </node>
</node>
