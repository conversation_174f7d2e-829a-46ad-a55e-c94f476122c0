<ion-fab
  horizontal="end"
  vertical="bottom"
  [activated]="activated"
  *transloco="let t"
>
  <ion-fab-button
    color="light"
    [closeIcon]="'checkmark-outline'"
    [title]="t(activated ? 'blogs.bloglist.saveBlog' : 'blogs.bloglist.editBlog')"
    [disabled]="synchronizing"
    (click)="onToggle()"
  >
    <ion-icon name="create-outline"></ion-icon>
  </ion-fab-button>
  <ion-fab-list side="top" (click)="doNothing($event)">
    <ion-fab-button
      color="light"
      [title]="t('blogs.bloglist.deleteBlog')"
      (click)="onRemove($event)"
    >
      <ion-icon name="trash-outline"></ion-icon>
    </ion-fab-button>
    <ion-fab-button
      [color]="canPublish && !synchronizing ? 'success' : 'light'"
      [disabled]="canPublish === false || synchronizing"
      [title]="t('blogs.bloglist.publishBlog')"
      (click)="onPublish($event)"
    >
      <ion-icon name="push-outline"></ion-icon>
    </ion-fab-button>
  </ion-fab-list>
</ion-fab>
