import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';

import { NgxsModule } from '@ngxs/store';

import { PersonSettings } from './person.settings';
import { PersonApiService } from './services/person-api.service';
import { PersonService } from './services/person.service';
import { PersonsState } from './state/persons.state';

@NgModule({
  declarations: [
  ],
  imports: [
    CommonModule,
    NgxsModule.forFeature([PersonsState]),
  ],
  exports: [
  ],
  providers: [
    PersonSettings,
    PersonApiService,
    PersonService,
  ]
})
export class PersonsModule { }
