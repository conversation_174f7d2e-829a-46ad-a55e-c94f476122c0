@import "../../../../scss/mixins";
@import "../../../../scss/variables";

.ips-blog-image-wrapper {
  @include cancel-grid-padding($top: true);

  height: 200px;
  overflow: hidden;
  position: relative;
}

ion-button {
  left: 50%;
  position: absolute;
  transform: translateX(-50%);
  bottom: 20px;
}

.ips-blog-image-background-mask {
  background-color: rgb(88, 85, 85);
  height: 100%;
  opacity: .3;
  position: absolute;
  width: 100%;
  z-index: -1;
}

.ips-blog-image {
  height: $ips-blog-image-height;
  left: 50%;
  position: absolute;
  transform: translateX(-50%);
}

.ips-blog-image-background {
  filter: blur(4px);
  position: absolute;
  top: 50%;
  transform: translateY(-50%) scale(1.02);
  width: 100%;
  z-index: -2;
}
