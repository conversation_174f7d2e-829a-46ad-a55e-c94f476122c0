﻿namespace IntranetProvincieZuidHolland.Modules.Handlers {
  using System.Collections.Generic;
  using System.Linq;
  using System.Net;
  using System.Xml;

  using InfoProjects.Dxe.Linq;
  using InfoProjects.Dxe.Process;
  using InfoProjects.Dxe.Util;

  using Newtonsoft.Json.Linq;

  /// <summary>
  /// The formulier handler.
  /// </summary>
  public class FormulierHandler : InfoProjects.Iprox.Modules.Formulier.Handler.FormulierHandler {
    /// <summary>
    /// The create mail unit.
    /// </summary>
    /// <param name="unit">
    /// The processunit.
    /// </param>
    /// <param name="staticPage">
    /// The static page.
    /// </param>
    /// <param name="context">
    /// The current context.
    /// </param>
    /// <param name="xmlDoc">
    /// The xml document.
    /// </param>
    /// <param name="r">
    /// The resultprops.
    /// </param>
    /// <returns>
    /// The <see cref="ProcessUnit"/>.
    /// </returns>
    protected override ProcessUnit CreateMailUnit(
      ProcessUnit unit,
      XmlDocument staticPage,
      ProcessContext context,
      XmlDocument xmlDoc,
      ResultProps r) {
      var mailUnit = base.CreateMailUnit(unit, staticPage, context, xmlDoc, r);

      var akaQuery = context.Sql.GetSqlWriter();
      akaQuery.AddTable("ItmTab", "Itm");
      akaQuery.AddField("Aka");
      akaQuery.AddCondition("ItmIdt", unit[Reserved.ID]);

      if (!string.IsNullOrEmpty(unit["groupId"]) && !string.IsNullOrEmpty(unit["targets"]) && akaQuery.GetFirstValueOrDefault<string>() == "groepsmail") {
        mailUnit[Reserved.TO] = unit["TO"];
      }
      
      return mailUnit;
    }
  }
}
