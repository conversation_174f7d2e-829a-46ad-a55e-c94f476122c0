import { <PERSON>pe, PipeTransform } from '@angular/core';

import { CommentAuthorizer, CommentPermission, PermissionFn } from '../models/authorizer.model';
import { IComment } from '../models/comment.model';

@Pipe({
  name: 'cAuthorize',
  pure: true,
})
export class CommentAuthorizerPipe implements PipeTransform {
  transform(permission: CommentPermission, authorizer: CommentAuthorizer, comment?: IComment): boolean {
    const fn: PermissionFn = `can${permission}` as PermissionFn;

    return authorizer.fn[fn](comment);
  }
}
