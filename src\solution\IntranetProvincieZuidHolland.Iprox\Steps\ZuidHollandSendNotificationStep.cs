﻿namespace IntranetProvincieZuidHolland.Iprox.Steps {
  using System;
  using System.Collections.Generic;
  using System.Linq;
  using System.Text.RegularExpressions;

  using InfoProjects.Dxe.Linq;
  using InfoProjects.Dxe.Process;
  using InfoProjects.Dxe.Prop;
  using InfoProjects.Dxe.Sql;
  using InfoProjects.Dxe.Util;
  using InfoProjects.Iprox.Handler;
  using InfoProjects.Iprox.Security.Handler;
  using InfoProjects.Iprox.Shared;

  /// <summary>
  /// The zuid holland send notification step.
  /// </summary>
  public class ZuidHollandSendNotificationStep : MaintenanceStep {
    /// <summary>
    /// Gets label
    /// </summary>
    public override string Name => "Send Daily Notifications";

    /// <summary>
    /// Gets flag label
    /// </summary>
    protected override string FlagLabel => "provzh:PzhSendNotification";

    /// <summary>
    /// Gets default frequence
    /// </summary>
    protected override MaintenanceFrequency DefaultFrequency => MaintenanceFrequency.Daily;

    /// <summary>
    /// Gets a value indicating whether maintenance steps are enabled
    /// </summary>
    protected override bool Enabled => CmsConfiguration.MaintenanceStepsEnabled && base.Enabled;

    /// <summary>
    /// Initializes class
    /// </summary>
    internal static void Init() {
      IproxMaintenanceHandler.Created += (o, e) => e.Add(new ZuidHollandSendNotificationStep());
    }

    /// <summary>
    /// Gets items to notify
    /// </summary>
    /// <param name="sql">SQL connection</param>
    /// <returns>Items to notify</returns>
    protected override IEnumerable<int> GetTodoList(SqlConnection sql) {
      var query = sql.GetSqlWriter()
        .AddTable("ItmTab")
        .AddCondition("SigDtm", Context.Current["today"])
        .AddField("ItmIdt");
      return query.GetValues<int>();
    }

    /// <summary>Process the PasswordPolicy task</summary>
    /// <param name="sql">SQL connection</param>
    /// <param name="itemsToNotify">Items to do</param>
    /// <returns>Items notified</returns>
    protected override IEnumerable<int> Commit(SqlConnection sql, IEnumerable<int> itemsToNotify) {
      this.SendNotification(sql, itemsToNotify);
      return itemsToNotify;
    }

    /// <summary>
    /// Checks if address is valid e-mail address
    /// </summary>
    /// <param name="emailAddress">E-mail address</param>
    /// <returns>Whether address is valid</returns>
    private static bool IsValidEmailAddress(string emailAddress) {
      return !String.IsNullOrEmpty(emailAddress) &&
             Regex.IsMatch(emailAddress, MaskTransform.PlugInstance.GetMask(16, null, "1", false));
    }

    /// <summary>
    /// Send a notification to a user
    /// </summary>
    /// <param name="sql">SQL connection</param>
    /// <param name="itemsToNotify">List with item ID's (ItmIdt)</param>
    private void SendNotification(SqlConnection sql, IEnumerable<int> itemsToNotify) {
      SqlWriter query = sql.GetSqlWriter()
        .AddTable("ItmTab")
        .AddCondition("ItmIdt", itemsToNotify)
        .AddField("ItmIdt");
      query.Distinct = true;

      var userItems = query.GetValues<int>().ToList();
      this.SendMail(userItems);
    }

    /// <summary>
    /// Create a mail processunit and send the mail
    /// </summary>
    /// <param name="userItems">List with item ID's (ItmIdt)</param>
    private void SendMail(IEnumerable<int> userItems) {
      Logger.Debug($"Send => {userItems.ToCsvString()}");
      if (!userItems.Any()) {
        return;
      }

      var emailAddress = Context.DefaultContext.GetProp("notification_emailadres", string.Empty);

      using (var pm = new PropMan()) {
        Settings.ExportSettings(pm);
        pm.SetProp("security_skip", "true", Settings.PropertyLevel("security"));
        using (var scheduler = Scheduler.GetInstance(pm)) {
          scheduler.ProcessStarted += delegate (ProcessContext context) {
            if (!IsValidEmailAddress(emailAddress)) {
              Logger.Error($"Invalid e-mail address for notification_emailadres {emailAddress}");
              return;
            }

            // Create process unit
            ProcessUnit mail = new ProcessUnit("Mail");
            mail.Action = InfoProjects.Dxe.Process.Action.SEND;
            mail[Reserved.TO] = emailAddress;
            mail[Reserved.FROM] = Context.Current.GetProp("notify_emailaddress", CmsConfiguration.DefaultEmailFromAddress);
            mail[Reserved.NAME] = Context.Current.GetProp("notify_name", CmsConfiguration.DefaultEmailFromName);
            mail[Reserved.SUBJECT] = Context.Current.GetProp("notify_subject", "IPROX Signaleringsmail");
            mail[Reserved.XDL] = Context.Current.GetProp("notify_xdl", "DailyNotify");
            mail[Reserved.XSL] = Context.Current.GetProp("notify_xsl", "DailyNotify");

            mail["ItmIdtLst"] = userItems.Distinct().ToCsvString();
            mail["AppIdt"] = "00001270";
            mail["iprox_language"] = "nl";
            mail["subject"] = mail[Reserved.SUBJECT];
            mail["subtitle"] = Context.Current.GetProp("notify_subtitle", "Van onderstaande pagina('s) is de signaleringsdatum bereikt.");
            context.Schedule(mail);
          };

          // Process
          scheduler.Process();
        }
      }
    }
  }
}
