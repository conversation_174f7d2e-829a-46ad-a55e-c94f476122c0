<h2 *ngIf="showTitle">{{ event.title }}</h2>

<dl class="ips-event-details">
  <div class="ips-event-meta">
    <ips-event-date [date]="event.date"></ips-event-date>

    <div *ngIf="event.location">
      <dt>
        <ion-icon slot="start" name="location-outline" [title]="'event.location.label' | transloco"></ion-icon>
      </dt>
      <dd>{{ event.location }}</dd>
    </div>
  </div>

  <ng-container *ngIf="detailed">
    <div *ngFor="let data of event.data" class="ips-event-data">
      <dt>{{ data.label }}</dt>
      <dd [innerHtml]="data.html"></dd>
    </div>
  </ng-container>
</dl>

<div class="ion-text-end ips-ion-button-wrapper">
  <ips-event-ics [event]="event"></ips-event-ics>
</div>
