import { Component, Input } from '@angular/core';

import { IPerson, PersonSearchService, VisitService } from '@ip/social-core';
import { Observable } from 'rxjs';
import { startWith, switchMap } from 'rxjs/operators';

@Component({
  selector: 'ips-recently-visited-persons',
  templateUrl: './recently-visited-persons.component.html',
})
export class RecentlyVisitedPersonsComponent {
  @Input()
  show!: boolean;

  recentlyVisitedPersons$: Observable<IPerson[]>;

  constructor(searchService: PersonSearchService, visitService: VisitService) {
    this.recentlyVisitedPersons$ = visitService.onPersonVisitSuccess$
      .pipe(
        startWith(undefined),
        switchMap(() => searchService.recentlyVisited()),
      );
  }
}
