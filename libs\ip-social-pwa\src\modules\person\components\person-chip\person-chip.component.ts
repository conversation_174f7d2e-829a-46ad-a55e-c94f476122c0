import { Component, Input, OnInit } from '@angular/core';

import { <PERSON>erson, PersonService } from '@ip/social-core';
import { Observable } from 'rxjs';

@Component({
  selector: 'ips-person-chip',
  templateUrl: './person-chip.component.html',
  styleUrls: ['./person-chip.component.scss'],
})
export class PersonChipComponent implements OnInit {
  @Input()
  id!: string;

  @Input()
  icon!: string;

  @Input()
  showAvatar = true;

  person$!: Observable<IPerson>;

  constructor(private personService: PersonService) { }

  ngOnInit() {
    this.person$ = this.personService.person$(this.id);
  }
}
