import { Reference } from '../../core/models';

export interface IComment {
  id: string;
  userId: string;
  reference: Reference;
  creationDate: Date;
  draft?: IComment;
  publicationDate: Date | null;
  lastPublicationDate: Date | null;
  lastModifiedDate: Date | null;
  commentCount: number;
  hasChanges: boolean;
  body: string;
  workingBody: string;
  attachments: IAttachment[];
  workingAttachments: Array<IAttachment | IAttachmentUpload>;
}

export interface Comments {
  commentCount: number;
  comments: ICommentTree[];
  draft?: IComment;
}

export interface ICommentTree extends IComment {
  comments: ICommentTree[];
}

export interface IWorkingBodyUpdate {
  hasChanges: boolean;
  lastModifiedDate: Date;
  workingBody: string;
}

export interface IAttachmentUpload {
  placeholder: true;
  fileId: string;
  fileName: string;
}

/** TODO: Move this to a generic attachments-module. */
export interface IAttachment {
  id: string;
  userId: string;
  reference: Reference;
  fileId: string;
  file: IIntranetFile;
}

/** TODO: Move this to a generic attachments-module. */
export interface IIntranetFile {
  // content: null;
  contentLength: number;
  contentType: string;
  fileName: string;
  // formatSize: null;
  id: string;
  // metadata: {};
  owner: Reference;
  // subRef: null;
  userId: string;
}
