﻿namespace IntranetProvincieZuidHolland.Modules.Handlers {
  using System.Collections.Generic;
  using InfoProjects.Dxe.Linq;
  using InfoProjects.Dxe.Prop;
  using InfoProjects.Iprox.Security.Handler;
  using InfoProjects.Iprox.Security.Lightbox;

  /// <summary>
  /// Front-end editing entrances
  /// </summary>
  public static class FrontendEditFaq {
    /// <summary>
    /// Registers plug
    /// </summary>
    public static void Register() {
      LightboxHandler.KeyProperties.Add("EdtFaqItmIdt");
      LightboxHandler.RegisterMappingDelegate("EdtFaqItmIdt", "ItmIdt", GetEdtFaqItmIdt2ItmIdtMapping);
      LightboxHandler.RegisterContextDelegate("EdtFaqItmIdt", GetItmIdtContext);
    }

    /// <summary>
    /// Gets mapping from EdtFaqItmIdt to ItmIdt
    /// </summary>
    /// <param name="lightbox">Lightbox handler</param>
    /// <returns>Mapping from EdtFaqItmIdt to ItmIdt</returns>
    private static IDictionary<int, int> GetEdtFaqItmIdt2ItmIdtMapping(ILightbox lightbox) {
      return lightbox.GetMapping("ItmIdt", lightbox.GetValues("EdtFaqItmIdt"), "ItmTab");
    }

    /// <summary>
    /// Gets item context
    /// </summary>
    /// <param name="lightbox">Lightbox handler</param>
    /// <param name="itemId">Item identity</param>
    /// <returns>Item context</returns>
    private static PropCollection GetItmIdtContext(ILightbox lightbox, int itemId) {
      PropCollection context = null;
      Item item;
      if (lightbox.Mappings.ItemMapping.TryGetValue(itemId, out item) && item.Sts == 4) {
        context = new PropCollection();
        context["ItmIdt"] = itemId.ToIproxString();
        context["Sts"] = item.Sts.ToIproxString();
        context["CorDtm"] = item.CorDtm.ToIproxString();
        context["CorTyd"] = item.CorTyd.ToIproxString();
        context["LstPubDtm"] = item.LstPubDtm.ToIproxString();
        context["LstPubTyd"] = item.LstPubTyd.ToIproxString();
        context["ItmTyp"] = item.ItmTyp.ToIproxString();
        context["PagTypIdt"] = item.PagTypIdt.ToIproxString();
        context["SitIdt"] = item.SitIdt.ToIproxString();
      }

      return context;
    }
  }
}
