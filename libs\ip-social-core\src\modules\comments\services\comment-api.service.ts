import { HttpHeaders } from '@angular/common/http';
import { Injectable } from '@angular/core';

import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';

import { reportUploadProgress } from '../../../utilities/file-progress.service';
import { IReference } from '../../core/models';
import { SocialHttpClient } from '../../core/services/api-client';
import { CommentParams } from '../models/comment-params.model';
import { IAttachment, IComment, ICommentTree, IWorkingBodyUpdate } from '../models/comment.model';
import { mapComment } from '../models/internal/api.mapper';
import { CommentsApi } from '../models/internal/api.models';

export interface IComments {
  totalCount: number;
  comments: ICommentTree[];
  draft?: IComment;
}

@Injectable()
export class CommentApiService {
  constructor(
    private socialApi: SocialHttpClient,
  ) { }

  get(reference: IReference, params = new CommentParams()): Observable<IComments> {
    return this.socialApi.get<CommentsApi.ICommentResponse>(
      `comment/${reference.collection}/${reference.id}`,
      { params: params.toHttpParams() }
    )
      .pipe(
        map(response => ({
          totalCount: response.totalCount,
          comments: response.items
            .map(item => mapComment(item)),
          draft: response.item ? mapComment(response.item) : undefined,
        }))
      );
  }

  changes(reference: IReference, fromDate: Date) {
    return this.socialApi.get<CommentsApi.IComment[]>(
      `comment/${reference.collection}/${reference.id}/changes`,
      { params: { fromDate: fromDate.toISOString() } }
    )
      .pipe(
        map(comments => comments.map(c => mapComment(c))),
      );
  }

  create(reference: IReference): Observable<ICommentTree> {
    return this.socialApi.post<CommentsApi.IComment>(`comment/${reference.collection}/${reference.id}`, undefined)
      .pipe(
        map(apiComment => mapComment(apiComment))
      );
  }

  remove(commentId: string): Observable<null> {
    return this.socialApi.delete<null>(`comment/${commentId}`);
  }

  discard(commentId: string): Observable<ICommentTree | null> {
    return this.socialApi.delete<CommentsApi.IComment | null>(`comment/${commentId}/draft`)
      .pipe(
        map(apiComment => apiComment ? mapComment(apiComment) : null)
      );
  }

  publish(comment: IComment): Observable<ICommentTree> {
    return this.socialApi.post<CommentsApi.IComment>(`comment/${comment.id}/publish`, JSON.stringify(comment.workingBody), {
      // eslint-disable-next-line @typescript-eslint/naming-convention
      headers: new HttpHeaders({ 'Content-Type': 'application/json' })
    })
      .pipe(
        map(apiComment => mapComment(apiComment))
      );
  }

  update(commentId: string, body: string): Observable<IWorkingBodyUpdate> {
    return this.socialApi.put<CommentsApi.IUpdateResponse>(`comment/${commentId}`, JSON.stringify(body), {
      // eslint-disable-next-line @typescript-eslint/naming-convention
      headers: new HttpHeaders({ 'Content-Type': 'application/json' })
    })
      .pipe(
        map((apiUpdateResponse): IWorkingBodyUpdate => ({
          hasChanges: apiUpdateResponse.hasChanges,
          lastModifiedDate: new Date(apiUpdateResponse.lastModifiedDate),
          workingBody: apiUpdateResponse.workingBody,
        }))
      );
  }

  postAttachment(commentId: string, file: File, trackingId: string): Observable<IAttachment> {
    const formData = new FormData();
    formData.append('file', file, file.name);
    formData.append('collection', 'Comment');
    formData.append('id', commentId);

    return this.socialApi.post<IAttachment>(`comment/${commentId}/attachment`, formData, {
      reportProgress: true,
      observe: 'events'
    })
      .pipe(
        reportUploadProgress(trackingId)
      );
  }

  removeAttachment(commentId: string, attachmentId: string): Observable<null> {
    return this.socialApi.delete<null>(`comment/${commentId}/attachment/${attachmentId}`);
  }
}
