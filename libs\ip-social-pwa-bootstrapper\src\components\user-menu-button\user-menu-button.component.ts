import { Component } from '@angular/core';

import { ResourceService, UserService } from '@ip/social-core';
import { Observable } from 'rxjs';
import { switchMap } from 'rxjs/operators';

@Component({
  selector: 'ips-user-menu-button',
  templateUrl: './user-menu-button.component.html',
  styleUrls: ['./user-menu-button.component.scss']
})
export class UserMenuButtonComponent {
  profileImage$: Observable<string>;

  constructor(userService: UserService, resourceService: ResourceService) {
    this.profileImage$ = userService.currentUser$.pipe(
      switchMap(user => resourceService.avatarSrc(user.id, user.profileImage))
    );
  }
}
