import { Component, Input, OnDestroy, OnInit } from '@angular/core';

import { Subject, timer } from 'rxjs';
import { switchMap, takeUntil } from 'rxjs/operators';

import { VisitService } from '../../services/visit.service';

@Component({
  selector: 'ips-person-visit-tracker',
  template: ''
})
export class PersonVisitTrackerComponent implements OnInit, OnDestroy {
  private readonly TIMER = 1000; // 1 second.

  private destroyed = new Subject();

  @Input()
  id!: string;

  constructor(private visitService: VisitService) { }

  ngOnInit() {
    this.visitTimer();
  }

  ngOnDestroy() {
    this.destroyed.next();
    this.destroyed.complete();
  }

  private visitTimer() {
    timer(this.TIMER)
      .pipe(
        takeUntil(this.destroyed),
        switchMap(() => this.visitService.registerPersonVisit(this.id)),
      )
      .subscribe();
  }
}
