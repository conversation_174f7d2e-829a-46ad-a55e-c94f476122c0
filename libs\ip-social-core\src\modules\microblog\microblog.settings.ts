import { Injectable } from '@angular/core';

import { FileType } from '../../utilities/file-types';
import { CommentConfig } from '../comments/comments.settings';
import { SocialConfigProvider } from '../core/config/config-provider.service';
import { ISocialConfig } from '../core/config/config.model';
import { MicroblogAccess } from './models/microblog-access.model';

export interface IMicroblogSettings {
  // TODO: Rename this to match CommentSettings
  disableAutoUpdate: boolean;
  // TODO: Rename this to match CommentSettings
  autoUpdatePollingInterval: number;
  itemsPerPage: number;
  depth: number;
  depthCount: number;
  height: number;
  postsMinlength: number;
  postsMaxlength: number;
  access: MicroblogAccess;
  enableLike: boolean;
  commentConfig: CommentConfig;
  enableAttachments: boolean;
  attachmentFileTypes: FileType[];
  imageFormatSize: string;
}

export type MicroblogConfig = Partial<IMicroblogSettings>;

@Injectable()
export class MicroblogSettings {
  private module: keyof ISocialConfig = 'microblog';

  private settings: IMicroblogSettings = {
    disableAutoUpdate: false,
    autoUpdatePollingInterval: 10,
    itemsPerPage: 20,
    depth: 2,
    depthCount: 3,
    height: 600,
    postsMinlength: 1,
    postsMaxlength: 280,
    access: 'public',
    enableLike: false,
    commentConfig: {},
    enableAttachments: true,
    attachmentFileTypes: [
      'image',
      'pdf',
      'document',
      'powerpoint',
      'spreadsheet',
    ],
    imageFormatSize: '128h'
  };

  constructor(settings: SocialConfigProvider) {
    Object.assign(this.settings, settings.get<IMicroblogSettings>(this.module));
  }

  get(config: MicroblogConfig): IMicroblogSettings {
    const settings: IMicroblogSettings = Object.assign({}, this.settings, config);

    settings.commentConfig = {
      count: settings.itemsPerPage,
      depth: settings.depth,
      depthCount: settings.depthCount,
      minLength: settings.postsMinlength,
      maxLength: settings.postsMaxlength,
      enableLike: settings.enableLike,
      autoUpdate: !settings.disableAutoUpdate,
      autoUpdateInterval: settings.autoUpdatePollingInterval,
      enableAttachments: settings.enableAttachments,
      attachmentFileTypes: settings.attachmentFileTypes,
      imageFormatSize: settings.imageFormatSize,
    };

    return settings;
  }
}
