@import "../../../../scss/variables";
@import "../../../../scss/mixins";

.ips-blog-list-heading {
  display: flex;
  justify-content: space-between;

  ion-list-header {
    @include font-size(-2);
  }
}

ion-item {
  --inner-padding-top: $ips-unit;
  --inner-padding-bottom: $ips-unit;

  position: relative;

  .ips-blog-status {
    display: inline-block;
    height: $ips-blog-status-size;
    margin: 0;
    position: absolute;
    right: 0;
    top: $ips-unit;
    width: $ips-blog-status-size;

    &.ips-blog-wip {
      color: var(--ion-color-warning-shade);
    }
  }

  ion-label h2 {
    color: var(--ion-color-primary);
    font-weight: 600;
  }
}

.ips-close-blog-list {
  float: right;
}
