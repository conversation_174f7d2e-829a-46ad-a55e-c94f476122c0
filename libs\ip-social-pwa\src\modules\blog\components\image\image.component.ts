import { Component, Input, OnChang<PERSON>, <PERSON><PERSON><PERSON>roy, SimpleChang<PERSON> } from '@angular/core';
import { FormGroup } from '@angular/forms';

import { CoreSettings, FileProgressService, FileTypes, Upload } from '@ip/social-core';
import { Subscription } from 'rxjs';

import { BlogService } from '../../services/blog.service';

@Component({
  selector: 'ips-blog-image',
  templateUrl: './image.component.html',
  styleUrls: ['./image.component.scss'],
})
export class BlogImageComponent implements OnChanges, OnDestroy {
  private subscription?: Subscription;

  @Input()
  editMode!: boolean;

  @Input()
  blogId!: string;

  @Input()
  image!: string | null;

  @Input()
  formGroup!: FormGroup;

  progress?: Upload<unknown> | undefined;

  accepts = FileTypes.acceptsImage;

  constructor(
    public settings: CoreSettings,
    private blogService: BlogService,
    private service: FileProgressService,
  ) { }

  ngOnChanges(changes: SimpleChanges) {
    if (changes.blogId?.currentValue !== changes.blogId?.previousValue) {
      this.subscription?.unsubscribe();
      this.subscription = this.service.get$(`blog-${changes.blogId.currentValue}-banner`)
        .subscribe(progress => this.progress = progress);
    }
  }

  ngOnDestroy() {
    this.subscription?.unsubscribe();
  }

  remove() {
    this.blogService.removeImage(this.blogId).subscribe();
  }
}
