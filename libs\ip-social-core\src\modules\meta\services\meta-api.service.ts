import { Injectable } from '@angular/core';

import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';
import { Reference } from '../../core/models';

import { SocialHttpClient } from '../../core/services/api-client';
import { ILike, ILikeCollection, ILikeCount, ILikeData, IMetaConstructor, IMetaConfig, Meta, MetaComponent } from '../models';
import { MetaApi } from '../models/internal/api.models';
import { IUserLike } from '../models/user-like.model';

export interface IPostLikeResponse {
  add: ILike[];
  remove: ILike[];
}

@Injectable()
export class MetaApiService {
  constructor(private socialApi: SocialHttpClient) { }

  get(requests: IMetaConfig[]): Observable<IMetaConstructor[]> {
    const references = requests.map(r => r.reference);

    return this.socialApi.post<MetaApi.CountResponse>('meta/counts', references)
      .pipe(
        map(response => this.mapResponse(requests, response)),
      );
  }

  like(meta: Meta, type: string): Observable<IPostLikeResponse> {
    return this.socialApi.post<IPostLikeResponse>('meta/like', { reference: meta.reference.toString(), type });
  }

  deleteLike(id: string): Observable<null> {
    return this.socialApi.delete<null>(`meta/like/${id}`);
  }

  private mapResponse(requests: IMetaConfig[], response: MetaApi.CountResponse): IMetaConstructor[] {
    return requests
      .map((request): IMetaConstructor | undefined => {
        const reference = new Reference(request.reference.id, request.reference.collection);
        const meta = response.find(r => reference.match(r.reference));

        if (!meta) {
          console.error('[meta] Request not found in meta response');
          return;
        }

        return {
          reference,
          components: request.components,
          timeStamp: new Date().getTime(),
          visitCount: request.components.includes(MetaComponent.Visits)
            ? meta.visits.count
            : undefined,
          commentCount: request.components.includes(MetaComponent.Comments)
            ? meta.comments.count
            : undefined,
          likes: request.components.includes(MetaComponent.Likes)
            ? this.mapLikeCollectionNew(meta.likes, meta.userLikes)
            : undefined,
        };
      })
      .filter((meta): meta is IMetaConstructor => !!meta);
  }

  private mapLikeCollectionNew(likes: ILikeCount[], userLikes: IUserLike[]): ILikeCollection {
    const data = likes.reduce((likeData: ILikeData, like): ILikeData => {
      likeData[like.type] = {
        count: like.count
      };

      return likeData;
    }, {});

    return {
      count: likes.length,
      userLikes,
      data,
    };
  }
}
