<ion-list class="list">
  <ion-list-header><PERSON><PERSON> waarop ik werk</ion-list-header>
  <ion-item>
    <ol *transloco="let t;">
      <li *ngFor="let box of workdayBoxes">

        <abbr [title]="t('workDays.' + box.label)">{{ box.label }}</abbr>

        <div class="ips-dayparts">
          <div
            *ngFor="let part of box.parts"
            class="ips-daypart"
            [class.ips-daypart-active]="part.active"
            [title]="t('workDays.' + part.label)"
          >
            <ion-icon *ngIf="part.active" slot="start" name="checkmark-sharp"></ion-icon>
          </div>
        </div>

      </li>
    </ol>
  </ion-item>
</ion-list>
