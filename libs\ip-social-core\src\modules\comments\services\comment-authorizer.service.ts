import { Inject, Injectable, InjectionToken, Optional } from '@angular/core';

import { from, Observable, of, throwError } from 'rxjs';
import { map, switchMap, take } from 'rxjs/operators';

import { IReference, User } from '../../core/models';
import { UserService } from '../../core/services/user.service';
import { SocialEnhancementsCommentAuthorizer } from '../authorizers/social-enhancements-comment.authorizer';
import { ICommentSettings } from '../comments.settings';
import { CommentAuthorizer } from '../models/authorizer.model';

export interface ICommentAuthorizerFactory {
  entities: string[];

  authorizer$: (reference: IReference, settings: ICommentSettings, user: User) => Observable<CommentAuthorizer>;
}

export const COMMENT_AUTHORIZER = new InjectionToken<ICommentAuthorizerFactory>('COMMENT_AUTHORIZER');

@Injectable()
export class CommentAuthorizerService {
  constructor(
    private userService: UserService,
    @Optional()
    @Inject(COMMENT_AUTHORIZER)
    private authorizers: ICommentAuthorizerFactory[],
    @Optional()
    @Inject('LegacyIproxAuthorization')
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    private iproxAuthorization: any,
  ) { }

  authorizer$(reference: IReference, settings: ICommentSettings): Observable<CommentAuthorizer> {
    const { authorizer$ } = this.authorizers.find(a => a.entities.includes(reference.collection)) ?? this.authorizers.find(a => a.entities.includes('default')) ?? {};

    if (!authorizer$) {
      return throwError('[CommentAuthorizer] No authorizer found');
    }

    return this.userService.currentUser$
      .pipe(
        take(1),
        switchMap(user => authorizer$(reference, settings, user)
          .pipe(
            switchMap(authorizer => this.overridePermissionsForModerator(authorizer, user, reference))
          )),
      );
  }

  private overridePermissionsForModerator(authorizer: CommentAuthorizer, user: User, reference: IReference): Observable<CommentAuthorizer> {
    if (this.iproxAuthorization && authorizer instanceof SocialEnhancementsCommentAuthorizer) {
      return from(this.iproxAuthorization.isModerator(user.loginName, reference.collection, reference.id) as Promise<boolean>)
        .pipe(
          map((isModerator) => {
            if (isModerator) {
              authorizer.fn.canEdit = () => true;
              authorizer.fn.canRemove = () => true;
            }

            return authorizer;
          })
        );
    }

    return of(authorizer);
  }
}
