<configuration xmlns:xdt="http://schemas.microsoft.com/XML-Document-Transform">
  <system.webServer>
    <modules>
      <add name="ForceAuthentication" type="InfoProjects.Dxe.Http.ForceAuthenticationModule, Dxe" xdt:Transform="Insert" />
    </modules>
    <handlers>
      <add name="sitemapindex" verb="GET" type="IntranetProvincieZuidHolland.Modules.Handlers.SitemapIndexHttpHandler, IntranetProvincieZuidHolland.Modules" path="sitemapindex.xml" xdt:Locator="Match(name)" xdt:Transform="SetAttributes" />
      <add name="sitemappersons" verb="GET" type="IntranetProvincieZuidHolland.Modules.Handlers.SitemapHttpHandler, IntranetProvincieZuidHolland.Modules" path="sitemappersons.xml" xdt:Transform="Insert" />
      <add name="sitemapblogs" verb="GET" type="IntranetProvincieZuidHolland.Modules.Handlers.SitemapHttpHandler, IntranetProvincieZuidHolland.Modules" path="sitemapblogs.xml" xdt:Transform="Insert" />
      <add name="sitemapquestions" verb="GET" type="IntranetProvincieZuidHolland.Modules.Handlers.SitemapHttpHandler, IntranetProvincieZuidHolland.Modules" path="sitemapquestions.xml" xdt:Transform="Insert" />
      <add name="sitemapposts" verb="GET" type="IntranetProvincieZuidHolland.Modules.Handlers.SitemapHttpHandler, IntranetProvincieZuidHolland.Modules" path="sitemapposts.xml" xdt:Transform="Insert" />
      <add name="sitemapevents" verb="GET" type="IntranetProvincieZuidHolland.Modules.Handlers.SitemapHttpHandler, IntranetProvincieZuidHolland.Modules" path="sitemapevents.xml" xdt:Transform="Insert" />      
    </handlers>
    <httpProtocol>
      <customHeaders>
        <add name="Content-Security-Policy" value="frame-ancestors 'self' *.pzh.nl *.iprox.nl" xdt:Locator="Match(name)" xdt:Transform="SetAttributes" />
      </customHeaders>
    </httpProtocol>
    <rewrite>
      <rules>
        <rule name="HTTP to HTTPS redirect" stopProcessing="true" xdt:Transform="Insert">
          <match url="(.*)" />
          <conditions>
            <add input="{HTTPS}" pattern="^OFF$" />
          </conditions>
          <action type="Redirect" url="https://{HTTP_HOST}/{R:1}" redirectType="Permanent" />
        </rule>
      </rules>
    </rewrite>
  </system.webServer>
</configuration>