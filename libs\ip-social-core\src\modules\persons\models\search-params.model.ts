import { HttpParams } from '@angular/common/http';

export interface ISearchParams {
  count: number;
  searchField: string;
  searchText: string;
  sort: string;
  start: number;
}

export class SearchParams implements ISearchParams {
  count = 15;

  searchField = 'all';

  searchText = '';

  sort = 'Name.LastName';

  start = 0;

  constructor(params?: Partial<ISearchParams>) {
    Object.assign(this, params);
  }

  update(params: Partial<ISearchParams>): void {
    Object.assign(this, params);
  }

  toHttpParams(): HttpParams {
    return new HttpParams({
      fromObject: {
        count: this.count.toString(),
        searchField: this.searchField,
        searchText: this.searchText,
        sort: this.sort,
        start: this.start.toString(),
      }
    });
  }
}
