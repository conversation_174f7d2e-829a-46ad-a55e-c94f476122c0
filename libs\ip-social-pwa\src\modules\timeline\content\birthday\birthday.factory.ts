import { ComponentFactoryResolver, Injectable } from '@angular/core';

import { TimelineContentFactory } from '../content-factory';
import { TimelineBirthdayComponent } from './birthday.component';

@Injectable()
export class BirthdayFactory extends TimelineContentFactory {
  component = TimelineBirthdayComponent;

  type = 'birthday';

  constructor(componentFactoryResolver: ComponentFactoryResolver) {
    super(componentFactoryResolver);
  }
}
