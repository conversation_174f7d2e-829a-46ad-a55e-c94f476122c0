<ng-container *ngIf="meta$ | async as meta; else loading">
  <span *ngIf="meta.visitCount !== undefined"><ion-icon name="eye-outline"></ion-icon>&nbsp;{{ meta.visitCount }}</span>
  <ng-container *ngIf="meta.commentCount !== undefined">
    <span
      *ngIf="allowCommentNavigation"
      [routerLink]="meta.reference | referenceLink"
      [queryParams]="{ scrollTo: 'comments' }"
    >
      <ion-icon name="chatbox-outline"></ion-icon>&nbsp;{{ meta.commentCount }}
    </span>
    <span *ngIf="!allowCommentNavigation">
      <ion-icon name="chatbox-outline"></ion-icon>&nbsp;{{ meta.commentCount }}
    </span>
  </ng-container>
  <ips-like
    *ngIf="meta.likes"
    [metaConfig]="config"
    [likes]="meta.likes"
  ></ips-like>
</ng-container>

<ng-template #loading>
  <span>laden...</span>
</ng-template>
