<?xml version="1.0" encoding="utf-8"?>
<xsl:stylesheet xmlns:xsl="http://www.w3.org/1999/XSL/Transform" version="1.0" xmlns:resources="urn:resources" extension-element-prefixes="resources">

  <xsl:import href="include/StartPage.xsl"/>

  <xsl:param name="RePublishFotoalbum.done"/>

  <!-- abstract variables -->
  <xsl:variable name="done" select="$RePublishFotoalbum.done" />
  <xsl:variable name="pagetype">fotoalbum</xsl:variable>

  <xsl:variable name="handler" select="concat('RePublish', translate(substring($pagetype, 1, 1), 'qwertyuiopasdfghjklzxcvbnm', 'QWERTYUIOPASDFGHJKLZXCVBNM'), substring($pagetype, 2, 10000))" />

  <xsl:template match="*" mode="body_inside_form">
    <xsl:if test="string($error = '')">
      <xsl:call-template name="editor"/>
      <form enctype="multipart/form-data">
        <xsl:apply-templates select="." mode="form_lib">
          <xsl:with-param name="form_AppIdt" select="$exitAppIdt"/>
        </xsl:apply-templates>
        <input type="hidden" name="{$handler}.$action" value="run"/>
        <input type="hidden" name="{$handler}.$id" value="{/data/site/item/@ItmIdt}"/>
        <table class="list">
          <xsl:call-template name="startpage-velden" />
        </table>
      </form>
    </xsl:if>
  </xsl:template>

  <xsl:template name="startpage-velden">
    <xsl:variable name="inhoud-velden" select="/data/site/item/page//clusterdefinition[Nam='Foto']/veld[site and not(Nam='Copyright')]" />
    <xsl:apply-templates select="$inhoud-velden" mode="startpage-veld" />
  </xsl:template>

  <xsl:template match="veld" mode="startpage-veld">
    <xsl:param name="Nam" select="translate(DefNam, ' ,/', '___')" />
    <xsl:call-template name="veld">
      <xsl:with-param name="definition" select="."/>
      <xsl:with-param name="curFieldName" select="concat($handler, '.', $Nam)"/>
      <xsl:with-param name="curClusterName" select="concat($handler, '_', $Nam)"/>
      <xsl:with-param name="curPath" select="concat('temp/', $User.GebIdt)"/>
      <xsl:with-param name="curSitIdt" select="$SitIdt"/>
      <xsl:with-param name="Wrd" select="''"/> 
    </xsl:call-template>
  </xsl:template>

</xsl:stylesheet>