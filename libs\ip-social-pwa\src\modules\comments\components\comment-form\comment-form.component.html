<form [formGroup]="form" (submit)="publish()" novalidate>
  <ion-item>
    <ion-label position="stacked">{{ 'comments.commentForm.add.comment' | transloco }}</ion-label>
    <ion-textarea
      #textarea
      formControlName="body"
      rows="2"
      autoGrow
      [maxlength]="settings.maxLength || $any(undefined)"
      (ionFocus)="focused = true"
      (ionBlur)="focused = false"
    ></ion-textarea>
  </ion-item>

  <input
    type="file"
    class="ion-hide"
    formControlName="attachments"
    [accept]="fileAccept"
    multiple
    #hiddenFileInput
  >

  <div class="ips-comment-form-footer" [class.ips-has-comment-length]="settings.maxLength">
    <span *ngIf="settings.maxLength" class="ips-comment-length">
      {{ settings.maxLength - body.value.length }}
      <span class="sr-only" role="status">
        {{ focused ? ('microblog.remaining' | transloco : { remaining: settings.maxLength - body.value.length }) : '' }}
      </span>
    </span>
    <span *ngIf="!settings.maxLength"></span>

    <div class="ips-comment-form-actions ion-text-end">
      <ion-button
        *ngIf="settings.enableAttachments"
        size="small"
        color="light"
        [attr.aria-label]="'comments.attachments' | transloco"
        (click)="hiddenFileInput.click()"
      >
        <ion-icon name="attach-outline"></ion-icon>
      </ion-button>

      <ion-button
        *ngIf="comment || reply"
        size="small"
        color="light"
        [attr.aria-label]="'comments.cancel' | transloco"
        (click)="discard()"
      >
        <ion-icon name="close"></ion-icon>
      </ion-button>

      <ion-button
        type="submit"
        size="small"
        [attr.aria-label]="(comment?.publicationDate ? 'comments.save' : 'comments.send') | transloco"
        [disabled]="inProgress || body?.invalid"
      >
        <ion-icon *ngIf="comment?.publicationDate" name="save"></ion-icon>
        <ion-icon *ngIf="!comment?.publicationDate"name="send"></ion-icon>
      </ion-button>
    </div>
  </div>

  <ul *ngIf="settings.enableAttachments && comment && comment.workingAttachments.length > 0" class="ips-attachments">
    <li *ngFor="let attachment of comment.workingAttachments">
      <ips-comment-attachment
        [attachment]="attachment"
        [editMode]="true"
        (remove)="removeAttachment(comment, $event)"
      ></ips-comment-attachment>
    </li>
  </ul>
</form>
