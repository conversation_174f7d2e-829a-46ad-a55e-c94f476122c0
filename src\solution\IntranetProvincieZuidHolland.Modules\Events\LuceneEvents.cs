﻿namespace IntranetProvincieZuidHolland.Modules.Events {
  using System.Linq;
  using System.Xml;
  using System.Xml.Linq;

  using InfoProjects.Dxe.Linq;
  using InfoProjects.Dxe.Sql;
  using InfoProjects.Dxe.Util;
  using InfoProjects.Iprox.Modules.Frontend.Handler;
  using InfoProjects.Iprox.Modules.Frontend.Shared;

  using Lucene.Net.Documents;

  /// <summary>
  /// Handles Lucene events.
  /// </summary>
  public static class LuceneEvents {
    #region Methods

    /// <summary>
    /// Attach to events
    /// </summary>
    internal static void Init() {
      LuceneIndexItemHandler.BeforeIndexItem += IndexProduct;
      LuceneIndexItemHandler.BeforeIndexItem += IndexAuteur;
      LuceneIndexItemHandler.BeforeIndexItem += IndexForHome;
      LuceneSearchHandler.BeforeIndexItemFound += AuteurFieldFound;
    }

    /// <summary>
    /// Add the 'Auteur' field
    /// </summary>
    /// <param name="e">The lucene event details.</param>
    private static void AuteurFieldFound(LuceneEventArgs e) {
      var auteur = e.Document.Get("auteur");
      if (e.TypeContent == LuceneContentType.RESULT && !string.IsNullOrEmpty(auteur)) {
        e.Result.SetAttribute("Auteur", auteur);
      }
    }

    /// <summary>
    /// Gets image element
    /// </summary>
    /// <param name="staticPage">Static page</param>
    /// <returns>Image element</returns>
    private static XElement GetImage(XmlDocument staticPage) {
      if (staticPage == null) {
        return null;
      }

      var icoon = (from veld in staticPage.SelectNodes("//veld[@GgvTyp='10' and Txt]").Cast<XmlElement>()
                   let img = (XmlElement)veld.SelectSingleNode("Txt//img[@width and @height]")
                   where
                     img != null && img.GetAttribute("width").To<int>() <= 120
                     && img.GetAttribute("height").To<int>() <= 120
                   select veld).FirstOrDefault();
      if (icoon != null) {
        var veld = icoon.ToXElement();
        veld.Element("Nam").Value = "Afbeelding voor index";
        return veld;
      }

      var productTypeNode =
        staticPage.SelectSingleNode("descendant-or-self::cluster[Nam = 'Meta']/veld[Nam = 'Type']/item/Trf");
      if (productTypeNode == null) {
        return null;
      }

      var productType = productTypeNode.InnerText;
      if (string.IsNullOrEmpty(productType)) {
        return null;
      }

      using (var sql = SqlConnection.GetSqlConnection()) {
        var query = sql.GetSqlWriter();
        query.AddTable("LibCatTab", "LibCat", JoinType.FIRST, null);
        query.AddCondition("LibCat.Nam", Context.DefaultContext.GetProp("product_iconen_libcatnam", "Product iconen"));
        query.AddTable("LibTab", "Lib", JoinType.INNER, "LibCat.LibCatIdt = Lib.LibCatIdt");
        query.AddCondition("Lib.Wrd", productType, "=", SqlOption.IgnoreCase);
        query.AddCondition("Lib.Src IS NOT NULL");
        query.AddCondition("Lib.Txt IS NOT NULL");
        query.AddCondition("Lib.Typ", "img");
        query.OrderBy = "LEN(Lib.Wrd)";
        query.AddField("Lib", "LibCatIdt", "LibCatIdt");
        query.AddField("Lib", "Wrd", "Wrd");
        query.AddField("Lib", "Src", "Src");
        query.AddField("Lib", "Txt", "Txt");
        var libItem = query.GetFirstRowOrDefault();
        if (libItem == null) {
          return null;
        }

        var src = string.Format("library/{0}/{1}", libItem["LibCatIdt"], libItem["Src"]);
        var div = XElement.Parse(libItem["Txt"]);
        if (div.Name.LocalName == "img") {
          div = new XElement("div", div);
        }

        var img = div.Descendants("img").FirstOrDefault();
        if (img == null) {
          return null;
        }

        img.SetAttributeValue("src", src);
        img.SetAttributeValue("class", "library-icon");
        return new XElement(
          "veld",
          new XAttribute("GgvTyp", "10"),
          new XElement("Nam", "Afbeelding voor index"),
          new XElement("Wrd", libItem["Wrd"]),
          new XElement("Src", src),
          new XElement("Txt", div));
      }
    }

    /// <summary>
    /// Indexes the 'Auteur' field
    /// </summary>
    /// <param name="e">The lucene event details.</param>
    private static void IndexAuteur(LuceneEventArgs e) {
      if (e.TypeContent != LuceneContentType.CONTENT || e.Page == null) {
        return;
      }

      var auteurWrd = e.Page.SelectSingleNode("descendant::cluster[Nam='Meta']/veld[Nam='Auteur' and Wrd]/Wrd");
      if (auteurWrd != null && !string.IsNullOrEmpty(auteurWrd.InnerText)) {
        e.Document.Add(
          LuceneIndexItemHandler.CreateField("auteur", auteurWrd.InnerText, Field.Store.YES, Field.Index.ANALYZED));
      }
    }

    /// <summary>
    /// Indexes a product.
    /// </summary>
    /// <param name="e">The lucene event details.</param>
    private static void IndexProduct(LuceneEventArgs e) {
      if (string.IsNullOrWhiteSpace(e.Pagetype) || !e.Pagetype.Equals("product")) {
        return;
      }

      if (e.Pagetype.Equals("product")) {
        SetProductSummaryLink(e);
        SetProductIndexIcon(e);
      }
    }

    /// <summary>
    /// Indexes the 'Homepage nieuws' field
    /// </summary>
    /// <param name="e">The lucene event details.</param>
    private static void IndexForHome(LuceneEventArgs e) {
      if (e.TypeContent != LuceneContentType.CONTENT || e.Page == null) {
        return;
      }

      var forHomeWrd = e.Page.SelectSingleNode("descendant::cluster[Nam='Meta']/veld[Nam='Homepage nieuws' and Wrd = '1']/Wrd");
      if (forHomeWrd != null && !string.IsNullOrEmpty(forHomeWrd.InnerText)) {
        e.Document.Add(
          LuceneIndexItemHandler.CreateField("homepagenews", "1", Field.Store.YES, Field.Index.ANALYZED));
      }
    }

    /// <summary>
    /// Sets a catalog icon as the product index icon, if the icon exists.
    /// </summary>
    /// <param name="e">The lucene event details.</param>
    private static void SetProductIndexIcon(LuceneEventArgs e) {
      if (e.Document.GetFields().Any(f => f.Name == "IdxImg")) {
        return;
      }

      var image = GetImage(e.Page);
      if (image != null) {
        e.Document.Add(
          LuceneIndexItemHandler.CreateField(
            "IdxImg",
            image.ToString(SaveOptions.DisableFormatting),
            Field.Store.YES,
            Field.Index.NO));
      }
    }

    /// <summary>
    /// Modifies the product summary based on the regel-nu link.
    /// </summary>
    /// <param name="e">The lucene event details.</param>
    private static void SetProductSummaryLink(LuceneEventArgs e) {
      if (e.Page == null) {
        return;
      }

      Logger.Trace("SetProductSummaryLink for product with ID {0}", e.ItmIdt);

      // in producten nooit de normale samenvatting (als die er zou zijn) gebruiken, maar alleen (zie onder) eventueel daar een regel-knop in pluggen
      e.Document.RemoveField("sam");

      var linkNode =
        e.Page.SelectSingleNode("descendant-or-self::cluster[Nam = 'Regel nu']/descendant::veld[Nam = 'Link']");
      if (linkNode == null) {
        return;
      }

      var siteLinkNode = linkNode.SelectSingleNode("link[@NarItmIdt]");
      var externLinkNode = linkNode.SelectSingleNode("Src");

      if (siteLinkNode == null && externLinkNode == null) {
        Logger.Trace("No link found for product with ID {0}", e.ItmIdt);
        return;
      }

      var summaryWithLink = string.Empty;
      if (externLinkNode != null) {
        summaryWithLink =
          string.Format(
            "<div class=\"summary\"><p class=\"regel-nu\"><a class=\"externLink\" href=\"{0}\">Regel nu</a></p></div>",
            externLinkNode.InnerXml);
      }
      else if (siteLinkNode != null) {
        summaryWithLink =
          string.Format(
            "<div class=\"summary\"><p class=\"regel-nu\"><a class=\"siteLink\" href=\"{0}\">Regel nu</a></p></div>",
            siteLinkNode.Attributes["NarItmIdt"].Value);
      }

      Logger.Trace("Setting summary with link for product with ID {0}, {1}", e.ItmIdt, summaryWithLink);

      e.Document.Add(new Field("sam", summaryWithLink, Field.Store.YES, Field.Index.NO));
    }

    #endregion
  }
}