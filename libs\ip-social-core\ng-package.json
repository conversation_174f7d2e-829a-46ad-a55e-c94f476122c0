{"$schema": "../../node_modules/ng-packagr/ng-package.schema.json", "dest": "../../dist/libs/ip-social-core", "assets": ["./assets/**", "./src/service-worker/service-worker.ts"], "lib": {"entryFile": "src/public-api.ts", "umdModuleIds": {"@azure/msal-common": "azure-msal-common", "@azure/msal-angular": "azure-msal-angular", "@azure/msal-browser": "azure-msal-browser", "@ionic/angular": "ionic-angular", "@ip/pwa": "ip-pwa", "@ngneat/transloco": "ngneat-transloco", "@ngxs/store": "ngxs-store", "idb-keyval": "idb-keyval", "uuid": "uuid"}}, "allowedNonPeerDependencies": ["."]}