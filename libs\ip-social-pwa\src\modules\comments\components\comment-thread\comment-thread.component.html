<ips-comment
  [class.ips-is-reply]="depth > 1"
  [settings]="settings"
  [authorizer]="authorizer"
  [comment]="comment"
  [canReply]="settings.depth > depth"
  (reply)="reply()"
  (remove)="remove(comment)"
></ips-comment>

<ips-comment-thread
  *ngFor="let c of comment.comments; trackBy: trackByFn"
  [settings]="settings"
  [depth]="depth + 1"
  [authorizer]="authorizer"
  [comment]="c"
  (reply)="reply()"
  (remove)="remove(c)"
></ips-comment-thread>

<div class="ips-comment-thread-actions">
  <ion-button
    *ngIf="settings.depth > depth && comment.comments.length > 0 && !openForm"
    (click)="reply()"
  >
    <span>{{ 'comments.replyThread' | transloco }}</span>
  </ion-button>
  <ion-button
    *ngIf="comment.comments.length < comment.commentCount"
    (click)="loadMore(comment)"
  >
    <span>{{ 'comments.more' | transloco: { count: comment.commentCount - comment.comments.length } }}</span>
  </ion-button>
</div>

<ng-container *ngIf="'Create' | cAuthorize:authorizer:comment">
  <ips-comment-draft-reminder
    *ngIf="comment.draft && !openForm"
    (click)="openForm = true"
  ></ips-comment-draft-reminder>

  <ips-comment-form
    *ngIf="openForm"
    [settings]="settings"
    [reference]="{ id: comment.id, collection: 'Comment' }"
    [comment]="comment.draft"
    [reply]="true"
    (cancel)="openForm = false"
    (saved)="openForm = false"
  ></ips-comment-form>
</ng-container>
