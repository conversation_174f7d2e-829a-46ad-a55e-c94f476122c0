@import "../../../../scss/variables";
@import "../../../../scss/mixins";

$ips-social-size: $ips-unit * 4;
$ips-socials-max-width: 350px;

ion-list-header {
  @include font-size(-2);
}

ion-item {
  > ion-icon {
    color: rgba(var(--ion-text-color-rgb, 0, 0, 0), .8);
    margin-inline-end: $ips-unit * 2;
  }
}

ol {
  display: flex;
  list-style-type: none;
  margin-bottom: 0;
  margin-top: 0;
  max-width: $ips-socials-max-width;
  padding-left: 0;
  width: 100%;

  li {
    flex: 0 0 $ips-unit * 5;
  }
}

ion-button {
  --background: rgba(var(--ion-color-primary-rgb), .2);
  --padding-end: 0;
  --padding-start: 0;

  color: var(--ion-color-primary);
  display: inline-block;
  height: $ips-social-size;
  position: relative;
  width: $ips-social-size;

  ion-icon {
    @include font-size(1);
  }
}
