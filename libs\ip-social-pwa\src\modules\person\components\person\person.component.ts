import { Component, Inject, Input, OnInit } from '@angular/core';

import { IPerson, PersonService } from '@ip/social-core';
import { Observable } from 'rxjs';

import { PERSON_ROUTE } from '../../person-route';

@Component({
  selector: 'ips-person',
  templateUrl: './person.component.html',
  styleUrls: ['./person.component.scss']
})
export class PersonComponent implements OnInit {
  @Input()
  id!: string;

  @Input()
  showAvatar = true;

  person$!: Observable<IPerson>;

  constructor(
    @Inject(PERSON_ROUTE) public personRoute: string,
    private personService: PersonService,
  ) { }

  ngOnInit() {
    this.person$ = this.personService.person$(this.id);
  }
}
