import { Component, ElementRef, OnDestroy, OnInit, ViewChild } from '@angular/core';

import { IonInfiniteScroll } from '@ionic/angular';
import { MenuService } from '@ip/pwa';
import { IPersonSearchState, PersonSearchService, SearchParams } from '@ip/social-core';
import { Observable, Subject } from 'rxjs';
import { takeUntil, tap } from 'rxjs/operators';

@Component({
  selector: 'ips-person-finder-page',
  templateUrl: './person-finder.component.html',
  providers: [PersonSearchService]
})
export class PersonFinderComponent implements OnInit, OnDestroy {
  private readonly COUNT = 15;

  private readonly destroyed = new Subject();

  @ViewChild(IonInfiniteScroll) infiniteScroll?: IonInfiniteScroll;

  state$: Observable<IPersonSearchState> = this.searchService.state$
    .pipe(tap(() => this.infiniteScroll?.complete()));

  value = '';

  constructor(public searchService: PersonSearchService, private menuService: MenuService, private elementRef: ElementRef) { }

  ngOnInit() {
    this.listenForMenuReset();
  }

  ngOnDestroy() {
    this.destroyed.next();
    this.destroyed.complete();
  }

  onSearchChange(event: CustomEvent<{ value: string }>) {
    if (event.detail?.value) {
      this.searchService.search(new SearchParams({
        count: this.COUNT,
        start: 0,
        searchText: event.detail.value,
      }));
    }
    else {
      this.clear();
    }
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  nextPage(event: CustomEvent<null>) {
    this.searchService.loadMore(this.COUNT);
  }

  private clear() {
    this.searchService.clear();
    this.value = '';
  }

  private listenForMenuReset(): void {
    this.menuService.get('persons')
      .pipe(takeUntil(this.destroyed))
      .subscribe(event => {
        const el = this.elementRef.nativeElement as HTMLElement;
        const pageHidden = el.className.includes('ion-page-hidden');

        if (event.selected && !pageHidden) {
          this.clear();
        }
      });
  }
}
