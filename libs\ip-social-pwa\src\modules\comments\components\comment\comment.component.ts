import { Component, ViewChild } from '@angular/core';

import { ActionSheetController, AlertController, IonRippleEffect } from '@ionic/angular';
import { ActionSheetButton } from '@ionic/core';
import { CommentBaseComponent } from '@ip/social-core';
import { TranslocoService } from '@ngneat/transloco';

@Component({
  selector: 'ips-comment',
  templateUrl: './comment.component.html',
  styleUrls: ['./comment.component.scss']
})
export class CommentComponent extends CommentBaseComponent {
  @ViewChild(IonRippleEffect)
  ionRippleEffect!: IonRippleEffect;

  constructor(
    private actionSheetController: ActionSheetController,
    private alertController: AlertController,
    private transloco: TranslocoService,
  ) {
    super();
  }

  pressed() {
    if (this.editMode || (!this.authorizer.fn.canRemove(this.comment) && !this.authorizer.fn.canEdit(this.comment))) {
      return;
    }

    this.ionRippleEffect.addRipple(0, 0)
      .then(removeRipple => {
        this.presentActionSheet();
        removeRipple();
      });
  }

  async presentActionSheet() {
    const buttons: ActionSheetButton[] = [
      {
        text: this.transloco.translate('comment.actionSheet.cancel'),
        icon: 'close',
        role: 'cancel',
      }
    ];

    if (this.authorizer.fn.canEdit(this.comment)) {
      buttons.unshift({
        text: this.transloco.translate('comment.actionSheet.edit'),
        icon: 'pencil',
        handler: () => this.editMode = true
      });
    }

    if (this.authorizer.fn.canRemove(this.comment)) {
      buttons.unshift({
        text: this.transloco.translate('comment.actionSheet.delete'),
        role: 'destructive',
        icon: 'trash',
        handler: () => this.removeInteractive(),
      });
    }

    const actionSheet = await this.actionSheetController.create({
      mode: 'md',
      buttons
    });

    await actionSheet.present();
  }

  async removeInteractive() {
    const alert = await this.alertController.create({
      header: this.transloco.translate('comment.removeAlert.title'),
      message: this.transloco.translate('comment.removeAlert.message'),
      buttons: [
        {
          cssClass: 'ips-alert-primary',
          text: this.transloco.translate('comment.removeAlert.confirmBtn'),
          handler: () => this.remove.emit(),
        },
        {
          text: this.transloco.translate('comment.removeAlert.cancelBtn'),
          role: 'cancel',
        },
      ]
    });

    await alert.present();
  }
}
