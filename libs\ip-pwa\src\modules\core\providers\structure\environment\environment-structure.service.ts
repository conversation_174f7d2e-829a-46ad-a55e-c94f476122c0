import { Inject, Injectable } from '@angular/core';
import { Router } from '@angular/router';

import { ModuleRoute } from '../../../models/module-route.model';
import { Structure } from '../../../models/structure.model';
import { MODULE_ROUTE } from '../module-route';
import { StructureService } from '../structure.service';
import { ENVIRONMENT_STRUCTURE } from './environment-structure';

@Injectable()
export class EnvironmentStructure extends StructureService {
  constructor(
    router: Router,
    @Inject(ENVIRONMENT_STRUCTURE) environmentStructure: Structure,
    @Inject(MODULE_ROUTE) moduleRoutes: ModuleRoute[],
  ) {
    super(router);

    moduleRoutes.forEach(r => this.registerRoute(r));
    this.configureRouter(environmentStructure, moduleRoutes);
    this.structure.next(environmentStructure);
  }

  init(): Promise<void> {
    return new Promise<void>((resolve) => resolve());
  }
}
