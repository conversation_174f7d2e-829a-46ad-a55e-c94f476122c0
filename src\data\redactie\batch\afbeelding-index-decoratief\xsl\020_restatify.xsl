<?xml version="1.0" encoding="utf-8"?>
<xsl:stylesheet xmlns:xsl="http://www.w3.org/1999/XSL/Transform" version="1.0">

  <xsl:output method="xml" version="1.0" encoding="utf-8" indent="yes" />

  <xsl:template match="/*">
    <data>
      <xsl:apply-templates select="/data/veld" />
    </data>
  </xsl:template>

  <xsl:template match="veld">
    <table>
      <xsl:attribute name="name">FlgItmTab.<xsl:value-of select="@ItmIdt" /></xsl:attribute>
      <prop name="$action">add</prop>
      <prop name="Nam">iprox:Restatify</prop>
      <prop name="Idt"><xsl:value-of select="@ItmIdt" /></prop>
    </table>
  </xsl:template>

</xsl:stylesheet>