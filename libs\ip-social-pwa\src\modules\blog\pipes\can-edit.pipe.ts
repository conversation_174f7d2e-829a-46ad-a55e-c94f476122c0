import { Pipe, PipeTransform } from '@angular/core';

import { UserService } from '@ip/social-core';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';

import { IBlog } from '../models';

@Pipe({
  name: 'canEditBlog'
})
export class CanEditBlogPipe implements PipeTransform {
  constructor(private userService: UserService) { }

  transform(blog: IBlog): Observable<boolean> {
    return this.userService.user$.pipe(
      map(user => user?.id === blog.userId)
    );
  }
}
