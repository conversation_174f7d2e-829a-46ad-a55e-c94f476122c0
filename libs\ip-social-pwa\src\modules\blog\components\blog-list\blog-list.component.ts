
import { Component, Host, Input, OnInit, Optional } from '@angular/core';

import { Observable } from 'rxjs';
import { startWith, switchMap, tap } from 'rxjs/operators';

import { ContentVisibleDirective } from '../../../shared/directives/content-visible.directive';
import { IBlogSummary } from '../../models';
import { BlogApiService } from '../../services/blog-api.service';

@Component({
  selector: 'ips-blog-list',
  templateUrl: './blog-list.component.html',
  styleUrls: ['./blog-list.component.scss'],
})
export class BlogListComponent implements OnInit {
  @Input()
  userId!: string;

  @Input()
  isOwnProfile!: boolean;

  @Input()
  fullList = false;

  @Input()
  route = './';

  highlightCount = 3;

  list$?: Observable<IBlogSummary[]>;

  constructor(
    private blogApiService: BlogApiService,
    @Host() @Optional() private contentVisible?: ContentVisibleDirective,
  ) { }

  ngOnInit() {
    this.list$ = this.blogApiService.onBlogChange$.pipe(
      startWith(undefined),
      switchMap(() => this.blogApiService.list(this.userId)),
      tap(list => {
        if (this.contentVisible) {
          this.contentVisible.show = list.length > 0;
        }
      }),
    );
  }
}
