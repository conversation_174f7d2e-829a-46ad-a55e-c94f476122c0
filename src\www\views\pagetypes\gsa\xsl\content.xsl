<?xml version="1.0" encoding="utf-8"?>

<xsl:stylesheet version="1.0"
                xmlns="http://www.w3.org/1999/xhtml"
                xmlns:xsl="http://www.w3.org/1999/XSL/Transform"
                xmlns:msxsl="urn:schemas-microsoft-com:xslt"
                xmlns:formatter="urn:formatter"
                extension-element-prefixes="msxsl formatter">

  <xsl:template match="content[page/@pagetype = 'gsa']" mode="content_specific">
    <xsl:apply-templates select="gsa/data/*" mode="xcopy" />
  </xsl:template>

  <xsl:template match="a[starts-with(@href,'/gsa.html')]/@href" mode="xcopy">
    <xsl:attribute name="data-orghref">
      <xsl:value-of select="." />
    </xsl:attribute>
    <xsl:attribute name="href">
      <xsl:value-of select="substring-after(.,'/gsa.html')" />
    </xsl:attribute>
  </xsl:template>

  <xsl:template match="script/@src" mode="xcopy">
    <xsl:attribute name="src">
      <xsl:value-of select="." />?v=1<xsl:text />
    </xsl:attribute>
  </xsl:template>

  <xsl:template match="form[@action = 'search']/@action" mode="xcopy">
    <xsl:attribute name="data-orgaction">
      <xsl:value-of select="." />
    </xsl:attribute>
    <xsl:attribute name="action">
      <xsl:text>.</xsl:text>
    </xsl:attribute>
  </xsl:template>

</xsl:stylesheet>
