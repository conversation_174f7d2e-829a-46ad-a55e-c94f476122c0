<?xml version="1.0" encoding="utf-8"?>
<xsl:stylesheet version="1.0"
                xmlns="http://www.sitemaps.org/schemas/sitemap/0.9"
                xmlns:xsl="http://www.w3.org/1999/XSL/Transform"
                xmlns:formatter="urn:formatter"
                extension-element-prefixes="formatter">

  <xsl:output method="xml" version="1.0" encoding="utf-8" indent="no" />

  <xsl:param name="protocol_name" select="'http'" />

  <xsl:template match="/">
    <urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">
      <xsl:apply-templates select=".//url" />
    </urlset>
  </xsl:template>

  <xsl:template match="url">
    <url>
      <xsl:if test="@loc != ''">
        <loc>
          <xsl:value-of select="@loc" />
        </loc>
      </xsl:if>
      <xsl:if test="@lastmod != ''">
        <lastmod>
          <xsl:value-of select="@lastmod" />
        </lastmod>
      </xsl:if>
    </url>
  </xsl:template>

</xsl:stylesheet>
