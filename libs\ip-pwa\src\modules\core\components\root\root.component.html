<ion-app>

  <ion-tabs *ngIf="menu$ | async as menu">
    <ion-tab-bar [hidden]="fullscreen$ | async">
      <ng-container *ngFor="let menuItem of menu.items">
        <ion-tab-button *ngIf="!menuItem.component" [tab]="menuItem.path" (click)="menuService.click($event, menuItem.path)">
          <ng-container *ngIf="menuItem.icon">
            <ion-icon [name]="menuItem.icon"></ion-icon>
            <ion-icon class="ip-tab-selected-icon" [name]="menuItem.icon | activeMenuIcon"></ion-icon>
          </ng-container>
          <ion-label>{{ menuItem.label }}</ion-label>
        </ion-tab-button>
        <ion-tab-button *ngIf="menuItem.component" [tab]="menuItem.path" (click)="menuService.click($event, menuItem.path)">
          <ip-custom-menu-button *ngIf="menuItem.component" [component]="menuItem.component"></ip-custom-menu-button>
        </ion-tab-button>
      </ng-container>
    </ion-tab-bar>
  </ion-tabs>

</ion-app>
