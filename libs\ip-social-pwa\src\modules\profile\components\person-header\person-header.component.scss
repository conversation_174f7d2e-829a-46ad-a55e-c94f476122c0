@import "../../../../scss/variables";
@import "../../../../scss/mixins";

$ips-person-header-height: $ips-unit * 16;
$ips-person-header-avatar-offset: $ips-unit * 2;

:host {
  @include cancel-grid-padding($top: true);

  display: block;
  background-color: var(--ion-color-primary);
  height: $ips-person-header-height * .5 + $ips-person-header-avatar-offset;
  margin-bottom: $ips-person-header-height * .5;

  img {
    border-radius: 50%;
    border: 6px solid #fff; // TODO replace with background ionic variable?
    height: $ips-person-header-height;
    left: 50%;
    position: absolute;
    transform:translate(-50%, $ips-person-header-avatar-offset);
    width: $ips-person-header-height;
  }
}

.ips-profile-actions {
  position: relative;
  right: $ips-unit * 2;
  text-align: right;
  top: $ips-person-header-height * .5;
}
