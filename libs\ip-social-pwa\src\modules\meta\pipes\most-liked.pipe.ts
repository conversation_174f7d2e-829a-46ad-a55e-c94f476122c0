import { Pipe, PipeTransform } from '@angular/core';

import { ILikeOption, IMostLiked } from '@ip/social-core';

@Pipe({
  name: 'mostLiked'
})
export class MostLikedPipe implements PipeTransform {
  transform(mostLiked: IMostLiked | undefined, options: ILikeOption[]): string {
    if (mostLiked) {
      const found = options.find(o => o.type === mostLiked.type);

      return found
        ? `${found.unicode} ${mostLiked.totalLikes}`
        : `${options[0].unicode} 0`;
    }

    return `${options[0].unicode} 0`;
  }
}
