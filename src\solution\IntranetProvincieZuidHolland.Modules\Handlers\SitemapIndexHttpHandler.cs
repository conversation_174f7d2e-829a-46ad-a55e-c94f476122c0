﻿namespace IntranetProvincieZuidHolland.Modules.Handlers {
  using System;
  using System.Threading;
  using System.Web;
  using System.Web.SessionState;

  using InfoProjects.Dxe.Util;

  public class SitemapIndexHttpHandler : <PERSON><PERSON>ttp<PERSON><PERSON><PERSON>, IReadOnlySessionState {
    public bool IsReusable => true;

    public void ProcessRequest(HttpContext context) {
      try {
        var target = string.Format(
          "/aspx/get.aspx?xdl={0}&xsl={1}{2}",
          "/views/binnenplein/xdl/seo/sitemap_index",
          "/views/binnenplein/xsl/seo/sitemap_index",
          context.Request.QueryString["refresh"] == "true" ? "&refresh=true" : string.Empty);

        context.Server.Transfer(target);
      }
      catch (Exception e) {
        if (e is ThreadAbortException) {
          // ignore
        }
        else {
          Logger.Exception(e, "Error in SitemapIndexHttpHandler");
        }
      }
    }
  }
}
