# This file is generated by running "yarn install" inside your project.
# Manual changes might be lost - proceed with caution!

__metadata:
  version: 8
  cacheKey: 10

"@infoprojects-local/baseline-styling@npm:14.2.1":
  version: 14.2.1
  resolution: "@infoprojects-local/baseline-styling@npm:14.2.1"
  peerDependencies:
    normalize.css: ^8.0.1
  checksum: 10/6e9bd025f19ec0cf03f83fb56f1326011137b92b6fefdfb7c30b951a07e25a0c7ff0f1efaab0f732f685b2c33a7fe47583c04fee3c6087d7959aadc0c743a24d
  languageName: node
  linkType: hard

"@infoprojects/baseline-grid@npm:1.5.0":
  version: 1.5.0
  resolution: "@infoprojects/baseline-grid@npm:1.5.0"
  checksum: 10/15201aae319fe760bd62d79b61479ffc0cf63491853706da3e7a042951f444d70632673374a8c1168d8dee614b53a27258756911ce4138e6d4aca143fcaa269e
  languageName: node
  linkType: hard

"pzh-binnenplein@workspace:.":
  version: 0.0.0-use.local
  resolution: "pzh-binnenplein@workspace:."
  dependencies:
    "@infoprojects-local/baseline-styling": "npm:14.2.1"
    "@infoprojects/baseline-grid": "npm:1.5.0"
  peerDependencies:
    normalize.css: 8.0.1
  languageName: unknown
  linkType: soft
