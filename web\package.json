{"name": "pzh-intranet-social", "version": "0.0.0", "scripts": {"ng": "ng", "postinstall": "yarn ngcc", "start": "yarn copy:local-settings && ng serve --port 4206", "start:test": "yarn copy:test-settings && ng serve --port 4206", "copy:local-settings": "node -e \"const fs = require('fs'); fs.copyFileSync('./src/environments/social-web.settings.local.js', './src/social-web.settings.js');\"", "copy:test-settings": "node -e \"const fs = require('fs'); fs.copyFileSync('./src/environments/social-web.settings.test-dev.js', './src/social-web.settings.js');\"", "build": "ng build --configuration production", "lint": "ng lint"}, "private": true, "dependencies": {"@angular/animations": "11.2.14", "@angular/cdk": "11.2.12", "@angular/common": "11.2.14", "@angular/compiler": "11.2.14", "@angular/core": "11.2.14", "@angular/forms": "11.2.14", "@angular/localize": "11.2.14", "@angular/material": "^11.2.13", "@angular/platform-browser": "11.2.14", "@angular/platform-browser-dynamic": "11.2.14", "@angular/router": "11.2.14", "@angular/upgrade": "11.2.14", "@ip/ng": "0.1.1", "@ip/social-core": "3.3.2522", "@ip/social-web": "3.3.2522", "@ng-bootstrap/ng-bootstrap": "9.0.0", "@pzh-local/mylex-search": "1.1.3", "core-js": "3.6.5", "file-saver": "2.0.5", "less": "3.11.1", "ngx-timeago": "^2.0.0", "pupa": "^3.1.0", "query-string": "5", "rxjs": "~6.6.3", "tinymce": "5.7.1", "tslib": "^2.0.0", "zone.js": "0.10.3"}, "devDependencies": {"@angular-devkit/build-angular": "0.1102.14", "@angular/cli": "11.2.14", "@angular/compiler-cli": "11.2.14", "@angular/language-service": "11.2.14", "@types/jasmine": "~3.6.0", "@types/jasminewd2": "2.0.8", "@types/node": "13.13.2", "codelyzer": "^6.0.0", "html-loader": "0.5.5", "jasmine-core": "~3.6.0", "jasmine-spec-reporter": "~5.0.0", "karma": "~6.3.4", "karma-chrome-launcher": "~3.1.0", "karma-coverage-istanbul-reporter": "~3.0.2", "karma-jasmine": "~4.0.0", "karma-jasmine-html-reporter": "^1.5.0", "protractor": "~7.0.0", "ts-node": "8.9.0", "tslint": "~6.1.0", "typescript": "4.1.5"}, "resolutions": {"@ngxs/store": "3.7.3"}}