import { HTTP_INTERCEPTORS } from '@angular/common/http';
import { Provider } from '@angular/core';

import {
  MSAL_GUARD_CONFIG,
  MSAL_INSTANCE,
  MSAL_INTERCEPTOR_CONFIG,
  MsalBroadcastService,
  MsalGuardConfiguration,
  MsalInterceptor,
  MsalInterceptorConfiguration,
  MsalService,
} from '@azure/msal-angular';
import {
  BrowserCacheLocation,
  Configuration,
  InteractionType,
  IPublicClientApplication,
  PublicClientApplication,
} from '@azure/msal-browser';

import { AuthenticationConfig } from '../base/authentication.config';
import { AuthenticationService } from '../base/authentication.service';
import { AzureAuthenticationService } from './azure-authentication.service';

// eslint-disable-next-line @typescript-eslint/naming-convention
export function MsalInstanceFactory(configuration: Configuration): IPublicClientApplication {
  return new PublicClientApplication(Object.assign(
    {
      cache: {
        cacheLocation: BrowserCacheLocation.LocalStorage
      },
    },
    configuration
  ));
}

// eslint-disable-next-line @typescript-eslint/naming-convention
export function MsalInterceptorConfigFactory(protectedResourceMap: Map<string, Array<string>>): MsalInterceptorConfiguration {
  protectedResourceMap.set('https://graph.microsoft.com/v1.0/me', ['User.Read']);
  protectedResourceMap.set('https://graph.microsoft.com/v1.0/**batch', ['Calendars.ReadWrite', 'Mail.Read', 'User.Read']);
  protectedResourceMap.set('https://graph.microsoft.com/v1.0/sites', ['Sites.ReadWrite.All']);

  return {
    interactionType: InteractionType.Redirect,
    protectedResourceMap,
  };
}

// eslint-disable-next-line @typescript-eslint/naming-convention
export function MsalGuardConfigFactory(): MsalGuardConfiguration {
  return {
    interactionType: InteractionType.Redirect
  };
}

export interface IAzureAuthenticationConfig {
  msalInstance: Configuration;
  protectedResourceMap: Map<string, Array<string>>;
}

// eslint-disable-next-line @typescript-eslint/naming-convention
export function AzureConfigurationFactory(): IAzureAuthenticationConfig {
  const protectedResourceMap = new Map<string, Array<string>>();

  const coreSettings = window.social.core;
  const { azure } = coreSettings.authentication;

  if (!azure?.authorityId || !azure?.clientId) {
    throw new Error('[azure] This application uses Azure authenticaton but authorityId or clientId is not configured!');
  }

  return {
    msalInstance: {
      auth: {
        authority: `https://login.microsoftonline.com/${azure.authorityId}`,
        clientId: azure.clientId,
        redirectUri: coreSettings.baseUrl,
        postLogoutRedirectUri: coreSettings.baseUrl,
      },
    },
    protectedResourceMap
  };
}

export class AzureAuthenticationConfig extends AuthenticationConfig {
  private configuration = AzureConfigurationFactory();

  constructor() {
    super();
  }

  getProviders(): Provider[] {
    const apiUrl = window.social.core.apiUrl;

    if (!apiUrl) {
      throw new Error('[azure] apiUrl is not defined on window.social.core.apiUrl');
    }

    this.addSocialToProtectedResourceMap(this.configuration.protectedResourceMap, apiUrl);

    return [
      {
        provide: MSAL_INSTANCE,
        useValue: MsalInstanceFactory(this.configuration.msalInstance)
      },
      {
        provide: MSAL_GUARD_CONFIG,
        useValue: MsalGuardConfigFactory()
      },
      {
        provide: MSAL_INTERCEPTOR_CONFIG,
        useValue: MsalInterceptorConfigFactory(this.configuration.protectedResourceMap)
      },
      {
        provide: HTTP_INTERCEPTORS,
        useClass: MsalInterceptor,
        multi: true
      },
      MsalService,
      MsalBroadcastService,
      {
        provide: AuthenticationService,
        useClass: AzureAuthenticationService
      },
    ];
  }

  private addSocialToProtectedResourceMap(protectedResourceMap: Map<string, Array<string>>, apiUrl: string) {
    protectedResourceMap.set(`${apiUrl}/user**`, ['api://social/Access.All']);
    protectedResourceMap.set(`${apiUrl}/meta**`, ['api://social/Access.All']);
    protectedResourceMap.set(`${apiUrl}/person**`, ['api://social/Access.All']);
    protectedResourceMap.set(`${apiUrl}/blog**`, ['api://social/Access.All']);
    protectedResourceMap.set(`${apiUrl}/device**`, ['api://social/Access.All']);
    protectedResourceMap.set(`${apiUrl}/timeline**`, ['api://social/Access.All']);
    protectedResourceMap.set(`${apiUrl}/token**`, ['api://social/Access.All']);
    protectedResourceMap.set(`${apiUrl}/iprox**`, ['api://social/Access.All']);
    protectedResourceMap.set(`${apiUrl}/comment**`, ['api://social/Access.All']);
    protectedResourceMap.set(`${apiUrl}/anniversary**`, ['api://social/Access.All']);
  }
}
