import { Component, Input, OnDestroy, OnInit } from '@angular/core';
import { FormControl, FormGroup } from '@angular/forms';

import { DeviceSettingsService, IPushSettings, IUserAgent } from '@ip/social-core';
import { EMPTY, merge, Subject } from 'rxjs';
import { map, mergeAll, switchMap, takeUntil } from 'rxjs/operators';

@Component({
  selector: 'ips-push-settings',
  templateUrl: './push-settings.component.html',
})
export class PushSettingsComponent implements OnInit, OnDestroy {
  private destroyed = new Subject<void>();

  @Input()
  userAgent!: IUserAgent;

  @Input()
  settings!: IPushSettings;

  activities: string[] = [];

  formGroup!: FormGroup;

  constructor(public deviceSettingsService: DeviceSettingsService) { }

  ngOnInit() {
    this.createForm(this.settings);
    this.patchChanges();
  }

  ngOnDestroy() {
    this.destroyed.next();
    this.destroyed.complete();
  }

  private createForm(settings: IPushSettings) {
    const formGroup = new FormGroup({
      enabled: new FormControl(settings.enabled),
      outsideOfficeHours: new FormControl(settings.outsideOfficeHours),
    });

    Object.entries(settings.activities).forEach(([name, value]) => {
      this.activities = [...this.activities, `activity.${name}`];
      formGroup.registerControl(`activity.${name}`, new FormControl(value));
    });

    this.formGroup = formGroup;
  }

  private patchChanges() {
    const observables = Object.entries(this.formGroup.controls)
      .map(([name, control]) => control.valueChanges.pipe(
        map(value => ({ name, value }))
      ));

    merge(observables)
      .pipe(
        mergeAll(),
        takeUntil(this.destroyed),
        switchMap(({ name, value }) => {
          if (name === 'enabled' || name === 'outsideOfficeHours') {
            return this.deviceSettingsService.updatePushSetting(name, value);
          }

          if (name.startsWith('activity.')) {
            return this.deviceSettingsService.updatePushActivity(name.replace('activity.', ''), value);
          }

          return EMPTY;
        })
      )
      .subscribe();
  }
}
