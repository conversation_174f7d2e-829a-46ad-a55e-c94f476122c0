<?xml version="1.0" encoding="utf-8"?>

<xsl:stylesheet
  xmlns:xsl="http://www.w3.org/1999/XSL/Transform"
  xmlns="http://www.w3.org/1999/xhtml"
  xmlns:social="urn:social"
  extension-element-prefixes="social"
  version="1.0"
>

  <xsl:import href="../../baseline/xsl/landingspagina.xsl" />
  <xsl:import href="plugs.xsl" />

  <!-- Als dit cluster een ng-blok is, geen ID opnemen voor front-end editing in oper -->
  <xsl:template match="page[@pagetype = 'workgroup' and @ItmIdt]//cluster[starts-with(Typ, 'workgroup-') or social:IsSocialComponent(.)]" mode="grid-extra-attribs">
    <xsl:apply-imports />
    <xsl:if test="not($EnvPrv)">
      <xsl:attribute name="id" />
    </xsl:if>
  </xsl:template>

  <!-- ... alle andere cluster-blokken: alleen bewerkbaar als huidige status pagina 'gepubliceerd' is -->
  <xsl:template match="page[@pagetype = 'workgroup' and @ItmIdt]//cluster[not(starts-with(Typ, 'workgroup-') or social:IsSocialComponent(.))]" mode="grid-extra-attribs">
    <xsl:apply-imports />
    <xsl:if test="not($EnvPrv) and ancestor::content[1]/workgroup/realstatus/@Sts != '4'">
      <xsl:attribute name="id" />
    </xsl:if>
  </xsl:template>

  <!-- Als dit cluster een gekoppelde faq-index is, ID opnemen voor front-end toevoegen -->
  <xsl:template match="page[@pagetype = 'workgroup' and @ItmIdt]//cluster[Nam = 'Vraag en antwoord index' and .//veld[Nam = 'Vraag en antwoord index']/link/@pagetype = 'faqindex']" mode="grid-extra-attribs">
    <!-- 'Bewerk dit blok' alleen in preview/redactiescherm -->
    <xsl:if test="$EnvPrv">
      <xsl:apply-imports />
    </xsl:if>

    <xsl:attribute name="data-id">
      <xsl:text>FaqItm_</xsl:text>
      <xsl:value-of select=".//veld[Nam = 'Vraag en antwoord index']/link[@pagetype = 'faqindex']/@NarItmIdt" />
    </xsl:attribute>
  </xsl:template>

  <!-- Als dit cluster een gekoppeld fotoalbum is, ID opnemen voor front-end toevoegen -->
  <xsl:template match="page[@pagetype = 'workgroup' and @ItmIdt]//cluster[Nam = 'Fotoalbum' and .//veld[Nam = 'Fotoalbum']/link/@pagetype = 'fotoalbum']" mode="grid-extra-attribs">
    <!-- 'Bewerk dit blok' alleen in preview/redactiescherm -->
    <xsl:if test="$EnvPrv">
      <xsl:apply-imports />
    </xsl:if>

    <xsl:attribute name="data-id">
      <xsl:text>EdtAlbItm_</xsl:text>
      <xsl:value-of select=".//veld[Nam = 'Fotoalbum']/link[@pagetype = 'fotoalbum']/@NarItmIdt" />
    </xsl:attribute>
  </xsl:template>

  <!-- Als dit cluster een gekoppelde index is, ID opnemen voor front-end toevoegen -->
  <xsl:template match="page[@pagetype = 'workgroup' and @ItmIdt]//cluster[Nam = 'Index' and index-link/@NarItmIdt]" mode="grid-extra-attribs">
    <!-- 'Bewerk dit blok' alleen in preview/redactiescherm -->
    <xsl:if test="$EnvPrv">
      <xsl:apply-imports />
    </xsl:if>

    <xsl:attribute name="data-id">
      <xsl:text>IdxItm_</xsl:text>
      <xsl:value-of select="index-link/@NarItmIdt" />
    </xsl:attribute>
  </xsl:template>

  <!-- Als dit cluster een gekoppelde evenementenagenda is, ID opnemen voor front-end toevoegen -->
  <xsl:template match="page[@pagetype = 'workgroup' and @ItmIdt]//cluster[Nam = 'Evenementenagenda' and evenementenagenda-link/@NarItmIdt]" mode="grid-extra-attribs">
    <!-- 'Bewerk dit blok' alleen in preview/redactiescherm -->
    <xsl:if test="$EnvPrv">
      <xsl:apply-imports />
    </xsl:if>

    <xsl:attribute name="data-id">
      <xsl:text>EvtItm_</xsl:text>
      <xsl:value-of select="evenementenagenda-link/@NarItmIdt" />
    </xsl:attribute>
  </xsl:template>

  <!-- Als dit cluster een inhoudsblok is, ID opnemen voor front-end bewerken -->
  <xsl:template match="page[@pagetype = 'workgroup' and @ItmIdt]//cluster[Nam = 'Inhoud']" mode="grid-extra-attribs">
    <xsl:choose>
      <xsl:when test="$EnvPrv">
        <xsl:apply-imports />
      </xsl:when>
      <xsl:otherwise>
        <xsl:attribute name="id">
          <xsl:text>EdtInhCls_</xsl:text>
          <xsl:value-of select="@PagClsIdt" />
        </xsl:attribute>
      </xsl:otherwise>
    </xsl:choose>
  </xsl:template>

  <!-- Als dit cluster een mediawidget-blok is.. -->
  <xsl:template match="page[@pagetype = 'workgroup' and @ItmIdt]//cluster[Nam = 'Mediawidget']" mode="grid-extra-attribs">
    <!-- 'Bewerk dit blok' alleen in preview/redactiescherm -->
    <xsl:if test="$EnvPrv">
      <xsl:apply-imports />
    </xsl:if>
  </xsl:template>

  <!-- ipv. ID 'Itm_' for frontend-editing, om een maatwerk-bewerkingsdialoog te kunnen bieden -->
  <xsl:template match="cluster[Nam = 'Vraag en antwoord index']//entry[@type = 'site' and @pagetype = 'faq' and number(id) &gt; 0]" mode="grid-extra-attribs">
    <xsl:choose>
      <xsl:when test="$EnvPrv">
        <xsl:apply-imports />
      </xsl:when>
      <xsl:otherwise>
        <xsl:attribute name="id">
          <xsl:text>EdtFaqItm_</xsl:text>
          <xsl:value-of select="id" />
        </xsl:attribute>
      </xsl:otherwise>
    </xsl:choose>
  </xsl:template>

  <!-- ipv. ID 'Itm_' for frontend-editing, om een maatwerk-bewerkingsdialoog te kunnen bieden -->
  <xsl:template match="cluster[Nam = 'Index']//entry[@type = 'site' and @pagetype = 'artikel' and number(id) &gt; 0]" mode="grid-extra-attribs">
    <xsl:choose>
      <xsl:when test="$EnvPrv">
        <xsl:apply-imports />
      </xsl:when>
      <xsl:otherwise>
        <xsl:attribute name="id">
          <xsl:text>EdtIdxItm_</xsl:text>
          <xsl:value-of select="id" />
        </xsl:attribute>
      </xsl:otherwise>
    </xsl:choose>
  </xsl:template>

  <!-- ipv. ID 'Itm_' for frontend-editing, om een maatwerk-bewerkingsdialoog te kunnen bieden -->
  <xsl:template match="cluster[Nam = 'Evenementenagenda']//entry[@type = 'site' and @pagetype = 'evenement' and number(id) &gt; 0]" mode="grid-extra-attribs">
    <xsl:choose>
      <xsl:when test="$EnvPrv">
        <xsl:apply-imports />
      </xsl:when>
      <xsl:otherwise>
        <xsl:attribute name="id">
          <xsl:text>EdtEvtItm_</xsl:text>
          <xsl:value-of select="id" />
        </xsl:attribute>
      </xsl:otherwise>
    </xsl:choose>
  </xsl:template>

</xsl:stylesheet>
