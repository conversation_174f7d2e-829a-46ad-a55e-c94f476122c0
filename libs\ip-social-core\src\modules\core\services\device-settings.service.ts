import { Injectable } from '@angular/core';

import { Observable, throwError } from 'rxjs';
import { tap } from 'rxjs/operators';

import { SocialHttpClient } from './api-client';
import { DeviceService } from './device.service';

@Injectable()
export class DeviceSettingsService {
  constructor(
    private socialApi: SocialHttpClient,
    private deviceService: DeviceService,
  ) { }

  updateDeviceName(name: string): Observable<void> {
    const deviceId = this.deviceService.stateSnapshot().device?.deviceId;

    if (!deviceId) {
      return throwError('Device not available');
    }

    return this.socialApi.patch<void>(`device/${deviceId}/devicename`, { name })
      .pipe(
        tap(() => this.deviceService.updateState(state => ({
          ...state,
          device: state?.device
            ? {
              ...state.device,
              deviceName: name
            }
            : undefined
        })))
      );
  }

  updatePushSetting(setting: string, value: boolean) {
    const deviceId = this.deviceService.stateSnapshot().device?.deviceId;

    if (!deviceId) {
      return throwError('Device not available');
    }

    return this.socialApi.patch(`device/${deviceId}/settings/push`, { [setting]: value })
      .pipe(
        tap(() => this.deviceService.updateState(state => ({
          ...state,
          device: state?.device
            ? {
              ...state.device,
              pushSettings: {
                ...state.device.pushSettings,
                [setting]: value
              }
            }
            : undefined
        })))
      );
  }

  updatePushActivity(activity: string, value: boolean) {
    const deviceId = this.deviceService.stateSnapshot().device?.deviceId;

    if (!deviceId) {
      return throwError('Device not available');
    }

    return this.socialApi.patch(`device/${deviceId}/settings/push/activity`, { activity, value })
      .pipe(
        tap(() => this.deviceService.updateState(state => ({
          ...state,
          device: state?.device
            ? {
              ...state.device,
              pushSettings: {
                ...state.device.pushSettings,
                activities: {
                  ...state.device.pushSettings.activities,
                  [activity]: value
                }
              }
            }
            : undefined
        })))
      );
  }
}
