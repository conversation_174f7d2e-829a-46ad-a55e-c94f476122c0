import { Component } from '@angular/core';
import { Router } from '@angular/router';

import { ModalController } from '@ionic/angular';
import { AuthenticationService, DeviceService } from '@ip/social-core';

import { BlogApiService } from '../../../blog/services/blog-api.service';

// DEPRECATED
@Component({
  selector: 'ips-person-menu',
  templateUrl: './person-menu.component.html',
})
export class PersonMenuComponent {
  constructor(
    public modalController: ModalController,
    private auth: AuthenticationService,
    private blogApi: BlogApiService,
    private router: Router,
    private deviceService: DeviceService,
  ) { }

  createBlog() {
    this.blogApi.create()
      .subscribe(blog => {
        this.modalController.dismiss();
        this.router.navigate(['/blog', blog.id], { queryParams: { editMode: true } });
      });
  }

  logout() {
    this.deviceService
      .unsubscribe()
      .subscribe(() => this.auth.logout());
  }
}
