const path = require('path');
const process = require('process');
const webpack = require('webpack');

const TsconfigPathsPlugin = require('tsconfig-paths-webpack-plugin');
const { injectManifest } = require('workbox-build');

const srcServiceWorker = path.join(__dirname, 'service-worker.ts');
const destServiceWorkerDir = path.join(process.cwd(), 'dist', 'apps', 'social-pwa');
const destServiceWorkerFileName = 'service-worker.js';

const prodSettings = {
  mode: 'production',
  entryFile: srcServiceWorker,
  distDir: destServiceWorkerDir,
  distFilename: destServiceWorkerFileName,
}

const devSettings = {
  mode: 'development',
  entryFile: srcServiceWorker,
  distDir: path.join(process.cwd(), 'apps', 'social-pwa', 'src'),
  distFilename: 'service-worker-dev.js',
};

function workboxConfig(distDir, distSwFile) {
  return {
    globDirectory: distDir,
    globFollow: true,
    globStrict: true,
    globIgnores: ['**/*-es5.*.js', '3rdpartylicenses.txt', 'settings.js'],
    maximumFileSizeToCacheInBytes: 5000000,
    swSrc: distDir + '\\' + distSwFile,
    swDest: distDir + '\\' + distSwFile
  };
}

function webpackConfig(settings) {
  return {
    mode: settings.mode,
    devtool: settings.mode === 'development' ? 'source-map' : undefined,
    entry: settings.entryFile,
    output: {
      path: settings.distDir,
      filename: settings.distFilename
    },
    plugins: settings.mode === 'production'
      ? [new webpack.NormalModuleReplacementPlugin(
        /\.\/sw-environment.ts/,
        './sw-environment.prod.js'
      )]
      // ? [new InjectWorkboxPlugin(workboxConfig(settings.distDir, settings.distFilename))]
      : [new webpack.NormalModuleReplacementPlugin(
        /\.\/sw-environment.ts/,
        './sw-environment.prod.js'
      )],
    module: {
      rules: [
        {
          test: /\.ts$/,
          loader: 'ts-loader',
          exclude: /node_modules/,
          options: {
            onlyCompileBundledFiles: true
          }
        }
      ]
    },
    resolve: {
      extensions: ['.ts', '.wasm', '.mjs', '.js', '.json'],
      plugins: [
        new TsconfigPathsPlugin({
          configFile: './apps/social-pwa/src/service-worker/tsconfig.json',
        }),
      ]
    }
  };
}

module.exports = env => {
  const settings = {
    dev: devSettings,
    prod: prodSettings
  };

  const swMode = env
    ? env.swMode || 'prod'
    : 'prod';

  const usedSettings = settings[swMode];

  if (!usedSettings) {
    throw new Error('Incorrect swMode!');
  }

  return webpackConfig(usedSettings);
};

class InjectWorkboxPlugin {
  constructor(config) {
    this.config = config;
  }

  apply(compiler) {
    compiler.hooks.done.tap('donePlugin', (stats) => {
      injectManifest(this.config)
        .then(({ count, size }) => {
          console.log(`Generated ${this.config.swDest}, which will precache ${count} files, totaling ${Math.ceil(size / 1024)} kB.`);
        })
        .catch(err => {
          console.error('Injectmanifest failed: ', err);
        });
    });
  }
}
