<ion-header>
  <ip-header></ip-header>
</ion-header>

<ion-content *transloco="let t;">
  <ng-container *ngIf="timeline$ | async as entries;">
    <ion-refresher
      slot="fixed"
      (ionRefresh)="refresh($event)"
      pullingIcon="arrow-dropdown"
      [disabled]="!isScrolledToTop"
    >
      <ion-refresher-content
        refreshingSpinner="circular"
        [refreshingText]="t('timeline.loading')"
      ></ion-refresher-content>
    </ion-refresher>

    <virtual-scroller
      #scroll
      [items]="entries"
      [enableUnequalChildrenSizes]="true"
      (vsEnd)="loadData($event, entries.length)"
      (vsUpdate)="scrollPos()"
    >

      <ips-timeline-entry
        *ngFor="let entry of scroll.viewPortItems; trackBy: trackByFn"
        [entry]="entry"
      ></ips-timeline-entry>

      <!-- Removed because it can be confusing, the loader should be absolutely positioned so it doesn't disturb the scroll length -->
      <!-- <div *ngIf="fetching$ | async" class="loader">{{ t('timeline.loading') }}</div> -->

    </virtual-scroller>

    <ips-timeline-new-entries-toast></ips-timeline-new-entries-toast>
  </ng-container>
</ion-content>
