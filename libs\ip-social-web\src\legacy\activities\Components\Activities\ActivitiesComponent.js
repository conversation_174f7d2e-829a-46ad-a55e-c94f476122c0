import template from '!html-loader?minimize=true&conservativeCollapse=false&collapseInlineTagWhitespace=true!./Activities.html';

(function (angular, debugActions) {
  'use strict';

  angular.module('Intranet.Activities').component('ipsActivities', {
    bindings: {
      blockId: '@',
      owner: '@',
      config: '&?'
    },
    template: template,
    controller: ['ActivitiesByType', 'Settings', 'User', function (ActivitiesByType, Settings, User) {
      var self = this;

      self.$onInit = function () {
        self.settings = Settings.get('Activities', self.config);
        self.settings.maxAge = typeof self.settings.maxAge !== 'number' ? -1 : self.settings.maxAge;
        self.settings.maxItems = typeof self.settings.maxItems !== 'number' ? -1 : self.settings.maxItems;

        self.activities = [];

        self.actions = debugActions.reduce(function (accumulator, action) {
          accumulator[action] = false;

          return accumulator;
        }, {});

        self.loadActivities();
      };

      self.updateList = function () {
        // only actions which are checked
        var actions = _(self.actions)
          .map(function (value, key) {
            return value && key;
          })
          .compact()
          .value();

        self.loadActivities({
          excludeSelf: true,
          actions: actions.join(',')
        });
      };

      self.loadActivities = function (extraParams) {
        if (self.hasMoreResults || !self.initialized) {
          var params = getLoadActivitiesParams();

          if (params) {
            ActivitiesByType(self.settings.type, angular.extend(params, extraParams)).then(function (results) {
              self.hasMoreResults = (results.data.length === self.settings.itemsPerPage + 2) && (self.settings.maxAge + self.settings.maxItems > -2);
              var resultsToAdd = self.initialized ? results.data.slice(1, self.settings.itemsPerPage + 1) : results.data.slice(0, self.settings.itemsPerPage);
              Array.prototype.push.apply(self.activities, resultsToAdd);

              self.initialized = true;
            });
          }
        }
      };

      function getLoadActivitiesParams() {
        var maxAge = self.settings.maxAge;
        var maxItems = self.settings.maxItems;

        var lastItemDate = self.activities.length > 0 ? new Date(self.activities[self.activities.length - 1].dateTime) : new Date();

        var maxAgeDate = null;
        if (maxAge > 0) {
          maxAgeDate = new Date();
          maxAgeDate.setDate(maxAgeDate.getDate() - maxAge);
        }

        var reachedMaxAge = maxAgeDate ? maxAgeDate >= lastItemDate : null;
        var reachedMaxItems = maxItems > 0 ? maxItems <= self.activities.length : null;

        if (reachedMaxAge || reachedMaxItems) {
          return null;
        }

        return {
          count: maxItems > 0 ? Math.min(self.settings.itemsPerPage, maxItems - self.activities.length) + 2 : self.settings.itemsPerPage + 2,
          fromDate: maxAge > 0 ? maxAgeDate.toISOString() : null,
          toDate: lastItemDate.toISOString()
        };
      }
    }],
    controllerAs: 'ActivitiesCtrl'
  });
})(angular, [
  // 'UpdateProfile',
  'UpdateProfileEmailAddress',
  'UpdateProfileWorkDays',
  'UpdateProfileDataField',
  'UpdateProfileTelephone',
  'FollowPerson',
  'UnFollowPerson',
  'FollowGroup',
  'UnFollowGroup',
  // 'FollowProduct',
  // 'UnFollowProduct',
  // 'IgnoreProduct',
  // 'UnIgnoreProduct',
  'AddDocument',
  // 'DeleteDocument',
  // 'UpdateDocument',
  // 'GetDocumentFile',
  'AddEvent',
  'DeleteEvent',
  'UpdateEvent',
  'AddQuestion',
  // 'DeleteQuestion',
  // 'UpdateQuestion',
  'AddAnswer',
  // 'DeleteAnswer',
  // 'UpdateAnswer',
  'AcceptAnswer',
  // 'ResetAcceptedAnswer',
  'AddAnswerComment',
  // 'DeleteAnswerComment',
  // 'UpdateAnswerComment',
  // 'GetQuestionFile',
  'AddSkill',
  // 'RemoveSkill',
  'EndorseSkill',
  'UnEndorseSkill',
  // 'UpdateGroup',
  // 'ActivateGroup',
  // 'AddMemberToGroup',
  // 'RemoveMemberFromGroup',
  'PublishBlog',
  // 'PostBlog',
  // 'UpdateBlog',
  // 'DeleteBlog',
  'CommentOnBlog',
  // 'CommentedOnBlog',
  // 'IproxPublishedGroup',
  // 'IproxPublishedFaq',
  // 'IproxPublishedGroupArticle',
  // 'NominatedBlogIsAccepted',
  // 'NominatedBlogIsRemoved',
  // 'NominatedBlogIsRejected',
  'AddWeblink',
  // 'DeleteWeblink',
  'ExpertLevelIncreased'
  // 'CreateGuest',
  // 'CreateMember',
  // 'CreateEmployee',
  // 'IproxPublishedEvent',
  // 'NominatedBlogIsHidden',
  // 'HiddenNominatedBlogIsShown',
  // 'MoveQuestion'
]);
