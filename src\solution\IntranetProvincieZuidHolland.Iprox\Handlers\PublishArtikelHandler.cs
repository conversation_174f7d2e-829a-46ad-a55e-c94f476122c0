﻿namespace IntranetProvincieZuidHolland.Iprox.Handlers {
  using InfoProjects.Dxe.Process;
  using InfoProjects.Dxe.Util;
  using InfoProjects.Iprox.Model;
  using InfoProjects.Iprox.Model.Fields;
  using InfoProjects.Iprox.Model.Util;

  using IntranetProvincieZuidHolland.Iprox.Model;

  /// <summary>
  /// Handles publish Artikel
  /// </summary>
  public class PublishArtikelHandler : PublishNewPageHandler<Artikel> {
    #region Methods

    /// <summary>
    /// Sets content of page.
    /// </summary>
    /// <param name="unit">
    /// The process unit.
    /// </param>
    /// <param name="page">
    /// The iprox page.
    /// </param>
    protected override void SetContent(ProcessUnit unit, Artikel page) {
      unit.SetValueOf(page.Meta.Samenvatting, "Samenvatting");
      unit.SetValueOf(page.Meta.AfbeeldingVoorIndex, "Afbeelding_voor_index");
      unit.SetValueOf(page.Inleiding, "Inleiding");
      unit.SetValueOf(page.Inhoud, "Inhoud");
      var userEmail = Context.Current.GetProp("User.Email");
      if (!string.IsNullOrEmpty(userEmail)) {
        page.Reactiemogelijkheid.EMailAttendering.Value = new AddressValue(userEmail);
      }
    }

    #endregion
  }
}