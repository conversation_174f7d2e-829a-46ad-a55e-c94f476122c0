﻿<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <ProductVersion>9.0.30729</ProductVersion>
    <TargetFramework>net48</TargetFramework>
    <LangVersion>10</LangVersion>
    <GenerateAssemblyInfo>false</GenerateAssemblyInfo>
    <TreatWarningsAsErrors>true</TreatWarningsAsErrors>
    <AssemblyTitle>IntranetProvincieZuidHolland.Iprox</AssemblyTitle>
    <OutputPath>bin\$(Configuration)\</OutputPath>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugType>full</DebugType>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
  </PropertyGroup>
  <ItemGroup>
    <PackageReference Include="InfoProjects.Dxe" Version="4.9.2513" />
    <PackageReference Include="InfoProjects.Iprox" Version="4.9.2513" />
    <PackageReference Include="InfoProjects.Iprox.Model" Version="4.9.2513" />
    <PackageReference Include="InfoProjects.Iprox.Security" Version="4.9.2513" />
    <PackageReference Include="RestSharp" Version="112.0.0" />
  </ItemGroup>
  <ItemGroup>
    <Reference Include="Frontend">
      <HintPath>..\..\..\com\Frontend.dll</HintPath>
    </Reference>
    <Reference Include="Publish">
      <HintPath>..\..\..\com\Publish.dll</HintPath>
    </Reference>
    <Reference Include="System.Data.DataSetExtensions" />
  </ItemGroup>
  <Target Name="PostBuild" AfterTargets="PostBuildEvent">
    <Exec Command="if $(ConfigurationName) == Debug (&#xA;  copy $(TargetDir)$(TargetName).* $(SolutionDir)..\..\deploy\redactie\www\bin&#xA;)" />
  </Target>
</Project>
