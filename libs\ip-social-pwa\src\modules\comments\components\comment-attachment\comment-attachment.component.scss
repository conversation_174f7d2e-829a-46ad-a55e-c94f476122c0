:host {
  display: flex;
  font-size: 0.8em;
  font-style: italic;
  line-height: 1.6em;
  position: relative;

  align-items: center;
  justify-content: center;

  &.ips-attachment-metadata {
    height: 120px;
    width: 120px;
  }
}

ion-button {
  bottom: 0;
  position: absolute;
  right: 0;
}

ion-icon.ips-file-icon {
  display: block;
  font-size: 28px;
  text-align: center;
}

.ips-attachment-progress,
a {
  align-items: center;
  display: flex;
  flex-direction: column;
  height: 100%;
  justify-content: center;
  width: 100%;
}

ion-progress-bar {
  bottom: 0;
  position: absolute;
  width: 100%;
}

.ips-file-size {
  font-size: 0.8em;
}

.ips-file-title {
  -webkit-line-clamp: 3;
  line-height: 1.2;
  max-height: 3.6em;
  word-break: break-all;
  overflow: hidden;
  text-overflow: ellipsis;
}

span {
  text-align: center;
  display: block;
}

img {
  max-height: 120px;
  max-width: 120px;
}

.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0,0,0,0);
  border: 0;
}
