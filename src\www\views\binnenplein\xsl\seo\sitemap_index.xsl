<?xml version="1.0" encoding="utf-8"?>
<xsl:stylesheet version="1.0"
                xmlns="http://www.sitemaps.org/schemas/sitemap/0.9"
                xmlns:xsl="http://www.w3.org/1999/XSL/Transform"
                xmlns:formatter="urn:formatter"
                extension-element-prefixes="formatter">

  <xsl:output method="xml" version="1.0" encoding="utf-8" indent="no" />

  <xsl:param name="protocol_name" select="'http'" />
  <xsl:param name="server_name" />

  <xsl:template match="/*">
    <sitemapindex xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">
      <xsl:apply-templates select="item" />
      <xsl:apply-templates select="socialitem" />
    </sitemapindex>
  </xsl:template>

  <xsl:template match="item">
    <sitemap>
      <loc>
        <xsl:value-of select="concat($protocol_name, '://', @Url, '/sitemap.xml')" />
      </loc>
      <lastmod>
        <xsl:value-of select="substring-before(formatter:FormatDateTime(@LstPubDtm, @LstPubTyd, 's', 'nl-NL'), 'T')" />
      </lastmod>
    </sitemap>
  </xsl:template>

  <xsl:template match="socialitem">
    <sitemap>
      <loc>
        <xsl:value-of select="concat($protocol_name, '://', $server_name, '/sitemap', substring-after(@socialfeed, 'social.'), '.xml')" />
      </loc>
      <xsl:if test="@lastmod != ''">
        <lastmod>
          <xsl:value-of select="@lastmod" />
        </lastmod>
      </xsl:if>
    </sitemap>
  </xsl:template>

</xsl:stylesheet>
