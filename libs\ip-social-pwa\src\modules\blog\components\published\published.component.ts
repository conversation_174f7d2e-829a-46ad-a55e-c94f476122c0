import { ChangeDetectionStrategy, Component, Input, OnChanges } from '@angular/core';

import { IBlog } from '../../models';

@Component({
  selector: 'ips-blog-published',
  templateUrl: './published.component.html',
  styleUrls: ['./published.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class BlogPublishedComponent implements OnChanges {
  @Input()
  editMode!: boolean;

  @Input()
  blog!: IBlog;

  @Input()
  canEdit!: boolean | null;

  @Input()
  synchronizing!: boolean | null;

  status!: string;

  ngOnChanges() {
    this.status = this.getStatus(this.blog);
  }

  private getStatus(blog: IBlog): string {
    if (blog.published === false) {
      return 'blog.status.unpublished';
    }

    if (blog.isModified) {
      return 'blog.status.modified';
    }
    else {
      return 'blog.status.published';
    }
  }
}
