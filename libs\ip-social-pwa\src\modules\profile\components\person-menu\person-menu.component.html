<ion-list *transloco="let t;">
  <ion-list-header>
    <ion-toolbar>
      <ion-buttons slot="end">
        <ion-button (click)="modalController.dismiss()">{{ t('person.menu.close') }}</ion-button>
      </ion-buttons>
    </ion-toolbar>
  </ion-list-header>

  <ion-item button (click)="createBlog()">
    <ion-label>{{ t('blog.create') }}</ion-label>
    <ion-icon slot="end" name="add-outline"></ion-icon>
  </ion-item>

  <ion-item button>
    <ion-label>{{ t('app.install') }}</ion-label>
    <ion-icon slot="end" name="download-outline"></ion-icon>
  </ion-item>

  <ion-item button (click)="logout()">
    <ion-label>{{ t('logout') }}</ion-label>
    <ion-icon slot="end" name="log-out-outline"></ion-icon>
  </ion-item>
</ion-list>
