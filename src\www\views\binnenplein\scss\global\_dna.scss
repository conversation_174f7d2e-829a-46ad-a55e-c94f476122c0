$dna-scale-factor: 20;
$dna-fixed-width-bq: 1440px;
$dna-max-vertical-block-count: 6;

$dna-small-width: $dna-fixed-width-bq / $dna-scale-factor;
$dna-large-width: 100% / $dna-scale-factor;

@media screen and (min-width: $bl-mq-small) {
  main::before {
    background: url("/views/binnenplein/images/dna/Stijlvak_1_RGB_DNA-balk_02-fixed.svg") no-repeat;
    content: "";
    display: block;
    height: $dna-small-width * $dna-max-vertical-block-count;
    position: absolute;
    right: 0;
    width: $dna-small-width;

    @media screen and (min-width: $dna-fixed-width-bq) {
      height: 100%;
      max-height: ($bl-mq-large / $dna-scale-factor) * $dna-max-vertical-block-count;
      max-width: ($bl-mq-large / $dna-scale-factor);
      width: $dna-large-width;
    }
  }
}
