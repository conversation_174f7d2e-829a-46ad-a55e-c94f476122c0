@import "../../../../scss/variables";
@import "../../../../scss/mixins";

:host {
  display: block;
  padding-bottom: calc(var(--ion-grid-padding) + var(--ion-grid-column-padding));
  padding-top: calc(var(--ion-grid-padding) + var(--ion-grid-column-padding));
}

ion-item {
  --inner-padding-start: #{$ips-unit * 2};
}

.ips-comment-form-footer {
  display: flex;
  justify-content: space-between;

  ion-button {
    margin: $ips-unit 0 0;

    &:not(:last-child) {
      margin-right: 4px;
    }
  }
}

ion-textarea {
  margin-bottom: $ips-unit;
}

.ips-attachments {
  display: flex;
  flex-wrap: wrap;
  gap: .5rem;
  list-style: none;
  padding-left: 0;
}

.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0,0,0,0);
  border: 0;
}
