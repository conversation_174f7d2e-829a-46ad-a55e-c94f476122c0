// This file can be replaced during build by using the `fileReplacements` array.
// `ng build --prod` replaces `environment.ts` with `environment.prod.ts`.
// The list of file replacements can be found in `angular.json`.

import { ENVIRONMENT_STRUCTURE, EnvironmentStructure, IPPWA_SETTINGS, Settings as PwaSettings, PersonsRoot, Structure, StructureService, TimelineRoot, UserRoot } from '@ip/pwa';

import { AppEnvironment } from './interface';

// eslint-disable-next-line @typescript-eslint/naming-convention
export function StructureFactory(): Structure {
  return new Structure([
    TimelineRoot(),
    PersonsRoot(),
    UserRoot(),
  ]);
}

// eslint-disable-next-line @typescript-eslint/naming-convention
export function SettingsFactory(): PwaSettings {
  return {
    headerLogoUrl: '/assets/images/IPSOCGO.svg',
    fullscreenUrls: ['/'],
  };
}

export const environment: AppEnvironment = {
  production: false,
  serviceWorker: '/service-worker-dev.js',
  structureProviders: [
    {
      provide: StructureService,
      useClass: EnvironmentStructure,
    },
    {
      provide: ENVIRONMENT_STRUCTURE,
      useFactory: StructureFactory
    },
  ],
  settingProviders: [
    {
      provide: IPPWA_SETTINGS,
      useFactory: SettingsFactory
    }
  ]
};

/*
 * For easier debugging in development mode, you can import the following file
 * to ignore zone related error stack frames such as `zone.run`, `zoneDelegate.invokeTask`.
 *
 * This import should be commented out in production mode because it will have a negative impact
 * on performance if an error is thrown.
 */
// import 'zone.js/dist/zone-error';  // Included with Angular CLI.
