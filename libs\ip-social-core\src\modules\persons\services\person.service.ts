import { Injectable } from '@angular/core';

import { Store } from '@ngxs/store';
import { Observable } from 'rxjs';
import { distinctUntilChanged, filter, map } from 'rxjs/operators';

import { once } from '../../../utilities/once-operator';
import { ICachedPerson } from '../models/internal/cached-person.model';
import { IPerson } from '../models/person.model';
import { Persons } from '../state/persons.actions';
import { PersonsState } from '../state/persons.state';

@Injectable()
export class PersonService {
  private readonly FRESH_DURATION = 300 * 1000; // 5 minutes;

  constructor(private store: Store, private state: PersonsState) { }

  cache(persons: IPerson[]): void {
    this.store.dispatch(new Persons.PreCache(persons));
  }

  person$(id: string): Observable<IPerson> {
    return this.store.select(this.state.person(id))
      .pipe(
        once(person => {
          if (person === undefined || (this.isExpired(person) && person.fetching === false)) {
            this.store.dispatch(new Persons.Request(id));
          }
        }),
        map(cachedPerson => cachedPerson?.person),
        filter((person): person is IPerson => !!person),
        distinctUntilChanged(),
      );
  }

  private isExpired(person: ICachedPerson): boolean {
    return person.timestamp !== undefined && person.timestamp < (new Date().getTime() - this.FRESH_DURATION);
  }
}
