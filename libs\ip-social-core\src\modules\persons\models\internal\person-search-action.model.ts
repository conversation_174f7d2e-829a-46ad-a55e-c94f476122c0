import { ISearchParams, SearchParams } from '../search-params.model';

export type PersonSearchAction = IPersonSearchParamsAction | IPersonSearchUpdateAction;

export interface IPersonSearchParamsAction {
  append: boolean;
  params: SearchParams;
}

export interface IPersonSearchUpdateAction {
  params: ISearchParams;
  appel: boolean;
}

export function isSearchParamsAction(action: PersonSearchAction | undefined): action is IPersonSearchParamsAction {
  return action?.params instanceof SearchParams;
}
