{"$schema": "./node_modules/@angular/cli/lib/config/schema.json", "version": 1, "newProjectRoot": "projects", "projects": {"pzh-intranet-social": {"root": "", "sourceRoot": "src", "projectType": "application", "prefix": "app", "schematics": {"@schematics/angular:component": {"style": "less", "skipTests": true}, "@schematics/angular:class": {"skipTests": true}, "@schematics/angular:directive": {"skipTests": true}, "@schematics/angular:guard": {"skipTests": true}, "@schematics/angular:module": {"skipTests": true}, "@schematics/angular:pipe": {"skipTests": true}, "@schematics/angular:service": {"skipTests": true}}, "architect": {"build": {"builder": "@angular-devkit/build-angular:browser", "options": {"aot": true, "preserveSymlinks": true, "outputPath": "dist/pzh-intranet-social", "index": "src/index.html", "main": "src/main.ts", "polyfills": "src/polyfills.ts", "tsConfig": "src/tsconfig.app.json", "assets": ["src/favicon.ico", "src/assets", "src/social-web.settings.js", {"glob": "**/*", "input": "node_modules/tinymce", "output": "/tinymce/"}, {"glob": "**/nl.js", "input": "node_modules/@ip/social-web/src/assets/tinymce/langs", "output": "/tinymce/langs/"}, {"glob": "**/*", "input": "node_modules/@ip/social-web/src/assets/tinymce/plugins", "output": "/tinymce/plugins/"}], "styles": ["src/styles.less", "node_modules/@angular/cdk/a11y-prebuilt.css", {"input": "node_modules/@angular/material/prebuilt-themes/indigo-pink.css"}], "scripts": []}, "configurations": {"production": {"fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.prod.ts"}], "optimization": true, "outputHashing": "all", "sourceMap": false, "namedChunks": false, "aot": true, "extractLicenses": true, "vendorChunk": false, "buildOptimizer": true, "budgets": [{"type": "initial", "maximumWarning": "2mb", "maximumError": "5mb"}, {"type": "anyComponentStyle", "maximumWarning": "6kb"}]}}}, "serve": {"builder": "@angular-devkit/build-angular:dev-server", "options": {"browserTarget": "pzh-intranet-social:build", "sourceMap": {"scripts": true, "styles": true, "vendor": true}, "aot": true}, "configurations": {"production": {"browserTarget": "pzh-intranet-social:build:production"}}}, "extract-i18n": {"builder": "@angular-devkit/build-angular:extract-i18n", "options": {"browserTarget": "pzh-intranet-social:build"}}}}}, "defaultProject": "pzh-intranet-social", "cli": {"packageManager": "yarn"}}