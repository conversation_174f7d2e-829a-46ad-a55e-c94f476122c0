/// <reference lib="es2018" />
/// <reference lib="webworker" />

import { precacheAndRoute } from 'workbox-precaching';

// SR: Clients import from '@ip/social-core/src/service-worker/service-worker';
import { SocialServiceWorker } from '../../../../libs/ip-social-core/src/service-worker/service-worker';
import { settings } from './sw-environment';

declare const self: ServiceWorkerGlobalScope;

new SocialServiceWorker(settings);

// eslint-disable-next-line no-underscore-dangle
precacheAndRoute(self.__WB_MANIFEST || []);
