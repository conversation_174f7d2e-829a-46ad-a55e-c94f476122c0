﻿<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <ProductVersion>9.0.30729</ProductVersion>
    <TargetFramework>net48</TargetFramework>
    <LangVersion>10</LangVersion>
    <GenerateAssemblyInfo>false</GenerateAssemblyInfo>
    <TreatWarningsAsErrors>true</TreatWarningsAsErrors>
    <AssemblyTitle>IntranetProvincieZuidHolland.Modules</AssemblyTitle>
    <OutputPath>bin\$(Configuration)\</OutputPath>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugType>full</DebugType>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
  </PropertyGroup>
  <ItemGroup>
    <PackageReference Include="InfoProjects.Dxe" Version="4.9.2513" />
    <PackageReference Include="InfoProjects.Iprox.Security" Version="4.9.2513" />
    <PackageReference Include="Microsoft.AspNet.WebApi.Client" Version="5.2.9" />
    <PackageReference Include="Microsoft.AspNet.WebApi.Core" Version="5.2.9" />
  </ItemGroup>
  <ItemGroup>
    <Reference Include="formulier, Version=*******, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\..\..\com\formulier.dll</HintPath>
      <SpecificVersion>False</SpecificVersion>
    </Reference>
    <Reference Include="Frontend">
      <HintPath>..\..\..\com\Frontend.dll</HintPath>
    </Reference>
    <Reference Include="Lucene.Net">
      <HintPath>..\..\..\com\Lucene.Net.dll</HintPath>
    </Reference>
    <Reference Include="System.Web" />
    <Reference Include="System.Web.Http, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\..\..\com\System.Web.Http.dll</HintPath>
      <SpecificVersion>False</SpecificVersion>
    </Reference>
    <Reference Include="System.Data.DataSetExtensions" />
  </ItemGroup>
  <Target Name="PostBuild" AfterTargets="PostBuildEvent">
    <Exec Command="if $(ConfigurationName) == Debug (&#xA;  copy $(TargetDir)$(TargetName).* $(SolutionDir)..\..\deploy\redactie\www\bin&#xA;)" />
  </Target>
</Project>
