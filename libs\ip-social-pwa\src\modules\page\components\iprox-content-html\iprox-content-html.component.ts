import { AfterViewInit, Component, ElementRef, HostBinding, Input, Renderer2 } from '@angular/core';
import { SafeHtml } from '@angular/platform-browser';

import { CoreSettings, ResourceTokenService } from '@ip/social-core';

@Component({
  // eslint-disable-next-line @angular-eslint/component-selector
  selector: '[ips-iprox-content-html]',
  template: '',
})
export class IproxContentHtmlComponent implements AfterViewInit {
  @HostBinding('innerHTML')
  @Input()
  html!: SafeHtml;

  constructor(
    private renderer: Renderer2,
    private elementRef: ElementRef,
    private resourceTokenService: ResourceTokenService,
    private settings: CoreSettings,
  ) { }

  ngAfterViewInit() {
    this.transformInnerHtml(this.elementRef.nativeElement);
  }

  private transformInnerHtml(element: HTMLElement) {
    const imageElements: NodeListOf<HTMLImageElement> = element.querySelectorAll('img[src]');

    if (imageElements.length > 0) {
      this.resourceTokenService.token$
        .subscribe(token => imageElements.forEach(el => this.setImageSrc(el, token)));
    }
  }

  private setImageSrc(element: HTMLImageElement, token: string) {
    const filePath = element.src.replace(window.location.origin + '/', '');
    this.renderer.setAttribute(element, 'src', `${this.settings.apiUrl}iprox/content/inline?file=${filePath}&access-token=${token}`);
  }
}
