import blogViewTemplate from '!html-loader?minimize=true&conservativeCollapse=false&collapseInlineTagWhitespace=true!./Templates/BlogView.html';
import calendarEventViewTemplate from '!html-loader?minimize=true&conservativeCollapse=false&collapseInlineTagWhitespace=true!./Templates/CalendarEventView.html';
import fileViewTemplate from '!html-loader?minimize=true&conservativeCollapse=false&collapseInlineTagWhitespace=true!./Templates/FileView.html';
import personTemplate from '!html-loader?minimize=true&conservativeCollapse=false&collapseInlineTagWhitespace=true!./Templates/Person.html';
import personListTemplate from '!html-loader?minimize=true&conservativeCollapse=false&collapseInlineTagWhitespace=true!./Templates/PersonList.html';
import postViewTemplate from '!html-loader?minimize=true&conservativeCollapse=false&collapseInlineTagWhitespace=true!./Templates/PostView.html';
import printPersonListTemplate from '!html-loader?minimize=true&conservativeCollapse=false&collapseInlineTagWhitespace=true!./Templates/PrintPersonList.html';
import questionViewTemplate from '!html-loader?minimize=true&conservativeCollapse=false&collapseInlineTagWhitespace=true!./Templates/QuestionView.html';

angular.module('Intranet.Templates', [])
  .constant('blogViewTemplate', blogViewTemplate)
  .constant('calendarEventViewTemplate', calendarEventViewTemplate)
  .constant('fileViewTemplate', fileViewTemplate)
  .constant('personTemplate', personTemplate)
  .constant('personListTemplate', personListTemplate)
  .constant('postViewTemplate', postViewTemplate)
  .constant('printPersonListTemplate', printPersonListTemplate)
  .constant('questionViewTemplate', questionViewTemplate);
