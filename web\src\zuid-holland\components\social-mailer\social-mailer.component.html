<div *ngIf="!groups">
  Laden...
</div>

<fieldset *ngIf="groups" class="rij mode_input selectie rij_verplicht selectie_type_radio">
  <legend class="label">
    <span class="setlabel">{{ 'Mail naar' | refText }}<span class="verplicht"> *</span></span>
  </legend>
  <div class="invoer">
    <div class="antwoorden">
      <div class="antwoord">
        <input type="radio" id="select-all-groups" [(ngModel)]="mode" value="allGroups" (click)="emailAddresses = []; selectedGroups = [];">
        <label for="select-all-groups">{{ 'Mail alle groepen' | refText }}</label>
      </div>
      <div class="antwoord">
        <input type="radio" id="select-groups" [(ngModel)]="mode" value="specificGroups" (click)="emailAddresses = []">
        <label for="select-groups">{{ 'Mail bepaalde groepen' | refText }}</label>
      </div>
      <div class="pzh-selected-groups">
        <button
          *ngFor="let g of selectedGroups"
          type="button"
          class="btn btn-secondary ips-button pzh-mail-group"
          (click)="deselect(g)"
        >{{ g.label }} X</button>
      </div>
    </div>
  </div>
  <div *ngIf="mode === 'specificGroups'" class="invoer pzh-group-typeahead">
    <input id="social-group-typeahead" type="text" class="pzh-group-finder"
      [(ngModel)]="query"
      [ngbTypeahead]="search"
      [inputFormatter]="formatter"
      [resultFormatter]="formatter"
      [editable]='false'
      (selectItem)="select($event)"
    />
  </div>

  <button
    class="pzh-generate-email-adresses btn btn-primary ips-button"
    type="button"
    [disabled]="this.inProgress"
    (click)="$event.preventDefault(); generateEmailAddresses();"
  >
    <span>{{ 'Genereer email adressen' | refText }}</span>
    <i class="fa-solid fa-spin fa-spinner" *ngIf="inProgress"></i>
  </button>
</fieldset>

<fieldset class="rij mode_display">
  <legend class="label">
    <span class="setlabel">
      <button type="button" class="btn btn-text pzh-mail-receivers-button" (click)="showEmailAddresses = !showEmailAddresses">
        {{ 'Dit formulier wordt verzonden naar: [aantal] ontvangers' | refText: { aantal: emailAddresses.length } }}
      </button>
    </span>
  </legend>
  <ng-container *ngIf="showEmailAddresses">
    <div *ngFor="let email of emailAddresses">{{ email }}</div>
  </ng-container>
</fieldset>
