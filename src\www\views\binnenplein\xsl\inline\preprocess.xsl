<?xml version="1.0" encoding="utf-8" ?>
<xsl:stylesheet
  xmlns:xsl="http://www.w3.org/1999/XSL/Transform"
  xmlns:formatter="urn:formatter"
  extension-element-prefixes="formatter"
  version="1.0"
>

  <xsl:template match="cluster[Nam = 'Index']/cluster[Nam = 'Blok' and cluster[Nam = 'Koppeling']/veld[Nam = 'Index']/link/@pagetype = 'index']">
    <index-link NarItmIdt="{cluster[Nam = 'Koppeling']/veld[Nam = 'Index']/link/@NarItmIdt}" />
  </xsl:template>

  <xsl:template match="cluster[Nam = 'Evenementenagenda']/cluster[Nam = 'Blok' and cluster[Nam = 'Koppeling']/veld[Nam = 'Evenementenagenda']/link/@pagetype = 'evenementenagenda']">
    <evenementenagenda-link NarItmIdt="{cluster[Nam = 'Koppeling']/veld[Nam = 'Evenementenagenda']/link/@NarItmIdt}" />
  </xsl:template>

  <!-- lijst genereren voor 'Koppeling' naar index; baseline plug-->
  <xsl:template match="cluster[Nam = 'Index' and self::cluster//veld[@GgvTyp = '18']/link/@pagetype = 'index']" mode="list">
    <xsl:param name="cluster" select="self::cluster" />

    <xsl:variable name="range_start">
      <xsl:apply-templates select="self::cluster" mode="range_start" />
    </xsl:variable>

    <xsl:variable name="range_end">
      <xsl:apply-templates select="self::cluster" mode="range_end" />
    </xsl:variable>

    <xsl:variable name="range" select="key('grid_block', @PagClsIdt)/link/content/index/selectie/lucene/link[position() &gt;= number($range_start) and position() &lt;= number($range_end)]" />

    <feed>
      <xsl:apply-templates select="self::cluster" mode="index-title">
        <xsl:with-param name="range_start" select="$range_start" />
        <xsl:with-param name="range_end" select="$range_end" />
      </xsl:apply-templates>

      <xsl:variable name="notfound_text" select="key('grid_block', @PagClsIdt)/link/content//veld[Nam = 'Tekst bij niets gevonden']" />

      <xsl:choose>
        <xsl:when test="self::cluster//veld[Nam = 'Tekst']/Txt/div[*|text()]">
          <description>
            <xsl:copy-of select="self::cluster//veld[Nam = 'Tekst']/Txt/div" />
            <xsl:if test="not($range)">
              <xsl:copy-of select="$notfound_text/Txt/div" />
            </xsl:if>
          </description>
          <xsl:copy-of select="self::cluster//veld[Nam = 'Tekst']/styleattributes" />
          <xsl:copy-of select="$notfound_text/styleattributes" />
        </xsl:when>
        <xsl:when test="not($range)">
          <description>
            <xsl:copy-of select="$notfound_text/Txt/div" />
          </description>
          <xsl:copy-of select="$notfound_text/styleattributes" />
        </xsl:when>
      </xsl:choose>

      <xsl:copy-of select="self::cluster//veld[Nam = 'Foto']/Txt//img" />

      <xsl:apply-templates select="$range" mode="list-item">
        <xsl:with-param name="cluster" select="$cluster" />
        <xsl:with-param name="range_start" select="$range_start" />
        <xsl:with-param name="range_end" select="$range_end" />
      </xsl:apply-templates>

      <!-- plug: tonen meer link niet afhankelijk van aantal items  -->
      <xsl:if test="not($cluster//veld[Nam = 'Meer-link verbergen']/Wrd = 1)">
        <!-- 'meer' -->
        <more type="site">
          <xsl:attribute name="href">
            <xsl:value-of select="self::cluster//veld[@GgvTyp = '18']/link[@pagetype = 'index']/Url" />
            <xsl:if test="self::cluster//veld[@GgvTyp = '17']/Src">
              <xsl:text>?zoeken_trefwoord=</xsl:text>
              <xsl:value-of select="self::cluster//veld[@GgvTyp = '17']/Src" />
            </xsl:if>
          </xsl:attribute>
          <xsl:if test="self::cluster//veld[@GgvTyp = '18']/Wrd">
            <xsl:attribute name="label">
              <xsl:value-of select="self::cluster//veld[@GgvTyp = '18']/Wrd" />
            </xsl:attribute>
          </xsl:if>
        </more>
      </xsl:if>
    </feed>
  </xsl:template>

</xsl:stylesheet>
