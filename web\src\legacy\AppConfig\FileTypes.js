﻿(function (angular) {
  'use strict';

  angular.module('Intranet').constant('fileTypes', {
    doc: [
     'text/csv',
     'application/pdf',
     'application/msword',
     'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
     'application/vnd.openxmlformats-officedocument.wordprocessingml.template',
     'application/vnd.ms-word.document.macroEnabled.12',
     'application/vnd.ms-word.template.macroEnabled.12',
     'application/vnd.ms-excel',
     'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
     'application/vnd.openxmlformats-officedocument.spreadsheetml.template',
     'application/vnd.ms-excel.sheet.macroEnabled.12',
     'application/vnd.ms-excel.template.macroEnabled.12',
     'application/vnd.ms-excel.addin.macroEnabled.12',
     'application/vnd.ms-excel.sheet.binary.macroEnabled.12',
     'application/vnd.ms-powerpoint',
     'application/vnd.openxmlformats-officedocument.presentationml.presentation',
     'application/vnd.openxmlformats-officedocument.presentationml.template',
     'application/vnd.openxmlformats-officedocument.presentationml.slideshow',
     'application/vnd.ms-powerpoint.addin.macroEnabled.12',
     'application/vnd.ms-powerpoint.presentation.macroEnabled.12',
     'application/vnd.ms-powerpoint.slideshow.macroEnabled.12',
     'application/zip',
     '.csv',
     '.pdf',
     '.doc',
     '.docx',
     '.xls',
     '.xlsx',
     '.ppt',
     '.pptx',
     '.zip',
     '.7z'],
    image: [
      'image/bmp',
      'image/x-windows-bmp',
      'image/jpg',
      'image/jpeg',
      'image/gif',
      'image/png',
      '.bmp',
      '.jpg',
      '.jpeg',
      '.gif',
      '.png']
   });
})(angular);