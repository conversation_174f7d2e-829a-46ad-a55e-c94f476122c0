<ion-segment
  *ngIf="meta$ | async as meta"
  class="ips-like-segment"
  (click)="change(meta, $any($event))"
  [scrollable]="true"
  [value]="meta.likes!.userLikes[0]?.type"
  [disabled]="meta.working"
>
  <ion-segment-button
    *ngFor="let option of options;"
    [value]="option.type"
  >
    <ion-label>{{ option.unicode }}&#8201;{{ meta.likes!.data[option.type]?.count || 0 }}</ion-label>
  </ion-segment-button>
</ion-segment>
