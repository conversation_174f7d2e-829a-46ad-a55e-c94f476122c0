import { Component, Input } from '@angular/core';

@Component({
  selector: 'ips-activity-person',
  template: '<ips-person [id]="data"></ips-person>',
})
export class PersonComponent {
  @Input()
  data!: string;
}

@Component({
  selector: 'ips-activity-text',
  template: '<span>{{ data }}</span>',
})
export class TextComponent {
  @Input()
  data!: string;
}

@Component({
  selector: 'ips-activity-link',
  template: '<a [routerLink]="data.url">{{ data.text }}</a>',
})
export class LinkComponent {
  @Input()
  data!: { text: string; url: string; };
}
