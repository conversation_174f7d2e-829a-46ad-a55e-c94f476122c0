import { NgModule } from '@angular/core';

import { TrustHtmlComponent } from './components/trust-html/trust-html.component';
import { DownloadFileDirective } from './directives/download-file.directive';
import { FileValueAccessorDirective } from './directives/file-value-accessor.directive';
import { FileSizePipe } from './file-size.pipe';
import { SecureImageSrcDirective } from './directives/secure-image-src.directive';
import { LinkyPipe } from './linky.pipe';
import { PhoneNumberPipe } from './phone-number.pipe';
import { ReferenceLinkPipe } from './reference-link.pipe';

@NgModule({
  declarations: [
    TrustHtmlComponent,
    DownloadFileDirective,
    FileValueAccessorDirective,
    SecureImageSrcDirective,
    PhoneNumberPipe,
    ReferenceLinkPipe,
    LinkyPipe,
    FileSizePipe,
  ],
  providers: [
    LinkyPipe,
    PhoneNumberPipe,
  ],
  exports: [
    TrustHtmlComponent,
    DownloadFileDirective,
    FileValueAccessorDirective,
    SecureImageSrcDirective,
    PhoneNumberPipe,
    ReferenceLinkPipe,
    LinkyPipe,
    FileSizePipe,
  ],
})
export class UtilitiesModule { }
