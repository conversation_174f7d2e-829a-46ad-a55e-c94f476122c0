﻿(function (angular) {
  'use strict';

  angular.module('Intranet').run(['PersonCardMetaSchemeService', function (PersonCardMetaSchemeService) {
    PersonCardMetaSchemeService.createCardMetaScheme('mobileOrOtherNumber', function (person, $filter, scheme) {
      var displayPhoneNumber = person.phoneNumbers.phoneNumbers.reduce(function (displayNumber, phoneNumber) {
        if (displayNumber.type === 'mobile') {
          return displayNumber;
        }

        if (phoneNumber.number) {
          return phoneNumber;
        }

        return displayNumber;
      }, { number: '' });

      var formattedNumber = $filter('ipPhone')(displayPhoneNumber.number);

      return {
        href: 'tel:' + displayPhoneNumber.number,
        value: formattedNumber,
        title: formattedNumber
      };
    });
  }]);
})(angular);
