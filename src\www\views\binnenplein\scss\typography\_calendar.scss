$pzh-calendar-cell-size: #{$bl-unit * 4};

.type-kalender {
  margin-top: #{$bl-unit * 2};

  .events-calendar-table {
    .calendar {
      padding-bottom: .5rem;
      padding-left: .5rem;
      padding-right: .5rem;
    }
  }

  .navigatie {
    padding-top: $bl-unit;

    .maand {
      clear: none;
      font-weight: 500;
    }
  }

  .navigatie + .kalender {
    margin-top: #{$bl-unit / 2};
  }

  .kalender {
    .anderemaand {
      visibility: hidden;
    }

    th,
    td {
      height: $pzh-calendar-cell-size;
      width: $pzh-calendar-cell-size;
    }

    td a {
      background-color: $achtergrondkleur-2;
      border-radius: $pzh-border-radius;
      display: inline-block;
      height: $pzh-calendar-cell-size;
      line-height: $pzh-calendar-cell-size;
      position: relative;
      width: $pzh-calendar-cell-size;

      &:focus {
        box-shadow: none;
      }
    }
  }

  .calendar {
    .vorige {
      padding-left: #{$bl-unit / 2};
    }

    .volgende {
      padding-right: #{$bl-unit / 2};
    }

    .vorige a,
    .volgende a {
      i.ico::before {
        font-weight: bold;
      }
    }
  }
}
