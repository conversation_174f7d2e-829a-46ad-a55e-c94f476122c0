<?xml version="1.0" encoding="utf-8"?>
<xsl:stylesheet xmlns:xsl="http://www.w3.org/1999/XSL/Transform" version="1.0" xmlns:resources="urn:resources" extension-element-prefixes="resources">
  <xsl:import href="/xsl/include/StartPage.xsl" />

  <xsl:template name="PageControls_inside">
    <xsl:if test="$done != 'true' and string($error) = ''">
      <button class="btn btn-success" id="PublishBtn" onclick="submitForm('exit', 'publish', 'exitItmIdt', '{$exitItmIdtForPublish}', 'exitUrl', '{$exitUrl}')">
        <i class="star"></i>
        <span>
          <xsl:value-of select="resources:GetText('buttons','publish')" />
        </span>
      </button>
      <xsl:if test="not(contains($AppIdt,'toevoegen'))">
        <xsl:apply-templates select="." mode="remove-button" />
      </xsl:if>
    </xsl:if>
    <xsl:if test="$embedded_cms">
      <button class="btn btn-danger" id="CloseBtn" onclick="return goClose();">
        <i class="remove"></i>
        <span>
          <xsl:value-of select="resources:GetText('buttons','cancel')" />
        </span>
      </button>
    </xsl:if>
  </xsl:template>

  <xsl:template match="*" mode="remove-button">
    <button class="btn btn-warning" id="RemoveBtn">
      <xsl:attribute name="onclick">
        <xsl:text/>if (confirm('Weet u zeker dat u dit item wilt verwijderen?')) { submitForm('<xsl:value-of select="$handler" />.$action', 'none', 'RemoveItem.$action', 'run', 'RemoveItem.$id', '<xsl:value-of select="$ItmIdt" />', 'exit', 'publish', 'exitItmIdt', '<xsl:value-of select="$exitItmIdtForPublish" />', 'exitUrl', '<xsl:value-of select="$exitUrl" />'); }<xsl:text/>
      </xsl:attribute>
      <i class="trash"></i>
      <span>
        <xsl:value-of select="resources:GetText('buttons','delete')" />
      </span>
    </button>
  </xsl:template>

  <xsl:template match="veld" mode="minor-change">
    <table class="list">
      <xsl:variable name="Nam" select="translate(DefNam, ' ,/', '___')" />
      <xsl:variable name="current-field" select="/data/site/item/page//veld[@VldDefIdt=current()/@VldDefIdt]" />
      <xsl:call-template name="veld">
        <xsl:with-param name="definition" select="."/>
        <xsl:with-param name="curFieldName" select="concat($handler, '.', $Nam)"/>
        <xsl:with-param name="curClusterName" select="concat($handler, '_', $Nam)"/>
        <xsl:with-param name="curPath" select="concat('temp/', $User.GebIdt)"/>
        <xsl:with-param name="curSitIdt" select="$SitIdt"/>
        <xsl:with-param name="this" select="no-field"/>
        <!-- 'no-field' because we dont want to put the current value of the field back -->
      </xsl:call-template>
    </table>
    <br/>
  </xsl:template>

</xsl:stylesheet>