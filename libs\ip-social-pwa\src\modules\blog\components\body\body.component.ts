import { Component, Input } from '@angular/core';
import { FormGroup } from '@angular/forms';

import { ModalController } from '@ionic/angular';
import { Observable } from 'rxjs';

import { EditorConfig } from '../../../forms/services/tinymce.service';
import { BlogEditorComponent } from '../editor/editor.component';

@Component({
  selector: 'ips-blog-body',
  templateUrl: './body.component.html',
})
export class BlogBodyComponent {
  @Input()
  editMode!: boolean;

  @Input()
  body!: string;

  @Input()
  workingBody!: string;

  @Input()
  synchronizing$?: Observable<boolean>;

  @Input()
  editorConfig!: EditorConfig;

  @Input()
  formGroup!: FormGroup;

  constructor(private modalController: ModalController) { }

  async open() {
    const modal = await this.modalController.create({
      component: BlogEditorComponent,
      cssClass: 'ips-blog-editor-modal',
      componentProps: {
        editorConfig: this.editorConfig,
        formGroup: this.formGroup,
        synchronizing$: this.synchronizing$,
      }
    });

    return await modal.present();
  }
}
