import { Injectable } from '@angular/core';

import { get, set, Store } from 'idb-keyval';
import { BehaviorSubject, combineLatest, EMPTY, from, Observable, of } from 'rxjs';
import { catchError, map, switchMap, take, tap, withLatestFrom } from 'rxjs/operators';
import { v4 as uuid } from 'uuid';

import { capitalize } from '../../../utilities/capitalize';
import { PushActivitySetting } from '../models';
import { IRegisteredDevice } from '../models/device-model';
import { IUserDevice } from '../models/user-agent.model';
import { urlBase64ToUint8Array } from '../utilities/urlBase64ToUInt8Array';
import { SocialHttpClient } from './api-client';
import { UserAgentService } from './user-agent.service';
import { UserService } from './user.service';

export enum DeviceStatus {
  Initializing,
  Registered,
}

export interface IDeviceState {
  status: DeviceStatus;
  subscription: PushSubscription | null;
  device?: IRegisteredDevice;
}

@Injectable()
export class DeviceService {
  private store = new Store('ips-device', 'ips-device');

  private state = new BehaviorSubject<IDeviceState>({ status: DeviceStatus.Initializing, subscription: null });

  private working = false;

  public state$ = this.state.asObservable();

  public device$ = this.state$.pipe(map(state => state.device));

  constructor(
    private socialApi: SocialHttpClient,
    private userAgentService: UserAgentService,
    private userService: UserService,
  ) {
    this.initialize();
  }

  stateSnapshot(): IDeviceState {
    return this.state.value;
  }

  updateState(fn: (state: IDeviceState) => IDeviceState, thisArg?: unknown) {
    this.state.next(fn.call(thisArg, this.state.value));
  }

  initialize(): void {
    combineLatest([
      this.userDevice$(),
      this.getPushSubscription$()
    ])
      .pipe(
        take(1),
        switchMap(([userDevice, pushSubscription]) => this.registerDevice(userDevice, pushSubscription))
      )
      .subscribe((state) => {
        this.state.next({
          status: DeviceStatus.Registered,
          device: state.device,
          subscription: state.subscription,
        });
      });
  }

  subscribe(): void {
    if (this.working) {
      return;
    }

    this.working = true;

    from(navigator.serviceWorker.ready)
      .pipe(
        switchMap((registration) =>
          this.getVapidKey()
            .pipe(
              switchMap(convertedVapidKey =>
                from(registration.pushManager.subscribe({
                  userVisibleOnly: true,
                  applicationServerKey: convertedVapidKey
                }))
              ),
              tap(() => this.userAgentService.updateNotificationPermission(Notification.permission)),
              withLatestFrom(this.userDevice$()),
              switchMap(([subscription, userDevice]) => this.registerDevice(userDevice, subscription)),
              catchError(error => {
                // TODO: Toaster voor fouten?
                console.log('[pushService] new subscription failed', error);
                this.userAgentService.updateNotificationPermission(Notification.permission);
                this.working = false;
                return EMPTY;
              })
            )
        ),
        tap(({ device, subscription }) => this.state.next({ status: DeviceStatus.Registered, subscription, device }))
      )
      .subscribe(() => this.working = false);
  }

  unsubscribe(): Observable<void> {
    if (this.working) {
      return EMPTY;
    }

    this.working = true;

    return from(navigator.serviceWorker.ready)
      .pipe(
        switchMap(registration => from(registration.pushManager.getSubscription())),
        switchMap(subscription => subscription
          ? this.socialApi.post('device/subscription/unregister', { endpoint: subscription.endpoint })
            .pipe(
              map(() => subscription),
              catchError(() => of(subscription)),
              switchMap(() => from(subscription.unsubscribe())),
            )
          : of(false)),
        tap(() => this.updateState(state => ({ ...state, subscription: null }))),
        tap(() => this.working = false),
        map(() => void 0),
      );
  }

  private userDevice$() {
    return combineLatest([this.userService.currentUser$, this.userAgentService.userAgent$])
      .pipe(
        switchMap(async ([user, userAgent]) => {
          const userDevice = await get<IUserDevice | undefined>(`device-${user.id}`, this.store);

          const updatedUserDevice: IUserDevice = {
            ...userAgent,
            id: userDevice?.id ?? uuid(),
            userId: user.id,
          };

          if (!userDevice) {
            await set(`device-${user.id}`, updatedUserDevice, this.store);
          }

          return updatedUserDevice;
        })
      );
  }

  private getPushSubscription$(): Observable<PushSubscription | null> {
    return from(navigator.serviceWorker.ready)
      .pipe(
        switchMap(registration => registration.pushManager
          ? from(registration.pushManager.getSubscription())
          : of(null)
        ),
      );
  }

  private getVapidKey(): Observable<Uint8Array> {
    return this.socialApi.get<string>('device/subscription/publickey')
      .pipe(map(vapidKey => urlBase64ToUint8Array(vapidKey)));
  }

  private registerDevice(userDevice: IUserDevice, subscription: PushSubscription | null) {
    return this.socialApi.post<IRegisteredDevice>('device/register', {
      id: userDevice.id,
      subscription: subscription?.toJSON() ?? null,
      platform: userDevice.platform,
    })
      .pipe(
        map(device => ({
          device: {
            ...device,
            pushSettings: {
              ...device.pushSettings,
              activities: {
                ...defaultActivitySettings,
                ...Object.entries(device.pushSettings.activities).reduce((activities, [name, value]) => {
                  name = capitalize(name);
                  if (defaultActivitySettings[name as PushActivitySetting] !== undefined) {
                    return { ...activities, [name]: value };
                  }

                  return activities;
                }, {}),
              }
            }
          },
          subscription
        }))
      );
  }
}

// SR: Shortcut, Omdat we Updated en Published onder 1 switch willen zijn dit de settings. In de SW wordt Published en Updated
//     Uit de activity gefiltered, waardoor onderstaande overblijft. Niet een ideaal systeem!

/* eslint-disable @typescript-eslint/naming-convention */
const defaultActivitySettings: { [key in PushActivitySetting]: boolean } = {
  IproxArticle: true,
  IproxEvent: true,
  Blog: true,
  CommentOnBlog: true,
  Birthday: true,
  BirthdayPreview: true,
  CongratulationsOnBirthday: true,
};
