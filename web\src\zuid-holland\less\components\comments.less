.grid-blok {
  &:not(.elt-small):not(.elt-medium) {
    ips-comments {
      .btn.ips-comment-btn.btn-primary {
        > i {
          display: none;
        }
      }
    }
  }
}

.bs-blog-body ips-comments {
  background-color: @achtergrondkleur-5;
  margin-top: @pzh-outer-padding;
  padding-left: @pzh-inner-padding;
  padding-right: @pzh-inner-padding;
}

.type-social-enhancements .grid-box:not(:only-child):last-child {
  background-color: @achtergrondkleur-5;
  margin-bottom: @pzh-outer-padding;
  margin-top: @pzh-outer-padding;
}

.grid-blok.elt-medium {
  ips-comments {
    .ips-comment-actions {
      .btn.ips-comment-btn:not(.ips-reply-comment-btn) {
        span {
          .sr-only();
        }
      }
    }

    .ips-comment-body a {
      text-decoration: underline;
      text-decoration-thickness: 1px;
    }
  }
}
