<?xml version="1.0" encoding="utf-8"?>
<xsl:stylesheet
  xmlns:xsl="http://www.w3.org/1999/XSL/Transform"
  xmlns:formatter="urn:formatter"
  xmlns="http://www.w3.org/1999/xhtml"
  extension-element-prefixes="formatter"
  version="1.0"
>

  <!-- fieldType 'text', plugged for data-ip-get-userinfo -->
  <xsl:template match="*" mode="formulierveld_text" >
    <xsl:param name="fieldName" />
    <xsl:param name="fieldRequired" select="'false'" />
    <xsl:param name="fieldId" select="formatter:Replace($fieldName, '.', '_')"/>
    <xsl:param name="fieldPrefix" select="substring-after($fieldName, '.')" />
    <xsl:param name="fieldValue" >
      <xsl:apply-templates select="." mode="waarde">
        <xsl:with-param name="Nam" select="substring-after($fieldName, '.')" />
      </xsl:apply-templates>
    </xsl:param>
    <xsl:param name="fieldValueNode" select="ancestor-or-self::content[1]//field[@name = substring-after($fieldName, '.')]" />
    <xsl:param name="fieldType" select="'text'" />
    <xsl:param name="fieldPlh" select="''" />
    <xsl:param name="formHandler" select="''" />
    <xsl:param name="formError" select="''" />
    <xsl:param name="fieldMode" select="'input'" />
    <xsl:param name="fieldCols" select="''" />
    <xsl:param name="fieldRows" select="''" />
    <xsl:param name="fieldMax" select="''" />
    <xsl:param name="fieldPattern" select="''" />
    <xsl:param name="fieldContext" select="'none'" />
    <xsl:param name="fieldAutofillToken" select="''" />

    <xsl:if test="$fieldMode != 'display_only'">
      <xsl:variable name="field_title">
        <xsl:apply-templates select="." mode="formulierveld_text_title">
          <xsl:with-param name="fieldName" select="$fieldName" />
          <xsl:with-param name="fieldId" select="$fieldId" />
          <xsl:with-param name="fieldPrefix" select="$fieldPrefix" />
          <xsl:with-param name="fieldValue" select="$fieldValue" />
        </xsl:apply-templates>
      </xsl:variable>
      <input name="{$fieldName}" id="{$fieldId}" value="{$fieldValue}" class="input">
        <xsl:if test="$fieldContext/Nam = 'Naam' or contains($fieldName, '.Nam')">
          <!-- data-ip-get-userinfo -->
          <xsl:attribute name="data-ip-get-userinfo">fullName</xsl:attribute>
        </xsl:if>
        <xsl:if test="$fieldRequired = 'true' and $fieldMode != 'display'">
          <xsl:attribute name="required">required</xsl:attribute>
        </xsl:if>
        <xsl:if test="$fieldMode = 'readonly' or ($pathitem/@Aka = 'groepsmail' and $fieldContext/Nam = 'Naam')">
          <xsl:attribute name="readonly">readonly</xsl:attribute>
        </xsl:if>
        <xsl:if test="$fieldMode = 'disabled'">
          <xsl:attribute name="disabled">disabled</xsl:attribute>
        </xsl:if>
        <xsl:attribute name="type">
          <xsl:choose>
            <xsl:when test="$fieldMode = 'display'">hidden</xsl:when>
            <xsl:otherwise>text</xsl:otherwise>
          </xsl:choose>
        </xsl:attribute>
        <xsl:if test="$fieldMax != '' and $fieldMode != 'display'">
          <xsl:attribute name="maxlength"><xsl:value-of select="$fieldMax" /></xsl:attribute>
        </xsl:if>
        <xsl:if test="$fieldPlh != '' and $xsl_html_elements = 'living' and $fieldMode != 'display'">
          <xsl:attribute name="placeholder"><xsl:value-of select="$fieldPlh" /></xsl:attribute>
        </xsl:if>
        <xsl:if test="$field_title != ''">
          <xsl:attribute name="title"><xsl:value-of select="$field_title" /></xsl:attribute>
        </xsl:if>
        <xsl:if test="$fieldPattern != '' and $fieldMode != 'display'">
          <xsl:attribute name="pattern"><xsl:value-of select="$fieldPattern" /></xsl:attribute>
        </xsl:if>
        <xsl:if test="$fieldAutofillToken != '' and $fieldMode != 'display'">
          <xsl:attribute name="autocomplete">
            <xsl:value-of select="$fieldAutofillToken" />
          </xsl:attribute>
        </xsl:if>
      </input>
    </xsl:if>
    <xsl:if test="starts-with($fieldMode, 'display') and $fieldValue != 'null' and $fieldValue != ''">
      <xsl:value-of select="$fieldValue" />
    </xsl:if>
  </xsl:template>

  <!-- fieldType 'mail', plugged for data-ip-get-userinfo -->
  <xsl:template match="*" mode="formulierveld_mail" >
    <xsl:param name="fieldName" />
    <xsl:param name="fieldRequired" select="'false'"/>
    <xsl:param name="fieldId" select="formatter:Replace($fieldName, '.', '_')"/>
    <xsl:param name="fieldPrefix" select="substring-after($fieldName, '.')" />
    <xsl:param name="fieldValue" >
      <xsl:apply-templates select="." mode="waarde">
        <xsl:with-param name="Nam" select="substring-after($fieldName, '.')" />
      </xsl:apply-templates>
    </xsl:param>
    <xsl:param name="fieldValueNode" select="ancestor-or-self::content[1]//field[@name = substring-after($fieldName, '.')]" />
    <xsl:param name="fieldType" select="'text'" />
    <xsl:param name="fieldPlh" select="''" />
    <xsl:param name="formHandler" select="''" />
    <xsl:param name="formError" select="''" />
    <xsl:param name="fieldMode" select="'input'" />
    <xsl:param name="fieldCols" select="''" />
    <xsl:param name="fieldRows" select="''" />
    <xsl:param name="fieldMax" select="''" />
    <xsl:param name="fieldPattern" select="''" />
    <xsl:param name="fieldContext" select="'none'" />
    <xsl:param name="fieldAutofillToken" select="''" />

    <xsl:if test="$fieldMode != 'display_only'">
      <xsl:variable name="field_title">
        <xsl:apply-templates select="." mode="formulierveld_text_title">
          <xsl:with-param name="fieldName" select="$fieldName" />
          <xsl:with-param name="fieldId" select="$fieldId" />
          <xsl:with-param name="fieldPrefix" select="$fieldPrefix" />
          <xsl:with-param name="fieldValue" select="$fieldValue" />
        </xsl:apply-templates>
      </xsl:variable>
      <input name="{$fieldName}" id="{$fieldId}" value="{$fieldValue}" class="input">
        <xsl:if test="$fieldContext/Nam = 'E-mail' or contains($fieldName, '.Eml')">
          <!-- data-ip-get-userinfo -->
          <xsl:attribute name="data-ip-get-userinfo">email</xsl:attribute>
        </xsl:if>
        <xsl:if test="$fieldRequired = 'true' and $fieldMode != 'display'">
          <xsl:attribute name="required">required</xsl:attribute>
        </xsl:if>
        <xsl:if test="$fieldMode = 'readonly' or ($pathitem/@Aka = 'groepsmail' and $fieldContext/Nam = 'E-mail')">
          <xsl:attribute name="readonly">readonly</xsl:attribute>
        </xsl:if>
        <xsl:if test="$fieldMode = 'disabled'">
          <xsl:attribute name="disabled">disabled</xsl:attribute>
        </xsl:if>
        <xsl:attribute name="type">
          <xsl:choose>
            <xsl:when test="$fieldMode = 'display'">hidden</xsl:when>
            <xsl:otherwise>text</xsl:otherwise>
          </xsl:choose>
        </xsl:attribute>
        <xsl:if test="$fieldMax != '' and $fieldMode != 'display'">
          <xsl:attribute name="maxlength"><xsl:value-of select="$fieldMax" /></xsl:attribute>
        </xsl:if>
        <xsl:if test="$fieldPlh != '' and $xsl_html_elements = 'living' and $fieldMode != 'display'">
          <xsl:attribute name="placeholder"><xsl:value-of select="$fieldPlh" /></xsl:attribute>
        </xsl:if>
        <xsl:if test="$field_title != ''">
          <xsl:attribute name="title"><xsl:value-of select="$field_title" /></xsl:attribute>
        </xsl:if>
        <xsl:if test="$fieldMode != 'display'">
          <xsl:attribute name="pattern">
            <xsl:choose>
              <xsl:when test="$fieldPattern != ''">
                <xsl:value-of select="$fieldPattern" />
              </xsl:when>
              <xsl:otherwise>
                <xsl:value-of select="formatter:GetAddressMask()" />
              </xsl:otherwise>
            </xsl:choose>
          </xsl:attribute>
        </xsl:if>
        <xsl:if test="$fieldAutofillToken != '' and $fieldMode != 'display'">
          <xsl:attribute name="autocomplete">
            <xsl:value-of select="$fieldAutofillToken" />
          </xsl:attribute>
        </xsl:if>
      </input>
    </xsl:if>
    <xsl:if test="starts-with($fieldMode, 'display') and $fieldValue != 'null' and $fieldValue != ''">
      <xsl:value-of select="$fieldValue" />
    </xsl:if>
  </xsl:template>

</xsl:stylesheet>
