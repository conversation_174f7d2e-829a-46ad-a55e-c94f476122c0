import { defer, OperatorFunction } from 'rxjs';
import { tap } from 'rxjs/operators';

/**
 * For each subscriber execute the provided function once.
 */
export const once = <T>(fn: (value: T) => void): OperatorFunction<T, T> =>
  source => defer(() => {
    let executed = false;

    return source.pipe(
      tap(payload => {
        if (!executed) {
          fn(payload);
          executed = true;
        }
      })
    );
  });
