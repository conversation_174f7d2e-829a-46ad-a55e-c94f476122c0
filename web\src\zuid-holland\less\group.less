ips-group-meta {
  .ips-checkbox .ips-input > input[type="checkbox"] {
    opacity: 1;
    position: relative;
  }

  .ips-group-actions {
    .btn.ips-button.ips-default {
      .follow-btn();

      border-radius: 50%;

      .fa-envelope {
        font-size: 1rem;
      }
    }

    .ips-dropdown {
      &.open {
        .btn.ips-button {
          &,
          &:hover,
          &:active {
            background-color: @elementkleur !important;
          }

          i {
            color: @pzh-white !important;
          }
        }
      }
    }
  }
}

ips-component {
  .ips-content {
    border-top: 1px solid @pzh-grey-3;
    padding-top: @pzh-inner-padding !important;
  }

  .input-group .input-group-btn .btn.ips-button,
  content .ips-buttons .btn.ips-button.ips-primary,
  ips-pager .btn.btn-xs.ips-button.ips-primary {
    > i {
      color: @pzh-white;
    }
  }

  .ips-component-item > article > header .ips-component-title .ips-header-button {
    outline-offset: -1px;
  }
}

ips-question .ips-content-group.ips-content-group-actions > .btn.ips-button {
  color: @pzh-white;
  font-size: 1rem;
  padding: .25rem .5rem;

  > i {
    color: @pzh-white;
  }

  ips-inline-answer > article {
    > header .ips-component-title {
      @__width: 60px; // SR: Afleiden gaat niet goed?

      width: ~"calc(100% - @{__width})" !important;
    }
  }
}

ips-file {
  .ips-component-title > .btn.ips-button > i {
    margin-left: -4px;
  }
}

.elt-medium.type-social-calendar ips-calendar form.ips .ips-half {
  display: block !important;
  width: 100% !important;
}

.ips-group-notification-settings.ips-notifications-enabled {
  display: none;
}
