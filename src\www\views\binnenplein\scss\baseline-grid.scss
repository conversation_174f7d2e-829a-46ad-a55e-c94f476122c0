@import "./include/mq-vars";

// SR: large - Fluid: true even though container is not fluid, we max this ourselves.
// sass-lint:disable max-line-length
$baseline-config: (
  viewports: (
    (alias: "extra-small", max: $bl-mq-small - 1, columns: 3, fluid: true, override-container: 0),
    (alias: "small", min: $bl-mq-small, max: $bl-mq-medium, columns: 6, fluid: true, override-container: 0, margin: 96px, gutter-size: $pzh-small-gutter),
    (alias: "medium", min: $bl-mq-medium, max: $bl-mq-large - 1, columns: 12, fluid: true, override-container: 0, margin: 120px, gutter-size: $pzh-medium-gutter),
    (alias: "large", min: $bl-mq-large, columns: 12, fluid: true, override-container: $bl-mq-large, margin: 150px, gutter-size: $pzh-large-gutter)
  ),
  zones: (
    (columns: 12, blocks: (12, 10, 9, 8, 6, 5, 4, 3, 2)),
    (columns: 9, blocks: (9, 6, 3)),
    (columns: 8, blocks: (8, 4)),
    (columns: 6, blocks: (6, 3)),
    (columns: 4, blocks: (4)),
    (columns: 3, blocks: (3))
  ),
  pushes: (9, 8, 6, 4, 3),
  pulls: (9, 8, 6, 4, 3),
  column-width: 100px,
  outer-padding: $pzh-outer-padding,
  inner-padding: $pzh-inner-padding
);
// sass-lint:enable max-line-length

@import "@infoprojects/baseline-grid/scss/mixins/baseline-loader";

/////////////////////////////////
// Correct nesting widths
/////////////////////////////////
// sass-lint:disable-block class-name-format
@media screen and (max-width: $bl-mq-small - 1) { // extra-small
  .grid-zone.grid_12 .grid-blok.grid_12 .grid-blok.grid_6,
  .grid-zone.grid_12 .grid-blok.grid_12 .grid-blok.grid_4,
  .grid-zone.grid_12 .grid-blok.grid_12 .grid-blok.grid_3,
  .grid-zone.grid_12 .grid-blok.grid_9 .grid-blok.grid_3,
  .grid-zone.grid_12 .grid-blok.grid_8 .grid-blok.grid_4,
  .grid-zone.grid_9 .grid-blok.grid_9 .grid-blok.grid_3,
  .grid-zone.grid_8 .grid-blok.grid_8 .grid-blok.grid_4 {
    width: 100% !important;
  }
}

@media screen and (min-width: $bl-mq-small) and (max-width: $bl-mq-medium - 1) { // small
  .grid-zone.grid_12 .grid-blok.grid_12 .grid-blok.grid_6,
  .grid-zone.grid_12 .grid-blok.grid_12 .grid-blok.grid_4,
  .grid-zone.grid_12 .grid-blok.grid_12 .grid-blok.grid_3,
  .grid-zone.grid_12 .grid-blok.grid_9 .grid-blok.grid_3,
  .grid-zone.grid_9 .grid-blok.grid_9 .grid-blok.grid_3,
  .grid-zone.grid_8 .grid-blok.grid_8 .grid-blok.grid_4 {
    width: 50% !important;
  }

  .grid-persons .grid-zone.grid_12 .grid-blok.grid_12 .grid-blok.grid_6 {
    width: 100% !important;
  }
}

@media screen and (min-width: $bl-mq-medium) and (max-width: 1600px) { // Persoonkaartjes in nestings groot maken
  .grid-zone.grid_6 .grid-blok.grid_6 .grid-blok.grid_3 {
    width: 100% !important;
  }

  .grid-zone.grid_12 .grid-blok.grid_12.type-person-list-results .grid-blok.grid_3 {
    width: 50% !important;
  }
}

/////////////////////////////////
// Large logic from baseline styling
/////////////////////////////////
// sass-lint:enable class-name-format
$gutter: map-get($baseline, gutter);

@mixin large-logic($zones, $current-columns) {
  .grid-zone {
    @each $push in $pushes {

      &.push_#{$push} {
        left: percentage($push / $current-columns);
      }
    }

    @each $pull in $pulls {
      &.pull_#{$pull} {
        left: -#{percentage($pull / $current-columns)};
      }
    }
  }

  @each $zone in $zones {
    $zone-columns: map-get($zone, columns);
    $zone-blocks: map-get($zone, blocks);

    .grid-zone {
      &.grid_#{$zone-columns} {
        @each $block in $zone-blocks {
          .grid-blok {
            &.push_#{$block} {
              left: percentage($block / $zone-columns);
            }

            &.pull_#{$block} {
              left: -#{percentage($block / $zone-columns)};
            }

            &.prefix_#{$block} {
              $width: percentage($block / $zone-columns);

              @if $gutter > 0 {
                margin-left: calc(#{$width} + #{$gutter});
              }
              @else {
                margin-left: $width;
              }
            }

            &.suffix_#{$block} {
              $width: percentage($block / $zone-columns);

              @if $gutter > 0 {
                margin-right: calc(#{$width} + #{$gutter});
              }
              @else {
                margin-right: $width;
              }
            }
          }
        }
      }
    }
  }
}
// sass-lint:enable class-name-format

@media screen and (min-width: $bl-mq-medium) and (max-width: $bl-mq-large - 1) { // small
  $zones: map-get($baseline, zones);
  $current-columns: 12;

  @include large-logic($zones, $current-columns);
}

/////////////////////////////////
// Adjust grid for PZH.
/////////////////////////////////
@mixin grid-adjustment($margin, $gutter-size, $override-width) {
  .grid-wrapper {
    @if ($override-width == 0) {
      margin-left: $margin;
      margin-right: $margin;
    }
    @else {
      margin-left: auto;
      margin-right: auto;
      max-width: $override-width - ($margin * 2);
    }
  }

  .grid-blok .grid-element {
    padding-left: 0;
    padding-right: $gutter-size;
  }

  .grid-blok .grid-nesting {
    margin-left: 0;
    margin-right: $gutter-size * -1;
  }
}

@mixin pattern($margin) {
  background-size: $margin auto !important;
  right: $margin * -1;
  width: $margin;
}

@each $viewport in map-get($baseline, viewports) {
  @if (map-has-key($viewport, margin)) {
    $margin: map-get($viewport, margin);
    $gutter-size: map-get($viewport, gutter-size);
    $override-width: map-get($viewport, override-container);

    @if (map-has-key($viewport, min) and map-has-key($viewport, max)) {
      $min: map-get($viewport, min);
      $max: map-get($viewport, max);

      @media screen and (min-width: $min) and (max-width: $max) {
        @include grid-adjustment($margin, $gutter-size, $override-width);

        .grid-dashboard::after,
        .z-titel::after {
          @include pattern($margin);
        }
      }
    }

    @else if (map-has-key($viewport, max)) {
      $max: map-get($viewport, max);

      @media screen and (max-width: $max) {
        @include grid-adjustment($margin, $gutter-size, $override-width);

        .grid-dashboard::after,
        .z-titel::after {
          @include pattern($margin);
        }
      }
    }

    @else if (map-has-key($viewport, min)) {
      $min: map-get($viewport, min);

      @media screen and (min-width: $min) {
        @include grid-adjustment($margin, $gutter-size, $override-width);

        .grid-dashboard::after,
        .z-titel::after {
          @include pattern($margin);
        }
      }
    }

  }
}

@media screen and (max-width: $bl-mq-small - 1) { // extra-small
  .grid-blok > .grid-element {
    padding-left: 0;
    padding-right: 0;
  }

  .grid-nesting {
    margin-left: 0;
    margin-right: 0;
  }

  .grid-title,
  .grid-inside,
  .grid-box {
    padding-left: 30px;
    padding-right: 30px;
  }
}

.grid-dashboard {
  position: relative;
}
