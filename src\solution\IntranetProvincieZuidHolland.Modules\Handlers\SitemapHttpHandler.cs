﻿namespace IntranetProvincieZuidHolland.Modules.Handlers {
  using System;
  using System.Linq;
  using System.Threading;
  using System.Web;
  using System.Web.SessionState;

  using InfoProjects.Dxe.Util;

  public class SitemapHttpHandler : I<PERSON>ttp<PERSON><PERSON><PERSON>, IReadOnlySessionState {
    public bool IsReusable => true;

    public void ProcessRequest(HttpContext context) {
      try {
        var url = context.Request.Url.AbsolutePath;
        var sitemapSource = url.Split('/').Last().Split('.').First().Replace("sitemap", string.Empty);
        var target = string.Format(
          "{0}/aspx/get.aspx?xdl={1}&xsl={2}{3}&sitemapsource={4}",
          context.Request.ApplicationPath.TrimEnd('/'),
          Context.DefaultContext.GetProp("sitemap_urls_xdl", "/views/binnenplein/xdl/seo/socialsitemap_urls"),
          Context.DefaultContext.GetProp("sitemap_urls_xsl", "/views/binnenplein/xsl/seo/socialsitemap_urls"),
          context.Request.QueryString["refresh"] == "true" ? "&refresh=true" : string.Empty,
          sitemapSource);

        context.Server.Transfer(target);
      }
      catch (Exception e) {
        if (e is ThreadAbortException) {
          // ignore
        }
        else {
          Logger.Exception(e, "Error in SiteMapHttpHandler");
        }
      }
    }
  }
}
