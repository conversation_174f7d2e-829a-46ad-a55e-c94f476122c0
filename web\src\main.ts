import { bootstrapper, CoreModule, IproxAuthenticationConfig } from '@ip/social-web';
import { SharedModule } from '@ip/social-web/src/modules/shared/shared.module';
import { Config } from '@ip/social-web/src/modules/core/settings/config';

import { downgradeComponent } from '@angular/upgrade/static';

import './legacy/Templates.js';
import './legacy/App.js';
import './legacy/AppConfig/dialogs-nl-translation.js';
import './legacy/AppConfig/FileTypes.js';
import './legacy/AppConfig/LoadingBarConfig.js';
import './legacy/AppConfig/PersonCardMetaRows.js';
import './legacy/Authorizers/CalendarCommentsAuthorizer.js';
import './legacy/Authorizers/PostCommentsAuthorizer.js';
import './legacy/Authorizers/GroupPostsAuthorizer.js';
import './legacy/Authorizers/GroupQuestionsAuthorizer.js';
import './legacy/Directives/IpngLexisNexisDirective.js';

import { enableProdMode, NgModule } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { BrowserModule } from '@angular/platform-browser';
import { RouterModule } from '@angular/router';
import { UpgradeModule } from '@angular/upgrade/static';
import { platformBrowserDynamic } from '@angular/platform-browser-dynamic';
import { HttpClientModule, HttpClient } from '@angular/common/http';

import { MylexSearchModule } from '@pzh-local/mylex-search';

import { NgbTypeaheadModule } from '@ng-bootstrap/ng-bootstrap';
import { TINYMCE_SCRIPT_SRC } from '@tinymce/tinymce-angular';

import { PhoneNumberPipe, SOCIAL_CONFIG } from '@ip/social-core';
import { PERSON_CARD_META_TYPE } from '@ip/social-web/src/modules/person/person-card-meta-type';

import globalSettings from './config/globalSettings.json';
import { tinyMceConfig } from './config/tinymce-config';

import { environment } from './environments/environment';
import { customMetaTypes } from './legacy/AppConfig/person-meta-card-rows';

import { MylexComponent } from './zuid-holland/components/mylex/mylex.component';
import { SocialMailerComponent } from './zuid-holland/components/social-mailer/social-mailer.component';
import { SocialMailerEditorComponent } from './zuid-holland/components/social-mailer-editor/social-mailer-editor.component';
import { SocialMailerSubjectEnricherComponent } from './zuid-holland/components/social-mailer-subject-enricher/social-mailer-subject-enricher.component';
import { RefTextPipe } from './zuid-holland/pipes/ref-text/ref-text.pipe';

import { NgxsModule } from '@ngxs/store';

import './zuid-holland/utilities/apply-angularjs.utility';

if (environment.production) {
  enableProdMode();
}

@NgModule({
  imports: [
    BrowserModule,
    UpgradeModule,
    RouterModule.forRoot([]),
    HttpClientModule,
    FormsModule,
    NgbTypeaheadModule,
    NgxsModule.forRoot(),
    CoreModule.forRoot(globalSettings, tinyMceConfig, new IproxAuthenticationConfig()),
    SharedModule,
    MylexSearchModule,
  ],
  declarations: [
    MylexComponent,
    SocialMailerComponent,
    SocialMailerEditorComponent,
    SocialMailerSubjectEnricherComponent,
    RefTextPipe,
  ],
  providers: [
    {
      provide: 'GroupService',
      useFactory: ($injector: any) => $injector.get('GroupService'),
      deps: ['$injector']
    },
    {
      provide: 'PersonResource',
      useFactory: ($injector: any) => $injector.get('PersonResource'),
      deps: ['$injector']
    },
    {
      provide: TINYMCE_SCRIPT_SRC,
      useValue: tinyScriptSrc()
    },
    {
      provide: SOCIAL_CONFIG,
      useValue: globalSettings,
      multi: true,
    },
    {
      provide: PERSON_CARD_META_TYPE,
      useFactory: (phoneNumberPipe: PhoneNumberPipe) => customMetaTypes(phoneNumberPipe),
      deps: [PhoneNumberPipe]
    },
    RefTextPipe,
  ],
})
export class AppModule {
  constructor(private http: HttpClient, private upgrade: UpgradeModule, private config: Config) { }

  ngDoBootstrap() {
    bootstrapper(this.http, this.upgrade, this.config);
  }
}

platformBrowserDynamic().bootstrapModule(AppModule)
  .catch(err => console.error(err));

function tinyScriptSrc() {
  const socialAssetsRoute = (environment['config'] && environment['config']['webUrl']) ||
    window['socialConfig'] && window['socialConfig'].config && window['socialConfig'].config.socialApiUrl && window['socialConfig'].config.socialApiUrl.replace('/api/', '');

  return socialAssetsRoute + '/tinymce/tinymce.min.js';
}

(window as any).angular.module('Intranet')
  .directive('pzhSocialMailerEditor', downgradeComponent({ component: SocialMailerEditorComponent }))
  .directive('pzhSocialMailer', downgradeComponent({ component: SocialMailerComponent }))
  .directive('pzhSocialMailerSubjectEnricher', downgradeComponent({ component: SocialMailerSubjectEnricherComponent }));
(window as any).angular.module('Intranet.Legacy').directive('ipsMylexSearch', downgradeComponent({ component: MylexComponent }));
