import { Component, Input } from '@angular/core';
import { ActivatedRoute } from '@angular/router';

import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';

@Component({
  selector: 'ips-blog-page',
  template: `
    <ion-header>
      <ip-header [title]="'blog.pageTitle' | transloco"></ip-header>
    </ion-header>
    <ion-content>
      <ips-blog *ngIf="blogId$ | async as blogId;" [blogId]="blogId"></ips-blog>
    </ion-content>
  `,
})
export class BlogPageComponent {
  @Input()
  id!: string;

  blogId$: Observable<string>;

  constructor(activatedRoute: ActivatedRoute) {
    // SR: || this.id voelt gek.
    this.blogId$ = activatedRoute.data
      .pipe(map(data => data.blogId || this.id));
  }
}
