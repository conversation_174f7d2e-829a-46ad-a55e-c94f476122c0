{"name": "@ip/social-web", "version": "3.3.2522-2", "license": "UNLICENSED", "publishConfig": {"registry": "https://npm.iphq.nl"}, "scripts": {}, "files": ["src"], "main": "src/main.ts", "peerDependencies": {"@angular-devkit/build-angular": "0.1102.13", "@angular/animations": "11.2.14", "@angular/cdk": "11.2.12", "@angular/cli": "11.2.13", "@angular/common": "11.2.14", "@angular/compiler": "11.2.14", "@angular/compiler-cli": "11.2.14", "@angular/core": "11.2.14", "@angular/forms": "11.2.14", "@angular/language-service": "11.2.14", "@angular/platform-browser": "11.2.14", "@angular/platform-browser-dynamic": "11.2.14", "@angular/router": "11.2.14", "@angular/upgrade": "11.2.14", "tinymce": "5.7.1"}, "dependencies": {"@azure/msal-angular": "2.0.2", "@azure/msal-browser": "2.16.1", "@ip/social-core": "3.3.2522-2", "@tinymce/tinymce-angular": "4.2.4", "focus-trap": "^7.2.0", "less": "3.9.0", "lodash": "4.17.21", "muuri": "^0.9.1", "ngx-timeago": "^2.0.0", "tinymce": "5.7.1", "tslib": "^2.0.0"}}