import { Component, Input, OnInit, ViewEncapsulation } from '@angular/core';

import { IonSegmentButton, ModalController } from '@ionic/angular';
import { ILikeOption, IMetaConfig, Meta, MetaService, MetaSettings } from '@ip/social-core';
import { Observable } from 'rxjs';

@Component({
  selector: 'ips-like-action-sheet',
  templateUrl: './like-action-sheet.component.html',
  encapsulation: ViewEncapsulation.None,
  styleUrls: ['./like-action-sheet.component.scss']
})
export class LikeActionSheetComponent implements OnInit {
  @Input()
  metaConfig!: IMetaConfig;

  options: ILikeOption[];

  meta$!: Observable<Meta>;

  constructor(
    private metaService: MetaService,
    private modalController: ModalController,
    settings: MetaSettings
  ) {
    this.options = settings.likeOptions;
  }

  ngOnInit() {
    this.meta$ = this.metaService.get$(this.metaConfig);
  }

  change(meta: Meta, event: Event): void {
    const type: string = (event.target as unknown as IonSegmentButton).value;
    const userLike = meta.likes?.userLikes[0];

    if (type === userLike?.type) {
      meta.delete(userLike.id, userLike.type);
    }
    else {
      meta.like(type);
    }

    this.modalController.dismiss();
  }
}
