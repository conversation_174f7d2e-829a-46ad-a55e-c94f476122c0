import { Component, Inject, Input, OnInit } from '@angular/core';

import { IPerson, PersonService } from '@ip/social-core';
import { Observable } from 'rxjs';

import { PERSON_ROUTE } from '../../person-route';

@Component({
  selector: 'ips-person-item',
  templateUrl: './person-item.component.html',
})
export class PersonItemComponent implements OnInit {
  @Input()
  id!: string;

  @Input()
  showAvatar = true;

  @Input()
  enableNavigation = true;

  person$!: Observable<IPerson>;

  constructor(
    @Inject(PERSON_ROUTE) public personRoute: string,
    private personService: PersonService,
  ) { }

  ngOnInit() {
    this.person$ = this.personService.person$(this.id);
  }
}
