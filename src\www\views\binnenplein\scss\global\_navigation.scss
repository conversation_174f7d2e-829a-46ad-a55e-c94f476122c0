.type-primaire-navigatie {
  .grid-inside.menu-container {
    padding-left: $pzh-inner-padding - $pzh-unit;
    padding-right: $pzh-inner-padding;
  }

  .bl-navitem {
    display: inline-block;
    font-size: font-size-value(5, true);
    margin-bottom: 1.25rem;
    position: relative;
    text-align: center;

    a {
      color: $kopkleur;
      font-weight: 500;
    }

    &::after {
      @include nav-expanding-up {
        bottom: -.5rem;
        left: 50%;
        transform: translateX(-50%);
      }

      @include nav-expanding {
        bottom: 0;
        left: 0;
      }

      background-color: transparent;
      content: "";
      display: inline-block;
      height: 2px;
      margin-top: 5px;
      position: absolute;
      transition: width .3s;
      width: 1%; // SR: this cannot be 0.
    }

    &.selected::after,
    &.active::after,
    &:hover::after {
      background-color: $elementkleur;
      width: calc(100% - #{$pzh-unit * 2});
    }

    + .bl-navitem {
      margin-left: 1rem;
    }
  }

  .nav-eenvoudig {
    margin-top: 0;
  }

  @include nav-expanding {
    margin-top: $pzh-nav-expanding-logo-height * -1;
    width: 100% !important;

    .elt-hidden-large {
      display: block;
    }

    .bl-navbar {
      > .grid-title {
        padding-bottom: 1rem;
        padding-top: 1rem;
        text-align: right;
      }

      .click-menu {
        font-size: 1rem;
        position: relative;

        .caret::before {
          font-weight: 200;
        }

        a.primaire-navigatie {
          bottom: 0;
          font-size: 0;
          left: 0;
          position: absolute;
          right: 0;
          top: 0;
        }
      }
    }

    .bl-navitem {
      display: block;
      margin-bottom: 0;
      margin-left: 1rem;
      text-align: left;

      +.bl-navitem {
        margin-left: 1rem;
      }
    }
  }
}
