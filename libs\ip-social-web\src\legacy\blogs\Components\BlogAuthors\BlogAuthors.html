<span>{{ 'blogs.coAuthors' | ipRefText }}</span>
<div
  class="ips-blog-author"
  ng-repeat="person in BlogAuthorsCtrl.authors | orderBy: BlogAuthorsCtrl.settings.orderBy"
>
  <ips-person-avatar class="ips-avatar-inline" person="person"></ips-person-avatar>
  <a
    ng-if="person.active"
    class="bs-blog-poster-name"
    ip-person-url="person"
  >{{ person.fullName }}</a>
  <span ng-if="!person.active" class="bs-blog-poster-name">
    {{ 'person.inactive' | ipRefText }}
  </span>
  <button
    type="button"
    class="btn btn-default btn-xs ips-button ips-remove-author"
    title="{{ 'blogs.author.removePerson' | ipRefText : { name: !person.active ? 'person.inactive' : person.fullName } }}"
    ng-if="BlogAuthorsCtrl.editMode"
    ng-click="BlogAuthorsCtrl.removeAuthor(person)"
  >
    <i ng-class=":: BlogAuthorsCtrl.settings.removeIcon"></i>
    <span>{{ 'blogs.author.remove' | ipRefText }}</span>
  </button>
</div>

<ips-person-suggest
  ng-if="BlogAuthorsCtrl.editMode && BlogAuthorsCtrl.authors.length &lt; BlogAuthorsCtrl.settings.maxAuthors"
  on-select="BlogAuthorsCtrl.addAuthor($person)"
  data-context="blogs.author"
  data-exclude="BlogAuthorsCtrl.exclude"
></ips-person-suggest>
