import { NgModule } from '@angular/core';
import { RouterModule } from '@angular/router';
import { BlogPageComponent } from '../blog/components/blog-page/blog-page.component';
import { BlogResolver } from '../blog/services/blog.resolver';

import { PersonBlogListPageComponent } from './components/person-blog-list-page/person-blog-list-page.component';
import { PersonPageComponent } from './components/person-page/person-page.component';
import { ProfileModule } from './profile.module';
import { IsOwnProfileResolver } from './resolver/is-own-profile.resolver';
import { PersonResolver } from './resolver/person.resolver';

@NgModule({
  imports: [
    ProfileModule,
    RouterModule.forChild([
      {
        path: '',
        component: PersonPageComponent,
        pathMatch: 'full',
        resolve: {
          person: PersonResolver,
          isOwnProfile: IsOwnProfileResolver,
        },
      },
      {
        path: 'blogs',
        component: PersonBlogListPageComponent,
        resolve: {
          person: PersonResolver,
          isOwnProfile: IsOwnProfileResolver,
        },
      },
      {
        path: 'blogs/:blogId',
        component: BlogPageComponent,
        resolve: {
          blogId: BlogResolver
        },
      },
    ]),
  ]
})
export class ProfileRouteModule {
}
