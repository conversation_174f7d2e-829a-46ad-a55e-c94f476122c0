﻿namespace IntranetProvincieZuidHolland.Iprox.Handlers {
  using System;
  using InfoProjects.Dxe.Linq;
  using InfoProjects.Dxe.Process;
  using InfoProjects.Dxe.Process.Handler;
  using InfoProjects.Iprox.Model;
  using IntranetProvincieZuidHolland.Iprox.Model;
  
  /// <summary>
  /// Handler to remove foto from IPROX fotoalbum.
  /// </summary>
  public class RemoveFotoHandler : IContextProcessHandler {
    /// <summary>
    /// Processes the unit.
    /// </summary>
    /// <param name="unit">
    /// Process unit.
    /// </param>
    /// <param name="context">
    /// Process context.
    /// </param>
    /// <returns>
    /// Result props with Success = true.
    /// </returns>
    public ResultProps Process(ProcessUnit unit, ProcessContext context) {
      var itmIdt = unit[Reserved.ID];
      string pagClsIdt = unit["PagClsIdt"];
      if (String.IsNullOrEmpty(itmIdt) && !String.IsNullOrEmpty(pagClsIdt)) {
        throw new ProcessException("IDs must be set");
      }

      using (var cms = new IproxCms()) {
        var fotoalbum = cms.GetItem<Fotoalbum>(itmIdt.To<int>());
        foreach (var foto in fotoalbum.Page.Foto) {
          if (foto.Id == pagClsIdt.To<int>()) {
            fotoalbum.Page.Foto.Remove(foto);
            fotoalbum.MarkPublished();
            break;
          }
        }

        cms.SubmitChanges(context);
      }

      var result = new ResultProps();
      result["Success"] = "true";
      return result;
    }
  }
}