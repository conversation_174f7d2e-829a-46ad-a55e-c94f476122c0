{"$schema": "../../node_modules/ng-packagr/ng-package.schema.json", "dest": "../../dist/libs/ip-social-pwa", "lib": {"entryFile": "src/public-api.ts", "umdModuleIds": {"@azure/msal-angular": "azure-msal-angular", "@azure/msal-browser": "azure-msal-browser", "@ionic/angular": "ionic-angular", "@ip/pwa": "ip-pwa", "@ip/social-core": "ip-social-core", "@ngneat/transloco": "ngneat-transloco", "@ngxs/store": "ngxs-store", "@ngxs/store/operators": "ngxs-store-operators", "ngx-timeago": "ngx-timeago", "ngx-timeago/language-strings/nl": "ngx-timeago-language-strings-nl", "ngx-virtual-scroller": "ngx-virtual-scroller"}}, "allowedNonPeerDependencies": ["."]}