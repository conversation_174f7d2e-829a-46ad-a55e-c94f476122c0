import { Compo<PERSON>, Element<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, OnInit, ViewChild } from '@angular/core';
import { IonRefresher } from '@ionic/angular';

import { MenuService } from '@ip/pwa';
import { Actions, ofActionDispatched, Select } from '@ngxs/store';
import { IPageInfo, VirtualScrollerComponent } from 'ngx-virtual-scroller';
import { Observable, Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';

import { ITimelineEntry } from '../../models';
import { TimelineService } from '../../services/timeline.service';
import { Timeline } from '../../state/timeline.actions';
import { TimelineState } from '../../state/timeline.state';

@Component({
  selector: 'ips-timeline-page',
  templateUrl: './timeline.component.html',
  styleUrls: ['./timeline.component.scss'],
})
export class TimelineComponent implements OnInit, OnD<PERSON>roy {
  /** Maximum number of entries below the viewport before virtual scroll should requests more entries */
  private static readonly LOAD_MORE_THRESHOLD = 4;

  private destroyed = new Subject();

  @ViewChild('scroll')
  scroll!: VirtualScrollerComponent;

  @Select(TimelineState.fetching)
  fetching$!: Observable<boolean>;

  @Select(TimelineState.entries)
  timeline$!: Observable<ITimelineEntry[]>;

  isScrolledToTop = true;

  // eslint-disable-next-line max-len
  constructor(
    private timelineService: TimelineService,
    private menuService: MenuService,
    private elementRef: ElementRef,
    private actions$: Actions,
  ) { }

  ngOnInit() {
    this.timelineService.refresh();
    this.menuResetListener();
    this.refreshListener();
  }

  ngOnDestroy() {
    this.destroyed.next();
    this.destroyed.complete();
  }

  trackByFn(index: number, entry: ITimelineEntry) {
    return entry.id + entry.timestamp + entry.mutation;
  }

  loadData(event: IPageInfo, numberOfEntries: number) {
    if (event.endIndex > (numberOfEntries - TimelineComponent.LOAD_MORE_THRESHOLD)) {
      this.timelineService.loadData();
    }
  }

  scrollPos() {
    this.isScrolledToTop = this.scroll.viewPortInfo.scrollStartPosition < 10;
  }

  refresh(event?: Event) {
    this.timelineService.refresh()
      .subscribe(() => (event?.target as unknown as IonRefresher)?.complete());
  }

  private refreshListener(): void {
    this.actions$
      .pipe(
        ofActionDispatched(Timeline.Refresh),
        takeUntil(this.destroyed)
      )
      .subscribe(() => {
        this.scroll.scrollToPosition(0);
      });
  }

  private menuResetListener(): void {
    this.menuService.get('tijdlijn')
      .pipe(takeUntil(this.destroyed))
      .subscribe(event => {
        const el = this.elementRef.nativeElement as HTMLElement;
        const pageHidden = el.className.includes('ion-page-hidden');

        if (event.selected && !pageHidden) {
          this.refresh();
        }
      });
  }
}
