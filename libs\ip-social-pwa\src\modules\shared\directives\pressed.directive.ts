import { Directive, EventEmitter, HostListener, OnDestroy, Output } from '@angular/core';
import { BehaviorSubject, of, Subscription, timer } from 'rxjs';
import { filter, switchMap } from 'rxjs/operators';

@Directive({ selector: '[ipsPressed]' })
export class PressedDirective implements OnDestroy {
  private subscription: Subscription;

  private pressed = new BehaviorSubject<boolean>(false);

  @Output()
  ipsPressed = new EventEmitter();

  threshold = 300;

  pressed$ = this.pressed.asObservable();

  constructor() {
    this.subscription = this.pressed$
      .pipe(
        switchMap(state =>
          state ? timer(this.threshold) : of(null)
        ),
        filter(value => value !== null)
      )
      .subscribe(() => this.ipsPressed.emit());
  }

  @HostListener('mousedown')
  onMouseDown() {
    this.pressed.next(true);
  }

  @HostListener('mouseup')
  onMouseUp() {
    this.pressed.next(false);
  }

  @HostListener('touchstart')
  onTouchStart() {
    this.pressed.next(true);
  }

  @HostListener('mouseend')
  onTouchEnd() {
    this.pressed.next(false);
  }

  ngOnDestroy(): void {
    this.subscription?.unsubscribe();
  }
}
