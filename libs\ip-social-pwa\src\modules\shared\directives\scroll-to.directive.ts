import { AfterViewInit, Directive, ElementRef, Input } from '@angular/core';
import { ActivatedRoute } from '@angular/router';

@Directive({ selector: '[ipsScrollTo]' })
export class ScrollToDirective implements AfterViewInit {
  @Input('ipsScrollTo')
  key!: string;

  @Input()
  scrollOptions: ScrollIntoViewOptions = {};

  @Input()
  scrollDelay = 500;

  constructor(
    private activatedRoute: ActivatedRoute,
    private elem: ElementRef
  ) { }

  ngAfterViewInit() {
    const scrollTo = this.activatedRoute.snapshot.queryParamMap.get('scrollTo');
    if (scrollTo === this.key) {
      setTimeout(() => {
        this.elem.nativeElement?.scrollIntoView({ behavior: 'smooth', block: 'start', ...this.scrollOptions });
      }, this.scrollDelay);
    }
  }
}
