import { Component, ComponentFactory, ComponentFactoryResolver, Input, OnInit, ViewChild, ViewContainerRef } from '@angular/core';

import * as Activity from '../../activities/components';
import { ActivityPart, ActivityParts } from '../../activities/models';

type ActivityPartComponent = Activity.PersonComponent | Activity.TextComponent | Activity.LinkComponent;

@Component({
  templateUrl: './timeline-activity.component.html',
  selector: 'ips-timeline-activity',
  styleUrls: ['./timeline-activity.component.scss']
})
export class TimelineActivityComponent implements OnInit {
  @ViewChild('outlet', { static: true, read: ViewContainerRef })
  outlet!: ViewContainerRef;

  @Input()
  activity!: ActivityParts;

  constructor(private componentFactoryResolver: ComponentFactoryResolver) { }

  ngOnInit() {
    this.activity.forEach(part => {
      this.create(this.outlet, this.resolveComponentFactory(part), part.data);
    });
  }

  create(outlet: ViewContainerRef, componentFactory: ComponentFactory<ActivityPartComponent>, data: string | { text: string; url: string; }): void {
    const component = outlet.createComponent(componentFactory);
    component.instance.data = data;
  }

  resolveComponentFactory(part: ActivityPart): ComponentFactory<ActivityPartComponent> {
    return this.componentFactoryResolver
      .resolveComponentFactory<ActivityPartComponent>(part.component);
  }
}
