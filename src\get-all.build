<project name="GetResourcesForImpl" xmlns="http://nant.sf.net/release/0.85/nant.xsd" default="all">

  <target name="yarn">
    <exec
      program="yarn.cmd"
      workingdir="${src.dir}"
      failonerror="true">
      <arg line="--immutable" />
    </exec>
  </target>

  <target name="copy-nodemodules">
    <copy todir="${src.dir}/www/views/binnenplein/fonts/fontawesome">
      <fileset basedir="${src.dir}/node_modules/@infoprojects-local/baseline-styling/src/fontawesome/webfonts">
        <include name="**/*.*" />
        <include name="*.*" />
      </fileset>
    </copy>
  </target>

  <target name="all" depends="yarn copy-nodemodules" />
</project>
