import { ComponentFactoryResolver, Injectable } from '@angular/core';

import { TimelineContentFactory } from '../content-factory';
import { TimelineEventComponent } from './event.component';

@Injectable()
export class EventFactory extends TimelineContentFactory {
  component = TimelineEventComponent;

  type = 'event';

  constructor(componentFactoryResolver: ComponentFactoryResolver) {
    super(componentFactoryResolver);
  }
}
