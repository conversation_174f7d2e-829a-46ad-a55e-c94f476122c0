import { Injectable } from '@angular/core';
import { FormControl } from '@angular/forms';

import { ModalController } from '@ionic/angular';

import { PersonSelectionComponent } from './person-selection.component';

@Injectable()
export class PersonSelectionService {
  constructor(public modalController: ModalController) { }

  async open(formControl: FormControl, title: string) {
    const modal = await this.modalController.create({
      component: PersonSelectionComponent,
      cssClass: 'ips-person-selection-modal',
      componentProps: {
        formControl,
        title
      }
    });

    return await modal.present();
  }
}
