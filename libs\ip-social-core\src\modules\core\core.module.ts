import { PlatformModule } from '@angular/cdk/platform';
import { CommonModule } from '@angular/common';
import { HttpClient } from '@angular/common/http';
import { ModuleWithProviders, NgModule } from '@angular/core';

import { TranslocoModule } from '@ngneat/transloco';

import { ResourceTokenService } from '../authentication/base/resource-token.service';
import { MicroblogSettings } from '../microblog/microblog.settings';
import { PersonsModule } from '../persons/persons.module';
import { translateProviders } from '../translate/translate-providers';
import { SOCIAL_CONFIG, SocialConfigProvider } from './config/config-provider.service';
import { ISocialConfig, IWebSocialConfig } from './config/config.model';
import { CoreSettings } from './core.settings';
import { SocialClientHttpFactory, SocialHttpClient } from './services/api-client';
import { AppInstallService } from './services/app-install.service';
import { DeviceSettingsService } from './services/device-settings.service';
import { DeviceService } from './services/device.service';
import { ResourceService } from './services/resource.service';
import { UserAgentService } from './services/user-agent.service';
import { UserFactory } from './services/user.factory';
import { UserService } from './services/user.service';

declare global {
  interface Window {
    social: ISocialConfig;
    socialConfig: IWebSocialConfig;
  }
}

const getSocialConfig = (): ISocialConfig => {
  if (window.social === undefined && window.socialConfig === undefined) {
    throw new Error('[Core] window.social & window.socialConfig are undefined');
  }

  const transformSocialConfigToCorrectFormat = () => window.socialConfig ? {
    core: {
      apiUrl: window.socialConfig.config.socialApiUrl,
      baseUrl: '',
      authentication: {}
    }
  } : {};

  return {
    ...transformSocialConfigToCorrectFormat(), // this is iprox redactie configuration.
    ...window.social,
  };
};

@NgModule({
  declarations: [
  ],
  imports: [
    CommonModule,
    TranslocoModule,
    PlatformModule,
    PersonsModule,
  ],
  exports: [
    TranslocoModule,
  ],
  providers: [
    UserFactory,
  ]
})
export class CoreModule {
  static forRoot(): ModuleWithProviders<CoreModule> {
    return {
      ngModule: CoreModule,
      providers: [
        AppInstallService,
        CoreSettings,
        DeviceService,
        MicroblogSettings, // Todo: Uitzoeken waarom dit niet in microblog.module.ts kan.
        DeviceSettingsService,
        ResourceService,
        ResourceTokenService,
        SocialConfigProvider,
        UserAgentService,
        UserService,
        ...translateProviders(['nl']),
        {
          provide: SocialHttpClient,
          useFactory: SocialClientHttpFactory,
          deps: [HttpClient, CoreSettings]
        },
        {
          provide: SOCIAL_CONFIG,
          useValue: getSocialConfig(),
          multi: true,
        },
      ]
    };
  }
}
