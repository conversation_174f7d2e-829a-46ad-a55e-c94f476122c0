import { Component, HostBinding, Input } from '@angular/core';

@Component({
  templateUrl: './page-link.component.html',
  selector: 'ips-timeline-page-link',
  styleUrls: ['./page-link.component.scss']
})
export class TimelinePageLinkComponent {
  @Input()
  reference!: {
    id: string | null;
    collection: string | null;
  };

  @HostBinding('class.ion-activatable')
  ionActivatableClass = true;

  @Input()
  @HostBinding('class.ips-full-link-area')
  fullLink = false;
}
