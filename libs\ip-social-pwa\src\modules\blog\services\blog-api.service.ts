import { HttpHeaders } from '@angular/common/http';
import { Injectable } from '@angular/core';

import { SocialHttpClient, reportUploadProgress } from '@ip/social-core';
import { Observable, Subject } from 'rxjs';
import { map, tap } from 'rxjs/operators';

import { IBlog, IBlogSummary } from '../models';
import { BlogApi } from '../models/internal/api.models';

@Injectable()
export class BlogApiService {
  private blogChange = new Subject<void>();

  onBlogChange$ = this.blogChange.asObservable();

  constructor(private socialApi: SocialHttpClient) { }

  get(id: string): Observable<IBlog> {
    return this.socialApi.get<BlogApi.IBlogResponse>(`blog/${id}`)
      .pipe(
        map(res => this.mapBlog(res)),
      );
  }

  list(userId: string): Observable<IBlogSummary[]> {
    return this.socialApi.get<BlogApi.IBlogSummary[]>(`blog/getblogs/${userId}`)
      .pipe(
        map(response => response.map(apiBlogSummary => this.mapBlogsummary(apiBlogSummary))),
        tap(list => list.sort((a, b) => b.creationDate.getTime() - a.creationDate.getTime())),
      );
  }

  create(): Observable<IBlog> {
    return this.socialApi.get<BlogApi.IBlogResponse>('blog/startblog')
      .pipe(
        map(response => this.mapBlog(response)),
        tap(() => this.blogChange.next()),
      );
  }

  publish(id: string): Observable<IBlog> {
    return this.socialApi.put<BlogApi.IBlogResponse>(
      `blog/${id}/publish`,
      { blogId: id }
    )
      .pipe(
        map(response => this.mapBlog(response)),
        tap(() => this.blogChange.next()),
      );
  }

  remove(id: string): Observable<null> {
    return this.socialApi.delete<null>(`blog/${id}`)
      .pipe(
        tap(() => this.blogChange.next()),
      );
  }

  updateWorkingTitle(id: string, workingTitle: string): Observable<IBlog> {
    return this.socialApi.put<BlogApi.IBlogResponse>(
      // TODO: Replace with workingTitle
      `blog/${id}/updatetitle`,
      JSON.stringify(workingTitle),
      {
        // eslint-disable-next-line @typescript-eslint/naming-convention
        headers: new HttpHeaders({ 'Content-Type': 'application/json' })
      }
    )
      .pipe(
        map(res => this.mapBlog(res)),
        tap(() => this.blogChange.next()),
      );
  }

  updateWorkingBody(id: string, workingBody: string): Observable<IBlog> {
    return this.socialApi.put<BlogApi.IBlogResponse>(
      `blog/${id}/updateworkingbody`,
      JSON.stringify(workingBody),
      {
        // eslint-disable-next-line @typescript-eslint/naming-convention
        headers: new HttpHeaders({ 'Content-Type': 'application/json' }),
        reportProgress: true,
        observe: 'events',
      }
    )
      .pipe(
        reportUploadProgress(`blog-${id}-workingBody`),
        map(res => this.mapBlog(res)),
      );
  }

  inlineImage(id: string, file: File) {
    const formData = new FormData();

    formData.append('file', file, file.name);

    return this.socialApi.post<BlogApi.IUpdateImageResponse>(
      `blog/${id}/inlineimage`,
      formData,
      {
        reportProgress: true,
        observe: 'events'
      }
    )
      .pipe(reportUploadProgress(`blog-${id}-content-image-${file.name}`));
  }

  updateAuthors(id: string, personIds: string[]): Observable<IBlog> {
    return this.socialApi.patch<BlogApi.IBlogResponse>(
      `blog/${id}/updateauthors`,
      personIds,
    )
      .pipe(
        map(res => this.mapBlog(res)),
      );
  }

  updateImage(id: string, file: File): Observable<BlogApi.IUpdateImageResponse> {
    const formData = new FormData();
    formData.append('file', file, file.name);

    return this.socialApi.post<BlogApi.IUpdateImageResponse>(
      `blog/${id}/updateimage`,
      formData,
      {
        reportProgress: true,
        observe: 'events',
      })
      .pipe(reportUploadProgress(`blog-${id}-banner`));
  }

  removeImage(id: string): Observable<null> {
    return this.socialApi.put<null>(
      `blog/${id}/removeimage`,
      { blogId: id },
    )
      .pipe(
        tap(() => this.blogChange.next()),
      );
  }

  private mapBlogsummary(blogSummary: BlogApi.IBlogSummary): IBlogSummary {
    return {
      id: blogSummary.id,
      title: blogSummary.title,
      summary: blogSummary.summary,
      userId: blogSummary.userId,
      creationDate: new Date(blogSummary.creationDate),
      image: blogSummary.image,
      published: blogSummary.published,
      publishDate: new Date(blogSummary.publishDate),
      commentCount: blogSummary.commentCount,
      workInProgress: blogSummary.workInProgress,
    };
  }

  private mapBlog(blog: BlogApi.IBlogResponse): IBlog {
    const workingBody = blog.workingBody ?? '';
    const isModified = workingBody !== blog.body;

    return {
      id: blog.id,
      slug: blog.slug,
      userId: blog.userId,
      image: blog.image,
      creationDate: new Date(blog.creationDate),
      lastModifiedDate: new Date(blog.lastModifiedDate),
      publishDate: new Date(blog.publishDate),
      published: blog.published,
      authorIds: blog.authors.map(author => author.id),
      title: blog.title,
      summary: blog.summary,
      body: blog.body ?? '',
      workingTitle: blog.title, // TODO: Set to workingTitle
      workingBody,
      isModified,
      canPublish: blog.title?.length > 0 && workingBody.length > 0 && isModified === true,
      workInProgress: blog.workInProgress,
    };
  }
}
