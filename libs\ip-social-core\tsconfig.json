{"extends": "../../tsconfig.base.json", "files": [], "include": [], "references": [{"path": "./tsconfig.lib.json"}, {"path": "./tsconfig.lib.prod.json"}, {"path": "./tsconfig.spec.json"}], "compilerOptions": {"forceConsistentCasingInFileNames": true, "strict": true, "noImplicitReturns": true, "noFallthroughCasesInSwitch": true}, "angularCompilerOptions": {"strictInjectionParameters": true, "strictTemplates": true}}