import { Directive, HostBinding, HostListener, Input, OnChanges, SimpleChanges } from '@angular/core';

import { ResourceService, SocialImageSize } from '@ip/social-core';

@Directive({
  // eslint-disable-next-line @angular-eslint/directive-selector
  selector: 'img[ips-blog-image-src]',
})
export class BlogImageSrcDirective implements OnChanges {
  @Input()
  blogId!: string;

  @Input()
  image!: string | null;

  @Input()
  size: SocialImageSize = 'wide';

  @HostBinding('src')
  securedSrc?: string;

  @HostBinding('class.ips-animate-image')
  animateImage = true;

  @HostBinding('class.ips-loaded')
  loaded = false;

  constructor(private resourceService: ResourceService) { }

  @HostListener('load', ['$event'])
  onLoad() {
    this.loaded = true;
  }

  ngOnChanges(changes: SimpleChanges) {
    if (changes.image.currentValue !== changes.image.previousValue) {
      this.resourceService.blogImageSrc(this.blogId, this.image, this.size)
        .subscribe(src => this.securedSrc = src);
    }
  }
}
