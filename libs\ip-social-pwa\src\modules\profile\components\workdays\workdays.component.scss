@import "../../../../scss/variables";
@import "../../../../scss/mixins";

$ips-daypart-box-size: 24px;
$ips-daypart-box-spacing: 2px;
$ips-daypart-border-radius: 3px;

$ips-workdays-max-width: 350px;

ion-list-header {
  @include font-size(-2);
}

ol {
  display: flex;
  list-style-type: none;
  justify-content: space-between;
  margin-top: 0;
  max-width: $ips-workdays-max-width;
  padding-left: 0;
  width: 100%;
}

li {
  display: inline-block;
  text-align: center;
}

abbr {
  @include font-size(-2);

  color: var(--ion-color-primary);
  text-decoration: none;
  text-transform: uppercase;
}

.ips-dayparts {
  display: flex;
  justify-content: center;
}

.ips-daypart {
  background-color: rgba(var(--ion-color-primary-rgb), .2);
  border-radius: $ips-daypart-border-radius;
  height: $ips-daypart-box-size;
  position: relative;
  width: $ips-daypart-box-size;

  &.ips-daypart-active {
    background-color: var(--ion-color-primary);

    ion-icon {
      @include font-size(0);

      color: var(--ion-color-primary-contrast);
      left: 50%;
      position: absolute;
      top: 50%;
      transform: translate(-50%, -50%);
    }
  }

  +.ips-daypart {
    margin-left: $ips-daypart-box-spacing;
  }
}
