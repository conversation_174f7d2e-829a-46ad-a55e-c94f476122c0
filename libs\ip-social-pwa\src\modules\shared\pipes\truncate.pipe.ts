import { Pipe, PipeTransform } from '@angular/core';

export type TruncatePipeOptions = {
  limit: number;
  suffix?: string;
};

@Pipe({ name: 'truncate' })
export class TruncatePipe implements PipeTransform {
  /**
  * Truncates the input text. If limit is set to 0 truncate is disabled.
  */
  transform(value: string, { limit, suffix }: TruncatePipeOptions): string {
    return value.length > limit && limit > 0
      ? value.substring(0, Math.max(0, value.substring(0, limit).lastIndexOf(' '))) + (suffix ?? '')
      : value;
  }
}
