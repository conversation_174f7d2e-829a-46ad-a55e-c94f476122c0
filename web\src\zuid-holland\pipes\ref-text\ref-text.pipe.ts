import { Pipe, PipeTransform } from '@angular/core';

@Pipe({
  name: 'refText'
})
export class RefTextPipe implements PipeTransform {
  private context = 'groepsmail';

  transform(input: string, data?: ReferenceTextData): string {
    if (input === null) {
      return input;
    }

    return this.getText(input, data);
  }

  getText(alias: string, data?: ReferenceTextData) {
    const raw = this.iproxExists() ? (window as any).iprox.referenceLists.getText(alias, this.context) : alias;

    return this.replaceText(raw, data);
  }

  iproxExists() {
    return typeof (window as any).iprox !== 'undefined';
  }

  replaceText(raw: string, data?: ReferenceTextData) {
    return raw && data
      ? raw.replace(/\[\w+\]/g, placeholder => {
        const value = data[placeholder.slice(1, -1)];
        if (value !== undefined) {
          return value;
        }

        return placeholder;
      })
      : raw;
  }
}

type ReferenceTextData = {
  [key: string]: string;
};
