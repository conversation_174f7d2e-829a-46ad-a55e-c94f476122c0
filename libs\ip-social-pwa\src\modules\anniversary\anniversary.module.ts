import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';

import { IonicModule } from '@ionic/angular';

import { AnniversaryEventComponent } from '../anniversary/components/anniversary-event/anniversary-event.component';
import { CommentsModule } from '../comments/comments.module';
import { PersonModule } from '../person/person.module';
import { SharedModule } from '../shared/shared.module';
import { AnniversaryPreviewModalComponent } from './components/anniversary-preview-modal/anniversary-preview-modal.component';
import { AnniversaryPreviewComponent } from './components/anniversary-preview/anniversary-preview.component';
import { AnniversaryApiService } from './services/anniversary-api.service';

@NgModule({
  imports: [
    CommonModule,
    SharedModule,
    PersonModule,
    IonicModule,
    CommentsModule,
  ],
  declarations: [
    AnniversaryEventComponent,
    AnniversaryPreviewComponent,
    AnniversaryPreviewModalComponent,
  ],
  providers: [
    AnniversaryApiService,
  ],
  exports: [
    AnniversaryPreviewComponent,
    AnniversaryPreviewModalComponent,
  ]
})
export class AnniversaryModule { }
