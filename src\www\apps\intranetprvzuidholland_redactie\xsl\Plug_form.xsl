<?xml version="1.0" encoding="utf-8" ?>

<xsl:stylesheet version="1.0" xmlns:xsl="http://www.w3.org/1999/XSL/Transform">

  <xsl:import href="/apps/baseline_beheer/xsl/Plug_form.xsl"/>
  <xsl:import href="Plug_input_include.xsl"/>

  <xsl:output method="html" version="4.0" encoding="windows-1252" doctype-public="-//W3C//DTD HTML 4.01 Transitional//EN" indent="no"/>

  <xsl:template match="site" mode="default_css">
    <xsl:apply-imports />

    <!-- in lightbox-edit modus vanaf de voorkant, voor paginatype 'workgroup', knoppen "opslaan" en "tonen" verbergen -->
    <xsl:if test="$embedded_cms and not($exitEnvPrv) and /data/site/item/PagTypAka = 'workgroup'">
      <style type="text/css">
        #OkBtn_top,
        #ViewBtn_top {
          display:none;
          visibility:hidden;
        }
      </style>
    </xsl:if>
  </xsl:template>

  <xsl:template match="veld" mode="minor-change-field">
    <xsl:apply-templates select="(/data/site/item/page//veld[@VldDefIdt=current()/@VldDefIdt])[last()]" mode="showField_fieldprops">
      <xsl:with-param name="current_cluster" select="/data/site/item/page//cluster[@ClsIdt=current()/parent::clusterdefinition/@ClsIdt]" />
    </xsl:apply-templates>
    <table class="list">
      <xsl:apply-templates select="(/data/site/item/page//veld[@VldDefIdt=current()/@VldDefIdt])[last()]" mode="showField">
        <xsl:with-param name="current_cluster" select="/data/site/item/page//cluster[@ClsIdt=current()/parent::clusterdefinition/@ClsIdt]" />
      </xsl:apply-templates>
    </table>
  </xsl:template>

  <xsl:template match="page" mode="viewTab">
    <xsl:if test="$embedded_cms">
      <xsl:if test="$ActClsIdt != 'plus' and $ActClsIdt != 'info' and $ActClsIdt != 'aut' and $ActClsIdt != 'version' and $collectionTab != 'true'">
        <xsl:apply-templates select="/data/site/item/page//clusterdefinition[Nam='Meta']/*/veld[DefNam = 'Dit is een kleine wijziging' and site]" mode="minor-change-field" />
      </xsl:if>
    </xsl:if>
    <xsl:call-template name="page-viewTab" />
  </xsl:template>

</xsl:stylesheet>
