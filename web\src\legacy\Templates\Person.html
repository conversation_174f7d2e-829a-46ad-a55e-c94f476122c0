﻿<div class="grid-blok grid_3 rol-profileimage has-elt-breakpoints" ip-highlight="profileImage">
  <div class="grid-element">
    <div class="grid-edge">
      <div class="grid-box">
        <div class="bs-form-grid">
          <person-profile-image data-person="person"></person-profile-image>
          <edit-button data-person="person" data-ng-if-start="person.active"></edit-button>
          <ips-personal-account-switch data-person="person"></ips-personal-account-switch>
          <ips-community-manager-switch data-person="person"></ips-community-manager-switch>
          <ips-functional-profile-switches data-person="person"></ips-functional-profile-switches>
          <person-follow-button data-person="person" ng-if="person.loginName !== 'communitymanager'"></person-follow-button>
          <person-ip-engineer data-person="person" data-ng-if-end></person-ip-engineer>
          <mailer-settings ng-if="editMode() && personIsUser && person.loginName !== 'communitymanager'"></mailer-settings>
          <a
            ng-if="personIsUser && person.loginName === 'communitymanager'"
            class="btn btn-default btn-block"
            href="/?AppIdt=redirect-item&alias=groepsmail&mailerMode=socialGroups"
          >{{:: 'pzh.person.mailGroupOwners' | ipRefText }}</a>
        </div>
      </div>
    </div>
  </div>
</div>
<div class="grid-blok grid_9">
  <div class="grid-element">
    <div class="grid-edge">
      <div class="grid-nesting">
        <div class="grid-blok grid_9 rol-person-title has-elt-breakpoints">
          <div class="grid-element">
            <div class="grid-edge">
              <div class="grid-box">
                <h2>{{person.fullName}}</h2>
              </div>
            </div>
          </div>
        </div>
        <div class="grid-blok grid_9 rol-person-meta has-elt-breakpoints" ng-if="person.active === false">
          <div class="grid-element">
            <div class="grid-edge">
              <div class="grid-box">
                {{:: 'person.inactive' | ipRefText }}
              </div>
            </div>
          </div>
        </div>
        <div
          class="grid-blok grid_3 rol-person-meta has-elt-breakpoints"
          ng-if-start="person.active && person.loginName !== 'communitymanager' && person.loginName !== 'facilitairezaken' && person.loginName !== 'personeelenorganisatie' && person.loginName !== 'servicedesk' && person.loginName !== 'internecommunicatie'"
        >
          <div class="grid-element">
            <div class="grid-edge">
              <div class="grid-box">
                <person-property-list data-person="person" data-config="'location'" ip-highlight="location"></person-property-list>
                <person-property-list data-person="person" data-config="'jobFunction'" ip-highlight="jobFunction"></person-property-list>
              </div>
            </div>
          </div>
        </div>
        <div class="grid-blok grid_3 rol-person-meta has-elt-breakpoints">
          <div class="grid-element">
            <div class="grid-edge">
              <div class="grid-box">
                <person-email data-person="person" ip-highlight="email"></person-email>
                <person-phone data-person="person" ip-highlight="phoneMobile"></person-phone>
              </div>
            </div>
          </div>
        </div>
        <div class="grid-blok grid_3 rol-person-meta has-elt-breakpoints" ng-if-end>
          <div class="grid-element">
            <div class="grid-edge">
              <div class="grid-box">
                <person-work-days data-person="person" ip-highlight="workDays"></person-work-days>
                <person-social data-person="person" ip-highlight="socialSites"></person-social>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
<div class="grid-blok grid_12" ng-if-start="person.active && person.loginName !== 'communitymanager' && person.loginName !== 'facilitairezaken' && person.loginName !== 'personeelenorganisatie' && person.loginName !== 'servicedesk' && person.loginName !== 'internecommunicatie'">
  <div class="grid-element">
    <div class="grid-edge">
      <div class="grid-nesting">
        <div class="grid-blok grid_6 rol-social has-bgcolor has-elt-breakpoints" ip-highlight="aboutme">
          <div class="grid-element">
            <div class="grid-edge">
              <div class="grid-box">
                <person-data-field data-person="person" data-field="aboutMe"></person-data-field>
              </div>
            </div>
          </div>
        </div>
        <div class="grid-blok grid_6 rol-social has-bgcolor has-elt-breakpoints" ip-highlight="workExperience">
          <div class="grid-element">
            <div class="grid-edge">
              <div class="grid-box">
                <person-work-experience data-person="person"></person-work-experience>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
<div class="grid-blok grid_12">
  <div class="grid-element">
    <div class="grid-edge">
      <div class="grid-nesting">
        <person-group-list
          data-person="person"
          data-block-grid="6"
          data-block-class-names="rol-social has-bgcolor"
          data-card-grid="3"
          data-card-class-names="rol-social-white has-bgcolor"
          ip-highlight="groups"
        ></person-group-list>
        <div class="grid-blok grid_6 rol-social has-bgcolor has-elt-breakpoints" ip-highlight="skills">
          <div class="grid-element">
            <div class="grid-edge">
              <div class="grid-box">
                <person-skills data-person="person"></person-skills>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
<div class="grid-blok grid_12">
  <div class="grid-element">
    <div class="grid-edge">
      <div class="grid-nesting">
        <div class="grid-blok grid_6 rol-social has-bgcolor has-elt-breakpoints" ip-highlight="weblinks">
          <div class="grid-element">
            <div class="grid-edge">
              <div class="grid-box">
                <person-weblinks data-person="person"></person-weblinks>
              </div>
            </div>
          </div>
        </div>
        <div class="grid-blok grid_6 rol-social has-bgcolor has-elt-breakpoints" ip-highlight="blogs">
          <div class="grid-element">
            <div class="grid-edge">
              <div class="grid-box">
                <person-blog-list data-person="person"></person-blog-list>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
<div class="grid-blok grid_12" ng-if-end>
  <div class="grid-element">
    <div class="grid-edge">
      <div class="grid-nesting">
        <div class="grid-blok grid_12 rol-social has-bgcolor has-elt-breakpoints" ip-highlight="activities">
          <div class="grid-element">
            <div class="grid-edge">
              <div class="grid-title">
                <h2>{{:: 'person.activities.recentActivities' | ipRefText }}</h2>
              </div>
              <div class="grid-box">
                <ips-activities data-config="{ type: 'person' }"></ips-activities>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
