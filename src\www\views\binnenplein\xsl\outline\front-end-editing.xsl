<?xml version="1.0" encoding="utf-8"?>
<xsl:stylesheet xmlns="http://www.w3.org/1999/xhtml"
                xmlns:formatter="urn:formatter"
                xmlns:xhtml="http://www.w3.org/1999/xhtml"
                xmlns:xsl="http://www.w3.org/1999/XSL/Transform"
                extension-element-prefixes="formatter"
                version="1.0">

  <xsl:param name="EnvPrv" />

  <!-- Frontend-editing: alleen data-id (PagVld_, PagCls_, Itm_) doorlaten wanneer in preview-omgeving of voor blok foto-thumbnail-->
  <xsl:template match="xhtml:div[starts-with(@data-id, 'PagVld_') or starts-with(@data-id, 'PagCls_') or starts-with(@data-id, 'Itm_')]/@data-id" mode="page">
    <xsl:if test="$EnvPrv or ancestor::xhtml:div[formatter:HasClass(., 'foto-thumbnail')]">
      <xsl:copy />
    </xsl:if>
  </xsl:template>

</xsl:stylesheet>
