import { CommonModule, DatePipe } from '@angular/common';
import { NgModule, Provider } from '@angular/core';
import { RouterModule } from '@angular/router';

import { IonicModule } from '@ionic/angular';
import { IpPwaHeaderModule } from '@ip/pwa';
import { CoreModule } from '@ip/social-core';
import { NgxsModule } from '@ngxs/store';
import { VirtualScrollerModule } from 'ngx-virtual-scroller';
import { AnniversaryModule } from '../anniversary/anniversary.module';

import { CommentsModule } from '../comments/comments.module';
import { MetaModule } from '../meta/meta.module';
import { PageModule } from '../page/page.module';
import { PersonModule } from '../person/person.module';
import { SharedModule } from '../shared/shared.module';
import { ActivityService } from './activities/activity.service';
import { LinkComponent, PersonComponent, TextComponent } from './activities/components';
import { TimelineActivityComponent } from './components/timeline-activity/timeline-activity.component';
import { TimelineContentComponent } from './components/timeline-content/timeline-content.component';
import { TimelineEntryComponent } from './components/timeline-entry/timeline-entry.component';
import { TimelineNewEntriesToastComponent } from './components/timeline-new-entries-toast/timeline-new-entries-toast.component';
import { TimelineComponent } from './components/timeline/timeline.component';
import { TimelinePageLinkComponent } from './content-components/page-link/page-link.component';
import { TimelineTitleComponent } from './content-components/title/title.component';
import { TimelineArticleComponent } from './content/article/article.component';
import { ArticleFactory } from './content/article/article.factory';
import { TimelinePreviewBirthdayComponent } from './content/birthday-preview/birthday-preview.component';
import { BirthdayPreviewFactory } from './content/birthday-preview/birthday-preview.factory';
import { TimelineBirthdayComponent } from './content/birthday/birthday.component';
import { BirthdayFactory } from './content/birthday/birthday.factory';
import { TimelineBlogComponent } from './content/blog/blog.component';
import { BlogFactory } from './content/blog/blog.factory';
import { TimelineCommentComponent } from './content/comment/comment.component';
import { CommentFactory } from './content/comment/comment.factory';
import { TimelineEventComponent } from './content/event/event.component';
import { EventFactory } from './content/event/event.factory';
import { TimelineApiService } from './services/timeline-api.service';
import { TIMELINE_CONTENT_FACTORY, TIMELINE_CONTENT_MAP } from './services/timeline-content';
import { TimelineContentFactoryService } from './services/timeline-content-factory.service';
import { TimelineService } from './services/timeline.service';
import { TimelineState } from './state/timeline.state';

export function contentMap(type: string, factoryType: string): Provider {
  return {
    provide: TIMELINE_CONTENT_MAP,
    multi: true,
    useValue: {
      type,
      factoryType
    }
  };
}

@NgModule({
  imports: [
    CommonModule,
    IonicModule,
    IpPwaHeaderModule,
    RouterModule,
    SharedModule,
    NgxsModule.forFeature([TimelineState]),
    VirtualScrollerModule,
    CoreModule,
    MetaModule,
    CommentsModule,
    PageModule,
    PersonModule,
    AnniversaryModule
  ],
  declarations: [
    // Activity Components
    LinkComponent,
    PersonComponent,
    TextComponent,
    // Timeline Components
    TimelineActivityComponent,
    TimelineArticleComponent,
    TimelineArticleComponent,
    TimelineBirthdayComponent,
    TimelinePreviewBirthdayComponent,
    TimelineBlogComponent,
    TimelineCommentComponent,
    TimelineComponent,
    TimelineContentComponent,
    TimelineEntryComponent,
    TimelineEventComponent,
    TimelineNewEntriesToastComponent,
    // Content Components
    TimelinePageLinkComponent,
    TimelineTitleComponent,
  ],
  providers: [
    DatePipe,
    TimelineApiService,
    TimelineService,
    TimelineContentFactoryService,
    ActivityService,
    contentMap('blog', 'blog'),
    contentMap('iproxArticle', 'article'),
    contentMap('iproxEvent', 'event'),
    contentMap('comment', 'comment'),
    contentMap('birthday', 'birthday'),
    contentMap('birthdayPreview', 'birthday-preview'),

    {
      provide: TIMELINE_CONTENT_FACTORY,
      multi: true,
      useClass: ArticleFactory
    },
    {
      provide: TIMELINE_CONTENT_FACTORY,
      multi: true,
      useClass: BlogFactory
    },
    {
      provide: TIMELINE_CONTENT_FACTORY,
      multi: true,
      useClass: EventFactory
    },
    {
      provide: TIMELINE_CONTENT_FACTORY,
      multi: true,
      useClass: CommentFactory,
    },
    {
      provide: TIMELINE_CONTENT_FACTORY,
      multi: true,
      useClass: BirthdayFactory,
    },
    {
      provide: TIMELINE_CONTENT_FACTORY,
      multi: true,
      useClass: BirthdayPreviewFactory,
    },
  ]
})
export class TimelineModule {
}
