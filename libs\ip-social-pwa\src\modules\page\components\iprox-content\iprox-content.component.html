<ips-error-page *ngIf="responseError" [error]="responseError"></ips-error-page>

<ion-grid *ngIf="data && !responseError">
  <ion-row>
    <ion-col size="12">
      <h1>{{ data.content.page.title }}</h1>
    </ion-col>

    <ion-col size="12" *ngIf="data?.content?.page?.meta?.meta['afbeelding-voor-index']?.url">
      <img [ips-iprox-image-src]="data.content.page.meta.meta['afbeelding-voor-index'].url">
    </ion-col>
  </ion-row>

  <ion-row>
    <ion-col size="12">
      <ng-container
        *ngIf="data.pagetype === 'evenement' && iproxEvent"
        [ngTemplateOutlet]="eventTemplate"
        [ngTemplateOutletContext]="{ event: iproxEvent }"
      ></ng-container>
      <ng-container
        *ngIf="data.pagetype === 'artikel'"
        [ngTemplateOutlet]="article"
        [ngTemplateOutletContext]="{ article: data.content.page }"
      ></ng-container>
    </ion-col>

    <ion-col *ngIf="seeAlsoBlocks.length > 0" size="12">
      <ips-iprox-blocks [blocks]="seeAlsoBlocks"></ips-iprox-blocks>
    </ion-col>
  </ion-row>

  <ion-row *ngIf="metaConfig">
    <ion-col size="12">
      <ips-meta [config]="metaConfig"></ips-meta>
    </ion-col>
  </ion-row>
</ion-grid>

<ips-comments *ngIf="metaConfig && enableComments" [reference]="metaConfig.reference"></ips-comments>

<ips-visit-tracker *ngIf="metaConfig" [reference]="metaConfig.reference"></ips-visit-tracker>

<ng-template #article let-article='article'>
  <div
    *ngIf="article.artikel?.inhoud?.inleiding"
    class="ip-content ip-content-introduction"
    ips-iprox-content-html
    [html]="article.artikel.inhoud.inleiding | bypassSecurityHtml"
  ></div>
  <div
    *ngIf="article.artikel?.inhoud?.inhoud"
    class="ip-content"
    ips-iprox-content-html
    [html]="article.artikel.inhoud.inhoud | bypassSecurityHtml"
  ></div>
</ng-template>

<ng-template #eventTemplate let-event='event'>
  <ips-event [event]="event" [detailed]="true"></ips-event>
</ng-template>
