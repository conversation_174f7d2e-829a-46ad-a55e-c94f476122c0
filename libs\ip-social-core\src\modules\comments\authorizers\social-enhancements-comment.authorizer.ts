import { of } from 'rxjs';

import { IReference, User } from '../../core/models';
import { ICommentSettings } from '../comments.settings';
import { CommentAuthorizer } from '../models/authorizer.model';
import { IComment } from '../models/comment.model';
import { ICommentAuthorizerFactory } from '../services/comment-authorizer.service';

export class SocialEnhancementsCommentAuthorizerFactory implements ICommentAuthorizerFactory {
  entities = ['Artikel', 'SocialEnhancements'];

  constructor(private legacySocialEnhancementsAuthorizer: unknown) { }

  authorizer$ = (reference: IReference, settings: ICommentSettings, user: User) => {
    return of(new SocialEnhancementsCommentAuthorizer(user, reference, settings, this.legacySocialEnhancementsAuthorizer));
  };
}

export class SocialEnhancementsCommentAuthorizer extends CommentAuthorizer {
  // TODO nalopen iedere permission
  fn = {
    canView: (_comment: IComment | undefined): boolean => {
      return this.settings.allowCommentAsUser || this.legacySocialEnhancementsAuthorizer.canCreateComment(this.user, this.reference);
    },
    canCreate: (_comment: IComment | undefined): boolean => {
      return this.settings.allowCommentAsUser || this.legacySocialEnhancementsAuthorizer.canCreateComment(this.user, this.reference);
    },
    canEdit: (comment: IComment | undefined): boolean => {
      return this.legacySocialEnhancementsAuthorizer.canEditComment(this.user, comment, this.reference);
    },
    canRemove: (comment: IComment | undefined): boolean => {
      return this.legacySocialEnhancementsAuthorizer.canDeleteComment(this.user, comment, this.reference);
    },
    canAddAttachment: (_comment: IComment | undefined): boolean => {
      return true;
    },
    canRemoveAttachment: (_comment: IComment | undefined): boolean => {
      return true;
    },
  };

  constructor(
    private user: User,
    private reference: IReference,
    private settings: ICommentSettings,
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    private legacySocialEnhancementsAuthorizer: any,
  ) {
    super();
  }
}
