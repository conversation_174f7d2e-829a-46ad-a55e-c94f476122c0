<?xml version="1.0" encoding="utf-8"?>

<xsl:stylesheet xmlns:xsl="http://www.w3.org/1999/XSL/Transform" xmlns="http://www.w3.org/1999/xhtml" version="1.0">

  <xsl:import href="../../../baseline/xsl/mail/formulier_html.xsl" />
  <xsl:import href="all_html.xsl" />

  <xsl:template match="field[cluster/Nam = 'Editor']" mode="vrije_velden">
    <div class="label">
      <xsl:value-of select="cluster/veld[Nam = 'Label']/Wrd" />
    </div>
    <div class="waarde">
      <xsl:value-of select="value/text()" disable-output-escaping="yes" />
    </div>
    <xsl:apply-templates select="self::field" mode="mail_veld_plug" />
  </xsl:template>

</xsl:stylesheet>
