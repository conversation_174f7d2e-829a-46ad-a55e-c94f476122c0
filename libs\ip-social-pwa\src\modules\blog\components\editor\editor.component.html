<ion-header>
  <ion-toolbar>
    <ion-title>{{ 'blog.editorPageTitle' | transloco }}</ion-title>
    <ion-buttons slot="start">
      <ion-button
        *ngIf="(synchronizing$ | async) !== (null || false || undefined); else spinner"
        (click)="modalController.dismiss()"
      >
        <ion-icon name="arrow-back-outline"></ion-icon>
      </ion-button>
    </ion-buttons>
  </ion-toolbar>
</ion-header>

<ion-content [formGroup]="formGroup">
  <editor
    [init]="editorConfig"
    formControlName="workingBody"
  ></editor>
</ion-content>

<ng-template #spinner>
  <ion-spinner></ion-spinner>
</ng-template>
