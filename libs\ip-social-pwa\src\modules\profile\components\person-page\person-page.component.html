<ion-header class="ips-transparent">
  <ip-header [title]="'person.pageTitle' | transloco"></ip-header>
</ion-header>

<ion-content *ngIf="data$ | async as data;">
  <ion-grid *ngIf="user$ | async as user;">
    <ion-row class="ion-justify-content-center">
      <ion-col size="12" class="ion-text-center">
        <ips-person-header [person]="data.person">
          <!-- <ips-person-menu-btn
            *ngIf="data.isOwnProfile === true"
          ></ips-person-menu-btn> -->
          <!-- <ips-person-follow-btn
            *ngIf="data.isOwnProfile === false"
            [personId]="data.person.id"
            [user]="user"
          ></ips-person-follow-btn> -->
        </ips-person-header>

        <h1 class="ips-person-page-title">{{ data.person.fullName }}</h1>
        <span
          *ngFor="let jobfunction of data.person.jobFunctions"
          class="ips-person-page-subtitle"
        >
          {{ jobfunction.label }}
        </span>

        <ips-phone-numbers
          [phoneNumbers]="data.person.phoneNumbers"
        ></ips-phone-numbers>

        <ips-email
          [email]="data.person.email"
        ></ips-email>
        <ips-socials [socials]="data.person.social"></ips-socials>

        <ips-property-list property="locations" [properties]="data.person.locations"></ips-property-list>
        <ips-property-list property="propertyList1" [properties]="data.person.propertyList1"></ips-property-list>
        <ips-property-list property="propertyList2" [properties]="data.person.propertyList2"></ips-property-list>

        <ips-person-workdays [workdays]="data.person.workDays"></ips-person-workdays>

        <ion-text *ngIf="data.isOwnProfile">{{ 'persons.profile.editInfo' | transloco }}</ion-text>

      </ion-col>
    </ion-row>

    <ion-row class="ion-justify-content-center" ipsContentVisible>
      <ion-col size="12" class="ion-text-center">
        <ips-blog-list [userId]="data.person.id" route="./blogs/"></ips-blog-list>
      </ion-col>
    </ion-row>

  </ion-grid>

  <ips-person-visit-tracker *ngIf="!data.isOwnProfile" [id]="data.person.id"></ips-person-visit-tracker>
</ion-content>
