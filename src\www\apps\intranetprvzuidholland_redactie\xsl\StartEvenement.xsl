<?xml version="1.0" encoding="utf-8"?>
<xsl:stylesheet xmlns:xsl="http://www.w3.org/1999/XSL/Transform" version="1.0" xmlns:resources="urn:resources" extension-element-prefixes="resources">

  <xsl:import href="include/StartPage.xsl"/>

  <xsl:param name="PublishEvenement.done"/>

  <!-- abstract variables -->
  <xsl:variable name="done" select="$PublishEvenement.done" />
  <xsl:variable name="pagetype">evenement</xsl:variable>

  <xsl:template name="startpage-velden">
    <xsl:variable name="inhoud-veld" select="/data/clusterdefinition[Nam='Inhoud']/veld[site]" />
    <xsl:apply-templates select="$inhoud-veld[Nam = 'Startdatum']" mode="startpage-veld" />
    <xsl:apply-templates select="$inhoud-veld[Nam = 'Starttijd']" mode="startpage-veld" />
    <xsl:apply-templates select="$inhoud-veld[Nam = 'Eindtijd']" mode="startpage-veld" />
    <xsl:apply-templates select="$inhoud-veld[Nam = 'Locatie']" mode="startpage-veld" />
    <xsl:apply-templates select="$inhoud-veld[Nam = 'Doelgroep']" mode="startpage-veld" />
    <xsl:apply-templates select="$inhoud-veld[Nam = 'Omschrijving']" mode="startpage-veld" />
    <xsl:apply-templates select="$inhoud-veld[Nam = 'Meer informatie']" mode="startpage-veld" />
    <xsl:apply-templates select="$inhoud-veld[Nam = 'Aanmelden']" mode="startpage-veld" />
  </xsl:template>

</xsl:stylesheet>