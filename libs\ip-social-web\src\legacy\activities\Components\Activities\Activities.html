<ips-activity ng-repeat="activity in ActivitiesCtrl.activities" data-activity="activity"></ips-activity>

<div class="ips-activities-controls ips-buttons">
  <button ng-if="ActivitiesCtrl.hasMoreResults" type="button" class="btn ips-button ips-primary" ng-click="ActivitiesCtrl.loadActivities()" title="{{:: 'moreResults' | ipRefText }}">
    <span>{{:: 'moreResults' | ipRefText }}</span>
  </button>
  <a ng-if="ActivitiesCtrl.settings.linkToPage" class="btn ips-button ips-primary" ng-href="{{ ActivitiesCtrl.settings.linkToPage }}" title="{{:: 'activities.showMore' | ipRefText }}">
    <span>{{:: 'activities.showMore' | ipRefText }}</span>
  </a>
</div>

<div style="position: absolute; top: -10px; left: -320px; height: 400px; overflow: scroll;background-color: #eee" ng-if="ActivitiesCtrl.settings.type === 'debug'">
  <div ng-repeat="(action, enabled) in ActivitiesCtrl.actions">
    <label>
      <input type="checkbox" ng-model="ActivitiesCtrl.actions[action]" ng-click="ActivitiesCtrl.updateList()"/> {{action}}
    </label>
  </div>
</div>
