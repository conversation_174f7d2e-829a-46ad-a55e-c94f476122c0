import { Component, EventEmitter, Input, Output, ViewChild } from '@angular/core';

import { Alert<PERSON>ontroller, IonFab } from '@ionic/angular';
import { TranslocoService } from '@ngneat/transloco';

@Component({
  selector: 'ips-blog-fab',
  templateUrl: './blog-fab.component.html',
  styleUrls: ['./blog-fab.component.scss'],
})
export class BlogFabComponent {
  @Input()
  activated!: boolean;

  @Input()
  canPublish!: boolean;

  @Input()
  synchronizing!: boolean | null;

  @Output()
  toggle = new EventEmitter();

  @Output()
  publish = new EventEmitter();

  @Output()
  remove = new EventEmitter();

  @ViewChild(IonFab)
  ionFab!: IonFab;

  constructor(
    private alertController: AlertController,
    private transloco: TranslocoService,
  ) { }

  onToggle() {
    this.toggle.emit();
  }

  async onPublish(event: MouseEvent) {
    event.preventDefault();
    event.stopPropagation();

    if (this.canPublish) {
      await this.alertConfirm('blog.publishAlert', () => this.publish.emit());
    }
  }

  async onRemove(event: MouseEvent) {
    event.preventDefault();
    event.stopPropagation();

    await this.alertConfirm('blog.removeAlert', () => this.remove.emit());
  }

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  async alertConfirm(context: string, handler: ((value: any) => any) ) {
    const alert = await this.alertController.create({
      header: this.transloco.translate(`${context}.title`),
      message: this.transloco.translate(`${context}.message`),
      buttons: [
        {
          cssClass: 'ips-alert-primary',
          text: this.transloco.translate(`${context}.confirmBtn`),
          handler
        },
        {
          text: this.transloco.translate(`${context}.cancelBtn`),
          role: 'cancel',
        },
      ]
    });

    await alert.present();
  }

  doNothing(event: MouseEvent) {
    event.preventDefault();
    event.stopPropagation();
  }
}
