<p>{{ 'timeline.birthdayPreview.period' | transloco : { startDay: startDayString, endDay: endDayString } }}</p>

<p *ngIf="anniversaryPeriod.anniversaries.length === 0">{{ 'timeline.birthdayPreview.noBirthdays' | transloco }}</p>

<div *ngFor="let day of anniversaryPeriod.anniversaries">
  <span class="ips-weekday">{{ day.day }}</span>
  <div class="ips-person" *ngFor="let person of day.persons">
    <ips-person [id]="person.id"></ips-person>
  </div>
</div>
