import { ChangeDetectionStrategy, Component, Input, OnChanges } from '@angular/core';

import { DayAbbreviation, DayPart, PersonSettings, Workdays } from '@ip/social-core';

@Component({
  selector: 'ips-person-workdays',
  templateUrl: './workdays.component.html',
  styleUrls: ['./workdays.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class WorkdaysComponent implements OnChanges {
  @Input()
  workdays!: Workdays;

  workdayBoxes!: IWorkdayBox[];

  constructor(private personSettings: PersonSettings) { }

  ngOnChanges() {
    this.workdayBoxes = this.parseWorkdays(this.workdays);
  }

  private parseWorkdays(workdays: Workdays) {
    const { weekdays, dayParts } = this.personSettings.workdays;

    return weekdays.map(day => ({
      label: day,
      parts: dayParts.map(part => ({
        label: `${day}-${part}`,
        active: this.isActive(day, part, workdays)
      }))
    }));
  }

  private isActive(day: DayAbbreviation, part: DayPart, workdays: Workdays): boolean {
    return workdays.includes(`${day}-${part}` as `${DayAbbreviation}-${DayPart}`) || workdays.includes(day);
  }
}

interface IWorkdayBox {
  label: string;
  parts: Array<{
    label: string;
    active: boolean;
  }>;
}
