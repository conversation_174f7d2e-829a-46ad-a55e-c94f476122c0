import { Injectable } from '@angular/core';

import { Store } from '@ngxs/store';
import { Observable } from 'rxjs';

import { Timeline } from '../state/timeline.actions';
import { TimelineState } from '../state/timeline.state';

@Injectable()
export class TimelineService {
  constructor(private store: Store) { }

  refresh(): Observable<void> {
    return this.store.dispatch(new Timeline.Refresh());
  }

  loadData(): void {
    const canLoadData = this.store.selectSnapshot(TimelineState.canLoadMore);
    if (!canLoadData) {
      return;
    }

    this.store.dispatch(new Timeline.LoadMore());
  }

  dismissToast(): void {
    this.store.dispatch(new Timeline.DismissToast());
  }

  setProp(entryId: string, props: { [key: string]: unknown }): void {
    this.store.dispatch(new Timeline.SetProps({ entryId, props }));
  }
}
