import { TimelineApi } from '../models/internal/api.models';
import { LinkComponent, PersonComponent, TextComponent } from './components';

export type ActivityConstructorFn = (entry: TimelineApi.IEntry) => ActivityParts;

export type ActivityPart = Person | Text | Link;

export type ActivityParts = Array<ActivityPart>;

export class Person {
  public component = PersonComponent;

  constructor(public data: string) { }
}

export class Text {
  public component = TextComponent;

  constructor(public data: string) { }
}

export class Link {
  public component = LinkComponent;

  constructor(public data: { text: string; url: string; }) { }
}
