import { Inject, Injectable, ViewContainerRef } from '@angular/core';

import { TimelineContentFactory } from '../content/content-factory';
import { ITimelineEntry } from '../models';
import { ITimelineContentMap, TIMELINE_CONTENT_FACTORY, TIMELINE_CONTENT_MAP } from './timeline-content';

@Injectable()
export class TimelineContentFactoryService {
  private factories = new Map<string, TimelineContentFactory>();

  constructor(@Inject(TIMELINE_CONTENT_MAP) providedMaps: ITimelineContentMap[],
    @Inject(TIMELINE_CONTENT_FACTORY) providedFactories: TimelineContentFactory[],
  ) {
    providedMaps.forEach(map => {
      const factory = providedFactories.find(f => f.type === map.factoryType);
      if (factory) {
        this.factories.set(map.type, factory);
      }
    });
  }

  createComponent(entry: ITimelineEntry, outlet: ViewContainerRef): void {
    const content = entry.content;
    const factory = this.factories.get(content.type);

    factory?.create(outlet, entry, content.data);
  }
}
