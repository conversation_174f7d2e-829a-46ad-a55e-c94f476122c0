/////////////////////////////////
// Letterbalk
/////////////////////////////////

.type-letterbalk .letter a {
  color: $kopkleur;
}

/////////////////////////////////
// Pager
/////////////////////////////////

.type-pager {
  .pager_step {
    border-radius: $pzh-pager-border-radius;
    position: relative;
  }

  .pager_nav,
  .pager_step {
    @include button-focus();

    &:focus {
      margin: 0 0 4px -1px;

      .visuallyhidden {
        margin: 0;
      }
    }
  }
}

/////////////////////////////////
// Caroussel
/////////////////////////////////
.type-fotoalbum,
.type-carrousel {
  .slider-wrapper,
  .slick-slider-wrapper {
    .slick-dots {
      position: relative;

      li button::before {
        content: "\25AA";
        font-size: 30px;
        line-height: 22px;
      }
    }
  }
}
