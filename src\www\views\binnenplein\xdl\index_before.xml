<?xml version="1.0" encoding="utf-8"?>

<node name="index_before">
  <node name="current_alias" param="Aka">
    <field name="'$$Aka$$'" alias="Aka" type="attribute" />
  </node>
  <node name="setter" alias="lucene" collapse="hide">
    <if xpath="ancestor::content[1]/page/cluster[Nam = 'Selectie']//veld[Nam = 'Homepage nieuws']/Wrd/text()" expr="1"/>
    <field name="'homepagenews'" alias="Zoe_Extra_Fields" type="set" />
    <field name="'1'" alias="Zoe_homepagenews" type="set" />
  </node>
  <node name="setter" alias="operator" collapse="hide">
    <field name="'AND'" alias="Zoe_Default_Operator" type="set" />
  </node>
</node>
