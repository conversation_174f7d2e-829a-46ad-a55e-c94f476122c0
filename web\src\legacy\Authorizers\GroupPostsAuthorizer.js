(function (angular) {
  'use strict';

  angular.module('Intranet').factory('GroupPostsAuthorizer', ['GroupService', function (GroupService) {
    return {
      canCreatePost: function (user, entity) {
        var group = GroupService.getGroup(entity.id);
        return group.public || group.userIsMemberOrOwner(user.id) || user.isCommunityManager();
      },
      canEditPost: function (user, entity, post) {
        var group = GroupService.getGroup(entity.id);
        return user.id === post.userId || group.userIsMemberOrOwner(user.id) || user.isCommunityManager();
      },
      canMovePost: function (user, entity, post) {
        return false;
      },
      canDeletePost: function (user, entity, post) {
        var group = GroupService.getGroup(entity.id);
        return user.id === post.userId || group.userIsMemberOrOwner(user.id) || user.isCommunityManager();
      }
    };
  }]);
})(angular);
