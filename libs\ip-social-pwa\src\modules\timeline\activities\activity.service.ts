import { Injectable } from '@angular/core';

import { TimelineApi } from '../models/internal/api.models';
import { ActivityConstructorFn, ActivityParts, Person, Text } from './models';

@Injectable()
export class ActivityService {
  private activities = new Map<string, ActivityConstructorFn>();

  // TODO: Provide these mappings so they can be extended.
  constructor() {
    this.activities.set('PublishBlog', (entry) => [new Person(entry.userId), new Text(' heeft een blog gepubliceerd.')]);
    this.activities.set('CommentOnBlog', (entry) => [new Person(entry.userId), new Text(' heeft gereageerd op je blog.')]);
    this.activities.set('IproxPublishedArticle', (entry) => [new Person(entry.userId), new Text(' heeft een nieuwsartikel gepubliceerd.')]);
    this.activities.set('IproxUpdatedArticle', (entry) => [new Person(entry.userId), new Text(' heeft een nieuwsartikel aangepast.')]);
    this.activities.set('IproxPublishedEvent', (entry) => [new Person(entry.userId), new Text(' heeft een evenement gepubliceerd.')]);
    this.activities.set('IproxUpdatedEvent', (entry) => [new Person(entry.userId), new Text(' heeft een evenement aangepast.')]);
    this.activities.set('Birthday', (entry) => [new Person(entry.userId), new Text(' is vandaag jarig.')]);
    this.activities.set('BirthdayPreview', () => [new Text('Verjaardagen van deze week.')]);
  }

  create(entry: TimelineApi.IEntry): ActivityParts | undefined {
    const fn = this.activities.get(entry.action);

    return fn ? fn(entry) : undefined;
  }
}
