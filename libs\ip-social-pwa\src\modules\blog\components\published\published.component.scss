@import "../../../../scss/variables";
@import "../../../../scss/mixins";

:host {
  display: block;
  position: relative;

  .ips-blog-wip {
    @include font-size(-1);

    width: 100%;

    ion-icon {
      @include font-size(2);

      color: var(--ion-color-warning-shade);
      margin-left: $ips-unit * .5;
      vertical-align: middle;
    }
  }
}

:host-context(.ips-edit-mode) {
  ion-item {
    @include cancel-grid-padding();
  }

  .ips-published-wrapper {
    padding-left: calc(var(--ion-grid-padding) + var(--ion-grid-column-padding));
    padding-right: calc(var(--ion-grid-padding) + var(--ion-grid-column-padding));
    padding-top: calc(var(--ion-grid-padding) + var(--ion-grid-column-padding));
  }
}

.ips-published-wrapper {
  display: flex;
  flex-wrap: wrap;
  margin-bottom: $ips-unit * 2;

  .ips-created-date,
  .ips-published-date {
    @include font-size(-1);

    display: flex;
    flex: 0 0 100%;

    div + .ips-date {
      margin-left: $ips-unit;
    }
  }
}

.ips-synchronize-blog {
  padding: 0 calc(var(--ion-grid-padding) + var(--ion-grid-column-padding)) calc(var(--ion-grid-padding) + var(--ion-grid-column-padding));

  > * {
    @include font-size(-1);

    vertical-align: middle;
  }

  ion-spinner {
    height: 16px;
    margin-left: 16px;
    width: 16px;
  }
}
