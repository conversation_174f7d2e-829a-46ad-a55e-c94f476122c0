@import "../../../../scss/variables";
@import "../../../../scss/mixins";

$ips-meta-default-background-color: #f5f5f9;
$ips-meta-default-color: #000;
$ips-meta-accent-color: var(--ion-color-primary);

:host {
  @include font-size(0);

  display: flex;
  height: $ips-meta-height;

  > * {
    align-items: center;
    // box-shadow: inset 0 -3px 0 -1px $ips-meta-accent-color;
    color: var(--ips-meta-color, #{$ips-meta-default-color});
    display: flex;
    height: 100%;
    min-width: $ips-unit * 6;
    padding-left: $ips-unit * 2;
    padding-right: $ips-unit * 2;
  }

  > .ips-user-liked {
    background-color: var(--ips-meta-background-color, #{$ips-meta-default-background-color});
  }
}
