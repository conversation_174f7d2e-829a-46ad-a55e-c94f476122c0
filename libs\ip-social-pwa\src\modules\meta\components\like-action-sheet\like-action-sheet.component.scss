@import "../../../../scss/mixins";

$ips-like-button-size: 64px;

.ips-like-action-modal {
  --height: #{$ips-like-button-size};

  .modal-wrapper {
    bottom: 0;
    position: absolute;
  }
}

.ips-like-segment > ion-segment-button {
  --color: rgba(var(--ion-text-color-rgb, 0, 0, 0), 0.8);

  @include font-size(1);

  min-height: $ips-like-button-size;
  min-width: $ips-like-button-size + 12px;
}

.ips-like-segment > .segment-button-disabled {
  opacity: 1;
}
