import { BehaviorSubject, Observable } from 'rxjs';

import { AuthStatus } from './auth-status';

export abstract class AuthenticationService {
  private readonly authState = new BehaviorSubject<AuthStatus>(AuthStatus.Initial);

  public auth$: Observable<AuthStatus> = this.authState.asObservable();

  public get auth(): AuthStatus {
    return this.authState.value;
  }

  public set auth(value: AuthStatus) {
    this.authState.next(value);
  }

  public isAuthenticated(): boolean {
    return this.auth === AuthStatus.Authenticated;
  }

  abstract login(): void;

  abstract logout(): void;
}
