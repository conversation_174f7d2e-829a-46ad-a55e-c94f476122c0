import { Injectable } from '@angular/core';

import { Action, Actions, ofActionDispatched, Selector, State, StateContext, StateToken } from '@ngxs/store';
import { append, patch, updateItem } from '@ngxs/store/operators';
import { EMPTY, interval } from 'rxjs';
import { filter, switchMap, takeUntil, tap } from 'rxjs/operators';

import { ITimelineEntry } from '../models';
import { TimelineApiService } from '../services/timeline-api.service';
import { Timeline } from './timeline.actions';

export const TIMELINE_STATE_TOKEN = new StateToken<ITimelineStateModel>('timeline');

export interface ITimelineStateModel {
  entries: ITimelineEntry[];
  fetching: boolean;
  polledEntries: ITimelineEntry[];
  oldestEntryDate?: Date;
  newestEntryDate?: Date;
  reachedEnd: boolean;
  dismissedToast: boolean;
}

@State({
  name: TIMELINE_STATE_TOKEN,
  defaults: {
    entries: [],
    fetching: false,
    polledEntries: [],
    reachedEnd: false,
    dismissedToast: false,
  }
})
@Injectable()
export class TimelineState {
  private POLL_INTERVAL = 15 * 1000; // 15 seconds

  constructor(private actions$: Actions, private timelineApi: TimelineApiService) { }

  @Selector()
  static entries(state: ITimelineStateModel) {
    return state.entries;
  }

  @Selector()
  static unseenEntries(state: ITimelineStateModel) {
    return state.dismissedToast ? [] : state.polledEntries;
  }

  @Selector()
  static fetching(state: ITimelineStateModel) {
    return state.fetching;
  }

  @Selector()
  static canLoadMore(state: ITimelineStateModel) {
    return !state.fetching && !state.reachedEnd;
  }

  @Action(Timeline.Refresh, { cancelUncompleted: true })
  refresh({ patchState, dispatch }: StateContext<ITimelineStateModel>) {
    patchState({ fetching: true });

    return this.timelineApi.get({ toDate: new Date() })
      .pipe(
        tap(entries => patchState({
          entries,
          oldestEntryDate: entries[entries.length - 1].date,
          newestEntryDate: entries[0].date,
          fetching: false,
          reachedEnd: false,
          polledEntries: [],
        })),
        tap(() => dispatch(Timeline.Poll))
      );
  }

  @Action(Timeline.LoadMore)
  loadMore({ patchState, setState, getState }: StateContext<ITimelineStateModel>) {
    patchState({ fetching: true });

    const oldestEntryDate = getState().oldestEntryDate;
    const toDate = oldestEntryDate ? new Date(oldestEntryDate.getTime() - 1) : new Date();

    return this.timelineApi.get({ toDate })
      .pipe(
        takeUntil(
          this.actions$.pipe(
            ofActionDispatched(Timeline.Refresh),
            tap(() => patchState({ fetching: false })),
          )
        ),
        tap(entries => entries.length > 0
          ? setState(
            patch({
              entries: append(entries),
              oldestEntryDate: entries[entries.length - 1].date,
              fetching: false,
            })
          )
          : patchState({ fetching: false, reachedEnd: true })
        )
      );
  }

  @Action(Timeline.Poll, { cancelUncompleted: true })
  poll({ getState, dispatch }: StateContext<ITimelineStateModel>) {
    const reset$ = this.actions$.pipe(ofActionDispatched(Timeline.Refresh));

    return interval(this.POLL_INTERVAL)
      .pipe(
        filter(() => document.visibilityState === 'visible'),
        switchMap(() => {
          const newestEntryDate = getState().newestEntryDate;

          if (!newestEntryDate) {
            return EMPTY;
          }

          return this.timelineApi.get({ toDate: new Date(), fromDate: new Date(newestEntryDate.getTime() + 1) })
            .pipe(
              tap(entries => dispatch(new Timeline.PollResult(entries)))
            );
        }),
        takeUntil(reset$),
      );
  }

  @Action(Timeline.PollResult)
  pollResult({ patchState }: StateContext<ITimelineStateModel>, action: Timeline.PollResult) {
    const entries = action.payload;

    if (entries.length > 0) {
      patchState({
        polledEntries: entries,
        newestEntryDate: entries[0].date,
        dismissedToast: false,
      });
    }
  }

  @Action(Timeline.DismissToast)
  dismissToast({ patchState }: StateContext<ITimelineStateModel>) {
    patchState({
      dismissedToast: true,
    });
  }

  @Action(Timeline.SetProps)
  setProps({ setState }: StateContext<ITimelineStateModel>, action: Timeline.SetProps) {
    setState(
      patch({
        entries: updateItem<ITimelineEntry>(
          entry => entry?.id === action.payload.entryId,
          entry => ({ ...entry, mutation: entry.mutation + 1, props: { ...entry.props, ...action.payload.props } })
        )
      })
    );
  }
}
