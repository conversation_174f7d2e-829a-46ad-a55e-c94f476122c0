/* eslint-disable @typescript-eslint/naming-convention */
/* eslint-disable max-len */

import { Injectable } from '@angular/core';

import { FileUploader, SocialHttpClient } from '@ip/social-core';

export type EditorConfig = Record<string, unknown>;

@Injectable()
export class TinymceService {
  private baseConfig: EditorConfig = {
    mobile: {
      toolbar: 'bold italic | undo | bullist numlist | ip-link ip-file image media | formatselect',
    },
    language: 'nl',
    branding: true,
    menubar: false,
    statusbar: true,
    elementpath: false,
    link_title: false,
    valid_elements: 'a[!href|target=_blank|class|title],h3,h4,strong,em,b,i,ul,ol,li,p,br,img[!src|alt|style|height|width],iframe[src|title|width|height|allowfullscreen|frameborder]',
    block_formats: 'Paragraph=p;Kop groot=h3;Ko<PERSON> klein=h4',
    toolbar: 'formatselect | bold italic | undo | bullist numlist | ip-link ip-file image media',
    plugins: [
      'link',
      'lists',
      'paste',
      'image',
      'media',
      'help',
      'ip-file'
    ],
    file_picker_types: 'image file',
    entity_encoding: 'numeric',
    image_description: true,
    image_dimensions: true,
    paste_data_images: true, // Todo: SR: Overwegen of we automatic_uploads op true zetten. Zie https://www.tiny.cloud/docs/configure/file-image-upload/ voor uitgebreide documentatie.
    media_alt_source: false,
    media_poster: false,
    media_dimensions: true,
    relative_urls: false,
    remove_script_host: false,
    target_list: false,
  };

  constructor(private socialApi: SocialHttpClient) { }

  createConfig(config: EditorSettings): EditorConfig {
    let uploadHandler = {};

    if (config.imageUploadHandler) {
      uploadHandler = {
        images_upload_handler: config.imageUploadHandler,
      };
    }

    if (config.file !== undefined || config.image !== undefined) {
      return {
        ...this.baseConfig,
        ...uploadHandler,
        file_picker_callback: this.createFilePickerCallback(config.id, config.file, config.image),
      };
    }

    return { ...this.baseConfig, ...uploadHandler };
  }

  createFilePickerCallback(id: string, fileUploader?: FileUploader, imageUploader?: FileUploader) {
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    return (callback: TinymceFileCallback, value: any, meta: any) => {
      if (meta.filetype === 'file' && fileUploader !== undefined) {
        fileUploader.open();

        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        fileUploader.onSuccess.subscribe((response: any) => callback(response.url, { text: response.fileName }));
      }

      if (meta.filetype === 'image' && imageUploader !== undefined) {
        imageUploader.open();

        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        imageUploader.onSuccess.subscribe((response: any) => callback(response.url, { text: response.fileName }));
      }
    };
  }
}

type TinymceFileCallback = (url: string, data?: { text: string }) => void;

interface EditorSettings {
  id: string;
  file?: FileUploader;
  image?: FileUploader;
  imageUploadHandler?: TinymceImageUploadHandler;
}

export type TinymceImageUploadHandler = (
  blobInfo: {
    filename: () => string;
    blob: () => Blob;
  },
  success: (location: string) => void,
  failure: (error: string) => void,
  progress: (progress: number) => void,
) => void;
