import { ComponentFactory, ComponentFactoryResolver, Type, ViewContainerRef } from '@angular/core';

import { IContentComponent, ITimelineEntry } from '../models';

export abstract class TimelineContentFactory {
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  abstract component: Type<any>;

  abstract type: string;

  constructor(private componentFactoryResolver: ComponentFactoryResolver) { }

  create(outlet: ViewContainerRef, entry: ITimelineEntry, data?: Record<string, unknown>): void {
    const componentFactory = this.resolveComponentFactory();
    const component = outlet.createComponent(componentFactory);
    component.instance.entry = entry;
    component.instance.data = data;
  }

  resolveComponentFactory(): ComponentFactory<IContentComponent> {
    return this.componentFactoryResolver.resolveComponentFactory(this.component);
  }
}
