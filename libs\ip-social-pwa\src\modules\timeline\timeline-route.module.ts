import { NgModule } from '@angular/core';
import { RouterModule } from '@angular/router';

import { PageComponent } from '../page/components/page/page.component';
import { PageResolver } from '../page/services/page.resolver';
import { PERSON_ROUTE } from '../person/person-route';
import { TimelineComponent } from './components/timeline/timeline.component';
import { TimelineModule } from './timeline.module';

@NgModule({
  imports: [
    TimelineModule,
    RouterModule.forChild([
      {
        path: '',
        component: TimelineComponent,
        pathMatch: 'full'
      },
      {
        path: 'person/:id',
        loadChildren: () => import('../profile/profile-route.module').then(m => m.ProfileRouteModule),
      },
      {
        path: ':collection/:id',
        component: PageComponent,
        resolve: {
          reference: PageResolver
        }
      },
    ]),
  ],
  providers: [
    {
      provide: PERSON_ROUTE,
      useValue: '/tijdlijn/person'
    },
  ]
})
export class TimelineRouteModule { }
