<?xml version="1.0" encoding="utf-8"?>
<xsl:stylesheet
  xmlns:xsl="http://www.w3.org/1999/XSL/Transform"
  xmlns:xhtml="http://www.w3.org/1999/xhtml"
  xmlns:formatter="urn:formatter"
  xmlns="http://www.w3.org/1999/xhtml"
  extension-element-prefixes="formatter"
  version="1.0"
>

  <xsl:template match="xhtml:div[contains(@class, 'z-header')]//xhtml:div[contains(@class, 'type-zoeken')]//xhtml:input[@name = 'zoeken_term']" mode="page">
    <xsl:element name="{local-name()}">
      <xsl:apply-templates select="@*" mode="page" />
      <xsl:attribute name="name">
        <xsl:text>query</xsl:text>
      </xsl:attribute>
      <xsl:apply-templates select="node()" mode="page" />
    </xsl:element>
  </xsl:template>

</xsl:stylesheet>
