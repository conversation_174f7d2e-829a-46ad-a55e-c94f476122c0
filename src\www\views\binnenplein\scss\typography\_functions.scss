// sass-lint:disable no-warn

@function font-size-value($step, $small: false) {
  @if $small {
    @if not map-has-key($font-sizes-small, $step) {
      @warn "Fontsize `#{$step}` not found in $font-sizes-small map.";
    }

    @return map-get($font-sizes-small, $step);
  }
  @else {
    @if not map-has-key($font-sizes, $step) {
      @warn "Fontsize `#{$step}` not found in $font-sizes map.";
    }

    @return map-get($font-sizes, $step);
  }
}

@function line-height-value($step, $small: false) {
  @if $small {
    @if not map-has-key($line-heights-small, $step) {
      @warn "line-height `#{$step}` not found in $line-heights-small map.";
    }

    @return map-get($line-heights-small, $step);
  }
  @else {
    @if not map-has-key($line-heights, $step) {
      @warn "line-height `#{$step}` not found in $line-heights map.";
    }

    @return map-get($line-heights, $step);
  }
}
