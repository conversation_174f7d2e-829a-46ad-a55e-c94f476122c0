import { Injectable } from '@angular/core';

import { BehaviorSubject } from 'rxjs';
import { filter, map, take } from 'rxjs/operators';

import { once } from '../../../utilities/once-operator';
import { SocialHttpClient } from '../../core/services/api-client';

@Injectable()
export class ResourceTokenService {
  private expireThreshold = 20 * 1000; // 20 seconds;

  private fetching = false;

  private tokenSubject = new BehaviorSubject<ResourceToken | undefined>(undefined);

  public token$ = this.tokenSubject
    .asObservable()
    .pipe(
      once(data => this.fetchTokenIfInvalid(data)),
      filter((data): data is ResourceToken => this.isValid(data)),
      map(data => data.accessToken),
      take(1)
    );

  public get currentToken(): string | undefined {
    return this.tokenSubject.value?.accessToken;
  }

  constructor(private socialApi: SocialHttpClient) { }

  fetchTokenIfInvalid(data: ResourceToken | undefined) {
    if (!this.isValid(data) && this.fetching === false) {
      this.fetchToken();
    }
  }

  private fetchToken() {
    this.fetching = true;
    this.socialApi.get<ITokenResponse>('token')
      .subscribe(data => {
        this.fetching = false;

        this.tokenSubject.next({
          accessToken: data.accessToken,
          expires: new Date(data.expires).getTime(),
          issued: new Date(data.issued).getTime(),
        });
      });
  }

  private isValid(data: ResourceToken | undefined): boolean {
    if (data === undefined) {
      return false;
    }

    const now = new Date().getTime();
    return now < (data.expires - this.expireThreshold);
  }
}

type ResourceToken = {
  accessToken: string;
  issued: number;
  expires: number;
};

interface ITokenResponse {
  accessToken: string;
  issued: string;
  expires: string;
}
