import { HTTP_INTERCEPTORS } from '@angular/common/http';
import { Provider } from '@angular/core';

import { AuthenticationConfig } from '../base/authentication.config';
import { AuthenticationService } from '../base/authentication.service';
import { ActiveDirectoryInterceptor } from './active-directory-authentication.interceptor';
import { ActiveDirectoryAuthenticationService } from './active-directory-authentication.service';

export class ActiveDirectoryAuthenticationConfig extends AuthenticationConfig {
  constructor() {
    super();
  }

  getProviders(): Provider[] {
    return [
      {
        provide: HTTP_INTERCEPTORS,
        useClass: ActiveDirectoryInterceptor,
        multi: true,
      },
      {
        provide: AuthenticationService,
        useClass: ActiveDirectoryAuthenticationService,
      },
    ];
  }
}
