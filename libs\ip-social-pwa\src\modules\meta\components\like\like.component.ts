import { Component, HostBinding, HostListener, Input } from '@angular/core';
import { NavigationStart, Router } from '@angular/router';

import { ILikeCollection, ILikeOption, IMetaConfig, MetaSettings } from '@ip/social-core';
import { from } from 'rxjs';
import { filter, take, takeUntil } from 'rxjs/operators';

import { LikeActionSheetService } from '../../services/like-action-sheet.service';

@Component({
  selector: 'ips-like',
  templateUrl: './like.component.html',
})
export class LikeComponent {
  @HostBinding('class.ips-user-liked')
  get userHasLiked() {
    return this.likes.userLikes.length > 0;
  }

  @Input()
  metaConfig!: IMetaConfig;

  @Input()
  likes!: ILikeCollection;

  options: ILikeOption[];

  constructor(
    private likeActionSheet: LikeActionSheetService,
    private router: Router,
    settings: MetaSettings,
  ) {
    this.options = settings.likeOptions;
  }

  @HostListener('click')
  onClick() {
    this.likeActionSheet.open(this.metaConfig)
      .then(modal => this.dismissOnRouteChange(modal));
  }

  private dismissOnRouteChange(modal: HTMLIonModalElement) {
    this.router.events
      .pipe(
        filter(event => event instanceof NavigationStart),
        takeUntil(from(modal.onWillDismiss())),
        take(1)
      )
      .subscribe(() => modal.dismiss());
  }
}
