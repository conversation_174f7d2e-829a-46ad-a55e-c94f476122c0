﻿namespace IntranetProvincieZuidHolland.Iprox.Handlers {
  using System;
  using InfoProjects.Dxe.Linq;
  using InfoProjects.Dxe.Process;
  using InfoProjects.Dxe.Process.Handler;
  using InfoProjects.Dxe.Util;
  using InfoProjects.Iprox.Model;
  using IntranetProvincieZuidHolland.Iprox.Model;

  /// <summary>RePublishArtikel Handler</summary>
  public class RePublishArtikelHandler : IContextProcessHandler {
    /// <summary>RePublishArtikelHandler Handler</summary>
    /// <param name="unit">Process unit</param>
    /// <param name="context">Process context</param>
    /// <returns>Returns result</returns>
    public ResultProps Process(ProcessUnit unit, ProcessContext context) {
      string itmIdt = unit[Reserved.ID];
      if (!String.IsNullOrEmpty(itmIdt)) {
        Logger.Debug("Update item {0}", itmIdt);
        this.UpdateItem(unit, context, itmIdt);
      }

      return new ResultProps();
    }

    /// <summary>
    /// Update een item
    /// </summary>
    /// <param name="unit">ProcessUnit unit</param>
    /// <param name="context">ProcessContext context</param>
    /// <param name="itmIdt">string itmIdt</param>
    private void UpdateItem(ProcessUnit unit, ProcessContext context, string itmIdt) {
      using (var cms = new IproxCms()) {
        var artikel = cms.GetItem<Artikel>(itmIdt.To<int>(), false, ContentMode.Source);
        artikel.MarkPublished();

        this.UpdateArtikel(unit, context, artikel);

        cms.SubmitChanges(context);
      }
    }

    /// <summary>
    /// Bewerk bestaand artikel
    /// </summary>
    /// <param name="unit">ProcessUnit unit</param>
    /// <param name="context">ProcessContext context</param>
    /// <param name="artikel">Artikel model item</param>
    private void UpdateArtikel(ProcessUnit unit, ProcessContext context, Item<Artikel> artikel) {
      // Pagina titel en label
      if (unit.IsSet("Titel.Wrd")) {
        artikel.Title = unit["Titel.Wrd"];
        artikel.Page.Title = unit["Titel.Wrd"];
      }

      SharedRepublish.SetValueIfSupplied(unit, artikel.Page.Meta.Samenvatting, "Samenvatting");
      SharedRepublish.SetValueIfSupplied(unit, artikel.Page.Meta.AfbeeldingVoorIndex, "Afbeelding_voor_index");
      SharedRepublish.SetValueIfSupplied(unit, artikel.Page.Inleiding, "Inleiding");
      SharedRepublish.SetValueIfSupplied(unit, artikel.Page.Inhoud, "Inhoud");
    }
  }
}
