import { Component, Input } from '@angular/core';

import { IPhoneNumber } from '@ip/social-core';

@Component({
  selector: 'ips-phone-numbers',
  templateUrl: './phone-numbers.component.html',
  styleUrls: ['./phone-numbers.component.scss']
})
export class PhoneNumbersComponent {
  @Input()
  set phoneNumbers(value: IPhoneNumber[]) {
    this.filledPhoneNumbers = value.filter(s => !!s.number);
  }

  filledPhoneNumbers!: IPhoneNumber[];
}
