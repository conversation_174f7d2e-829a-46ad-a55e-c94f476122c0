﻿namespace IntranetProvincieZuidHolland.Iprox.Handlers {
  using InfoProjects.Dxe.Process;
  using InfoProjects.Iprox.Model;

  /// <summary>SharedRepublish methods</summary>
  public class SharedRepublish {
    /// <summary>
    /// Ony set value if specified
    /// </summary>
    /// <param name="unit">ProcessUnit unit</param>
    /// <param name="htmlField">Field in model</param>
    /// <param name="fieldName">The field name</param>
    internal static void SetValueIfSupplied(ProcessUnit unit, InfoProjects.Iprox.Model.Fields.HtmlField htmlField, string fieldName) {
      if (unit.IsSupplied(fieldName + ".Txt") && unit.IsSet(fieldName + ".SitIdt")) {
        // .SitIdt is there only when htmlField is dirty
        unit.SetValueOf(htmlField, fieldName);
      }
    }

    /// <summary>
    /// Ony set value if specified
    /// </summary>
    /// <param name="unit">ProcessUnit unit</param>
    /// <param name="dateField">Field in model</param>
    /// <param name="fieldName">The field name</param>
    internal static void SetValueIfSupplied(ProcessUnit unit, InfoProjects.Iprox.Model.Fields.DateField dateField, string fieldName) {
      if (unit.IsSupplied(fieldName + ".Dtm")) {
        unit.SetValueOf(dateField, fieldName);
      }
    }

    /// <summary>
    /// Ony set value if specified
    /// </summary>
    /// <param name="unit">ProcessUnit unit</param>
    /// <param name="timeField">Field in model</param>
    /// <param name="fieldName">The field name</param>
    internal static void SetValueIfSupplied(ProcessUnit unit, InfoProjects.Iprox.Model.Fields.TimeField timeField, string fieldName) {
      if (unit.IsSupplied(fieldName + ".Tyd")) {
        unit.SetValueOf(timeField, fieldName);
      }
    }

    /// <summary>
    /// Ony set value if specified
    /// </summary>
    /// <param name="unit">ProcessUnit unit</param>
    /// <param name="plainField">Field in model</param>
    /// <param name="fieldName">The field name</param>
    internal static void SetValueIfSupplied(ProcessUnit unit, InfoProjects.Iprox.Model.Fields.PlainField plainField, string fieldName) {
      if (unit.IsSupplied(fieldName + ".Wrd")) {
        unit.SetValueOf(plainField, fieldName);
      }
    }

    /// <summary>
    /// Ony set value if specified
    /// </summary>
    /// <param name="unit">ProcessUnit unit</param>
    /// <param name="imageField">Field in model</param>
    /// <param name="fieldName">The field name</param>
    internal static void SetValueIfSupplied(ProcessUnit unit, InfoProjects.Iprox.Model.Fields.ImageField imageField, string fieldName) {
      if (unit.IsSupplied(fieldName + ".Txt")) {
        unit.SetValueOf(imageField, fieldName);
      }
    }

    /// <summary>
    /// Ony set value if specified
    /// </summary>
    /// <param name="unit">ProcessUnit unit</param>
    /// <param name="booleanField">Field in model</param>
    /// <param name="fieldName">The field name</param>
    internal static void SetValueIfSupplied(ProcessUnit unit, InfoProjects.Iprox.Model.Fields.BooleanField booleanField, string fieldName) {
      if (unit.IsSupplied(fieldName + ".Wrd")) {
        unit.SetValueOf(booleanField, fieldName);
      }
    }
    
    /// <summary>
    /// Set value if supplied, or forced to '0' if NOT supplied
    /// </summary>
    /// <param name="unit">ProcessUnit unit</param>
    /// <param name="booleanField">Field in model</param>
    /// <param name="fieldName">The field name</param>
    internal static void SetBooleanValue(ProcessUnit unit, InfoProjects.Iprox.Model.Fields.BooleanField booleanField, string fieldName) {
      if (unit.IsSupplied(fieldName + ".Wrd")) {
        unit.SetValueOf(booleanField, fieldName);
      }
      else {
        booleanField.Value = false;
      }
    }
  }
}
