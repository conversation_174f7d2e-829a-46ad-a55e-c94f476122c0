<ion-header>
  <ion-toolbar>
    <ion-title>{{ title | transloco }}</ion-title>
    <ion-buttons slot="end">
      <ion-button (click)="modalController.dismiss()">
        <ion-icon name="close"></ion-icon>
      </ion-button>
    </ion-buttons>
  </ion-toolbar>
</ion-header>

<ion-content>

  <ion-grid>
    <ion-row>
      <ion-col size="12">
        <span *ngIf="formControl.value.length === 0">{{ 'personSelector.noSelection' | transloco }}</span>

        <ips-person-chip
          *ngFor="let personId of formControl.value"
          [id]="personId"
          icon="close-circle"
          (click)="removePerson(personId)"
        ></ips-person-chip>
      </ion-col>
    </ion-row>
  </ion-grid>

  <ng-container *ngIf="state$ | async as state;">
    <ion-searchbar
      [placeholder]="'persons.search.placeholder' | transloco"
      (ionChange)="onSearchChange($any($event))"
    ></ion-searchbar>

    <ion-list>
      <ips-person-item
        *ngFor="let person of state.persons"
        [id]="person.id"
        [enableNavigation]="false"
        (click)="personSelected(person.id)"
      ></ips-person-item>
    </ion-list>

    <ion-infinite-scroll [disabled]="!state.hasMore" (ionInfinite)="searchService.loadMore(10)">
      <ion-infinite-scroll-content loadingSpinner="circular" [loadingText]="'persons.loading' | transloco">
      </ion-infinite-scroll-content>
    </ion-infinite-scroll>
  </ng-container>
</ion-content>
