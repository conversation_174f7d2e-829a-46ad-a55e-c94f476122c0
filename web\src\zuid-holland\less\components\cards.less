@ips-person-avatar-radius: 0;

@ips-group-card-avatar-size: 64px;
@ips-group-card-image-hover: false;
@ips-group-card-image-border-radius: 0;

@ips-person-card-avatar-size: 64px;
@ips-person-card-image-hover: false;

@persons-block-bgcolor: @achtergrondkleur-5;
@persons-block-grid-blok-padding-bottom: 0;

@ipx-person-index-alias: 0;
@ipx-group-index-alias: 0;

@pzh-meta-width: ~"calc(100% - 58px)"; // SR: Maat is niet af te leiden.
@pzh-meta-width-with-action: ~"calc(100% - 105px)"; // SR: Maat is niet af te leiden.

.grid-blok {
  ips-person-card,
  ips-group-card {
    .ips-actions .btn.ips-button {
      .follow-btn();

      border-radius: 50%;
    }
  }

  ips-person-card {
    .ips-name {
      font-size: 1.25rem;
      font-weight: 500;
    }

    .ips-meta {
      font-size: 1rem;
    }

    .grid-container:not(.grid-@{ipx-person-index-alias}) &,
    body.small .grid-container.grid-@{ipx-person-index-alias} & {
      @__person-avatar-offset: @pzh-inner-padding;

      padding-bottom: 10px;
      padding-top: 0;

      .ips-image {
        margin-left: @__person-avatar-offset * -1;
      }

      .ips-description {
        height: 2.8rem;
        width: @pzh-meta-width;

        &.ips-follow-active {
          width: @pzh-meta-width-with-action !important;
        }
      }

      &.ips-personcard-actions {
        .ips-actions > div > .ips-button {
          i.fa-eye-slash {
            color: @elementkleur;
            font-size: 120%;
            height: @ips-round-button-size;
            left: 0;
            position: absolute;
            top: 6px;
            transition: font-size 0.5s ease-in-out;
            width: @ips-round-button-size;
          }

          &:hover {
            i.fa-eye-slash {
              font-size: 140%;
            }
          }
        }

        .ips-description {
          width: @pzh-meta-width-with-action !important;
        }
      }
    }
  }

  ips-group-card {
    @__group-avatar-offset: @pzh-inner-padding;

    font-size: 1rem;
    padding-bottom: 0 !important;
    padding-top: 0 !important;

    .ips-image {
      margin-left: @__group-avatar-offset * -1;
    }

    .ips-title {
      font-size: 1.25rem;
      font-weight: 500;
    }

    &:not(.ips-following) .ips-meta {
      width: @pzh-meta-width;
    }

    &.ips-following .ips-meta {
      width: @pzh-meta-width-with-action !important;
    }
  }

  person-group-list ips-group-card {
    .ips-meta {
      width: @pzh-meta-width-with-action !important;
    }
  }
}

.ipx-groepen {
  .group-card-container {
    padding: (@pzh-inner-padding * 2)  @pzh-inner-padding 0;

    .grid-blok {
      margin-bottom: @pzh-inner-padding;

      ips-group-card .ips-meta {
        width: @pzh-meta-width-with-action !important;
      }
    }

    .z-results .grid-blok.type-galerij:nth-last-child(2) {
      margin-bottom: @pzh-outer-padding * 2;
    }
  }
}

.type-person-list-result-meta {
  margin-bottom: @pzh-outer-padding * 2;
  margin-top: @pzh-outer-padding * 2;
}

.type-person-list-results > .grid-element > .grid-edge {
  padding-top: @pzh-inner-padding * 2;
}

person-group-list > .grid-blok > .grid-element > .grid-edge > .grid-nesting {
  padding: @pzh-inner-padding;
}

// Margin-bottoms for cards
person-group-list > .grid-blok > .grid-element > .grid-edge > .grid-nesting > .grid-blok,
.grid-nesting > .ips-person-card-container > .grid-blok,
.grid-blok.type-owners,
.grid-blok.type-members,
.grid-blok.type-cards {
  margin-top: 0 !important;
  margin-bottom: @pzh-inner-padding;
}

.bs-searchresults-text > h2 {
  font-weight: 400;
}
