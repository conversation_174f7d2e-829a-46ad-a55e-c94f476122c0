export enum AuthStatus {
  Initial = 'Initial',
  InProgress = 'InProgress',
  Authorized = 'Authorized',
  FetchingUser = 'FetchingUser',
  Authenticated = 'Authenticated',
  UnAuthenticated = 'UnAuthenticated',
}

/*
Azure authentication flow:
Initial   -> InProgress    -> Authorized          -> FetchingUser                -> Authenticated
App start -> Login pressed -> Ready to fetch user -> Requesting user from Social -> Succesfully fetched matching user.
*/
