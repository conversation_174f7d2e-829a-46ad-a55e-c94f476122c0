import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';

import { IReference } from '@ip/social-core';
import { Observable } from 'rxjs';

import { IAnniversaryEvent } from '../../models/anniversary.model';
import { AnniversaryApiService } from '../../services/anniversary-api.service';

@Component({
  selector: 'ips-anniversary-event',
  templateUrl: './anniversary-event.component.html',
  styleUrls: ['./anniversary-event.component.scss']
})
export class AnniversaryEventComponent implements OnInit {
  @Input()
  reference!: IReference;

  @Output()
  pageTitle = new EventEmitter<string>();

  anniversaryEvent$!: Observable<IAnniversaryEvent>;

  constructor(private anniversaryApiService: AnniversaryApiService) { }

  ngOnInit() {
    this.anniversaryEvent$ = this.anniversaryApiService.get(this.reference.id);
  }
}
