﻿<p class="ips-loading grid-realign" ng-if="BlogCtrl.isLoading"><i class="fa-solid fa-spin fa-spinner"></i></p>

<div class="grid-zone grid_12 z-blog-view z-blog-not-found" ng-if="!BlogCtrl.isLoading && !BlogCtrl.blog.id">
  <div class="grid-blok grid_12 rol-social-group-components has-elt-breakpoints">
    <div class="grid-element">
      <div class="grid-edge">
        <div class="grid-box">
          <ips-component data-component="not-found">
            <content>
              <ips-not-found></ips-not-found>
            </content>
          </ips-component>
        </div>
      </div>
    </div>
  </div>
</div>

<div class="grid-blok grid_9 push_3 has-elt-breakpoints" data-ng-if-start="BlogCtrl.blog.id">
  <div class="grid-element">
    <div class="grid-edge">
      <div class="grid-box">
        <blog-body></blog-body>
      </div>
    </div>
  </div>
</div>
<div class="grid-blok grid_3 pull_9 has-elt-breakpoints type-social-blog-aside" data-ng-if-end>
  <div class="grid-element">
    <div class="grid-edge">
      <div class="grid-nesting">
        <div class="grid-blok grid_3 rol-profileimage has-elt-breakpoints">
          <div class="grid-element">
            <div class="grid-edge">
              <div class="grid-box">
                <blog-controls></blog-controls>
              </div>
            </div>
          </div>
        </div>
        <div class="grid-blok grid_3 rol-blogview-person-blog-list has-elt-breakpoints">
          <div class="grid-element">
            <div class="grid-edge">
              <div class="grid-box">
                <blog-list-person></blog-list-person>
              </div>
            </div>
          </div>
        </div>
        <div class="grid-blok grid_3 rol-blogview-group-blog-list has-elt-breakpoints" data-ng-if="BlogCtrl.blog.iproxId && BlogCtrl.groupBlogList.length > 0">
          <div class="grid-element">
            <div class="grid-edge">
              <div class="grid-box">
                <blog-list-group></blog-list-group>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
