import { IMetaConfig, IReference, Persons<PERSON><PERSON> } from '@ip/social-core';

export namespace TimelineApi {
  export interface IEntriesWithReferences {
    entries: IEntry[];
    entities: IEntity[];
    persons: PersonsApi.IPersonApiModel[];
  }

  export interface IEntry {
    action: string;
    dateTime: string;
    context: IReference;
    entity: IReference;
    id: string;
    primaryValue: unknown;
    secondaryValue: unknown;
    userId: string;
  }

  export interface IEntity {
    id: string;
    collection: string;
    metaConfig: IMetaConfig;
  }

  export interface IContentEntity<T> extends IEntity {
    content: T;
  }
}
