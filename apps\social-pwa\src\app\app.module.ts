import { registerLocaleData } from '@angular/common';
import localeNl from '@angular/common/locales/nl';
import { LOCALE_ID, NgModule } from '@angular/core';
import { BrowserModule } from '@angular/platform-browser';

import { IpPwaModule } from '@ip/pwa';
import { AzureAuthenticationConfig, IpSocialPwaBootstrapperModule } from '@ip/social-pwa-bootstrapper';
import { NgxsReduxDevtoolsPluginModule } from '@ngxs/devtools-plugin';
import { NgxsModule } from '@ngxs/store';
import { TimeagoModule } from 'ngx-timeago';

import { environment } from '../environments/environment';
import { AppComponent } from './app.component';

registerLocaleData(localeNl);

@NgModule({
  declarations: [AppComponent],
  imports: [
    BrowserModule,
    NgxsModule.forRoot([]),
    NgxsReduxDevtoolsPluginModule.forRoot(),
    TimeagoModule.forRoot(),
    IpPwaModule.forRoot(),
    IpSocialPwaBootstrapperModule.forRoot(
      new AzureAuthenticationConfig(),
    ),
  ],
  providers: [
    ...environment.settingProviders,
    ...environment.structureProviders,
    { provide: LOCALE_ID, useValue: 'nl' },
  ],
  bootstrap: [AppComponent],
})
export class AppModule { }
