<h2 *ngIf="block.title">{{ block.title }}</h2>
<div *ngIf="block.image">
  <img [ips-iprox-image-src]="block.image.url" [alt]="block.image.label" />
</div>
<div
  *ngIf="block.text"
  class="ip-content"
  ips-iprox-content-html
  [html]="block.text | bypassSecurityHtml"
></div>
<ul>
  <li *ngFor="let reference of block.references">
    <a *ngIf="reference.type !== 'download'" class="ips-iprox-link" target="_blank" [href]="reference.url">
      <span>{{ reference.label }}</span>
      <ion-icon name="open-outline"></ion-icon>
    </a>
    <a *ngIf="reference.type === 'download'" class="ips-iprox-link" [ips-iprox-download]="reference.url">
      <span>{{ reference.label }}</span>
      <ion-icon name="download-outline"></ion-icon>
    </a>
  </li>
</ul>
