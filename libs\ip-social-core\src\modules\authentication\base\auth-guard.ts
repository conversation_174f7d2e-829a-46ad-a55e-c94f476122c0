import { Injectable } from '@angular/core';
import { ActivatedRouteSnapshot, CanActivate, Router, RouterStateSnapshot } from '@angular/router';

import { Observable } from 'rxjs';
import { filter, map, tap } from 'rxjs/operators';

import { AuthStatus } from './auth-status';
import { AuthenticationService } from './authentication.service';

@Injectable()
export class AuthGuard implements CanActivate {
  constructor(private authService: AuthenticationService, private router: Router) { }

  canActivate(_route: ActivatedRouteSnapshot, _state: RouterStateSnapshot): Observable<boolean> {
    return this.authService.auth$.pipe(
      filter(status => status === AuthStatus.Authenticated || status === AuthStatus.UnAuthenticated),
      map(status => status === AuthStatus.Authenticated),
      tap(canActivate => {
        if (!canActivate) {
          this.router.navigate(['/']);
        }
      })
    );
  }
}
