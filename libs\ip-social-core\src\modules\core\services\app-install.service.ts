import { Injectable } from '@angular/core';

import { ReplaySubject } from 'rxjs';
import { map, take } from 'rxjs/operators';

// BeforeInstallPromptEvent is not part of typescript typings yet. (Chrome/Android only);
// eslint-disable-next-line @typescript-eslint/no-explicit-any
const installEvent = new ReplaySubject<any | undefined | 'rejected'>();

const listener = (event: Event) => {
  event.preventDefault();

  installEvent.next(event);
  window.removeEventListener('beforeinstallprompt', listener);
};

// beforeinstallprompt will not fire once app is installed.
window.addEventListener('beforeinstallprompt', listener);

@Injectable()
export class AppInstallService {
  private installEvent$ = installEvent.asObservable();

  installed$ = installEvent.pipe(
    map(event => {
      if (event === undefined) {
        return 'installed';
      }

      if (event === 'rejected') {
        return 'rejected';
      }

      return 'uninstalled';
    })
  );

  prompt() {
    this.installEvent$
      .pipe(take(1))
      .subscribe(event => {
        event?.prompt().then((value: { outcome: 'accepted' | 'dismissed'; platform: string; }) => {
          if (value.outcome === 'accepted') {
            installEvent.next(undefined);
          }
          else {
            installEvent.next('rejected');
          }
        });
      });
  }
}
