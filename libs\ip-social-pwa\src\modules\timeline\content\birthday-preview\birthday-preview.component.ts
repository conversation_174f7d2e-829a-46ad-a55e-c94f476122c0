import { DatePipe } from '@angular/common';
import { Component, Input, OnInit } from '@angular/core';

import { ModalController } from '@ionic/angular';
import { capitalize, IPerson } from '@ip/social-core';
import { Observable, of } from 'rxjs';

import { AnniversaryPreviewModalComponent } from '../../../anniversary/components/anniversary-preview-modal/anniversary-preview-modal.component';
import { IAnniversaryPreview } from '../../../anniversary/models/anniversary-preview.model';

@Component({
  templateUrl: './birthday-preview.component.html',
  selector: 'ips-timeline-birthday',
  styleUrls: ['./birthday-preview.component.scss']
})
export class TimelinePreviewBirthdayComponent implements OnInit {
  private endDay!: Date;

  @Input()
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  data!: any;

  anniversaryPreview$!: Observable<{ week1: Array<IAnniversaryPreview>, week2: Array<IAnniversaryPreview>; }>;

  startDay!: Date;

  constructor(private datePipe: DatePipe, private modalController: ModalController) { }

  ngOnInit() {
    this.startDay = new Date(`${this.data.content.startDay.year}-${this.data.content.startDay.month}-${this.data.content.startDay.day}`);
    this.endDay = new Date(`${this.data.content.endDay.year}-${this.data.content.endDay.month}-${this.data.content.endDay.day}`);

    this.anniversaryPreview$ = this.getAnniversaryPreview();
  }

  async open(anniversaryPreview: Array<IAnniversaryPreview>) {
    const modal = await this.modalController.create({
      component: AnniversaryPreviewModalComponent,
      cssClass: 'ips-anniversary-preview-modal',
      componentProps: {
        anniversaryPreview,
        startDay: new Date(this.startDay.getFullYear(), this.startDay.getMonth(), this.startDay.getDate() + 7),
        endDay: this.endDay,
        modal: true
      }
    });

    return await modal.present();
  }

  private getAnniversaryPreview(): Observable<{ week1: Array<IAnniversaryPreview>, week2: Array<IAnniversaryPreview>; }> {
    const week1: Array<IAnniversaryPreview> = [];
    const week2: Array<IAnniversaryPreview> = [];

    for (let i = 0; i < 14; i++) {
      const persons = this.data.content.users?.filter((person: IPerson) => person.birthDate.getDate() === this.startDay.getDate() + i);

      if (persons?.length) {
        const day = this.datePipe.transform(new Date(`${this.data.content.startDay.year}-${this.data.content.startDay.month}-${this.data.content.startDay.day + i}`), 'EEEE') ?? '';

        const birthdays = {
          day: capitalize(day),
          persons
        };

        if (i < 7) {
          week1.push(birthdays);
        }

        if (i >= 7) {
          week2.push(birthdays);
        }
      }
    }

    return of({ week1, week2 });
  }
}
