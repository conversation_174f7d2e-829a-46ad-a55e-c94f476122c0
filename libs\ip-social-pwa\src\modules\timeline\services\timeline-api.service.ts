import { HttpParams } from '@angular/common/http';
import { Injectable } from '@angular/core';

import { IPerson, PersonService, SocialHttpClient, mapPersons } from '@ip/social-core';
import { Observable } from 'rxjs';
import { map, tap } from 'rxjs/operators';

import { IproxEventMapper } from '../../shared/utilities/iprox-event.mapper';
import { ActivityService } from '../activities/activity.service';
import { ITimelineContent, ITimelineEntry } from '../models';
import { TimelineApi } from '../models/internal/api.models';

// TODO: Provide these mappings so they can be extended.
const activityToContentMap = new Map<string, string>();
activityToContentMap.set('PublishBlog', 'blog');
activityToContentMap.set('CommentOnBlog', 'comment');
activityToContentMap.set('IproxPublishedArticle', 'iproxArticle');
activityToContentMap.set('IproxUpdatedArticle', 'iproxArticle');
activityToContentMap.set('IproxPublishedEvent', 'iproxEvent');
activityToContentMap.set('IproxUpdatedEvent', 'iproxEvent');
activityToContentMap.set('Birthday', 'birthday');
activityToContentMap.set('BirthdayPreview', 'birthdayPreview');

type GetParams = { toDate: Date; fromDate?: Date; } | { toDate?: Date; fromDate: Date; };

@Injectable()
export class TimelineApiService {
  constructor(
    private socialApi: SocialHttpClient,
    private personService: PersonService,
    private activityService: ActivityService,
    private iproxEventMapper: IproxEventMapper,
  ) { }

  get(params: GetParams): Observable<ITimelineEntry[]> {
    let httpParams = new HttpParams();

    if (params.toDate) {
      httpParams = httpParams.append('toDate', params.toDate.toISOString());
    }

    if (params.fromDate) {
      httpParams = httpParams.append('fromDate', params.fromDate.toISOString());
    }

    return this.socialApi.get<TimelineApi.IEntriesWithReferences>('timeline', { params: httpParams })
      .pipe(
        map(data => ({
          entries: data.entries,
          entities: data.entities,
          persons: mapPersons(data.persons)
        })),
        tap(data => this.personService.cache(data.persons)),
        map(data => {
          const timestamp = new Date().getTime();

          return data.entries.reduce<ITimelineEntry[]>((timelineEntries, apiEntry) => {
            const timelineEntry = this.mapTimelineEntry(apiEntry, data.entities, data.persons, timestamp);

            return timelineEntry
              ? [...timelineEntries, timelineEntry]
              : timelineEntries;
          }, []);
        }),
      );
  }

  private mapTimelineEntry(apiEntry: TimelineApi.IEntry, entities: TimelineApi.IEntity[], persons: IPerson[], timestamp: number): ITimelineEntry | undefined {
    const contentEntity = entities.find(e => e.id === apiEntry.entity.id);

    const content = contentEntity ? this.mapContent(apiEntry, contentEntity, persons) : undefined;
    const activity = this.activityService.create(apiEntry);

    if (!content) {
      console.log(`[timeline] could not map '${apiEntry.action}' to content.`);
      return;
    }

    if (!activity) {
      console.log(`[timeline] could not map '${apiEntry.action}' to an activity.`);
      return;
    }

    return {
      id: apiEntry.id,
      date: new Date(apiEntry.dateTime),
      action: apiEntry.action,
      activity,
      content,
      metaConfig: apiEntry.action !== 'CommentOnBlog' ? contentEntity?.metaConfig : undefined, // SR: Quick fix, iets beters voor verzinnen.
      primaryValue: apiEntry.primaryValue,
      timestamp,
      mutation: 0,
      props: {},
    };
  }

  private mapContent(apiEntry: TimelineApi.IEntry, contentEntity: TimelineApi.IEntity, persons: IPerson[]): ITimelineContent | undefined {
    const type = activityToContentMap.get(apiEntry.action);

    if (!type) {
      console.log(`[timeline] could not map '${apiEntry.action}' to a content type.`);
      return;
    }

    return {
      type,
      reference: `${apiEntry.entity.collection}/${apiEntry.entity.id}`,
      data: this.mapContentData(type, contentEntity, persons) // TODO: Map data
    };
  }

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  private mapContentData(type: string, entity: any, persons: IPerson[]): Record<string, unknown> {
    if (type === 'iproxEvent') {
      return {
        ...entity,
        event: this.iproxEventMapper.parseEvent(entity.content.page)
      };
    }

    if (entity.content.userIds && (type === 'birthday' || type === 'birthdayPreview')) {
      entity.content.users = entity.content.userIds
        .map((userId: string) => persons.find((person: IPerson) => person.id === userId))
        .filter((person: IPerson) => person !== undefined);
    }

    return entity;
  }
}
