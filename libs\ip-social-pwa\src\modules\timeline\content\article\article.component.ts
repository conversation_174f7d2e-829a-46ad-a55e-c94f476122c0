import { Component, Input, OnInit } from '@angular/core';

import { CoreSettings, ResourceTokenService } from '@ip/social-core';
import { map } from 'rxjs/operators';

import { PageComponent } from '../../../page/components/page/page.component';

@Component({
  templateUrl: './article.component.html',
  selector: 'ips-timeline-article',
  styles: [':host { display: block }']
})
export class TimelineArticleComponent implements OnInit {
  @Input()
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  data!: any;

  @Input()
  imageSrc?: string;

  component = PageComponent;

  constructor(private resourceTokenService: ResourceTokenService, private settings: CoreSettings) { }

  ngOnInit() {
    const imagePath = this.data?.content?.page?.meta?.meta['afbeelding-voor-index']?.url;
    if (imagePath) {
      this.setImageSrc(imagePath);
    }
  }

  private setImageSrc(imagePath: string) {
    this.resourceTokenService.token$
      .pipe(map(token => `${this.settings.apiUrl}iprox/content?file=${imagePath}&access-token=${token}`))
      .subscribe(src => this.imageSrc = src);
  }
}
