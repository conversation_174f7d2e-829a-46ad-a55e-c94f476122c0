import { CommonModule } from '@angular/common';
import { ModuleWithProviders, NgModule } from '@angular/core';

import { PersonVisitTrackerComponent } from './components/person-visit-tracker/person-visit-tracker.component';
import { VisitTrackerComponent } from './components/visit-tracker/visit-tracker.component';
import { MetaSettings } from './meta.settings';
import { MetaApiService } from './services/meta-api.service';
import { MetaService } from './services/meta.service';
import { VisitService } from './services/visit.service';

@NgModule({
  declarations: [
    PersonVisitTrackerComponent,
    VisitTrackerComponent,
  ],
  imports: [
    CommonModule,
  ],
  providers: [
    MetaApiService,
    MetaSettings,
  ],
  exports: [
    PersonVisitTrackerComponent,
    VisitTrackerComponent,
  ]
})
export class MetaModule {
  static forRoot(): ModuleWithProviders<MetaModule> {
    return {
      ngModule: MetaModule,
      providers: [
        MetaService,
        VisitService,
      ]
    };
  }
}
