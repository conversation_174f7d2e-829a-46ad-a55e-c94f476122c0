import { CommonModule } from '@angular/common';
import { HttpClientModule } from '@angular/common/http';
import { APP_INITIALIZER, ModuleWithProviders, NgModule } from '@angular/core';
import { RouterModule } from '@angular/router';

import { IonicModule } from '@ionic/angular';

import { IpPwaHeaderModule } from '../header/header.module';
import { CustomMenuButtonComponent } from './components/custom-menu-button/custom-menu-button.component';
import { PageComponent } from './components/page/page.component';
import { RootComponent } from './components/root/root.component';
import { ActiveMenuIconPipe } from './pipes/active-icon.pipe';
import { SettingsService } from './providers/settings/settings.service';
import { StructureService } from './providers/structure/structure.service';
import { MenuService } from './services/menu.service';

@NgModule({
  declarations: [
    PageComponent,
    RootComponent,
    CustomMenuButtonComponent,
    ActiveMenuIconPipe,
  ],
  imports: [
    CommonModule,
    HttpClientModule,
    RouterModule.forRoot([
      {
        path: '**',
        redirectTo: ''
      }
    ]),
    IpPwaHeaderModule.forRoot(),
    IonicModule,
  ],
  providers: [
  ],
  exports: [
    RouterModule,
    RootComponent,
  ]
})
export class IpPwaModule {
  static forRoot(): ModuleWithProviders<IpPwaModule> {
    return {
      ngModule: IpPwaModule,
      providers: [
        {
          provide: APP_INITIALIZER,
          useFactory: (structureService: StructureService) => () => structureService.init(),
          deps: [StructureService],
          multi: true
        },
        SettingsService,
        MenuService,
      ]
    };
  }
}
