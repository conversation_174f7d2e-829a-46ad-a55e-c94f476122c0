window.smoothAnchorExcludes.push "#fieldsetTo .setlabel a"

$ ->
  timeToFade = 400
  $("#fieldsetTo .setlabel a").unlessProcessed("fieldsetToClick").click (event) ->
    $("#mailAdressen").show()
    $(@).find(".recipientCount").hide()
    event.preventDefault()
  $("#mailAdressen").find("input").unlessProcessed("mailAdressenClick").click ->
    $checkbox = $(@)
    if !$checkbox.prop("checked") and !$checkbox.closest(".antwoord").is(":last-child")
      $checkbox.closest(".antwoord").fadeOut timeToFade, ->
        antwoordDiv = $(@)
        antwoordenDiv = antwoordDiv.closest(".antwoorden")
        antwoordDiv.detach().appendTo(antwoordenDiv).fadeIn timeToFade
