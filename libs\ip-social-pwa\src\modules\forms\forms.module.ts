import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';

import { IonicModule } from '@ionic/angular';
import { UtilitiesModule } from '@ip/social-core';
import { EditorModule, TINYMCE_SCRIPT_SRC } from '@tinymce/tinymce-angular';

import { PersonModule } from '../person/person.module';
import { SharedModule } from '../shared/shared.module';
import { PersonSelectionComponent } from './components/person-selection/person-selection.component';
import { PersonSelectionService } from './components/person-selection/person-selection.service';
import { TinymceService } from './services/tinymce.service';

@NgModule({
  declarations: [
    PersonSelectionComponent,
    // Dit mag hier niet omdat FileValueAccessor broncode niet in dezelfde repo leeft.
  ],
  providers: [
    TinymceService,
    { provide: TINYMCE_SCRIPT_SRC, useValue: 'tinymce/tinymce.js' },
    PersonSelectionService,
  ],
  imports: [
    CommonModule,
    IonicModule,
    SharedModule,
    PersonModule,
    FormsModule,
    ReactiveFormsModule,
    UtilitiesModule,
    EditorModule,
  ],
  exports: [
    EditorModule,
    ReactiveFormsModule,
  ]
})
export class SocialFormsModule { }
