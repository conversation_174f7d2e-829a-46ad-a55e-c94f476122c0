import { IReference } from '../../../core/models';

export namespace CommentsApi {
  export interface ICommentResponse {
    facets: null;
    totalCount: number;
    items: IComment[];
    item: null | IComment;
  }

  export interface IComment {
    hasChanges: boolean;
    body: string;
    workingBody: string;
    commentCount: number;
    comments: null | IComment[];
    draft: null | IComment;
    creationDate: string;
    id: string;
    publicationDate: null | string;
    lastPublicationDate: null | string;
    lastModifiedDate: null | string;
    reference: IReference;
    tags: null; // Unused
    userId: string;
    userInfo: unknown;
    attachments?: IAttachment[];
    workingAttachments?: IAttachment[];
  }

  export interface IUpdateResponse {
    workingBody: string;
    lastModifiedDate: string;
    hasChanges: boolean;
  }

  /** Future: Move this to a generic attachments-module. */
  export interface IAttachment {
    id: string;
    userId: string;
    reference: IReference;
    fileId: string;
    file: IIntranetFile;
  }

  /** Future: Move this to a generic attachments-module. */
  export interface IIntranetFile {
    content: null;
    contentLength: number;
    contentType: string;
    fileName: string;
    formatSize: null;
    id: string;
    metadata: unknown;
    owner: IReference;
    subRef: null;
    userId: string;
  }
}
