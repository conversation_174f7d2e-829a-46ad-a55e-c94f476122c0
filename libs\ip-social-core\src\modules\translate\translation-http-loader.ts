import { Injectable } from '@angular/core';

import { Translation, TRANSLOCO_LOADER, TranslocoLoader } from '@ngneat/transloco';

import { SocialHttpClient } from '../core/services/api-client';

@Injectable({ providedIn: 'root' })
export class TranslationHttpLoader implements TranslocoLoader {
  constructor(private socialApi: SocialHttpClient) { }

  getTranslation(lang: string) {
    return this.socialApi.get<Translation>(`language/${lang}`);
  }
}

export const translationHttpLoader = {
  provide: TRANSLOCO_LOADER,
  useClass: TranslationHttpLoader
};
