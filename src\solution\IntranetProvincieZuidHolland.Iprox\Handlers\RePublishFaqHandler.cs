﻿namespace IntranetProvincieZuidHolland.Iprox.Handlers {
  using System;
  using InfoProjects.Dxe.Linq;
  using InfoProjects.Dxe.Process;
  using InfoProjects.Dxe.Process.Handler;
  using InfoProjects.Dxe.Util;
  using InfoProjects.Iprox.Model;
  using IntranetProvincieZuidHolland.Iprox.Model;

  /// <summary>RePublishFaq Handler</summary>
  public class RePublishFaqHandler : IContextProcessHandler {
    /// <summary>RePublishFaqHandler Handler</summary>
    /// <param name="unit">Process unit</param>
    /// <param name="context">Process context</param>
    /// <returns>Returns result</returns>
    public ResultProps Process(ProcessUnit unit, ProcessContext context) {
      string itmIdt = unit[Reserved.ID];
      if (!String.IsNullOrEmpty(itmIdt)) {
        Logger.Debug("Update item {0}", itmIdt);
        this.UpdateItem(unit, context, itmIdt);
      }

      return new ResultProps();
    }

    /// <summary>
    /// Update een item
    /// </summary>
    /// <param name="unit">ProcessUnit unit</param>
    /// <param name="context">ProcessContext context</param>
    /// <param name="itmIdt">string itmIdt</param>
    private void UpdateItem(ProcessUnit unit, ProcessContext context, string itmIdt) {
      using (var cms = new IproxCms()) {
        var faq = cms.GetItem<Faq>(itmIdt.To<int>(), false, ContentMode.Source);
        faq.MarkPublished();

        this.UpdateFaq(unit, context, faq);

        cms.SubmitChanges(context);
      }
    }

    /// <summary>
    /// Bewerk bestaande vraag en antwoord
    /// </summary>
    /// <param name="unit">ProcessUnit unit</param>
    /// <param name="context">ProcessContext context</param>
    /// <param name="faq">Faq model item</param>
    private void UpdateFaq(ProcessUnit unit, ProcessContext context, Item<Faq> faq) {
      // Pagina titel en label
      if (unit.IsSet("Titel.Wrd")) {
        faq.Title = unit["Titel.Wrd"];
        faq.Page.Title = unit["Titel.Wrd"];
      }
      
      SharedRepublish.SetValueIfSupplied(unit, faq.Page.VraagEnAntwoord.Antwoord, "Antwoord");
    }
  }
}
