export interface IIcsEvent {
  start: Date;
  end?: Date;
  subject: string;
  description?: string;
  location?: string;
}

export class IcsEvent implements IIcsEvent {
  start: Date;
  end?: Date;
  subject: string;
  description?: string;
  location?: string;

  constructor(e: IIcsEvent) {
    this.start = e.start;
    this.end = e.end;
    this.subject = e.subject;
    this.description = e.description;
    this.location = e.location;
  }

  toIcs(): string[] {
    return [
      'BEGIN:VEVENT',
      'CLASS:PUBLIC',
      `DESCRIPTION:${this.description ? new DOMParser().parseFromString(this.description, 'text/html').documentElement.textContent : ''}`,
      'DTSTART;VALUE=DATE:' + this.toIcsDate(this.start),
      `DTEND;VALUE=DATE:${this.end ? this.toIcsDate(this.end) : ''}`,
      `LOCATION:${this.location || ''}`,
      'SUMMARY;LANGUAGE=en-us:' + this.subject,
      'TRANSP:TRANSPARENT',
      'END:VEVENT'
    ];
  }

  private toIcsDate(date: Date): string {
    return date.toISOString().substring(0, 19).replace(new RegExp('-|:', 'g'), '') + 'Z';
  }
}

export class Ics {
  private separator = '\n';

  private calendarStart = ['BEGIN:VCALENDAR', 'VERSION:2.0'];

  private calendarEnd = ['END:VCALENDAR'];

  private events: IcsEvent[] = [];

  addEvent(event: IIcsEvent): void {
    this.events.push(new IcsEvent(event));
  }

  generate(): string {
    const icsEvents = this.events.map(e => e.toIcs()).reduce((i, j) => i.concat(j));

    const ics = [
      ...this.calendarStart,
      ...icsEvents,
      ...this.calendarEnd
    ];

    return ics.join(this.separator);
  }
}
