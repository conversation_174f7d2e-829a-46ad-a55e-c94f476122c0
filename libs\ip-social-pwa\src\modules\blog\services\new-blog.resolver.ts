import { Injectable } from '@angular/core';
import { ActivatedRouteSnapshot, Resolve, Router, RouterStateSnapshot } from '@angular/router';

import { Observable } from 'rxjs';
import { map, tap } from 'rxjs/operators';

import { BlogApiService } from './blog-api.service';

@Injectable()
export class NewBlogResolver implements Resolve<string> {
  constructor(private blogApi: BlogApiService, private router: Router) { }

  resolve(_route: ActivatedRouteSnapshot, _state: RouterStateSnapshot): Observable<string> {
    return this.blogApi.create()
      .pipe(
        tap(blog => {
          this.router.navigate(['/you/blogs', blog.id], { queryParams: { editMode: true } });
        }),
        map(blog => blog.id)
      );
  }
}
