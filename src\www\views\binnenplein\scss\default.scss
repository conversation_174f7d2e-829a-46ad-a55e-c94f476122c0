@import "@infoprojects-local/baseline-styling/src/foundation/vars/global-vars";
@import "./include/mq-vars";
@import "./include/baseline-vars";
@import "./include/local-vars";

@mixin small {
  @media screen and (max-width: $bl-mq-small) {
    @content;
  }
}

@import "./typography/mixins";

// Different path;
$bl-font-path: "/views/binnenplein/fonts";

@import "./typography/fonts";

%text-font {
  font-family: $bl-text-font;
  font-weight: $bl-font-weight-regular;
}

.htmlView { // sass-lint:disable-line class-name-format
  // @import "@infoprojects-local/baseline-styling/src/baseline-base";

  @import "./typography/blockquote";

  @extend %text-font;
  font-size: $pzh-font-size-htmlview;

  h2,
  h3,
  h4,
  h5,
  h6 {
    color: $kopkleur;
    margin-bottom: $bl-unit;
    margin-top: $bl-unit * 3;
  }

  h2 {
    margin-bottom: 2rem;
    margin-top: 2.5rem;
  }

  h3 {
    margin-bottom: 1rem;
    margin-top: 1.5rem;
  }

  p {
    color: $tekstkleur;
  }

  ul,
  ul li {
    list-style: square !important;
  }

  blockquote {
    border-left: 0;
    font-size: $pzh-blockquote-font-size;
    font-style: normal;
    position: relative;

    &::before,
    &::after {
      position: absolute;
    }

    p {
      display: inline-block;
      font-size: $pzh-blockquote-font-size;
      font-style: normal;
      line-height: 1.2;
    }
  }

  .Email,
  .Telefoon,
  .Locatie {
    padding-bottom: $bl-unit - 3;
    padding-left: $bl-unit * 4;
    padding-top: $bl-unit - 3;
  }


  .Email {
    background: url("/views/binnenplein/images/extra/email.png") no-repeat 0 55%;
  }

  .Telefoon {
    background: url("/views/binnenplein/images/extra/phone.png") no-repeat 0 55%;
  }

  .Locatie {
    background: url("/views/binnenplein/images/extra/locatie.png") no-repeat 0 55%;
  }

  table {
    th {
      border: 0;
      font-weight: bold;
    }

    th,
    td {
      @extend %text-font;
      font-size: 1em;
    }
  }

  table[border="1"],
  table[border="2"],
  table[border="3"],
  table[border="4"] {
    td {
      border: 0 !important;
    }
  }
}
