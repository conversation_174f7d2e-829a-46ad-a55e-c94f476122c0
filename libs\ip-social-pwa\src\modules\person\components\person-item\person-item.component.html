<ng-container *ngIf="enableNavigation === true; else noNavigation">
  <ion-item *ngIf="person$ | async as person;" [routerLink]="[personRoute, person.id]">
    <ion-avatar *ngIf="showAvatar" slot="start">
      <img [ips-avatar-src]="person">
    </ion-avatar>
    <ion-label>
      <h2>{{ person.fullName }}</h2>
    </ion-label>
  </ion-item>
</ng-container>

<ng-template #noNavigation>
  <ion-item *ngIf="person$ | async as person;">
    <ion-avatar *ngIf="showAvatar" slot="start">
      <img [ips-avatar-src]="person">
    </ion-avatar>
    <ion-label>
      <h2>{{ person.fullName }}</h2>
    </ion-label>
  </ion-item>
</ng-template>
