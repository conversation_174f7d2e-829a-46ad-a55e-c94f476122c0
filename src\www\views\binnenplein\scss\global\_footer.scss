$pzh-footer-list-margin: 1.25rem;

footer {
  .grid-zone.z-social-bookmark-button {
    margin-bottom: 2rem;
  }

  .grid-blok.has-bgcolor.type-tekst,
  .grid-blok.has-bgcolor.type-lijst {
    margin-bottom: 0;
  }

  .type-tekst {
    .grid-title {
      @include small {
        margin-bottom: $pzh-footer-list-margin;
      }

      margin-top: $pzh-footer-list-margin;
      padding-top: $pzh-inner-padding;

      h1,
      h2 {
        font-size: font-size-value(5, false);
      }
    }
  }

  .type-lijst {
    @include large {
      .grid-inside {
        padding-top: 0 !important;
      }
    }

    ul.iprox-content {
      @include small {
        margin-bottom: 0;
        margin-top: 0;
      }

      margin-bottom: $pzh-footer-list-margin;
      margin-top: $pzh-footer-list-margin;

      li.entry {
        @media screen and (min-width: $bl-mq-medium) {
          &:first-child {
            margin-top: 2.5rem; // Horizontal lining with the motto;
          }
        }

        &:not(:last-child) {
          @include small {
            margin-bottom: 0.75rem;
          }

          margin-bottom: 1.5rem;
        }

        &::before {
          display: none;
        }

        a {
          font-size: 1.125rem;
          font-weight: 500;
          text-decoration: none;

          &:hover {
            text-decoration: underline;
            text-decoration-thickness: $pzh-link-richcontent-hover-text-decoration-thickness;
          }
        }
      }
    }

    ~ .type-lijst {
      .grid-inside {
        @include small {
          padding-top: 0 !important; // Shrink gap in between lists on small
        }
      }
    }
  }
}
