import { AfterViewChecked, ChangeDetectionStrategy, Component, ElementRef, HostBinding, HostListener, Input, OnChanges, SimpleChanges } from '@angular/core';
import { DomSanitizer, SafeHtml } from '@angular/platform-browser';

import { ResourceService } from '../../../core/services/resource.service';
import { LinkyPipe } from '../../linky.pipe';

@Component({
  // eslint-disable-next-line @angular-eslint/component-selector
  selector: '[ips-trust-html]',
  template: '',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class TrustHtmlComponent implements OnChanges, AfterViewChecked {
  /** Only use this for trusted HTML. */
  // eslint-disable-next-line @angular-eslint/no-input-rename
  @Input('ips-trust-html')
  set contentHtml(content: string) {
    this.html = this.sanitizer.bypassSecurityTrustHtml(
      this.linkyPipe.transform(content)
    );
  }

  /** This is for legacy angularJS support. TODO: Remove this + ngOnChanges. */
  @Input()
  htmlContent?: string;

  /** This is for legacy angularJS support. TODO: Remove this */
  shouldRunAccessToken = true;

  // eslint-disable-next-line @typescript-eslint/member-ordering
  @HostBinding('innerHTML')
  html!: SafeHtml;

  constructor(
    private resourceService: ResourceService,
    private linkyPipe: LinkyPipe,
    private sanitizer: DomSanitizer,
    private elementRef: ElementRef,
  ) { }

  @HostListener('click', ['$event'])
  download(event: Event) {
    const target = event.target;

    if (target instanceof HTMLAnchorElement) {
      event.preventDefault();
      event.stopImmediatePropagation();

      this.resourceService.downloadFile(target.href);
    }
  }

  /** This is for legacy angularJS support. TODO: Remove this + htmlContent input. */
  ngOnChanges(changes: SimpleChanges) {
    if ((changes.htmlContent.currentValue !== changes.htmlContent.previousValue) || changes.htmlContent.firstChange) {
      this.shouldRunAccessToken = true;
      this.html = this.sanitizer.bypassSecurityTrustHtml(
        this.linkyPipe.transform(changes.htmlContent.currentValue)
      );
    }
  }

  ngAfterViewChecked() {
    if (this.shouldRunAccessToken && this.elementRef.nativeElement instanceof HTMLElement) {
      this.shouldRunAccessToken = false;
      this.setImageAccessToken(this.elementRef.nativeElement);
    }
  }

  private setImageAccessToken(element: HTMLElement) {
    this.resourceService.setImageAccessToken(element.querySelectorAll('img[src]'));
  }
}
