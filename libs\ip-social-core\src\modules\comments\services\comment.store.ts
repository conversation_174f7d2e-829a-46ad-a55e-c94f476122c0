import { Observable, Subject } from 'rxjs';
import { map, scan, shareReplay, takeUntil } from 'rxjs/operators';

import { IReference } from '../../core/models';
import { ICommentCollection } from '../models/comment-collection.model';
import { Comments, IComment, ICommentTree } from '../models/comment.model';
import { Action } from './comment.actions';

export class CommentStore {
  private readonly commentActions = new Subject<Action>();

  private readonly collection$ = this.commentActions.pipe(
    scan<Action, ICommentCollection[]>((collections, action) => action.exec(collections), []),
    shareReplay(1),
  );

  constructor(destroyed$: Observable<void>) {
    this.collection$
      .pipe(takeUntil(destroyed$))
      .subscribe();
  }

  public comments$(reference: IReference): Observable<Comments> {
    return this.collection$.pipe(
      map(collections => {
        const { draft, comments, commentCount } = this.getComments(collections, reference);

        return {
          commentCount,
          comments,
          draft,
        };
      }),
    );
  }

  public dispatch(action: Action) {
    this.commentActions.next(action);
  }

  private getComments(
    collections: ICommentCollection[],
    reference: IReference,
    providedDraft?: IComment,
  ): { draft?: IComment; comments: ICommentTree[]; commentCount: number; } {
    const { comments, count, draft } = collections.find(c => c.reference.match(reference)) ?? {};

    const foundDraft = comments?.find(c => c.draft !== undefined)?.draft;

    return {
      draft: draft ?? providedDraft,
      commentCount: count ?? 0,
      comments: (comments ?? []).map<ICommentTree>(comment => ({
        ...comment,
        // SR: Dit kan mogelijk geoptimaliseerd worden door comment.commentCount > 0 ? this.getComments().
        ...this.getComments(collections, { id: comment.id, collection: 'Comment' }, foundDraft),
      }))
    };
  }
}
