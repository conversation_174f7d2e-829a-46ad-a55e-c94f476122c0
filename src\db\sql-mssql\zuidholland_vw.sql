--------
-- Microsoft SQL Server 2005 code voor creatie IPROX views
-----------

PRINT 'VIEW TrfAncDscSlfTab'
GO

IF EXISTS (select name from sysobjects where name='TrfAncDscSlfTab')
DROP VIEW TrfAncDscSlfTab
GO

CREATE VIEW TrfAncDscSlfTab
AS SELECT 
      Rel.VanTrfIdt AS VanTrfIdt
   ,
      Rel.NarTrfIdt AS NarTrfIdt
   FROM
      TrfTab AS Trf
   INNER JOIN
      TrfRelTab AS Rel
   ON
      (Rel.NarTrfIdt = Trf.TrfIdt)
   WHERE
      Rel.TrfRelTypIdt = 1
UNION ALL SELECT 
      Trf.TrfIdt AS VanTrfIdt
   ,
      Trf.TrfIdt AS NarTrfIdt
   FROM
      TrfTab AS Trf
GO

-- Zet permissies voor view TrfAncDscSlfTab
GRANT SELECT ON TrfAncDscSlfTab TO public
GO

-- Einde Microsoft SQL Server 2005 DDL code
