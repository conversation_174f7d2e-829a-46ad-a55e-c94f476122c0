﻿namespace IntranetProvincieZuidHolland.Iprox.Events {
  using System.Linq;

  using InfoProjects.Dxe.Linq;
  using InfoProjects.Dxe.Sql;
  using InfoProjects.Iprox.Modules.Publish.Handler;

  using IntranetProvincieZuidHolland.Iprox.Handlers;

  /// <summary>
  /// Class to handle published workgroup change events.
  /// </summary>
  internal static class WorkgroupPublishEvents {
    #region Public Methods and Operators

    /// <summary>
    /// Initializes event handlers.
    /// </summary>
    public static void Init() {
      PublishPageHandler.PublishPage += pagePublish => {
        if (pagePublish.Pagetype == "workgroup") {
          CheckGroupMemberFunction(pagePublish);
        }
      };
    }

    #endregion

    #region Methods

    /// <summary>
    /// Checks the group member function for correct passive/active profile.
    /// </summary>
    /// <param name="pagePublish">
    /// The page publish.
    /// </param>
    private static void CheckGroupMemberFunction(IPagePublish pagePublish) {
      var itmIdt = pagePublish.ItmIdt.To<int>();
      var active = RegisterGroupMemberHandler.GetProjectMembersActive(itmIdt, pagePublish.Context);
      var functions = RegisterGroupMemberHandler.GetFunctionIds(itmIdt, pagePublish.Context);
      var wrongFunctionId = functions.SingleOrDefault(kvp => kvp.Key.Active == !active).Value;
      if (wrongFunctionId == null) {
        return;
      }

      var wrongProfile = RegisterGroupMemberHandler.GroupMemberProfile.Get(GroupMemberRole.Lid, !active);
      var rightProfile = RegisterGroupMemberHandler.GroupMemberProfile.Get(GroupMemberRole.Lid, active);
      var update = pagePublish.Context.Sql.GetSqlWriter(SqlType.UPDATE);
      update.AddTable("TakTab");
      update.AddCondition("FunIdt", wrongFunctionId);
      update.AddCondition("ParIdt", RegisterGroupMemberHandler.GroupMemberRoleId.Value[wrongProfile]);
      update.SetFieldValue("ParIdt", RegisterGroupMemberHandler.GroupMemberRoleId.Value[rightProfile].ToIproxString());
      update.SetFieldValue("PrfIdt", active ? "6" : "3");
      update.Execute();
    }

    #endregion
  }
}