import { AfterViewInit, ChangeDetectionStrategy, Component, ElementRef, HostBinding, HostListener, Input } from '@angular/core';
import { DomSanitizer, SafeHtml } from '@angular/platform-browser';

import { NavController } from '@ionic/angular';
import { CoreSettings, LinkyPipe, ResourceService } from '@ip/social-core';

@Component({
  // eslint-disable-next-line @angular-eslint/component-selector
  selector: '[ips-html-content]',
  template: '',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class HtmlContentComponent implements AfterViewInit {
  /** Only use this for trusted HTML. */
  // eslint-disable-next-line @angular-eslint/no-input-rename
  @Input('ips-html-content')
  set contentHtml(content: string) {
    this.html = this.sanitizer.bypassSecurityTrustHtml(
      this.linkyPipe.transform(content)
    );
  }

  @Input()
  handleNavigation = false;

  @HostBinding('innerHTML')
  html!: SafeHtml;

  constructor(
    private settings: CoreSettings,
    private resourceService: ResourceService,
    private navController: NavController,
    private linkyPipe: LinkyPipe,
    private sanitizer: DomSanitizer,
    private elementRef: ElementRef,
  ) { }

  // SR: Deze functie maakt het combineren van dit component en TrustHtmlComponent lastig.
  // https://angular.io/guide/roadmap#support-adding-directives-to-host-elements
  // Alternatief zou wellicht een InectionToken multi: true om aan deze hostListener functies te kunnen hangen op applicatie niveau?.
  @HostListener('click', ['$event'])
  click(event: Event) {
    if (event.target instanceof HTMLAnchorElement) {
      const baseUrl = this.settings.baseUrl;
      const isApplicationLink = event.target.href.startsWith(baseUrl);

      if (isApplicationLink) {
        event.preventDefault();
        event.stopImmediatePropagation();

        const url = event.target.href.replace(baseUrl, '');

        const segments = url.split('/');
        this.navController.navigateForward(segments);
      }
    }
  }

  ngAfterViewInit() {
    const el = this.elementRef.nativeElement;

    if (el instanceof HTMLElement) {
      this.setImageAccessToken(el);
    }
  }

  private setImageAccessToken(element: HTMLElement) {
    this.resourceService.setImageAccessToken(element.querySelectorAll('img[src]'));
  }
}
