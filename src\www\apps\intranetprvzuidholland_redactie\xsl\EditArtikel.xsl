<?xml version="1.0" encoding="utf-8"?>
<xsl:stylesheet xmlns:xsl="http://www.w3.org/1999/XSL/Transform" version="1.0" xmlns:resources="urn:resources" extension-element-prefixes="resources">

  <xsl:import href="include/StartPage.xsl"/>

  <xsl:param name="RePublishArtikel.done"/>

  <!-- abstract variables -->
  <xsl:variable name="done" select="$RePublishArtikel.done" />
  <xsl:variable name="pagetype">artikel</xsl:variable>

  <xsl:variable name="handler" select="concat('RePublish', translate(substring($pagetype, 1, 1), 'qwertyuiopasdfghjklzxcvbnm', 'QWERTYUIOPASDFGHJKLZXCVBNM'), substring($pagetype, 2, 10000))" />

  <xsl:template match="*" mode="body_inside_form">
    <xsl:if test="string($error = '')">
      <xsl:call-template name="editor"/>
      <form enctype="multipart/form-data">
        <xsl:apply-templates select="." mode="form_lib">
          <xsl:with-param name="form_AppIdt" select="$exitAppIdt"/>
        </xsl:apply-templates>
        <input type="hidden" name="{$handler}.$action" value="run"/>
        <input type="hidden" name="{$handler}.$id" value="{/data/site/item/@ItmIdt}"/>

        <xsl:apply-templates select="/data/site/item/page//clusterdefinition[Nam='Meta']/*/veld[DefNam = 'Dit is een kleine wijziging' and site]" mode="minor-change" />

        <table class="list">
          <xsl:call-template name="startpage-velden" />
        </table>
      </form>
    </xsl:if>
  </xsl:template>

  <xsl:template name="startpage-velden">
    <xsl:call-template name="veld">
      <xsl:with-param name="Nam" select="resources:GetText('labels', 'Titel')"/>
      <xsl:with-param name="curFieldName" select="concat($handler, '.Titel')"/>
      <xsl:with-param name="curClusterName" select="$handler"/>
      <xsl:with-param name="GgvTyp" select="'2'"/>
      <xsl:with-param name="Req" select="'1'"/>
      <xsl:with-param name="Wrd" select="/data/site/item/page/Tit"/>
    </xsl:call-template>

    <xsl:variable name="meta-velden" select="/data/site/item/page//clusterdefinition[Nam='Meta']/*/veld[site]" />
    <xsl:apply-templates select="$meta-velden[Nam = 'Samenvatting']" mode="startpage-veld" />
    <xsl:apply-templates select="$meta-velden[Nam = 'Afbeelding voor index']" mode="startpage-veld" />

    <xsl:variable name="inhoud-velden" select="/data/site/item/page//clusterdefinition[Nam='Artikel']/*/veld[site]" />
    <xsl:apply-templates select="$inhoud-velden[Nam = 'Inleiding']" mode="startpage-veld" />
    <xsl:apply-templates select="$inhoud-velden[Nam = 'Inhoud']" mode="startpage-veld" />

  </xsl:template>


  <xsl:template match="veld" mode="startpage-veld">
    <xsl:param name="Nam" select="translate(DefNam, ' ,/', '___')" />
    <xsl:call-template name="veld">
      <xsl:with-param name="definition" select="."/>
      <xsl:with-param name="curFieldName" select="concat($handler, '.', $Nam)"/>
      <xsl:with-param name="curClusterName" select="concat($handler, '_', $Nam)"/>
      <xsl:with-param name="curPath" select="concat('temp/', $User.GebIdt)"/>
      <xsl:with-param name="curSitIdt" select="$SitIdt"/>
      <xsl:with-param name="this" select="/data/site/item/page//veld[@VldDefIdt=current()/@VldDefIdt]"/>
    </xsl:call-template>
  </xsl:template>

</xsl:stylesheet>