import { Injectable } from '@angular/core';

import { AuthStatus } from '../base/auth-status';
import { AuthenticationService } from '../base/authentication.service';

@Injectable()
export class ActiveDirectoryAuthenticationService extends AuthenticationService {
  constructor() {
    super();
    this.auth = AuthStatus.Authorized;
  }

  // eslint-disable-next-line @typescript-eslint/no-empty-function
  login(): void { }

  // eslint-disable-next-line @typescript-eslint/no-empty-function
  logout(): void { }
}
