import { Injectable } from '@angular/core';

import { BehaviorSubject, EMPTY, Observable, of } from 'rxjs';
import { distinctUntilChanged, filter, map, switchMap, tap } from 'rxjs/operators';

import { AuthStatus } from '../../authentication/base/auth-status';
import { AuthenticationService } from '../../authentication/base/authentication.service';
import { User } from '../models/user.model';
import { SocialHttpClient } from './api-client';
import { UserFactory } from './user.factory';

@Injectable()
export class UserService {
  private user = new BehaviorSubject<User | undefined>(undefined);

  public user$ = this.user.asObservable();

  /**
   * @return An `Observable` with the user, only emits when the user is defined.
   */
  public currentUser$ = this.user$
    .pipe(
      filter((user): user is User => user !== undefined)
    );

  constructor(
    private socialApi: SocialHttpClient,
    private userFactory: UserFactory,
    private authenticationService: AuthenticationService,
  ) {
    authenticationService.auth$
      .pipe(
        distinctUntilChanged(),
        switchMap(status => {
          if (status === AuthStatus.Authorized) {
            this.authenticationService.auth = AuthStatus.FetchingUser;
            return this.getUser()
              .pipe(
                tap(user => this.user.next(user)),
                tap(() => this.authenticationService.auth = AuthStatus.Authenticated),
              );
          }

          if (status === AuthStatus.UnAuthenticated) {
            return of(undefined);
          }

          return EMPTY;
        })
        // TODO: met shareReplay kan dit de BehaviorSubject vervangen
      )
      .subscribe();
  }

  currentUser(): User | undefined {
    return this.user.value;
  }

  private getUser(): Observable<User> {
    return this.socialApi.get('user')
      .pipe(
        // TODO: Type this!
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        map((res: any) => this.userFactory.create(res))
      );
  }
}
