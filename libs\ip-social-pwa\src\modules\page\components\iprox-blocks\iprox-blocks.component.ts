import { ChangeDetectionStrategy, Component, Input } from '@angular/core';

import { IIproxBlock } from '../../models/iprox-block.model';

@Component({
  selector: 'ips-iprox-blocks',
  templateUrl: './iprox-blocks.component.html',
  styleUrls: ['./iprox-blocks.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class IproxBlocksComponent {
  @Input()
  blocks: IIproxBlock[] = [];
}
