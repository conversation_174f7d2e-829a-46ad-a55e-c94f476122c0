import template from '!html-loader?minimize=true&conservativeCollapse=false&collapseInlineTagWhitespace=true!./BlogAuthors.html';

(function (angular) {
  'use strict';

  angular.module('Intranet.Blogs')
    .run(['$templateCache', function ($templateCache) {
      $templateCache.put('Blogs.Authors', template);
    }])
    .component('ipsBlogAuthors', {
      bindings: {
        authors: '<',
        editMode: '<',
        updateAuthors: <AUTHORS>
        owner: '<'
      },
      templateUrl: 'Blogs.Authors',
      controller: ['Settings', function (Settings) {
        var self = this;

        self.$onInit = function () {
          self.settings = Settings.get('BlogAuthors');
        };

        self.addAuthor = function (author) {
          self.updateAuthors({ authors: self.authors.concat([author]) });
        };

        self.removeAuthor = function (author) {
          self.updateAuthors({ authors: self.authors.filter(function (a) { return a.id !== author.id; }) });
        };

        self.$onChanges = function () {
          self.exclude = self.authors.concat([self.owner]);
        };
      }],
      controllerAs: 'BlogAuthorsCtrl'
    });
})(angular);
