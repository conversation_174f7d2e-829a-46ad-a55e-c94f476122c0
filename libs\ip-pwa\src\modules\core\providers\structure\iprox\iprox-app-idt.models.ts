export interface ApiNav {
  parent: unknown[];
  nav: NavigationItem[];
}

/* eslint-disable @typescript-eslint/naming-convention */
export interface NavigationItem {
  SitIdt: string;
  SitItmIdt: string;
  SitParIdt: string;
  ItmTyp: string;
  ItmIdt: string;
  pagetype: string;
  Lbl: string;
  hidden: string;
  VlgNum: string;
  Lev: string;
  Url: string;
  resolved: string;
  relUrl: string;
  item?: NavigationItem[];
}
