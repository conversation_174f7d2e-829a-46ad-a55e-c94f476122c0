<project name="IPROX-CMS build" default="all" xmlns="http://nant.sf.net/release/0.85/nant.xsd">

  <target name="webconfigtransform">
    <xmllist property="build.transforms" file="${base.dir}\src\project.xml" delim="," xpath="/project/webconfigtransform[@type='build']/@alias" />

    <foreach item="String" in="${build.transforms}" delim="," property="transformalias">
      <xmlpeek file="${base.dir}\src\project.xml" xpath="/project/webconfigtransform[@type='build' and @alias='${transformalias}']/@src" property="currenttransform_src" failonerror="true" />
      <xmlpeek file="${base.dir}\src\project.xml" xpath="/project/webconfigtransform[@type='build' and @alias='${transformalias}']/@with" property="currenttransform_with" failonerror="true" />
      <xmlpeek file="${base.dir}\src\project.xml" xpath="/project/webconfigtransform[@type='build' and @alias='${transformalias}']/@dest" property="currenttransform_dest" failonerror="true" />

      <property name="src" value="${build.dir}${currenttransform_src}" />
      <property name="with" value="${base.dir}\src\${currenttransform_with}" />
      <property name="dest" value="${build.dir}${currenttransform_dest}" />

      <echo message="Transforming web.config with following settings : ${src} | ${with} | ${dest}" />

      <choose>
        <when test="${file::exists(src) and file::exists(with)}">
          <echo message="Applying Web.config transformation to ${dest}." />
          <exec program="${tools.dir}\webconfig-transform\WebConfigTransformRunner.exe">
            <arg line="${src}" />
            <arg line="${with}" />
            <arg line="${dest}" />
          </exec>
        </when>
      </choose>
    </foreach>
  </target>

  <target name="all" depends="webconfigtransform" />

</project>
