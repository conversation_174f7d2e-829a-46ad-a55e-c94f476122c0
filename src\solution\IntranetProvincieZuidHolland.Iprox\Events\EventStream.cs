﻿namespace IntranetProvincieZuidHolland.Iprox.Events {
  using System;
  using System.Collections.Generic;
  using System.Linq;

  using InfoProjects.Dxe.Linq;
  using InfoProjects.Dxe.Sql;
  using InfoProjects.Dxe.Util;
  using InfoProjects.Iprox.Events;

  using IntranetProvincieZuidHolland.Iprox.Events.EventModel;

  using Newtonsoft.Json;
  using RestSharp;

  /// <summary>
  /// Generator of event stream for published items.
  /// </summary>
  internal static class EventStream {
    /// <summary>
    /// Pagetypes in/under group that require events, combined with their index page container (if exists)
    /// </summary>
    private static readonly Dictionary<string, string> GroupPagetypes = new()
    {
      { "artikel", "index" },
      { "evenement", "evenementenagenda" },
      { "faq", "faqindex" },
      { "opiniepeiling", null },
      { "workgroup", null },
    };

    /// <summary>
    /// The workgroup pagetype
    /// </summary>
    private static readonly RobustLazy<Pagetype> GroupPagetype =
      new(() => Pagetypes.Value.Values.Single(p => p.Alias == "workgroup"));

    /// <summary>
    /// Pagetypes in/under group and under index-like item that require events.
    /// </summary>
    private static readonly RobustLazy<Dictionary<string, Pagetype>> IndexedGroupPagetypes =
      new(
        () => {
          return (from indexed in GroupPagetypes.Where(p => p.Value != null)
            join kvp in Pagetypes.Value on indexed.Value equals kvp.Value.Alias
            select new { indexed.Key, Pagetype = kvp.Value }).ToDictionary(o => o.Key, o => o.Pagetype);
        });

    /// <summary>
    /// The pagetype info.
    /// </summary>
    private static readonly RobustLazy<Dictionary<int, Pagetype>> Pagetypes =
      new(
        () => {
          using (var sql = SqlConnection.GetSqlConnection()) {
            var query = sql.GetSqlWriter();
            query.AddTable("PagTypTab");
            query.AddCondition("ProTyp", 0);
            query.AddField("PagTypIdt");
            query.AddField("Aka");
            query.AddField("Nam");
            return query.GetRawRows()
              .ToDictionary(
                row => row.Get<int>("PagTypIdt"),
                row =>
                new Pagetype {
                  Id = row.Get<int>("PagTypIdt"),
                  Alias = row.Get<string>("Aka"),
                  Name = row.Get<string>("Nam")
                });
          }
        });

    /// <summary>
    /// The "Dit is een kleine wijziging" field id.
    /// </summary>
    private static readonly RobustLazy<int> MinorChangeFieldId = new(
      () => {
        using (var sql = SqlConnection.GetSqlConnection()) {
          return
            sql.DetermineFieldValue(
              "VldDefTab",
              "VldDefIdt",
              "Nam",
              "Dit is een kleine wijziging").To<int>();
        }
      });

    private static bool useMinorChangeCheck = false;

    /// <summary>
    /// Initializes class.
    /// </summary>
    public static void Init() {
      PublicationEvents.ItemAdded +=
        (_, e) => e.Context.Transaction.AfterCommit += sql => SendPublicationEvent(e.NewInfo, true, sql);
      PublicationEvents.ItemChanged += (_, e) => {
        if (e.IsPublicationEvent) {
          e.Context.Transaction.AfterCommit += sql => SendPublicationEvent(e.NewInfo, false, sql);
        }
      };
    }

    /// <summary>
    /// Sends a publication event (if necessary)
    /// </summary>
    /// <param name="item">
    /// The published item.
    /// </param>
    /// <param name="added">
    /// Whether the item was added (e.g. previously not published)
    /// </param>
    /// <param name="sql">
    /// The SQL connection.
    /// </param>
    private static void SendPublicationEvent(StructureInfo item, bool added, SqlConnection sql) {
      if (!item.PagTypIdt.HasValue
          || !Pagetypes.Value.TryGetValue(item.PagTypIdt.Value, out var pagetype)
          || !GroupPagetypes.ContainsKey(pagetype.Alias)) {
        Logger.Debug("Cancel SendPublicationEvent for pagetype with ID {0}", item.PagTypIdt.HasValue ? item.PagTypIdt.Value.ToString() : "no pagetype id");
        return;
      }

      // Query the data
      var query = sql.GetSqlWriter();
      Action<string> addItemFields = prefix => {
        query.AddField(prefix, "ItmIdt", prefix + "ItmIdt");
        query.AddField(prefix, "PagTypIdt", prefix + "PagTypIdt");
        query.AddField(prefix, "Lbl", prefix + "Lbl");
        query.AddField(prefix, "LstPubDtm", prefix + "LstPubDtm");
        query.AddField(prefix, "LstPubTyd", prefix + "LstPubTyd");
      };
      query.AddTable("ItmTab", "Itm");
      query.AddCondition("Itm.ItmIdt", item.ItmIdt);
      addItemFields("Itm");
      query.AddTable("GebTab", "Geb", JoinType.INNER, "Itm.LstPubGebIdt = Geb.GebIdt");
      query.AddField("Geb", "Log", "GebLog");
      query.AddField("Geb", "Dmn", "GebDmn");
      query.AddField("Geb", "Nam", "GebNam");
      if (useMinorChangeCheck) {
        query.AddTable("ItmPagVldVew", "ItmPagVld", JoinType.LEFT, "Itm.ItmIdt = ItmPagVld.ItmIdt");
        query.AddCondition("ItmPagVld.VldDefIdt", MinorChangeFieldId.Value);
        query.AddField("ItmPagVld", "Wrd", "MinorChange");
      }

      var groupPrefix = "Itm";
      var descendantPrefix = "Itm";

      Logger.Debug("Check item for eventstream {0}", item.ItmIdt);

      if (IndexedGroupPagetypes.Value.TryGetValue(pagetype.Alias, out var indexPagetype)) {
        descendantPrefix = "Idx";
        query.AddTable("AncDscTab", "IdxAncDsc", JoinType.INNER, "Itm.ItmIdt = IdxAncDsc.NarItmIdt");
        query.AddTable("ItmTab", "Idx", JoinType.INNER, "IdxAncDsc.VanItmIdt = Idx.ItmIdt");
        query.AddCondition("Idx.PagTypIdt", indexPagetype.Id);
        addItemFields("Idx");
      }

      // we dont want to send a notification when a group is first published
      if (pagetype.Alias == "workgroup" && added) {
        Logger.Debug("Cancel SendPublicationEvent on first publication of workgroup");
        return;
      }

      // we dont want to send a notification when a opiniepeiling is published after changes
      if (pagetype.Alias == "opiniepeiling" && !added) {
        Logger.Debug("Cancel SendPublicationEvent on publication of opiniepeiling changes");
        return;
      }

      if (pagetype.Alias != "workgroup") {
        groupPrefix = "Grp";
        query.AddTable("AncDscTab", "GrpAncDsc", JoinType.INNER, descendantPrefix + ".ItmIdt = GrpAncDsc.NarItmIdt");
        query.AddTable("ItmTab", "Grp", JoinType.INNER, "GrpAncDsc.VanItmIdt = Grp.ItmIdt");
        query.AddCondition("Grp.PagTypIdt", GroupPagetype.Value.Id);
        addItemFields("Grp");
      }

      var row = query.GetFirstRowOrDefault();
      if (row == null) {
        return;
      }

      if (useMinorChangeCheck && row.Get<string>("MinorChange") == "1") {
        Logger.Debug("Cancel SendPublicationEvent because MinorChange is true");
        return;
      }

      Func<string, Item> extractItem =
        prefix =>
        new Item {
          Id = row.Get<int>(prefix + "ItmIdt"),
          Label = row[prefix + "Lbl"],
          LatestPublicationDateTime =
            DateTransformer.ToDateTime(row[prefix + "LstPubDtm"], row[prefix + "LstPubTyd"]),
          Pagetype = Pagetypes.Value[row.Get<int>(prefix + "PagTypIdt")]
        };

      // Collect all useful data
      var groupItemPublishEvent = new GroupItemPublishEvent {
        Added = added,
        GroupItem = extractItem(groupPrefix),
        IndexItem =
          row.Keys().Contains("IdxItmIdt")
            ? extractItem("Idx")
            : null,
        PublishedItem = extractItem("Itm"),
        User =
          new User {
            Domain = row["GebDmn"],
            Login = row["GebLog"],
            Name = row["GebNam"]
          }
      };

      Logger.Debug("Sending event for {0}", groupItemPublishEvent);
      Logger.Debug("Event JSON = {0}", JsonConvert.SerializeObject(groupItemPublishEvent));

      // Now put it in the minimal model that MVC wants to receive, missing out on many useful things
      var iproxGroupChangeModel = new IproxGroupChangeModel {
        Action = groupItemPublishEvent.PublishedItem.Pagetype.Alias,
        Data = groupItemPublishEvent.PublishedItem.Label,
        LoginName = groupItemPublishEvent.User.Login
      };

      const string iproxActivityPath = "Api/Public/Group/{0}/IproxActivity";
      var request =
        new RestRequest(string.Format(iproxActivityPath, groupItemPublishEvent.GroupItem.Id), Method.Post)
          .AddJsonBody(iproxGroupChangeModel);
      var response = new RestClient(Context.DefaultContext["SocialApiUrl"]).Execute(request);

      if ((int)response.StatusCode / 100 == 2) {
        Logger.Debug("Social IproxActivity for {0} yielded {1} {2}",
          groupItemPublishEvent.PublishedItem.Id,
          response.StatusCode,
          response.Content);
      }
      else {
        Logger.Error("Social IproxActivity for {0} yielded {1} {2}",
          groupItemPublishEvent.PublishedItem.Id,
          response.StatusCode,
          response.Content);
      }
    }
  }
}
