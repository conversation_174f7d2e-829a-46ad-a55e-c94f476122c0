import { Injectable } from '@angular/core';

import { Action, createSelector, State, StateContext, StateOperator, StateToken, Store } from '@ngxs/store';
import { insertItem, patch, updateItem } from '@ngxs/store/operators';
import { Subject } from 'rxjs';
import { bufferTime, filter, mergeMap } from 'rxjs/operators';

import { ICachedPerson } from '../models/internal/cached-person.model';
import { IPerson } from '../models/person.model';
import { PersonApiService } from '../services/person-api.service';
import { Persons } from './persons.actions';

export const PERSONS_STATE_TOKEN = new StateToken<IPersonsStateModel>('persons');

export interface IPersonsStateModel {
  persons: ICachedPerson[];
}

@State({
  name: PERSONS_STATE_TOKEN,
  defaults: {
    persons: []
  }
})
@Injectable()
export class PersonsState {
  private readonly EXPIRE_TIME = 10 * 60 * 1000; // 10 minutes

  private readonly requestBuffer = new Subject<string>();

  constructor(private store: Store, private personApi: PersonApiService) {
    this.requestListener();
  }

  @Action(Persons.PreCache)
  refresh({ setState }: StateContext<IPersonsStateModel>, action: Persons.PreCache) {
    setState(this.cachePersons(action.persons));
  }

  @Action(Persons.Request)
  request({ getState, setState }: StateContext<IPersonsStateModel>, action: Persons.Request) {
    const cachedPerson = getState().persons.find(p => p.id === action.id);

    setState(
      patch({
        persons: cachedPerson
          ? updateItem<ICachedPerson>(
            person => person?.id === action.id,
            person => ({ ...person, fetching: true }))
          : insertItem({ id: action.id, fetching: true })
      })
    );

    this.requestBuffer.next(action.id);
  }

  @Action(Persons.Cache)
  fetchedPersons({ setState }: StateContext<IPersonsStateModel>, action: Persons.Cache) {
    setState(this.cachePersons(action.persons));
  }

  person(id: string) {
    return createSelector([PersonsState], (state: IPersonsStateModel): ICachedPerson | undefined => state.persons.find(p => p.id === id));
  }

  private requestListener() {
    this.requestBuffer
      .pipe(
        bufferTime(250),
        filter(ids => ids.length > 0),
        mergeMap(ids => this.personApi.get(ids))
      )
      .subscribe(persons => this.store.dispatch(new Persons.Cache(persons)));
  }

  private cachePersons(persons: IPerson[]): StateOperator<IPersonsStateModel> {
    return state => {
      const newState = { ...state };
      const ids = persons.map(p => p.id);
      const now = new Date().getTime();

      newState.persons = [
        ...state.persons.filter(p => !ids.includes(p.id)).filter(p => this.isExpired(p, now) === false),
        ...persons.map(p => ({
          id: p.id,
          fetching: false,
          timestamp: now,
          person: p
        }))
      ];
      return newState;
    };
  }

  private isExpired(cachedPerson: ICachedPerson, now: number = new Date().getTime()): boolean {
    return cachedPerson.timestamp !== undefined && cachedPerson.timestamp < now - this.EXPIRE_TIME;
  }
}
