.rij {
  .error-message {
    background-color: $pzh-yellow;
    border-radius: $pzh-border-radius;
    padding: 0.875rem 1rem;
  }

  &.selectie_type_multiselect .antwoorden, // sass-lint:disable-line class-name-format
  &.selectie_type_dropdown .antwoorden, // sass-lint:disable-line class-name-format
  input[type="text"],
  input[type="file"],
  .ui-datepicker-trigger,
  textarea {
    &:not([hidden]) {
      display: flex;
      position: relative;
      z-index: 1;
    }

    + .error-message {
      border-top-left-radius: 0;
      border-top-right-radius: 0;
      margin-top: 0;
      position: relative;
      top: -4px;
    }
  }

  .deletefile {
    bottom: auto;
    height: $pzh-form-file-delete-button-size;
    right: 0;
    top: 0;
  }

  input[type="text"],
  input[type="file"],
  textarea {
    &::placeholder {
      color: $pzh-placeholder-text-color;
      opacity: 1;
    }
  }

  input[type="radio"],
  input[type="checkbox"] {
    &:focus + label::before {
      color: $pzh-button-focus-outline-color;
    }

    &[checked] {
      &:focus + label::before {
        background-color: $pzh-light-green;
        color: $pzh-button-checked-focus-outline-color;
      }
    }
  }
}

.ui-datepicker {
  box-shadow: 0 0 2px rgba(0, 0, 0, 0.3), 0 0 12px rgba(0, 0, 0, 0.2);
}

.checkform {
  .uitkomst {
    font-weight: 500;
    margin-bottom: 2rem;

    .antwoorden {
      ul,
      ol {
        padding-left: 2rem;
      }
    }
  }

  .iprox-content {
    font-style: italic;
  }
}

.type-formulier {
  .terug {
    @include secondary-button;
    box-shadow: inset 0 0 0 1px $pzh-border-color;
    display: inline-block;
    margin-bottom: 2rem;
  }
}

.type-foutmelding {
  .incorrectmessage {
    border-radius: $pzh-border-radius;
    font-size: 0.875em;
    padding: $bl-input-padding;
  }
}

// duet datepicker
.duet-date__toggle {
  box-shadow: none;
}

.duet-date__table-header {
  padding: 0.57rem 0;
}

.duet-date__select {
  margin-top: 0;
}

.duet-date__select-label {
  font-size: 1rem;
}

.zoekenmylex {
  .z-facet,
  .z-content {
    .knoppen button {
      background-color: $bl-button-background-color;
      border: none;
      color: $bl-button-color;
      padding: $bl-button-padding;
      
      &:hover {
        background-color: $bl-button-hover-background-color;
      }
    }
  }

  .z-facet {
    button.pzh-more-facets {
      background-color: transparent;
      border: none;
      text-align: right;
      width: 100%;

      &::before {
        content: "➕";
        display: inline-block;
        font-size: 0.75rem;
        margin-right: 0.25rem;
      }
    }
  }
}
