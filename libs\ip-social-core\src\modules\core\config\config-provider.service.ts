import { Inject, Injectable, InjectionToken } from '@angular/core';

import { ISocialConfig } from './config.model';

export const SOCIAL_CONFIG = new InjectionToken<ISocialConfig>('SOCIAL_CONFIG');

@Injectable()
export class SocialConfigProvider {
  private cache = new Map<string, Record<string, unknown>>();

  constructor(@Inject(SOCIAL_CONFIG) config: ISocialConfig[]) {
    config.forEach(c => Object.entries(c).forEach(([key, moduleConfig]) => this.cache.set(key, moduleConfig)));
  }

  public get<T>(module: string): T | undefined {
    return this.cache.get(module) as T;
  }
}
