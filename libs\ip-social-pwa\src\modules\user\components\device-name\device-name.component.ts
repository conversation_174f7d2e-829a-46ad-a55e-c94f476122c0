import { Component, Input, OnD<PERSON>roy, OnInit } from '@angular/core';

import { DeviceSettingsService } from '@ip/social-core';
import { Subject } from 'rxjs';
import { debounceTime, switchMap, takeUntil } from 'rxjs/operators';

@Component({
  selector: 'ips-device-name',
  templateUrl: './device-name.component.html',
})
export class DeviceNameComponent implements OnInit, OnDestroy {
  private destroyed = new Subject<void>();

  @Input()
  name?: string;

  subject = new Subject<string>();

  constructor(public deviceSettingsService: DeviceSettingsService) { }

  ngOnInit() {
    this.updateNameChanges();
  }

  updateNameChanges(): void {
    this.subject.pipe(
      takeUntil(this.destroyed),
      debounceTime(300),
      switchMap(name => this.deviceSettingsService.updateDeviceName(name))
    )
      .subscribe();
  }

  ngOnDestroy() {
    this.destroyed.next();
    this.destroyed.complete();
  }
}
