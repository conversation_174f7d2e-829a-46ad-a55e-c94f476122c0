import { Injectable } from '@angular/core';
import { FormControl, FormGroup } from '@angular/forms';

import { CoreSettings, FileTypes, FileUploader, FileValidators, SocialHttpClient } from '@ip/social-core';
import { EMPTY, merge, OperatorFunction, ReplaySubject, Subscription } from 'rxjs';
import { debounceTime, distinctUntilChanged, filter, map, mergeAll, mergeMap, switchMap, tap, withLatestFrom } from 'rxjs/operators';

import { TinymceImageUploadHandler, TinymceService } from '../../forms/services/tinymce.service';
import { FileProgressService, isUploadComplete } from '@ip/social-core';
import { IBlog } from '../models';
import { BlogApi } from '../models/internal/api.models';
import { BlogApiService } from './blog-api.service';

@Injectable()
export class BlogService {
  private subscription?: Subscription;

  private readonly blog = new ReplaySubject<IBlog>(1);

  blog$ = this.blog.asObservable();

  constructor(
    private blogApiService: BlogApiService,
    private socialApi: SocialHttpClient,
    private tinymceService: TinymceService,
    private settings: CoreSettings,
    private fileUploadService: FileProgressService,
  ) { }

  init(blogId: string) {
    return this.blogApiService.get(blogId)
      .pipe(
        tap(blog => this.blog.next(blog)),
        map(blog => ({
          formGroup: this.createForm(blog),
          editorConfig: this.createTinymceConfig(blog.id)
        })),
        tap(({ formGroup }) => this.changeListener(formGroup, blogId))
      );
  }

  publish(blogId: string) {
    return this.blogApiService.publish(blogId)
      .pipe(
        tap(blog => this.blog.next(blog))
      );
  }

  remove(blogId: string) {
    return this.blogApiService.remove(blogId);
  }

  removeImage(blogId: string) {
    return this.blogApiService.removeImage(blogId)
      .pipe(
        this.updateState(() => ({ image: null }))
      );
  }

  destroy() {
    this.subscription?.unsubscribe();
  }

  private changeListener(formGroup: FormGroup, blogId: string) {
    this.subscription?.unsubscribe();

    const fieldChanges = Object.entries(formGroup.controls)
      .map(([name, control]) => {
        const observable$ = control.valueChanges;

        switch (name) {
          case 'workingTitle':
            return observable$.pipe(
              debounceTime(400),
              switchMap((value: string) => this.blogApiService.updateWorkingTitle(blogId, value)),
              tap(blog => this.blog.next(blog)),
            );

          case 'workingBody':
            return observable$.pipe(
              debounceTime(400),
              distinctUntilChanged(),
              filter((value: string) => value.match(/src="data:image\/.*;base64/) === null),
              switchMap((value: string) => this.blogApiService.updateWorkingBody(blogId, value)),
              tap(blog => this.blog.next(blog)),
            );

          case 'image':
            return observable$.pipe(
              debounceTime(400),
              filter(() => formGroup.get('image')?.valid === true),
              switchMap((value: FileList) => value[0]
                ? this.blogApiService.updateImage(blogId, value[0])
                : EMPTY
              ),
              map(response => response.fileName),
              this.updateState(fileName => ({ image: fileName })),
            );

          case 'authors':
            return observable$.pipe(
              switchMap((value: string[]) => this.blogApiService.updateAuthors(blogId, value)),
              tap(blog => this.blog.next(blog)),
            );

          default:
            return observable$.pipe(tap(({ value }) => console.log('UNIMPLEMENTED', value)));
        }
      });

    this.subscription = merge(fieldChanges)
      .pipe(mergeAll())
      .subscribe();
  }

  private updateState<T, R>(fn: (value: T) => R, thisArg?: unknown): OperatorFunction<T, T> {
    return source => source.pipe(
      withLatestFrom(this.blog$),
      tap(([payload, blog]) => {
        const newBlog = fn.call(thisArg, payload);

        this.blog.next({ ...blog, ...newBlog });
      }),
      map(([payload]) => payload),
    );
  }

  private createForm(blog: IBlog) {
    return new FormGroup({
      workingTitle: new FormControl(blog.title),
      authors: new FormControl(blog.authorIds),
      image: new FormControl(null, [FileValidators.type(FileTypes.image)]),
      workingBody: new FormControl(blog.workingBody),
    });
  }

  private createTinymceConfig(blogId: string) {
    return this.tinymceService.createConfig({
      id: blogId,
      file: this.createFileUploader(blogId),
      image: this.createImageUploader(blogId),
      imageUploadHandler: this.imageUploadHandler(blogId),
    });
  }

  private imageUploadHandler(blogId: string): TinymceImageUploadHandler {
    return async (
      blobInfo: {
        filename: () => string;
        blob: () => Blob;
      },
      success: (location: string) => void,
      _failure: (error: string) => void,
      _progress: (progress: number) => void,
    ) => {
      const blob: Blob = blobInfo.blob();
      const file = new File([blob], blobInfo.filename());

      const location = await new Promise<string>((resolve, _reject) => {
        this.blogApiService.inlineImage(blogId, file)
          .pipe(
            mergeMap(() => this.fileUploadService.get$(`blog-${blogId}-content-image-${file.name}`)),
            filter(isUploadComplete)
          )
          .subscribe(upload => {
            const fileName = (upload.response as BlogApi.IUpdateImageResponse).fileName;

            resolve(this.createContentUrl(blogId, 'image', fileName));
          });
      });

      success(location);
    };
  }

  private createFileUploader(blogId: string) {
    return new FileUploader(
      this.socialApi,
      {
        id: blogId,
        uploadUrl: `blog/${blogId}/inlinefile`,
        responseFn: (response: { fileName: string }) => ({
          url: this.createContentUrl(blogId, 'file', response.fileName),
          fileName: response.fileName
        }),
        // SR: We don't report progress because the dialog is a blocking UX.
        // progressId: `blog-${blogId}-content-file`,
      }
    );
  }

  private createImageUploader(blogId: string) {
    return new FileUploader(
      this.socialApi,
      {
        id: blogId,
        uploadUrl: `blog/${blogId}/inlineimage`,
        responseFn: (response: { fileName: string }) => ({
          url: this.createContentUrl(blogId, 'image', response.fileName),
          fileName: response.fileName
        }),
        // SR: We don't report progress because the dialog is a blocking UX.
        // progressId: `blog-${blogId}-content-image`,
      }
    );
  }

  private createContentUrl(blogId: string, type: 'image' | 'file', fileName: string) {
    return `${this.settings.apiUrl}blog/${blogId}/${type}/${fileName}`;
  }
}
