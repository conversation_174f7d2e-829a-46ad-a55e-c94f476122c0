import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { RouterModule } from '@angular/router';

import { IonicModule } from '@ionic/angular';
import { MetaModule as SocialMetaModule } from '@ip/social-core';
import { UtilitiesModule } from '@ip/social-core';

import { LikeActionSheetComponent } from './components/like-action-sheet/like-action-sheet.component';
import { LikeComponent } from './components/like/like.component';
import { MetaComponent } from './components/meta/meta.component';
import { MostLikedPipe } from './pipes/most-liked.pipe';
import { LikeActionSheetService } from './services/like-action-sheet.service';

@NgModule({
  imports: [
    CommonModule,
    IonicModule,
    RouterModule,
    SocialMetaModule,
    UtilitiesModule,
  ],
  declarations: [
    LikeActionSheetComponent,
    LikeComponent,
    MetaComponent,
    MostLikedPipe,
  ],
  providers: [
    LikeActionSheetService,
  ],
  exports: [
    MetaComponent,
    SocialMetaModule,
  ]
})
export class MetaModule {
}
