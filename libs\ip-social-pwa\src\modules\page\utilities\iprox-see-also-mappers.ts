import { IIproxPhotoBlock } from '../models/iprox-photo-block.model';
import { IIproxContentBlock } from '../models/iprox-content-block.model';
import { IproxVerwijzingType, IIproxVerwijzing } from '../models/iprox-verwijzing.model';
import { IproxSeeAlsoMapperFn } from '../models/iprox-see-also-mapper-fn.model';

// eslint-disable-next-line @typescript-eslint/no-explicit-any
export const mapFoto = (foto: any): IIproxPhotoBlock => {
  return {
    type: 'foto',
    title: foto.foto?.algemeen?.titel,
    text: foto.foto?.algemeen?.tekst,
    image: foto.foto?.algemeen?.afbeelding
      ? {
        label: foto.foto.algemeen.afbeelding.label,
        url: foto.foto.algemeen.afbeelding.url,
      }
      : undefined
  };
};

// eslint-disable-next-line @typescript-eslint/no-explicit-any
export const mapInhoud = (inhoud: any): IIproxContentBlock => {
  return {
    type: 'inhoud',
    title: inhoud.blok?.algemeen?.titel,
    text: inhoud.blok?.algemeen?.tekst,
    image: inhoud.blok?.algemeen?.afbeelding
      ? {
        label: inhoud.blok.algemeen.afbeelding.label,
        url: inhoud.blok.algemeen.afbeelding.url,
      }
      : undefined,
    references: mapVerwijzing(inhoud.blok?.verwijzingen ?? []),
  };
};

// eslint-disable-next-line @typescript-eslint/no-explicit-any
export const mapReference = (verwijzing: any, type: IproxVerwijzingType): IIproxVerwijzing => {
  const keys = {
    internal: 'link',
    external: 'link',
    download: 'bestand',
  };

  return {
    type,
    url: verwijzing[keys[type]]?.url,
    label: verwijzing[keys[type]]?.label,
  };
};

// eslint-disable-next-line @typescript-eslint/no-explicit-any
export const mapVerwijzing = (verwijzingen: any): IIproxVerwijzing[] => {
  if (!Array.isArray(verwijzingen.verwijzing)) {
    return [];
  }

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  return verwijzingen.verwijzing.reduce((references: IIproxVerwijzing[], v: any): IIproxVerwijzing[] => {
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    const internLinks: any[] = Array.isArray(v['interne-verwijzing'])
      ? v['interne-verwijzing']
      : v['interne-verwijzing'] ? [v['interne-verwijzing']] : [];
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    const externLinks: any[] = Array.isArray(v['externe-verwijzing'])
      ? v['externe-verwijzing']
      : v['externe-verwijzing'] ? [v['externe-verwijzing']] : [];
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    const downloadLinks: any[] = Array.isArray(v['download'])
      ? v['download']
      : v['download'] ? [v['download']] : [];

    return [
      ...references,
      ...internLinks.map(l => mapReference(l, 'internal')),
      ...externLinks.map(l => mapReference(l, 'external')),
      ...downloadLinks.map(l => mapReference(l, 'download')),
    ];
  }, []);
};

// eslint-disable-next-line @typescript-eslint/no-explicit-any
export const defaultDataMapper: IproxSeeAlsoMapperFn = (seeAlso?: any): IIproxContentBlock[] => {
  const blokken = seeAlso?.blokken;

  if (Array.isArray(blokken)) {
    return blokken.reduce((blocks, b): IIproxContentBlock[] => {
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      const fotos: any[] = Array.isArray(b.foto)
        ? b.foto
        : b.foto ? [b.foto] : [];
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      const inhoud: any[] = Array.isArray(b.inhoud)
        ? b.inhoud
        : b.inhoud ? [b.inhoud] : [];

      return [
        ...blocks,
        ...inhoud.map(i => mapInhoud(i)),
        ...fotos.map(f => mapFoto(f)),
      ];
    }, []);
  }

  return [];
};
