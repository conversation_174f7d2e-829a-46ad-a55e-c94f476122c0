import { CommonModule } from '@angular/common';
import { ModuleWithProviders, NgModule } from '@angular/core';

import { IonicModule } from '@ionic/angular';

import { HeaderComponent } from './components/header/header.component';
import { HeaderService } from './services/header.service';

@NgModule({
  declarations: [
    HeaderComponent,
  ],
  imports: [
    CommonModule,
    IonicModule,
  ],
  providers: [
  ],
  exports: [
    HeaderComponent,
  ]
})
export class IpPwaHeaderModule {
  static forRoot(): ModuleWithProviders<IpPwaHeaderModule> {
    return {
      ngModule: IpPwaHeaderModule,
      providers: [
        HeaderService,
      ]
    };
  }
}
