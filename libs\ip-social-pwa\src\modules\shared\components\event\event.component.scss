@import "../../../../scss/mixins";

$ips-event-icon-size: 24px;

::ng-deep {
  dl {
    margin-bottom: 0;
    margin-top: 0;

    dt, dd {
      display: inline-block;
      vertical-align: top;
    }

    .ips-event-meta {
      dt {
        width: $ips-event-icon-size;

        ion-icon {
          font-size: $ips-event-icon-size;
          vertical-align: middle;
        }
      }

      dd {
        width: calc(100% - #{$ips-event-icon-size});
      }
    }

    .ips-event-data {
      dt {
        display: block;
        font-weight: 700;
      }

      dd {
        padding-left: 0;

        > div > *:first-child {
          margin-top: 0;
        }

        > div > *:last-child {
          margin-bottom: 0;
        }
      }
    }

    dd {
      margin-left: 0;
      padding-left: 16px;

      &:last-child {
        margin-bottom: 8px;
      }

      + dd {
        margin-left: $ips-event-icon-size;
      }
    }

    .ips-event-date-label {
      @include font-size(-1);

      display: block;
      font-style: italic;
    }
  }
}
