import { Component, ElementRef, Inject, OnInit } from '@angular/core';
import { downgradeComponent } from '@angular/upgrade/static';
import { BehaviorSubject, Observable } from 'rxjs';
import { debounceTime, distinctUntilChanged, filter, map } from 'rxjs/operators';

@Component({
  // tslint:disable-next-line: component-selector
  selector: 'pzh-social-mailer',
  templateUrl: './social-mailer.component.html',
  styleUrls: ['./social-mailer.component.scss']
})
export class SocialMailerComponent implements OnInit {
  private emailAddressAccumulator: BehaviorSubject<string[]>;

  groups: any[];

  selectedGroups = [];

  mode = 'allGroups';

  query = '';

  showEmailAddresses = false;

  emailAddresses = [];

  inProgress = false;

  constructor(private element: ElementRef, @Inject('GroupService') private groupService: any, @Inject('PersonResource') private personResource: any) { }

  ngOnInit() {
    this.groupService.getAllGroups().$promise
      .then(groups => this.groups = groups);
  }

  deselect(group: any) {
    this.emailAddresses = [];
    this.selectedGroups = this.selectedGroups.filter(g => g.iproxId !== group.iproxId);
  }

  select(event: any) {
    event.preventDefault();

    if (!this.selectedGroups.find(g => g.iproxId === event.iproxId)) {
      this.emailAddresses = [];
      this.selectedGroups = this.selectedGroups.concat([event.item]);
    }
  }

  generateEmailAddresses() {
    if (this.inProgress) {
      return;
    }

    this.inProgress = true;
    this.emailAddresses = [];
    const groups = this.mode === 'allGroups' ? this.groups : this.selectedGroups;

    const userIds: string[] = groups
      .reduce((users, group) => users.concat(group.members.filter(m => m.role === 'owner')), [])
      .reduce((personIds, user) => personIds.indexOf(user.personId) > -1 ? personIds : personIds.concat([user.personId]), []);

    const numberOfUsers = userIds.length;

    this.emailAddressAccumulator = new BehaviorSubject([]);
    this.emailAddressAccumulator
      .asObservable()
      .subscribe(
        fetchedAddresses => {
          this.emailAddresses = this.emailAddresses.concat(fetchedAddresses);
          if (userIds.length > 0) {
            this.getEmailAddresses(userIds.splice(0, 10));
            return;
          }

          if (this.emailAddresses.length === numberOfUsers) {
            this.emailAddressAccumulator.complete();
          }
        },
        undefined,
        () => {
          this.pasteEmailAddresses();
          this.inProgress = false;
        }
      );

    this.getEmailAddresses(userIds.splice(0, 10));
  }

  getEmailAddresses(ids: string[]) {
    this.personResource.getByIds({ id: ids }, model => {
      this.emailAddressAccumulator.next(model.map(p => p.email));
    });
  }

  pasteEmailAddresses() {
    const parentElement: HTMLElement = this.element.nativeElement.parentElement;
    const emailBucket: HTMLInputElement = parentElement.querySelector('#pzhSocialMailerTo > input');

    if (emailBucket) {
      emailBucket.value = this.emailAddresses.join(',');
    }
  }

  formatter = (group: any) => group.label;

  search = (text$: Observable<string>) => text$.pipe(
    debounceTime(200),
    distinctUntilChanged(),
    filter(term => term.length >= 2),
    map(term => this.groups.filter(group => group.label.toLowerCase().indexOf(term.toLowerCase()) > -1).slice(0, 10))
  );
}
