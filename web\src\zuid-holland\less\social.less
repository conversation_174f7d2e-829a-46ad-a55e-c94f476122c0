@import "./variables.less";
@import "./fonts.less";

@import "./bootstrap-overrides.less";

@import "./mylex.less";

@import "./controls/buttons.less";
@import "./controls/button-icons.less";
@import "./controls/button-mixins.less";
@import "./controls/forms.less";
@import "./controls/pagination.less";
@import "./components/anchors.less";
@import "./components/back-buttons.less";
@import "./components/blog.less";
@import "./components/blog-card.less";
@import "./components/calendar-event.less";
@import "./components/cards.less";
@import "./components/comments.less";
@import "./components/file.less";
@import "./components/meta.less";
@import "./components/not-found.less";
@import "./components/page-meta.less";
@import "./components/person-search.less";
@import "./components/post.less";
@import "./components/question.less";
@import "./components/rich-content.less";
@import "./components/skills.less";
@import "./components/user-alerts.less";
@import "./components/work-experience.less";
@import "./components/workdays.less";


@import "./group.less";
@import "./profile.less";

@import "./css-variables.less";

ips-person-finder {
  .grid-blok label {
    .sr-only();
  }

  .show-more-container a {
      font-weight: 500;
  }
}

.grid-blok.ips-pager.has-bgcolor {
  margin-bottom: 0;
}

.type-social-blog-aside h2 {
  font-size: 1.5rem;
}

.uib-datepicker {
  .btn.btn-default {
    &.btn-info {
      background-color: darken(@elementkleur, 10%);
    }

    .text-info {
      color: @pzh-white;
    }
  }
}

.ips-activities-controls {
  margin-top: @pzh-inner-padding;
}

ips-user-menu {
  .ips-menu {
    > .btn {
      &,
      i {
        color: @elementkleur !important;
      }

      &:hover,
      &:active {
        background-color: @pzh-green !important;
        color: @pzh-white !important;

        &,
        span,
        i {
          color: @pzh-white !important;
        }
      }
    }
  }
}

.ips-personal-account-switch span {
  display: none;
}

.bs-comment-item {
  .btn + .btn {
    margin-left: 4px;
  }
}
