import { ComponentFactoryResolver, Injectable } from '@angular/core';

import { TimelineContentFactory } from '../content-factory';
import { TimelineCommentComponent } from './comment.component';

@Injectable()
export class CommentFactory extends TimelineContentFactory {
  component = TimelineCommentComponent;

  type = 'comment';

  constructor(componentFactoryResolver: ComponentFactoryResolver) {
    super(componentFactoryResolver);
  }
}
