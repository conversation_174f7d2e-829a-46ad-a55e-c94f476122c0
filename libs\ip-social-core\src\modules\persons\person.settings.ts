import { Injectable } from '@angular/core';

import { SocialConfigProvider } from '../core/config/config-provider.service';
import { ISocialConfig } from '../core/config/config.model';
import { DayAbbreviation, DayPart } from './models';

export interface IWorkdaysSettings {
  weekdays: DayAbbreviation[];
  dayParts: DayPart[];
}

// TODO: Consider recursive Partial?
export interface IPersonConfig {
  workdays?: Partial<IWorkdaysSettings>;
}

export interface IPersonSettings {
  workdays: IWorkdaysSettings;
}

@Injectable()
export class PersonSettings implements IPersonSettings {
  private module: keyof ISocialConfig = 'person';

  workdays: IWorkdaysSettings = {
    weekdays: ['ma', 'di', 'wo', 'do', 'vr'],
    dayParts: ['1', '2'],
  };

  constructor(configProvider: SocialConfigProvider) {
    const config = configProvider.get<IPersonConfig>(this.module);

    if (!config) {
      return;
    }

    if (config.workdays) {
      this.workdays = Object.assign(this.workdays, config.workdays);
    }
  }
}
