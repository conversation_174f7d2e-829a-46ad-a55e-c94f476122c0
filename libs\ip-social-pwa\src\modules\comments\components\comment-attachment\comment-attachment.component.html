<div class="ips-attachment-progress" *ngIf="placeholder">
  <ion-spinner aria-hidden="true"></ion-spinner>
  <span class="ips-file-title">{{ placeholder.fileName }}</span>
  <ng-container *ngIf="progress$ | async as progress">
    <ion-progress-bar
      [id]="placeholder.fileId"
      [value]="(progress.progress / 100)"
    ></ion-progress-bar>
  </ng-container>
</div>

<ng-container *ngIf="file">
  <a
    target="_blank"
    [href]="file.file | attachmentDownloadHref"
    [ips-download-file]="file.file.fileName"
    [openInNewTab]="!showMetaData"
  >
    <img
      *ngIf="!showMetaData"
      alt="{{ file.file.fileName }} ({{ file.file.contentLength | fileSize }})"
      [ips-image-src]="file.file | attachmentDownloadHref : imageFormatSize"
    />
    <ng-container *ngIf="showMetaData">
      <ion-icon name="document" class="ips-file-icon"></ion-icon>
      <span class="ips-file-title">{{ file.file.fileName }}</span>
      <span class="ips-file-size"> {{ file.file.contentLength | fileSize }}</span>
    </ng-container>
    <span *ngIf="!showMetaData" class="sr-only">{{ 'comments.attachment.opensInNewWindow' | transloco }}</span>
  </a>
  <ion-button
    *ngIf="editMode"
    size="small"
    color="light"
    [attr.aria-label]="'attachment.delete' | transloco"
    (click)="remove.emit(file)"
  >
    <ion-icon name="close"></ion-icon>
  </ion-button>
</ng-container>
