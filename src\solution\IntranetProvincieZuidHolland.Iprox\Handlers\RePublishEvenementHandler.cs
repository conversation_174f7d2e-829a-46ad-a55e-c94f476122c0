﻿namespace IntranetProvincieZuidHolland.Iprox.Handlers {
  using System;
  using InfoProjects.Dxe.Linq;
  using InfoProjects.Dxe.Process;
  using InfoProjects.Dxe.Process.Handler;
  using InfoProjects.Dxe.Util;
  using InfoProjects.Iprox.Model;
  using IntranetProvincieZuidHolland.Iprox.Model;

  /// <summary>RePublishEvenement Handler</summary>
  public class RePublishEvenementHandler : IContextProcessHandler {
    /// <summary>RePublishEvenementHandler Handler</summary>
    /// <param name="unit">Process unit</param>
    /// <param name="context">Process context</param>
    /// <returns>Returns result</returns>
    public ResultProps Process(ProcessUnit unit, ProcessContext context) {
      string itmIdt = unit[Reserved.ID];
      if (!String.IsNullOrEmpty(itmIdt)) {
        Logger.Debug("Update item {0}", itmIdt);
        this.UpdateItem(unit, context, itmIdt);
      }

      return new ResultProps();
    }

    /// <summary>
    /// Update een item
    /// </summary>
    /// <param name="unit">ProcessUnit unit</param>
    /// <param name="context">ProcessContext context</param>
    /// <param name="itmIdt">string itmIdt</param>
    private void UpdateItem(ProcessUnit unit, ProcessContext context, string itmIdt) {
      using (var cms = new IproxCms()) {
        var evenement = cms.GetItem<Evenement>(itmIdt.To<int>(), false, ContentMode.Source);
        evenement.MarkPublished();

        this.UpdateEvenement(unit, context, evenement);

        cms.SubmitChanges(context);
      }
    }

    /// <summary>
    /// Bewerk bestaand artikel
    /// </summary>
    /// <param name="unit">ProcessUnit unit</param>
    /// <param name="context">ProcessContext context</param>
    /// <param name="evenement">Artikel model item</param>
    private void UpdateEvenement(ProcessUnit unit, ProcessContext context, Item<Evenement> evenement) {
      // Pagina titel en label
      if (unit.IsSet("Titel.Wrd")) {
        evenement.Title = unit["Titel.Wrd"];
        evenement.Page.Title = unit["Titel.Wrd"];
      }

      SharedRepublish.SetValueIfSupplied(unit, evenement.Page.Inhoud.Startdatum, "Startdatum");
      SharedRepublish.SetValueIfSupplied(unit, evenement.Page.Inhoud.Starttijd, "Starttijd");
      SharedRepublish.SetValueIfSupplied(unit, evenement.Page.Inhoud.Eindtijd, "Eindtijd");
      SharedRepublish.SetValueIfSupplied(unit, evenement.Page.Inhoud.Locatie, "Locatie");
      SharedRepublish.SetValueIfSupplied(unit, evenement.Page.Inhoud.Omschrijving, "Omschrijving");
      SharedRepublish.SetValueIfSupplied(unit, evenement.Page.Inhoud.Doelgroep, "Doelgroep");
      SharedRepublish.SetValueIfSupplied(unit, evenement.Page.Inhoud.MeerInformatie, "Meer_informatie");
      SharedRepublish.SetValueIfSupplied(unit, evenement.Page.Inhoud.Aanmelden, "Aanmelden");
    }
  }
}
