﻿namespace IntranetProvincieZuidHolland.Iprox.Handlers {
  using System;
  using System.Drawing;
  using System.IO;
  using InfoProjects.Dxe.Linq;
  using InfoProjects.Dxe.Process;
  using InfoProjects.Dxe.Process.Handler;
  using InfoProjects.Dxe.Util;
  using InfoProjects.Iprox.Model;
  using InfoProjects.Iprox.Model.Fields;
  using IntranetProvincieZuidHolland.Iprox.Model;

  /// <summary>RePublishFotoalbum Handler</summary>
  public class RePublishFotoalbumHandler : IContextProcessHandler {
    /// <summary>RePublishFotoalbumHandler Handler</summary>
    /// <param name="unit">Process unit</param>
    /// <param name="context">Process context</param>
    /// <returns>Returns result</returns>
    public ResultProps Process(ProcessUnit unit, ProcessContext context) {
      string itmIdt = unit[Reserved.ID];      
      if (!String.IsNullOrEmpty(itmIdt)) {        
        Logger.Debug("Update item {0}", itmIdt);
        this.UpdateItem(unit, context, itmIdt);
      }

      return new ResultProps();
    }

    /// <summary>
    /// Update een item
    /// </summary>
    /// <param name="unit">ProcessUnit unit</param>
    /// <param name="context">ProcessContext context</param>
    /// <param name="itmIdt">string itmIdt</param>
    private void UpdateItem(ProcessUnit unit, ProcessContext context, string itmIdt) {
      using (var cms = new IproxCms()) {
        var fotoalbum = cms.GetItem<Fotoalbum>(itmIdt.To<int>(), false, ContentMode.Source);
        fotoalbum.MarkPublished();

        this.AddFoto(unit, context, fotoalbum);

        cms.SubmitChanges(context);
      }
    }

    /// <summary>
    /// Voeg foto toe
    /// </summary>
    /// <param name="unit">ProcessUnit unit</param>
    /// <param name="context">ProcessContext context</param>
    /// <param name="fotoalbum">Fotoalbum model item</param>
    private void AddFoto(ProcessUnit unit, ProcessContext context, Item<Fotoalbum> fotoalbum) {        
      var foto = fotoalbum.Page.Foto.AddNew<Fotoalbum.FotoCluster>();
      SharedRepublish.SetValueIfSupplied(unit, foto.Titel, "Titel");
      SharedRepublish.SetValueIfSupplied(unit, foto.Beschrijving, "Beschrijving");
      SharedRepublish.SetValueIfSupplied(unit, foto.Foto, "Foto");
    }
  }
}
