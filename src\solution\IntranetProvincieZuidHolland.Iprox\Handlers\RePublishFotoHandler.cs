﻿namespace IntranetProvincieZuidHolland.Iprox.Handlers {
  using System;
  using System.Drawing;
  using System.IO;
  using InfoProjects.Dxe.Linq;
  using InfoProjects.Dxe.Process;
  using InfoProjects.Dxe.Process.Handler;
  using InfoProjects.Dxe.Util;
  using InfoProjects.Iprox.Model;
  using InfoProjects.Iprox.Model.Fields;
  using IntranetProvincieZuidHolland.Iprox.Model;

  /// <summary>RePublishFoto Handler</summary>
  public class RePublishFotoHandler : IContextProcessHandler {
    /// <summary>RePublishFotoHandler Handler</summary>
    /// <param name="unit">Process unit</param>
    /// <param name="context">Process context</param>
    /// <returns>Returns result</returns>
    public ResultProps Process(ProcessUnit unit, ProcessContext context) {
      string itmIdt = unit[Reserved.ID];
      string pagClsIdt = unit["PagClsIdt"];
      if (!String.IsNullOrEmpty(itmIdt) && !String.IsNullOrEmpty(pagClsIdt)) {        
        Logger.Debug("Update item {0}, foto {1}", itmIdt, pagClsIdt);
        this.UpdateItem(unit, context, itmIdt, pagClsIdt);
      }

      return new ResultProps();
    }

    /// <summary>
    /// Update een item
    /// </summary>
    /// <param name="unit">ProcessUnit unit</param>
    /// <param name="context">ProcessContext context</param>
    /// <param name="itmIdt">string itmIdt</param>
    /// <param name="pagClsIdt">string pagClsIdt (foto ID)</param>
    private void UpdateItem(ProcessUnit unit, ProcessContext context, string itmIdt, string pagClsIdt) {
      using (var cms = new IproxCms()) {
        var fotoalbum = cms.GetItem<Fotoalbum>(itmIdt.To<int>(), false, ContentMode.Source);
        fotoalbum.MarkPublished();

        this.UpdateFoto(unit, context, fotoalbum, pagClsIdt);

        cms.SubmitChanges(context);
      }
    }

    /// <summary>
    /// Bewerk velden van bestaande foto
    /// </summary>
    /// <param name="unit">ProcessUnit unit</param>
    /// <param name="context">ProcessContext context</param>
    /// <param name="fotoalbum">Fotoalbum model item</param>
    /// <param name="pagClsIdt">string pagClsIdt (foto ID)</param>
    private void UpdateFoto(ProcessUnit unit, ProcessContext context, Item<Fotoalbum> fotoalbum, string pagClsIdt) {
      foreach (var foto in fotoalbum.Page.Foto) {
        if (foto.Id == pagClsIdt.To<int>()) {
          SharedRepublish.SetValueIfSupplied(unit, foto.Titel, "Titel");
          SharedRepublish.SetValueIfSupplied(unit, foto.Beschrijving, "Beschrijving");
          SharedRepublish.SetValueIfSupplied(unit, foto.Foto, "Foto");
        }
      }
    }
  }
}
