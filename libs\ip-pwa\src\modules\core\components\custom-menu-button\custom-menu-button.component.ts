import { Component, ComponentFactoryResolver, Input, OnInit, Type, ViewChild, ViewContainerRef } from '@angular/core';

@Component({
  template: '<ng-template #outlet></ng-template>',
  selector: 'ip-custom-menu-button',
})
export class CustomMenuButtonComponent implements OnInit {
  @ViewChild('outlet', { static: true, read: ViewContainerRef })
  outlet!: ViewContainerRef;

  @Input()
  component!: Type<unknown>;

  constructor(private componentFactoryResolver: ComponentFactoryResolver) { }

  ngOnInit() {
    this.create(this.outlet);
  }

  create(outlet: ViewContainerRef): void {
    const componentFactory = this.componentFactoryResolver.resolveComponentFactory(this.component);

    if (componentFactory) {
      outlet.createComponent(componentFactory);
    }
  }
}
