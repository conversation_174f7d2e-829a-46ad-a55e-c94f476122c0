import { Component, Input } from '@angular/core';

import { User } from '@ip/social-core';

@Component({
  selector: 'ips-person-follow-btn',
  templateUrl: './follow-btn.component.html',
})
export class PersonFollowBtnComponent {
  @Input()
  personId!: string;

  @Input()
  user!: User;

  toggleFollow(personId: string) {
    if (this.user.followPersons.includes(personId)) {
      this.user.unfollowPerson(personId).subscribe();
    }
    else {
      this.user.followPerson(personId).subscribe();
    }
  }
}
