import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { RouterModule } from '@angular/router';

import { IonicModule } from '@ionic/angular';
import { IpPwaHeaderModule } from '@ip/pwa';

import { AnniversaryModule } from '../anniversary/anniversary.module';
import { BlogModule } from '../blog/blog.module';
import { CommentsModule } from '../comments/comments.module';
import { MetaModule } from '../meta/meta.module';
import { SharedModule } from '../shared/shared.module';
import { IproxBlocksComponent } from './components/iprox-blocks/iprox-blocks.component';
import { IproxPhotoBlockComponent } from './components/iprox-photo-block/iprox-photo-block.component';
import { IproxContentHtmlComponent } from './components/iprox-content-html/iprox-content-html.component';
import { IproxContentBlockComponent } from './components/iprox-content-block/iprox-content-block.component';
import { IproxContentComponent } from './components/iprox-content/iprox-content.component';
import { PageComponent } from './components/page/page.component';
import { IproxFileDownloadDirective } from './directives/iprox-download/iprox-download.directive';
import { IproxImageSrcDirective } from './pipes/iprox-image-src.directive';
import { PageResolver } from './services/page.resolver';

@NgModule({
  imports: [
    CommonModule,
    IonicModule,
    SharedModule,
    BlogModule,
    MetaModule,
    RouterModule,
    CommentsModule,
    IpPwaHeaderModule,
    AnniversaryModule,
  ],
  declarations: [
    PageComponent,
    IproxContentComponent,
    IproxBlocksComponent,
    IproxContentHtmlComponent,
    IproxPhotoBlockComponent,
    IproxContentBlockComponent,
    IproxImageSrcDirective,
    IproxFileDownloadDirective,
  ],
  providers: [
    PageResolver,
  ],
  exports: [
    PageComponent,
  ]
})
export class PageModule {
}
