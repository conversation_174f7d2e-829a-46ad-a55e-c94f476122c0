@font-sizes: {
  @1: .75rem;    // IP
  @2: .875rem;   // IP
  @3: 1rem;      // IP
  @4: 1.25rem;   // body; subtitle.
  @5: 1.5rem;    // h3; introductie
  @6: 2.25rem;   // h2
  @7: 3rem;      // h1 quote
};

@font-sizes-small: {
  @1: .75rem;    // IP
  @2: .875rem;   // IP
  @3: 1rem;      // IP
  @4: 1.25rem;   // body; subtitle.
  @5: 1.375rem;  // h3; introductie
  @6: 1.5rem;    // h2
  @7: 2rem;      // h1 quote
};

@line-heights: {
  @1: 1.5;       // IP
  @2: 1.5;       // IP
  @3: 1.5;       // IP
  @4: 1.5;       // body; subtitle.
  @5: 32 / 24;   // h3; introductie
  @6: 44 / 36;   // h2
  @7: 56 / 48;   // h1 quote
};

@line-heights-small: {
  @1: 1.5;       // IP
  @2: 1.5;       // IP
  @3: 1.5;       // IP
  @4: 1.5;       // body; subtitle.
  @5: 30 / 22;   // h3; introductie
  @6: 32 / 24;   // h2
  @7: 35 / 32;   // h1 quote
};

.pzh-font-size(@step, @line-height: false) {
  @media (max-width: @bl-mq-small) {
    font-size: @font-sizes-small[@@step];

    & when (@line-height) {
      line-height: @line-heights-small[@@step];
    }
  }

  font-size: @font-sizes[@@step];

  & when (@line-height) {
    line-height: @line-heights[@@step];
  }
}

.pzh-line-height(@step) {
  @media (max-width: @bl-mq-small) {
    line-height: @line-heights-small[@@step];
  }

  line-height: @line-heights[@@step];
}
