import { ChangeDetectionStrategy, Component } from '@angular/core';

import { CommentService, CommentThreadBaseComponent } from '@ip/social-core';

@Component({
  selector: 'ips-comment-thread',
  templateUrl: './comment-thread.component.html',
  styleUrls: ['./comment-thread.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class CommentThreadComponent extends CommentThreadBaseComponent {
  constructor(commentService: CommentService) {
    super(commentService);
  }
}
