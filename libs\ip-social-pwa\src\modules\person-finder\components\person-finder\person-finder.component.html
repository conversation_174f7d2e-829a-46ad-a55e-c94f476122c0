<ion-header>
  <ip-header></ip-header>
</ion-header>

<ion-content *ngIf="state$ | async as state;">
  <ion-searchbar
    [placeholder]="'persons.search.placeholder' | transloco"
    (ionChange)="onSearchChange($any($event))"
    [(ngModel)]="value"
  ></ion-searchbar>

  <ion-grid>
    <ion-row>
      <ion-col size="12">
        <ips-recently-visited-persons [show]="state.persons === undefined"></ips-recently-visited-persons>

        <ion-list>
          <ion-item *ngIf="state.persons?.length === 0">
            {{ 'noResults' | transloco }}
          </ion-item>
          <ips-person-item
            *ngFor="let person of state.persons"
            [id]="person.id"
          ></ips-person-item>
        </ion-list>

        <ion-infinite-scroll [disabled]="!state.hasMore" (ionInfinite)="nextPage($any($event))">
          <ion-infinite-scroll-content loadingSpinner="circular" [loadingText]="'persons.loading' | transloco">
          </ion-infinite-scroll-content>
        </ion-infinite-scroll>
      </ion-col>
    </ion-row>
  </ion-grid>
</ion-content>
