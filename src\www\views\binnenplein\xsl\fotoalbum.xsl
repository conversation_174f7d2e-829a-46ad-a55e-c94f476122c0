<?xml version="1.0" encoding="utf-8"?>

<xsl:stylesheet xmlns:xsl="http://www.w3.org/1999/XSL/Transform" xmlns="http://www.w3.org/1999/xhtml" version="1.0">
  <xsl:import href="../../baseline/xsl/fotoalbum.xsl" />
  <xsl:import href="plugs.xsl" />

  <!-- In publicatieomgeving ivm. frontend-editing: hanteer `EdtFotCls_` ipv. ID 'Itm_' , om een maatwerk-bewerkingsdialoog te kunnen bieden -->
  <xsl:template match="content[page/@pagetype = 'fotoalbum']//cluster[Typ = 'foto-thumbnail']" mode="grid-extra-attribs">
    <xsl:choose>
      <xsl:when test="$EnvPrv">
        <xsl:apply-imports />
      </xsl:when>
      <xsl:otherwise>
        <xsl:attribute name="id">
          <xsl:text>EdtFotCls_</xsl:text>
          <xsl:value-of select="@PagClsIdt" />
        </xsl:attribute>
      </xsl:otherwise>
    </xsl:choose>
  </xsl:template>

</xsl:stylesheet>
