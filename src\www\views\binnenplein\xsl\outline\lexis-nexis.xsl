<?xml version="1.0" encoding="utf-8"?>
<xsl:stylesheet
  xmlns:xsl="http://www.w3.org/1999/XSL/Transform"
  xmlns:xhtml="http://www.w3.org/1999/xhtml"
  xmlns:formatter="urn:formatter"
  xmlns="http://www.w3.org/1999/xhtml"
  extension-element-prefixes="formatter"
  version="1.0"
>

  <xsl:param name="lexisnexis_apikey" select="'36CD40AA-F8D5-4C3C-A4D9-C3230E8C6B96'" />

  <!-- Links naar //mediaportal.lexisnexis.nl/instantaccess krijgen een apikey gesuffixed, en een ipng-lexisnexis class -->
  <xsl:template match="xhtml:a[contains(@href, '//mediaportal.lexisnexis.nl/instantaccess')]" mode="page">
    <xsl:element name="{local-name()}">
      <xsl:apply-templates select="@*" mode="page" />
      <xsl:attribute name="class">
        <xsl:value-of select="@class" />
        <xsl:text> ipng-lexisnexis</xsl:text>
      </xsl:attribute>
      <xsl:attribute name="href">
        <xsl:apply-templates select="@href" mode="href" />
      </xsl:attribute>
      <xsl:apply-templates select="node()" mode="page" />
    </xsl:element>
  </xsl:template>

  <xsl:template match="@*" mode="href">
    <xsl:value-of select="." />
  </xsl:template>

  <xsl:template match="@*[contains(., '//mediaportal.lexisnexis.nl/instantaccess')]" mode="href">
    <xsl:value-of select="." />
    <xsl:if test="not(formatter:EndsWith(., '/'))">/</xsl:if>
    <xsl:value-of select="$lexisnexis_apikey" />
  </xsl:template>

</xsl:stylesheet>
