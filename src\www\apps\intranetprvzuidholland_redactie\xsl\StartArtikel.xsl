<?xml version="1.0" encoding="utf-8"?>
<xsl:stylesheet xmlns:xsl="http://www.w3.org/1999/XSL/Transform" version="1.0" xmlns:resources="urn:resources" extension-element-prefixes="resources">

  <xsl:import href="include/StartPage.xsl"/>

  <xsl:param name="PublishArtikel.done"/>

  <!-- abstract variables -->
  <xsl:variable name="done" select="$PublishArtikel.done" />
  <xsl:variable name="pagetype">artikel</xsl:variable>

  <xsl:template name="startpage-velden">
    <xsl:variable name="meta-veld" select="/data/clusterdefinition[Nam='Meta']/*/veld[site]" />
    <xsl:apply-templates select="$meta-veld[Nam = 'Samenvatting']" mode="startpage-veld" />
    <xsl:apply-templates select="$meta-veld[Nam = 'Afbeelding voor index']" mode="startpage-veld" />

    <xsl:variable name="inhoud-veld" select="/data/clusterdefinition[Nam='Artikel']/*/veld[site]" />
    <xsl:apply-templates select="$inhoud-veld[Nam = 'Inleiding']" mode="startpage-veld" />
    <xsl:apply-templates select="$inhoud-veld[Nam = 'Inhoud']" mode="startpage-veld" />
  </xsl:template>

</xsl:stylesheet>