<ion-content *ngIf="auth.auth$ | async as status;">
  <ion-grid *ngIf="status === authStatus.UnAuthenticated || status === authStatus.InProgress || status === authStatus.Authorized || status === authStatus.FetchingUser">

    <ion-row class="ion-justify-content-center ion-margin-top">
      <ion-col size="4" class="ion-text-center">
        <img src="/assets/images/pwa-logo.svg" alt="" />
      </ion-col>
    </ion-row>

    <ion-row class="ion-justify-content-center">
      <ion-col size="6" class="ion-text-center">
        <ion-button
          expand="full"
          [disabled]="status === authStatus.InProgress || status === authStatus.Authorized || status === authStatus.FetchingUser"
          (click)="auth.login()"
        >
          {{ 'auth.login' | transloco }}
        </ion-button>

        <ng-container *ngIf="appInstall.installed$ | async as status;">
          <ion-button
            *ngIf="status !== 'installed'"
            (click)="appInstall.prompt()"
            [disabled]="status === 'rejected'"
            button
            expand="full"
          >
            {{ 'app.install' | transloco }}
          </ion-button>
        </ng-container>
      </ion-col>
    </ion-row>

  </ion-grid>
</ion-content>
