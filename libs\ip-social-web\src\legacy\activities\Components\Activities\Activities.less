@ips-activities-vertical-activity-space: 8px;
@ips-activities-item-border: 1px solid #555;
@ips-activities-line-height: 1.5rem;
@ips-activities-date-font-size: @ips-date-font-size-sm;
@baseline-grid-breakpoint-small: 480px;

ips-activities {
  display: block;
  padding-bottom: @ips-activities-vertical-activity-space;
  padding-top: @ips-activities-vertical-activity-space;
  position: relative;

  ips-activity {
    border-bottom: @ips-activities-item-border;
    display: block;
    padding-top: @ips-activities-vertical-activity-space;
    padding-bottom: ~"calc(@{ips-activities-vertical-activity-space} + @{ips-activities-date-font-size})";
    position: relative;
    width: 100%;

    .ips-date-time {
      bottom: 0;
      float: right;
      font-size: @ips-activities-date-font-size;
      position: absolute;
      right: @grid-gutter-width;
    }

    .activity-avatar {
      display: inline-block;
      margin-right: @ips-activities-vertical-activity-space;
      vertical-align: top;
      width: @ips-person-avatar-size;
    }

    .activity-text {
      @__width: @ips-person-avatar-size + @ips-activities-vertical-activity-space;

      display: inline-block;
      line-height: @ips-activities-line-height;
      width: ~"calc(100% - @{__width})";

      .ips-widget {
        font-style: italic;
      }
    }

    @media screen and (max-width: @baseline-grid-breakpoint-small) {
      .activity-text {
        padding-bottom: @ips-activities-vertical-activity-space;
      }
    }
  }

  ips-activity:last-child {
    border-bottom: 0;
  }

  .ips-activities-controls {
    .clearfix();

    > a {
      float: right;
    }
  }
}
