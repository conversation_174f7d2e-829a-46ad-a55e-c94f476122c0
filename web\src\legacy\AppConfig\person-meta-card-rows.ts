import { <PERSON><PERSON>, PhoneNumberPipe } from '@ip/social-core';
import { IMetaRow } from '@ip/social-web/src/modules/person/person.models';

export const customMetaTypes = (phoneNumberPipe: PhoneNumberPipe) => new Map<string, (person: <PERSON>erson, scheme?: string) => IMetaRow>([
  ['mobileOrOtherNumber', (person) => {
    const displayPhoneNumber = person.phoneNumbers.reduce((displayNumber, phoneNumber) => {
      if (displayNumber.type === 'Mobile') {
        return displayNumber;
      }

      if (phoneNumber.number) {
        return phoneNumber;
      }

      return displayNumber;
    }, { number: '', type: '' });

    const formattedNumber = phoneNumberPipe.transform(displayPhoneNumber.number);

    return {
      href: 'tel:' + displayPhoneNumber.number,
      value: formattedNumber,
      title: formattedNumber,
    };
  }]
]);
