import { flow, startsWith } from 'lodash';

import template from '!html-loader?minimize=true&conservativeCollapse=false&collapseInlineTagWhitespace=true!./Activity.html';

(function (angular) {
  'use strict';

  angular.module('Intranet.Activities').component('ipsActivity', {
    bindings: {
      activity: '<'
    },
    template: template,
    controller: ['$element', '$compile', '$rootScope', 'ActivityWidgetRenderer', 'RefListService', function ($element, $compile, $rootScope, ActivityWidgetRenderer, RefListService) {
      var self = this;

      var renderers = flow(ActivityWidgetRenderer, RefListService.pluralize);
      var scope = angular.extend($rootScope.$new(true), self.activity.data);

      var dataKeys = Object.keys(self.activity.data);
      var context = self.activity.data.context[0];

      if (context && context.iproxId) {
        dataKeys.forEach(function (key, index) {
          if (key !== 'userInfo' && key !== 'context' && key !== 'active' && key !== 'title') {
            if (self.activity.data[key]) {
              self.activity.data[key].iproxId = context.iproxId;
            }
          }
        });
      }

      if (startsWith(self.activity.text, '<userInfo>')) {
        var avatarElement = angular.element('<div class="activity-avatar">' + renderers('<avatar>') + '</div>');
        var avatarResult = $compile(avatarElement)(scope);
        angular.element('.ips-activity', $element).append(avatarResult);
      }

      var element = angular.element('<div class="activity-text">' + renderers(self.activity.text) + '</div>');
      var result = $compile(element)(scope);

      angular.element('.ips-activity', $element).append(result);
    }],
    controllerAs: 'ActivityCtrl'
  });
})(angular);
