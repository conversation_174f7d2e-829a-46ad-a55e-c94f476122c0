import { Injectable } from '@angular/core';

import { merge, MonoTypeOperatorFunction, Observable, pipe, Subject } from 'rxjs';
import { bufferTime, filter, map, mergeMap, tap } from 'rxjs/operators';

import { once } from '../../../utilities/once-operator';
import { IReference } from '../../core/models';
import { IMetaConfig, Meta, MetaComponent } from '../models';
import { MetaApiService } from './meta-api.service';
import { MetaCache } from './meta.cache';

@Injectable()
export class MetaService {
  private metaRequestSubject = new Subject<IMetaConfig>();

  private updateMetaSubject = new Subject<Meta[]>();

  private cache = new MetaCache();

  private actions$: Observable<Meta[]> = this.metaRequestSubject.pipe(
    bufferTime(250),
    this.filterRequests(),
    mergeMap((requests) =>
      merge(
        this.fetchMeta(requests),
        this.updateMetaSubject
      )
    ),
    tap(metaCollection => this.cache.add(metaCollection)),
  );

  constructor(private metaApi: MetaApiService) {
    this.actions$.subscribe();
  }

  public get$({ reference, components }: IMetaConfig): Observable<Meta> {
    return this.cache.get$(reference)
      .pipe(
        once(() => this.queueMetaFetch(reference, components)),
        filter((meta): meta is Meta => !!meta),
      );
  }

  queueMetaFetch(reference: IReference, components: MetaComponent[]): void {
    this.metaRequestSubject.next({ reference, components });
  }

  updateMeta(meta: Meta): void {
    this.updateMetaSubject.next([meta]);
  }

  like(meta: Meta, type: string) {
    return this.metaApi.like(meta, type);
  }

  deleteLike(id: string): Observable<null> {
    return this.metaApi.deleteLike(id);
  }

  addVisit(reference: IReference) {
    this.cache.addVisit(reference);
  }

  private fetchMeta(requests: IMetaConfig[]): Observable<Meta[]> {
    return this.metaApi.get(requests).pipe(
      map(data => data.map(metaConstructor => new Meta(this, metaConstructor)))
    );
  }

  private filterRequests(): MonoTypeOperatorFunction<IMetaConfig[]> {
    return pipe(
      filter(requests => requests.length > 0),
      map(requests => {
        this.cache.clearExpired();
        const now = new Date().getTime();
        return requests.filter(r => !this.cache.isFresh(r.reference, now));
      }),
      filter(requests => requests.length > 0),
    );
  }
}
