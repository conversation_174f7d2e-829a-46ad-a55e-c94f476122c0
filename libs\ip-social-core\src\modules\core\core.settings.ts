import { Injectable } from '@angular/core';

import { ISocialConfig } from './config/config.model';
import { SocialConfigProvider } from './config/config-provider.service';

export interface IAuthenticationConfiguration {
  azure?: {
    authorityId: string;
    clientId: string;
  };
}

export interface ICoreSettings {
  apiUrl: string;
  baseUrl: string;
  authentication: IAuthenticationConfiguration;
}

@Injectable()
export class CoreSettings implements ICoreSettings {
  private module: keyof ISocialConfig = 'core';

  apiUrl: string;

  baseUrl: string;

  authentication: IAuthenticationConfiguration;

  constructor(configProvider: SocialConfigProvider) {
    const config = configProvider.get<ICoreSettings>(this.module);

    if (config === undefined) {
      throw new Error('[Settings] core configuration is not defined.');
    }

    this.apiUrl = config.apiUrl.replace(/\/+$/, '').replace(/^\/\//, window.location.protocol + '//') + '/';
    this.baseUrl = config.baseUrl.replace(/\/+$/, '') + '/';
    this.authentication = config.authentication;
  }
}
