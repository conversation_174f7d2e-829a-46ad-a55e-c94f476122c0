import { <PERSON>erson } from '@ip/social-core';

export namespace Blog<PERSON><PERSON> {
  export interface IBlogResponse {
    authors: <PERSON>erson[];
    body: string | null;
    commentCount: number;
    creationDate: string;
    id: string;
    image: string | null;
    iproxId: string | null;
    lastModifiedDate: string;
    like: null;
    nomination: null;
    publishDate: string;
    published: boolean;
    readCount: number;
    slug: string;
    summary: string;
    tags: unknown; // TODO: Type
    title: string;
    userId: string;
    userInfo: IPerson;
    workingBody: string | null;
    workInProgress: boolean;
  }

  export interface IBlogSummary {
    id: string;
    title: string;
    summary: string;
    userId: string;
    creationDate: string;
    image: string | null;
    published: boolean;
    commentCount: number;
    publishDate: string;
    workInProgress: boolean;
  }

  export interface IUpdateImageResponse {
    fileName: string;
  }
}
