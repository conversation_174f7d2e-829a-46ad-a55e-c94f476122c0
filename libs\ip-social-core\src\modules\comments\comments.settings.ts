import { Injectable } from '@angular/core';
import { FileType } from '../../utilities/file-types';

import { SocialConfigProvider } from '../core/config/config-provider.service';
import { ISocialConfig } from '../core/config/config.model';

export interface ICommentSettings {
  count: number;
  depth: number;
  depthCount: number;
  enableLike: boolean;
  /** Users can comment, regardless of group-membership etc. */
  allowCommentAsUser: boolean;
  minLength: number;
  maxLength?: number;
  enableAttachments: boolean;
  attachmentFileTypes: FileType[];
  imageFormatSize: string;
  autoUpdate: boolean;
  /** In seconds */
  autoUpdateInterval: number;
}

export type CommentConfig = Partial<ICommentSettings>;

@Injectable()
export class CommentSettings {
  private module: keyof ISocialConfig = 'comments';

  private settings: ICommentSettings = {
    count: 10,
    depth: 2,
    depthCount: 3,
    enableLike: true,
    allowCommentAsUser: true,
    minLength: 1,
    enableAttachments: false,
    attachmentFileTypes: [
      'image',
      'pdf',
      'document',
      'powerpoint',
      'spreadsheet',
    ],
    imageFormatSize: '128h',
    autoUpdate: true,
    autoUpdateInterval: 10,
  };

  constructor(settings: SocialConfigProvider) {
    Object.assign(this.settings, settings.get<ICommentSettings>(this.module));
  }

  get(config: CommentConfig | undefined): ICommentSettings {
    return Object.assign(
      {},
      this.settings,
      config
        ? {
          ...config,
          maxLength: config.maxLength && config.maxLength >= 0 ? config.maxLength : undefined
        }
        : {}
    );
  }
}
