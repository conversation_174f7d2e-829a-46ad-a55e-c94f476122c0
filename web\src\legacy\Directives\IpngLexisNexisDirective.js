(function (angular) {
  'use strict';

  angular.module('Intranet.Legacy').directive('ipngLexisnexis', ['User', 'LexisnexisSettings',
    function (User, LexisnexisSettings) {
      return {
        restrict: 'C',
        link: function (scope, element, attrs) {
          if (element[0].tagName === 'A') {
            User.user.$promise.then(function (user) {
              var queryObject = {
                username: user.loginName,
                email: user.email,
                firstname: user.name.usualName,
                lastname: user.name.lastName
              };

              // make sure ngHref goes first
              var observer = attrs.$observe('href', function () {
                if (attrs.href.indexOf(LexisnexisSettings.baseUrl) > -1) {
                  element.attr('href', attrs.href + '?' + angular.element.param(queryObject));
                  // stop watching, it will never change anyway
                  observer();
                }
              });
            });
          }
        }
      };
    }
  ]).constant('LexisnexisSettings', {
    baseUrl: 'mediaportal.lexisnexis.nl'
  });
})(angular);

