<?xml version="1.0" encoding="utf-8"?>

<xsl:stylesheet version="1.0"
  xmlns:xsl="http://www.w3.org/1999/XSL/Transform"
  xmlns="http://www.w3.org/1999/xhtml"
  xmlns:formatter="urn:formatter"
  extension-element-prefixes="formatter">

  <xsl:import href="../../baseline/xsl/enquete.xsl" />
  <xsl:import href="plugs.xsl" />

  <xsl:param name="targets" />
  <xsl:param name="groupId" />
  <xsl:param name="mailerMode" />

  <xsl:template match="content[page/@pagetype = 'enquete']" mode="js_pagetype">
    <script src="/views/binnenplein/js/enquete.js"></script>
  </xsl:template>

  <xsl:template match="cluster" mode="pr_formulier_mail_templates">
    <input type="hidden" name="Formulier.text.Type" value="text" />
    <input type="hidden" name="Formulier.text.Xsl" value="{$view_folder}xsl/mail/formulier_text" />

    <input type="hidden" name="Formulier.html.Type" value="html" />
    <input type="hidden" name="Formulier.html.Xsl" value="{$view_folder}xsl/mail/formulier_html" />
  </xsl:template>

  <xsl:template match="cluster" mode="formulier_extra_parts_plug">
    <xsl:param name="formHandler" />

    <xsl:if test="$curitem/@Aka = 'groepsmail'">
      <input type="hidden" name="{$formHandler}.targets" value="{$targets}" />
      <input type="hidden" name="targets" value="{$targets}" />
      <input type="hidden" name="{$formHandler}.groupId" value="{$groupId}" />
      <input type="hidden" name="groupId" value="{$groupId}" />
      <input type="hidden" name="{$formHandler}.mailerMode" value="{$mailerMode}" />
      <input type="hidden" name="mailerMode" value="{$mailerMode}" />
      <pzh-social-mailer-subject-enricher></pzh-social-mailer-subject-enricher>
    </xsl:if>
  </xsl:template>

  <xsl:template match="veld[Nam = 'Bezoeker mag Kopie naar invullen']" mode="pr_formulier_formulier_veld" >
    <xsl:param name="formHandler" select="'Formulier'" />

    <xsl:choose>
      <xsl:when test="$curitem/@Aka = 'groepsmail'">
        <xsl:apply-templates select="." mode="formulierregel">
          <xsl:with-param name="fieldName" select="concat($formHandler, '.Cc')" />
          <xsl:with-param name="fieldType" select="'mail'" />
          <xsl:with-param name="fieldRequired" select="'false'" />
          <xsl:with-param name="fieldLabel" select="'Cc'" />
          <xsl:with-param name="fieldPlh" select="'<EMAIL>,<EMAIL>'" />
          <xsl:with-param name="fieldPattern" select="formatter:GetAddressMask()" />
          <xsl:with-param name="fieldValue">
            <xsl:if test="$Formulier.Valid != ''">
              <xsl:value-of select="ancestor::content[1]/*/Formulier/field[@name = 'Cc']/value/text()" />
            </xsl:if>
          </xsl:with-param>
          <xsl:with-param name="formHandler"   select="$formHandler" />
          <xsl:with-param name="formError"     select="$Formulier_error" />
        </xsl:apply-templates>

        <xsl:if test="$mailerMode = 'socialGroups'">
          <pzh-social-mailer></pzh-social-mailer>
          <div class="verborgen" id="pzhSocialMailerTo">
            <input type="text" id="{$formHandler}.To" name="{$formHandler}.To" value="" />
          </div>
        </xsl:if>

        <xsl:if test="$mailerMode != 'socialGroups'">
          <fieldset class="rij mode_input selectie rij_verplicht selectie_type_checkbox" id="fieldsetTo">
            <legend class="label">
              <span class="setlabel">
                <a href="#mailAdressen">
                  <xsl:call-template name="find_label">
                    <xsl:with-param name="value" select="'Dit formulier wordt gemaild aan'" />
                  </xsl:call-template>
                  <span class="recipientCount">
                    <xsl:value-of select="' '" />
                    <xsl:value-of select="count(/data/content/enquete_before/persons/person)" />
                    <xsl:value-of select="' '" />
                    <xsl:call-template name="find_label">
                      <xsl:with-param name="value" select="'ontvangers'" />
                    </xsl:call-template>
                  </span>
                </a>
              </span>
            </legend>

            <div class="invoer" id="mailAdressen">
              <xsl:attribute name="data-emptymessage">
                <xsl:call-template name="find_label">
                  <xsl:with-param name="value" select="'Selecteer tenminste 1 e-mailadres.'" />
                </xsl:call-template>
              </xsl:attribute>
              <div class="antwoorden">
                <xsl:for-each select="/data/content/enquete_before/persons/person">
                  <xsl:sort select="@name" data-type="text" order="ascending" />
                  <div class="antwoord">
                    <input type="checkbox" name="{$formHandler}.To" id="{$formHandler}.To{position()}" value="{@email}" checked="checked" />
                    <label for="{$formHandler}.To{position()}" title="{@email}">
                      <xsl:value-of select="@name" />
                    </label>
                  </div>
                </xsl:for-each>
              </div>
            </div>
          </fieldset>
        </xsl:if>
      </xsl:when>
      <xsl:otherwise>
        <xsl:apply-imports />
      </xsl:otherwise>
    </xsl:choose>
  </xsl:template>

  <xsl:template match="cluster[Nam = 'Editor']" mode="pr_formulier_vrijveld">
    <xsl:param name="formHandler" select="'Formulier'" />
    <xsl:variable name="this" select="." />

    <xsl:apply-templates select="ancestor::content[1]" mode="formulierregel">
      <xsl:with-param name="fieldName" select="concat($formHandler, '.PagClsIdt_', @PagClsIdt)" />
      <xsl:with-param name="fieldRequired" select="string(veld[Nam = 'Verplicht']/Wrd = '1')" />
      <xsl:with-param name="fieldLabel" select="veld[Nam = 'Label']/Wrd" />
      <xsl:with-param name="fieldValue">
        <xsl:apply-templates select="self::cluster" mode="waarde">
          <xsl:with-param name="Nam" select="concat('PagClsIdt_', @PagClsIdt)" />
        </xsl:apply-templates>
      </xsl:with-param>
      <xsl:with-param name="fieldValueNode" select="ancestor::content[1]/*/Formulier/field[@PagClsIdt = $this/@PagClsIdt]" />
      <xsl:with-param name="fieldType">textarea</xsl:with-param>
      <xsl:with-param name="fieldClass">rich-textarea</xsl:with-param>
      <xsl:with-param name="formHandler" select="$formHandler" />
      <xsl:with-param name="formError" select="$Formulier_error" />
      <xsl:with-param name="fieldMode">
        <xsl:choose>
          <xsl:when test="$Formulier_mode = concat('bevestiging_', parent::cluster/parent::cluster/@PagClsIdt, '_', ancestor::content[1]/page/@ItmIdt) and $Formulier.Valid = 'true'">display</xsl:when>
          <xsl:otherwise>input</xsl:otherwise>
        </xsl:choose>
      </xsl:with-param>
      <xsl:with-param name="fieldCols">40</xsl:with-param>
      <xsl:with-param name="fieldRows">5</xsl:with-param>
      <xsl:with-param name="fieldContext" select="$this" />
    </xsl:apply-templates>

    <xsl:apply-templates select="self::cluster" mode="pr_formulier_after_veld_plug">
      <xsl:with-param name="formHandler" select="$formHandler" />
    </xsl:apply-templates>
  </xsl:template>

</xsl:stylesheet>
