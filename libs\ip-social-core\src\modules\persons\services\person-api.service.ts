import { HttpParams } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';

import { SocialHttpClient } from '../../core/services/api-client';
import { IPerson } from '../models/person.model';

@Injectable()
export class PersonApiService {
  constructor(private socialApi: SocialHttpClient) { }

  get(ids: string[]): Observable<IPerson[]>;

  get(id: string): Observable<IPerson>;

  get(id: string | string[]): Observable<IPerson> | Observable<IPerson[]> {
    if (Array.isArray(id)) {
      return this.socialApi.get<IPerson[]>('person', { params: { id } });
    }

    return this.socialApi.get<IPerson>(`person/${id}`);
  }

  followPerson(userId: string, personId: string) {
    return this.socialApi.post(
      `person/${userId}/followPerson`,
      { id: userId, personId, },
      { params: new HttpParams().append('personid', personId) }
    );
  }

  unfollowPerson(userId: string, personId: string) {
    return this.socialApi.delete(`person/${userId}/followPerson/${personId}`);
  }
}
