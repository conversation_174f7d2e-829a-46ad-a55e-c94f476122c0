.ips-inbox-message {
--color: #000;
box-shadow: none;

  &.ips-inbox-notification {
    background-color: #f0fdd1;

    ion-card-content .ips-inbox-message-content {
      padding-left: 48px + 16px;
    }
  }

  &.ips-inbox-notification-announcement {
    background-color: #e7e8ff;
  }

  &.ips-inbox-notification-system {
    background-color:  #fffde6;
  }

  ion-card-content {
    align-items: center;

    ion-avatar {
      position: absolute;
      left: 5px;
      top: 5px;

    }
  }

  .ips-inbox-message-type {
    font-size: 14px;
    font-weight: 700;
  }

  .ips-inbox-message-content {
    position: relative;
    min-height: 48px + 16px;
    display: flex;
    align-items: center;
  }

  .ips-inbox-message-actions {
    justify-content: flex-end;
    display: flex;
  }
}

h2 {
  font-size: 16px;
}

::ng-deep .ips-inbox-message {
  ion-avatar {
    left: 0;
    position: absolute;
    top: 8px;
  }
}
