import { NgModule } from '@angular/core';
import { RouterModule } from '@angular/router';
import { PERSON_ROUTE } from '../person/person-route';

import { PersonFinderComponent } from './components/person-finder/person-finder.component';
import { PersonFinderModule } from './person-finder.module';

@NgModule({
  imports: [
    PersonFinderModule,
    RouterModule.forChild([
      {
        path: '',
        component: PersonFinderComponent,
        pathMatch: 'full'
      },
      {
        path: ':id',
        loadChildren: () => import('../profile/profile-route.module').then(m => m.ProfileRouteModule),
      },
    ]),
  ],
  providers: [
    {
      provide: PERSON_ROUTE,
      useValue: '/persons'
    },
  ]
})
export class PersonFinderRouteModule {
}
