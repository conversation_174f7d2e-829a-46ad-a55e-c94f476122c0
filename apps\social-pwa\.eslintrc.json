{"extends": ["../../.eslintrc.json"], "ignorePatterns": ["!**/*", "**/service-worker/*"], "overrides": [{"files": ["*.ts"], "extends": ["@infoprojects/eslint-config/angular-typescript"], "parserOptions": {"project": ["apps/social-pwa/tsconfig.*?.json"]}, "rules": {"@angular-eslint/directive-selector": ["error", {"type": "attribute", "prefix": "ipSocial", "style": "camelCase"}], "@angular-eslint/component-selector": ["error", {"type": "element", "prefix": "ip-social", "style": "kebab-case"}]}}, {"files": ["*.html"], "extends": ["plugin:@nrwl/nx/angular-template"], "rules": {}}]}