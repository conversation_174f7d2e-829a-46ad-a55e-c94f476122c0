import { of } from 'rxjs';

import { IReference, User } from '../../core/models';
import { ICommentSettings } from '../comments.settings';
import { CommentAuthorizer } from '../models/authorizer.model';
import { IComment } from '../models/comment.model';
import { ICommentAuthorizerFactory } from '../services/comment-authorizer.service';

export class FileCommentAuthorizerFactory implements ICommentAuthorizerFactory {
  entities = ['File'];

  constructor(private legacyFileAuthorizer: unknown) { }

  authorizer$ = (reference: IReference, settings: ICommentSettings, user: User) => {
    return of(new FileCommentAuthorizer(user, reference, settings, this.legacyFileAuthorizer));
  };
}

export class FileCommentAuthorizer extends CommentAuthorizer {
  // TODO nalopen iedere permission
  fn = {
    canView: (_comment: IComment | undefined): boolean => {
      return this.settings.allowCommentAsUser || this.legacyFileAuthorizer.canCreateComment(this.user, this.reference);
    },
    canCreate: (_comment: IComment | undefined): boolean => {
      return this.settings.allowCommentAsUser || this.legacyFileAuthorizer.canCreateComment(this.user, this.reference);
    },
    canEdit: (comment: IComment | undefined): boolean => {
      return this.legacyFileAuthorizer.canEditComment(this.user, comment, this.reference);
    },
    canRemove: (comment: IComment | undefined): boolean => {
      return this.legacyFileAuthorizer.canDeleteComment(this.user, comment, this.reference);
    },
    canAddAttachment: (_comment: IComment | undefined): boolean => {
      return true;
    },
    canRemoveAttachment: (_comment: IComment | undefined): boolean => {
      return true;
    },
  };

  constructor(
    private user: User,
    private reference: IReference,
    private settings: ICommentSettings,
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    private legacyFileAuthorizer: any,
  ) {
    super();
  }
}
