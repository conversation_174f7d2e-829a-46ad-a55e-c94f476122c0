import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { FormsModule } from '@angular/forms';

import { IonicModule } from '@ionic/angular';
import { IpPwaHeaderModule } from '@ip/pwa';

import { PersonModule } from '../person/person.module';
import { ProfileModule } from '../profile/profile.module';
import { SharedModule } from '../shared/shared.module';
import { PersonFinderComponent } from './components/person-finder/person-finder.component';
import { RecentlyVisitedPersonsComponent } from './components/recently-visited-persons/recently-visited-persons.component';

@NgModule({
  declarations: [
    PersonFinderComponent,
    RecentlyVisitedPersonsComponent,
  ],
  imports: [
    CommonModule,
    SharedModule,
    FormsModule,
    IonicModule,
    PersonModule,
    ProfileModule,
    IpPwaHeaderModule,
  ],
  exports: [
    PersonFinderComponent,
  ]
})
export class PersonFinderModule {
}
