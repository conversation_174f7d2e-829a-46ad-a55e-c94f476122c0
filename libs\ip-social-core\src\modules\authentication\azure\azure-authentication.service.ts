import { HttpClient } from '@angular/common/http';
import { Inject, Injectable } from '@angular/core';

import { MSAL_GUARD_CONFIG, MsalBroadcastService, MsalGuardConfiguration, MsalService } from '@azure/msal-angular';
import { Constants } from '@azure/msal-common';

import { AuthStatus } from '../base/auth-status';
import { AuthenticationService } from '../base/authentication.service';
import { AzureUsernameStorage } from './azure-username-storage';

@Injectable()
export class AzureAuthenticationService extends AuthenticationService {
  usernameStorage = new AzureUsernameStorage();

  constructor(
    @Inject(MSAL_GUARD_CONFIG) private msalGuardConfig: MsalGuardConfiguration,
    private msalService: MsalService,
    private http: HttpClient,
    _broadcastService: MsalBroadcastService,
  ) {
    super();

    // On getToken error/refreshToken error etc. user leegmaken?
    // broadcastService.msalSubject$
    //   .subscribe(message => console.log('[azure]', message));

    this.handleRedirect();
    this.loadStoredAccount();
  }

  login(): void {
    this.auth = AuthStatus.InProgress;

    this.msalService.loginRedirect({
      scopes: [Constants.OPENID_SCOPE, Constants.PROFILE_SCOPE],
    });
  }

  logout(): void {
    this.usernameStorage.clear();
    this.msalService.logout();
  }

  private loadStoredAccount() {
    const storedUsername = this.usernameStorage.get();
    if (storedUsername) {
      const account = this.msalService.instance.getAccountByUsername(storedUsername);

      if (account) {
        return this.auth = AuthStatus.Authorized;
      }
    }

    return this.auth = AuthStatus.UnAuthenticated;
  }

  private handleRedirect() {
    this.msalService.handleRedirectObservable()
      .subscribe({
        next: (result) => {
          if (result?.account) {
            this.usernameStorage.set(result.account.username);
            this.auth = AuthStatus.Authorized;
          }
        },
        error: (error) => console.log('[azure] ERROR', error)
      });
  }
}
