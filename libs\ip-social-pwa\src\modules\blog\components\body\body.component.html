<div
  *ngIf="editMode === false"
  class="ips-blog-content-wrapper ips-rich-content"
  [ips-html-content]="body"
></div>

<ng-container *ngIf="editMode === true">
  <div class="ion-text-center">
    <ion-button (click)="open()">
      <ion-icon name="create"></ion-icon>
    </ion-button>
  </div>

  <p *ngIf="!workingBody" class="ips-text-italic">{{ 'blog.emptyWorkingBodyHint' | transloco }}</p>

  <div
    class="ips-blog-content-wrapper ips-rich-content"
    [ips-html-content]="workingBody"
  ></div>
</ng-container>
