import { Pipe, PipeTransform } from '@angular/core';

import { IReference } from '../core/models';

const contentMap: Map<string, string> = new Map([
  ['Artikel', 'IproxContent'],
  ['Evenement', 'IproxContent'],
  ['Verjaardag', 'AnniversaryEvent'],
]);

@Pipe({
  name: 'referenceLink'
})
export class ReferenceLinkPipe implements PipeTransform {
  transform(input: IReference): string[] {
    return ['./' + (contentMap.get(input.collection) ?? input.collection), input.id];
  }
}
