﻿namespace IntranetProvincieZuidHolland.Modules.Handlers {
  using System.Linq;
  using System.Net;
  using System.Text;
  using System.Text.RegularExpressions;
  using System.Xml;

  using InfoProjects.Dxe.Linq;
  using InfoProjects.Dxe.Util;
  using InfoProjects.Dxe.Xdl;
  using InfoProjects.Iprox.Modules.Frontend.Shared;

  using Newtonsoft.Json.Linq;

  using XdlTransform = InfoProjects.Dxe.Xdl.XdlBuilder.XdlTransform;

  /// <summary>
  /// The group mail transform.
  /// </summary>
  public class GroupMailTransform {
    /// <summary>
    /// Gets the xdl transform.
    /// </summary>
    public static XdlTransform XdlTransform {
      get {
        return Transform;
      }
    }

    /// <summary>
    /// The transform.
    /// </summary>
    /// <param name="node">
    /// The current node.
    /// </param>
    /// <param name="context">
    /// The context.
    /// </param>
    /// <returns>
    /// The <see cref="XmlElement"/>.
    /// </returns>
    private static XmlElement Transform(XmlElement node, XdlContext context) {
      XmlElement result = node;
      var groupId = node.GetAttribute("groupId");
      var targets = node.GetAttribute("targets");

      if (!string.IsNullOrEmpty(groupId)) {
        Logger.Debug("Retrieving mailinglist for group: {0}", groupId);

        using (WebClient wc = new WebClient()) {
          wc.Encoding = Encoding.UTF8;
          var groupPersonsJson = wc.DownloadString(string.Format(Context.DefaultContext.GetProp("get_social_group_url", "http://intra.test.pzhintra/IntranetApi/Api/Public/Group/ByIproxId/{0}/persons?roles={1}"), groupId, targets));
          Logger.Debug("group json : {0}", groupPersonsJson);
          var groupPersonsArray = JArray.Parse(groupPersonsJson);

          foreach (var person in groupPersonsArray) {
            if (person["person"] != null && person["person"]["email"] != null && !string.IsNullOrEmpty(person["person"]["email"].Value<string>())) {
              var personElement = result.OwnerDocument.CreateElement("person");
              personElement.SetAttribute("name", person["person"]["fullName"].Value<string>());
              personElement.SetAttribute("email", person["person"]["email"].Value<string>());
              result.AppendChild(personElement);
            }
          }
        }
      }

      return result;
    }
  }
}
