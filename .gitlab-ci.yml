variables:
  IP_DOCKER_TAG: latest
  DOCKER_AUTH_CONFIG: |
    {
      "auths": {
        "docker.infoprojects.nl": {
          "auth": "********************************************************"
        }
      }
    }

stages:
  - build
  - deploy

default:
  image:
    name: docker.infoprojects.nl/docker/build:${IP_DOCKER_TAG}
    entrypoint:
      - powershell.exe
  tags:
    - docker-windows

build:
  stage: build
  rules:
    - if: '$CI_COMMIT_BRANCH'
  artifacts:
    paths:
      - dist
  script:
    - Invoke-NantBuild.ps1
    - New-GitlabRelease.ps1 -IfBranch main -FromVersionFile

push to dist:
  stage: deploy
  rules:
    - if: '$CI_COMMIT_BRANCH == "main"'
  script: Invoke-NantDistribute.ps1

push branch to dist:
  stage: deploy
  when: manual
  script: Invoke-NantDistribute.ps1