﻿namespace IntranetProvincieZuidHolland.Iprox.Handlers {
  using System;
  using InfoProjects.Dxe.Linq;
  using InfoProjects.Dxe.Process;
  using InfoProjects.Dxe.Process.Handler;
  using InfoProjects.Iprox.Model;
  
  /// <summary>
  /// Handler to remove IPROX item.
  /// </summary>
  public class RemoveItemHandler : IContextProcessHandler {
    /// <summary>
    /// Processes the unit.
    /// </summary>
    /// <param name="unit">
    /// Process unit.
    /// </param>
    /// <param name="context">
    /// Process context.
    /// </param>
    /// <returns>
    /// Result props with Success = true.
    /// </returns>
    public ResultProps Process(ProcessUnit unit, ProcessContext context) {
      var itmIdt = unit[Reserved.ID];
      if (String.IsNullOrEmpty(itmIdt)) {
        throw new ProcessException("ID must be set");
      }

      using (var cms = new IproxCms()) {
        cms.RemoveOnSubmit(cms.GetItem(itmIdt.To<int>()));
        cms.SubmitChanges(context);
      }

      var result = new ResultProps();
      result["Success"] = "true";
      return result;
    }
  }
}