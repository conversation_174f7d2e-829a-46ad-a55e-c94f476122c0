<div class="ips-timeline-entry-holder">
  <div class="ips-timeline-entry-header">
    <ips-timeline-activity [activity]="entry.activity"></ips-timeline-activity>
    <span class="ips-timeago">{{ entry.date | timeago }}</span>
  </div>
  <div class="ips-timeline-entry-content">
    <ips-timeline-content [entry]="entry"></ips-timeline-content>
  </div>
  <div class="ips-timeline-entry-footer" *ngIf="entry.metaConfig && entry.metaConfig.components.length > 0">
    <ips-meta class="ion-justify-content-end" [config]="entry.metaConfig" [allowCommentNavigation]="true"></ips-meta>
  </div>
</div>
