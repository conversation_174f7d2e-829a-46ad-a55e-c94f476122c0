import { AfterViewInit, Component, ViewChild } from '@angular/core';

import { IonTextarea } from '@ionic/angular';
import { CommentFormBaseComponent, CommentService } from '@ip/social-core';

@Component({
  selector: 'ips-comment-form',
  templateUrl: './comment-form.component.html',
  styleUrls: ['./comment-form.component.scss']
})
export class CommentFormComponent extends CommentFormBaseComponent implements AfterViewInit {
  @ViewChild('textarea', { read: IonTextarea })
  textarea!: IonTextarea;

  constructor(commentService: CommentService) {
    super(commentService);
  }

  ngAfterViewInit() {
    setTimeout(() => this.textarea.setFocus(), 50);
  }
}
