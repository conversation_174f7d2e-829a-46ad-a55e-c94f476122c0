<ng-container *ngIf="person$ | async as person; else skeleton">
  <ion-avatar *ngIf="showAvatar">
    <img [ips-avatar-src]="person">
  </ion-avatar>
  <span [routerLink]="[personRoute, person.id]">{{ person.fullName }}</span>
</ng-container>

<ng-template #skeleton>
  <ion-avatar *ngIf="showAvatar">
    <ion-skeleton-text animated></ion-skeleton-text>
  </ion-avatar>
  <span>
    <ion-skeleton-text animated style="width: 150px; height: 16px;"></ion-skeleton-text>
  </span>
</ng-template>
