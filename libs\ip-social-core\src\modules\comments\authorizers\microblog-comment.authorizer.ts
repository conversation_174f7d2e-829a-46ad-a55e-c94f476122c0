import { IReference, User } from '../../core/models';
import { MicroblogAccess } from '../../microblog/models/microblog-access.model';
import { CommentAuthorizer } from '../models/authorizer.model';
import { IComment } from '../models/comment.model';

export class MicroblogCommentAuthorizer extends CommentAuthorizer {
  // TODO nalopen iedere permission
  fn = {
    canView: (_comment: IComment | undefined): boolean => {
      switch (this.access) {
        case 'public':
        case 'limited':
          return true;
        case 'restricted':
          return this.group?.userIsMemberOrOwner(this.user.id);
        default:
          return false;
      }
    },
    canCreate: (comment: IComment | undefined): boolean => {
      switch (this.access) {
        case 'public':
          return true;
        case 'limited':
          if (!this.group) {
            return true;
          }

          return comment
            ? true
            : this.group.userIsMemberOrOwner(this.user.id);
        case 'restricted':
          return this.group?.userIsMemberOrOwner(this.user.id);
        default:
          return false;
      }
    },
    canEdit: (comment: IComment | undefined): boolean => {
      return this.user.id === comment?.userId || this.user.isCommunityManager();
    },
    canRemove: (comment: IComment | undefined): boolean => {
      return this.user.id === comment?.userId || this.user.isCommunityManager();
    },
    canAddAttachment: (_comment: IComment | undefined): boolean => {
      return true;
    },
    canRemoveAttachment: (_comment: IComment | undefined): boolean => {
      return true;
    },
  };

  constructor(
    private user: User,
    private reference: IReference,
    private access: MicroblogAccess,
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    private group?: any,
  ) {
    super();
  }
}
