import { Injectable } from '@angular/core';

import { SocialConfigProvider } from '../core/config/config-provider.service';
import { ISocialConfig } from '../core/config/config.model';
import { ILikeOption } from './models';

export interface IMetaSettings {
  likeOptions: ILikeOption[];
}

@Injectable()
export class MetaSettings implements IMetaSettings {
  private module: keyof ISocialConfig = 'meta';

  likeOptions = [
    {
      type: 'thumbs-up',
      unicode: '👍'
    },
    {
      type: 'thumbs-down',
      unicode: '👎'
    },
    {
      type: 'love',
      unicode: '❤'
    },
    {
      type: 'laughing',
      unicode: '😂'
    },
    {
      type: 'angry',
      unicode: '😡'
    },
    {
      type: 'sad',
      unicode: '😥'
    },
    {
      type: 'astonished',
      unicode: '😲'
    },
  ];

  constructor(settings: SocialConfigProvider) {
    Object.assign(this, settings.get<IMetaSettings>(this.module));
  }
}
