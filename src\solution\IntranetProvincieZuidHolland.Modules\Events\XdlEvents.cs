﻿namespace IntranetProvincieZuidHolland.Modules.Events {
  using System.Xml;

  using InfoProjects.Dxe.Linq;
  using InfoProjects.Dxe.Sql;
  using InfoProjects.Dxe.Util;
  using InfoProjects.Dxe.Xdl;

  /// <summary>
  /// Class for binding XDL events
  /// </summary>
  internal class XdlEvents {
    /// <summary>
    /// Attaches event handler.
    /// </summary>
    public static void Init() {
      XdlBuilder.NodeStarting += XdlEventArgs.Filter("/views/baseline/xdl/include/functions.xml", "item", HeaderFooter);
      XdlBuilder.NodeFinished += XdlEventArgs.Filter("/xdl/include/Form_page.xml", "range", AddMetaToPagClsRange);
      XdlBuilder.NodeFinished += XdlEventArgs.Filter("/xdl/include/Form_page.xml", "clusters", AddMetaToActPagCls);
    }

    /// <summary>
    /// Alter Pagclsrange
    /// </summary>
    /// <param name="sender">
    /// The sender.
    /// </param>
    /// <param name="e">
    /// The event arguments.
    /// </param>
    private static void AddMetaToPagClsRange(object sender, XdlEventArgs e) {
      if (!string.IsNullOrEmpty(e.XdlContext.GetProp("exitAppIdt")) && !string.IsNullOrEmpty(e.XdlContext.GetProp("PagClsRange"))) {
        var sql = SqlConnection.GetSqlConnection();
        var getMetaIdQuery = sql.GetSqlWriter(SqlType.SELECT);
        getMetaIdQuery.AddTable("PagClsTab", "PagCls");
        getMetaIdQuery.AddTable("ClsTab", "Cls", JoinType.INNER, "PagCls.ClsIdt = Cls.ClsIdt");
        getMetaIdQuery.AddCondition("PagCls.PagIdt", e.XdlContext.GetProp("PagIdt"));
        getMetaIdQuery.AddCondition("Cls.Nam", "Meta");
        getMetaIdQuery.AddCondition(string.Format("Pagcls.ParIdt in ({0})", e.XdlContext.GetProp("PagClsRange")));
        getMetaIdQuery.AddField("PagCls", "PagClsIdt", "PagClsIdt");

        var metaId = getMetaIdQuery.GetSingleValue<int>();
        e.XdlContext.SetProp("PagClsRange", string.Format("{0},{1}", e.XdlContext.GetProp("PagClsRange"), metaId));
      }
    }

    /// <summary>
    /// Alter ActPagCls
    /// </summary>
    /// <param name="sender">
    /// The sender.
    /// </param>
    /// <param name="e">
    /// The event arguments.
    /// </param>
    private static void AddMetaToActPagCls(object sender, XdlEventArgs e) {
      if (!string.IsNullOrEmpty(e.XdlContext.GetProp("exitAppIdt")) && !string.IsNullOrEmpty(e.XdlContext.GetProp("PagClsRange"))) {
        var sql = SqlConnection.GetSqlConnection();
        var getMetaIdQuery = sql.GetSqlWriter(SqlType.SELECT);
        getMetaIdQuery.AddTable("PagClsTab", "PagCls");
        getMetaIdQuery.AddTable("ClsTab", "Cls", JoinType.INNER, "PagCls.ClsIdt = Cls.ClsIdt");
        getMetaIdQuery.AddCondition("PagCls.PagIdt", e.XdlContext.GetProp("PagIdt"));
        getMetaIdQuery.AddCondition("Cls.Nam", "Meta");
        getMetaIdQuery.AddCondition(string.Format("Pagcls.ParIdt in ({0})", e.XdlContext.GetProp("PagClsRange")));
        getMetaIdQuery.AddField("PagCls", "PagClsIdt", "PagClsIdt");

        var metaId = getMetaIdQuery.GetSingleValue<int>();
        e.XdlContext.SetProp("ActPagClsIdt", string.Format("{0},{1}", e.XdlContext.GetProp("ActPagClsIdt"), metaId));
      }
    }

    private static void HeaderFooter(object sender, XdlEventArgs e) {
      var currentViewMap = e.XdlContext.GetProp("Map");
      if (currentViewMap == "binnenplein") {
        // conditie in node : <where condition="Itm.Aka IN ('header', 'footer')" /> wijzigen
        var whereAliasCondition = e.XdlNode.SelectSingleNode("where[starts-with(@condition, 'Itm.Aka')]");
        whereAliasCondition.Attributes["condition"].Value = "Itm.Aka in ('header-binnenplein', 'footer-binnenplein')";

        // deze where conditie verwijderen : <where condition="Itm.pagetype = Itm.Aka" />
        var whereTypeCondition = e.XdlNode.SelectSingleNode("where[@condition = 'Itm.pagetype = Itm.Aka']");
        whereTypeCondition.ParentNode.RemoveChild(whereTypeCondition);
      }
    }
  }
}
