@pzh-file-upload-btn-size: 40px;

.bs-component.bs-blog-controls > .ips-image,
person-profile-image > .bs-rows {
  margin-bottom: @pzh-outer-padding;
  position: relative;
}

person-profile-image > .bs-rows:first-child > .row {
  margin-bottom: 0;
}

.bs-blog-controls > .ips-image,
.bs-profile-image {
  > img {
    max-width: 100%;
  }
}

.bs-profile-image-uploader {
  bottom: @pzh-inner-padding;
  left: calc(50% - (@pzh-file-upload-btn-size / 2));
  position: absolute;

  input {
    height: @pzh-file-upload-btn-size;
    left: 0;
    opacity: 0;
    overflow: hidden;
    position: absolute;
    top: 0;
    width: @pzh-file-upload-btn-size;
  }

  .bs-file-upload {
    .padding-horizontal(5px);

    background-color: @pzh-white;
    border-radius: 50%;
    border: 0;
    box-shadow: @pzh-box-shadow;
    color: @branding-color-secondary;
    display: inline-block;
    height: @pzh-file-upload-btn-size;
    padding-top: 5px;
    width: @pzh-file-upload-btn-size;

    i {
      color: @branding-color-secondary;

      &::before {
        line-height: 32px;
      }
    }

    span {
      .sr-only();
    }

    &:hover,
    &.active {
      background-color: @branding-color-secondary;
      color: @pzh-white;
      cursor: pointer;

      i {
        color: @pzh-white;
      }
    }

    &:focus-within {
      outline: 1px solid @pzh-black;
      outline-offset: 1px;
    }
  }
}
