import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HttpInterceptor, HttpRequest } from '@angular/common/http';
import { Injectable } from '@angular/core';

import { Observable } from 'rxjs';

import { CoreSettings } from '../../core/core.settings';

@Injectable()
export class IproxAccessTokenInterceptor implements HttpInterceptor {
  protectedResources = [this.coreSettings.apiUrl];

  unprotectedResources = [this.coreSettings.apiUrl + 'language/'];

  constructor(private coreSettings: CoreSettings) { }

  intercept(req: HttpRequest<unknown>, next: <PERSON>ttp<PERSON>and<PERSON>): Observable<HttpEvent<unknown>> {
    if (this.protectedResources.some(resource => req.url.includes(resource)) && !this.unprotectedResources.some(resource => req.url.includes(resource))) {
      const userSession = this.currentSession();

      if (userSession && userSession.accessToken) {
        const newRequest = req.clone({
          headers: req.headers.set(
            'iprox-access-token',
            userSession.accessToken
          )
        });

        return next.handle(newRequest);
      }
    }

    return next.handle(req);
  }

  private currentSession() {
    const session = this.getSession();
    return session ? session.store.getInitialState() : null;
  }

  private getSession() {
    // TODO Typings for window.iprox
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    const windowIprox = (window as any).iprox;
    return windowIprox && windowIprox.userSession ? windowIprox.userSession : null;
  }
}
