﻿namespace IntranetProvincieZuidHolland.Iprox.Events.EventModel {
  using System.Diagnostics.CodeAnalysis;

  [SuppressMessage("StyleCop.CSharp.DocumentationRules", "SA1600:ElementsMustBeDocumented", 
    Justification = "Reviewed. Suppression is OK here.")]
  public class User {
    #region Public Properties

    public string Domain { get; set; }

    public string Login { get; set; }

    public string Name { get; set; }

    #endregion

    /// <summary>
    /// Returns a string that represents the current object.
    /// </summary>
    /// <returns>
    /// A string that represents the current object.
    /// </returns>
    public override string ToString() {
      return string.Format("{0}\\{1} ({2})", this.Domain, this.Login, this.Name).TrimStart('\\');
    }
  }
}