import { Component, HostBinding, Input } from '@angular/core';

@Component({
  selector: 'ips-timeline-title',
  templateUrl: './title.component.html',
  styleUrls: ['./title.component.scss'],
})
export class TimelineTitleComponent {
  @Input()
  title!: string;

  @Input()
  imageSrc?: string;

  @Input()
  mode: 'wide' | 'normal' = 'normal';

  @HostBinding('class.ips-full-image')
  get isFullImage(): boolean {
    return this.mode === 'wide';
  }

  @Input()
  reference!: {
    id: string | null;
    collection: string | null;
  };

  imageLoaded = false;
}
