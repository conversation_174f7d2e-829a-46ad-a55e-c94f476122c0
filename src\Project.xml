<?xml version="1.0" encoding="utf-8"?>
<project name="IntranetProvincieZuidHolland" alias="intranetprvzuidholland" dbarch="mssql" version="4.2">
  <props>
    <prop name="cs6.enabled" value="true" />
    <prop name="scss.include-path" value="node_modules" />
    <prop name="scss.excludedirs" value="include" />
    <prop name="use-iprox-nuget-packages" value="true" />
  </props>

  <!-- Environments in implementation -->
  <environments>
    <environment type="beheer" service="false" login="true">redactie</environment>
    <environment type="oper" service="false" login="true">intranet</environment>
  </environments>

  <!-- Core products -->
  <iprox version="4.9.2517" />
  <modules version="4.9.2517">
    <pagetype>applicatielink</pagetype>
    <pagetype>artikel</pagetype>
    <pagetype>enquete</pagetype>
    <pagetype>evenementenagenda</pagetype>
    <pagetype>faqindex</pagetype>
    <pagetype>fotoalbum</pagetype>
    <pagetype>index</pagetype>
    <pagetype>mailform</pagetype>
    <pagetype>media</pagetype>
    <pagetype>opiniepeiling</pagetype>
    <pagetype>prikbord</pagetype>
    <pagetype>styleguide</pagetype>
    <pagetype>zoeken</pagetype>

    <prototype>formulier</prototype>
    <prototype>selectie</prototype>

    <virtualtype>baseline</virtualtype>
    <virtualtype>closedusergroups</virtualtype>
    <virtualtype>entityfinder</virtualtype>
    <virtualtype>feedback</virtualtype>
    <virtualtype>googletagmanager</virtualtype>
    <virtualtype>ga4</virtualtype>
    <virtualtype>bundle</virtualtype>
    <virtualtype>apis</virtualtype>
    <virtualtype>social</virtualtype>
  </modules>

  <webconfigtransform type="build" alias="intranet" src="\intranet\www\Web-iprox.config" with="buildtransforms\Web.intranet.config" dest="\intranet\www\Web-iprox.config" />
  <webconfigtransform type="build" alias="redactie" src="\redactie\www\Web-iprox.config" with="buildtransforms\Web.redactie.config" dest="\redactie\www\Web-iprox.config" />

</project>
