import { Component, Input, ViewChild } from '@angular/core';
import { FormControl } from '@angular/forms';

import { IonInfiniteScroll, ModalController } from '@ionic/angular';
import { IPersonSearchState, PersonSearchService, SearchParams } from '@ip/social-core';
import { Observable } from 'rxjs';
import { tap } from 'rxjs/operators';

@Component({
  selector: 'ips-person-selection',
  templateUrl: './person-selection.component.html',
  providers: [PersonSearchService],
  styles: [':host ion-content { --padding-top: 0; }']
})
export class PersonSelectionComponent {
  @Input()
  title!: string;

  @Input()
  formControl!: FormControl;

  @ViewChild(IonInfiniteScroll)
  infiniteScroll?: IonInfiniteScroll;

  private get value(): string[] {
    return this.formControl.value;
  }

  state$: Observable<IPersonSearchState> = this.searchService.state$
    .pipe(tap(() => this.infiniteScroll?.complete()));

  constructor(public searchService: PersonSearchService, public modalController: ModalController) { }

  onSearchChange(event: CustomEvent<{ value: string }>) {
    if (event.detail.value) {
      this.searchService.search(new SearchParams({
        count: 15,
        start: 0,
        searchText: event.detail.value,
      }));
    }
    else {
      this.searchService.clear();
    }
  }

  personSelected(personId: string): void {
    if (this.value.includes(personId)) {
      return;
    }

    this.formControl.patchValue([...this.value, personId]);
  }

  removePerson(personId: string): void {
    this.formControl.patchValue(this.value.filter((p: string) => p !== personId));
  }
}
