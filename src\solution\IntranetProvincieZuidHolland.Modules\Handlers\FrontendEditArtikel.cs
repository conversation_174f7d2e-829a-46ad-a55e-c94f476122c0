﻿namespace IntranetProvincieZuidHolland.Modules.Handlers {
  using System.Collections.Generic;
  using InfoProjects.Dxe.Linq;
  using InfoProjects.Dxe.Prop;
  using InfoProjects.Iprox.Security.Handler;
  using InfoProjects.Iprox.Security.Lightbox;

  /// <summary>
  /// Front-end editing entrances
  /// </summary>
  public static class FrontendEditArtikel {
    /// <summary>
    /// Registers plug
    /// </summary>
    public static void Register() {
      LightboxHandler.KeyProperties.Add("EdtIdxItmIdt");
      LightboxHandler.RegisterMappingDelegate("EdtIdxItmIdt", "ItmIdt", GetEdtIdxItmIdt2ItmIdtMapping);
      LightboxHandler.RegisterContextDelegate("EdtIdxItmIdt", GetItmIdtContext);
    }

    /// <summary>
    /// Gets mapping from EdtIdxItmIdt to ItmIdt
    /// </summary>
    /// <param name="lightbox">Lightbox handler</param>
    /// <returns>Mapping from EdtIdxItmIdt to ItmIdt</returns>
    private static IDictionary<int, int> GetEdtIdxItmIdt2ItmIdtMapping(ILightbox lightbox) {
      return lightbox.GetMapping("ItmIdt", lightbox.GetValues("EdtIdxItmIdt"), "ItmTab");
    }

    /// <summary>
    /// Gets item context
    /// </summary>
    /// <param name="lightbox">Lightbox handler</param>
    /// <param name="itemId">Item identity</param>
    /// <returns>Item context</returns>
    private static PropCollection GetItmIdtContext(ILightbox lightbox, int itemId) {
      PropCollection context = null;
      Item item;
      if (lightbox.Mappings.ItemMapping.TryGetValue(itemId, out item) && item.Sts == 4) {
        context = new PropCollection();
        context["ItmIdt"] = itemId.ToIproxString();
        context["Sts"] = item.Sts.ToIproxString();
        context["CorDtm"] = item.CorDtm.ToIproxString();
        context["CorTyd"] = item.CorTyd.ToIproxString();
        context["LstPubDtm"] = item.LstPubDtm.ToIproxString();
        context["LstPubTyd"] = item.LstPubTyd.ToIproxString();
        context["ItmTyp"] = item.ItmTyp.ToIproxString();
        context["PagTypIdt"] = item.PagTypIdt.ToIproxString();
        context["SitIdt"] = item.SitIdt.ToIproxString();
      }

      return context;
    }
  }
}
