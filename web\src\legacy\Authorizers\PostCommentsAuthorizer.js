import { find } from 'lodash';

(function (angular) {
  'use strict';

  angular.module('Intranet').factory('PostCommentsAuthorizer', ['$ngRedux', 'GroupPostCommentAuthorizer', function ($ngRedux, GroupPostCommentAuthorizer) {
    var store = {};

    $ngRedux.connect(function (state) {
      return state.posts;
    })(store);

    return {
      canCreateComment: function (user, commentOwner) {
        var postOwner = resolvePostOwner(commentOwner);

        return GroupPostCommentAuthorizer.canCreateComment(user, postOwner);
      },
      canEditComment: function (user, comment, commentOwner) {
        return comment.userId === user.id || user.isCommunityManager();
      },
      canDeleteComment: function (user, comment, commentOwner) {
        return comment.userId === user.id || user.isCommunityManager();
      }
    };

    function resolvePostOwner(owner) {
      var post = find(store.items, { id: owner.id });
      return post && post.owner;
    }
  }]);
})(angular);
