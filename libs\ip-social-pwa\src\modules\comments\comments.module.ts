import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { ReactiveFormsModule } from '@angular/forms';
import { RouterModule } from '@angular/router';

import { CommentsModule as SocialCommentsModule } from '@ip/social-core';

import { IonicModule } from '@ionic/angular';

import { MetaModule } from '../meta/meta.module';
import { PersonModule } from '../person/person.module';
import { SharedModule } from '../shared/shared.module';
import { CommentFormComponent } from './components/comment-form/comment-form.component';
import { CommentComponent } from './components/comment/comment.component';
import { CommentsComponent } from './components/comments/comments.component';
import { CommentThreadComponent } from './components/comment-thread/comment-thread.component';
import { CommentDraftReminderComponent } from './components/comment-draft-reminder/comment-draft-reminder.component';
import { CommentAttachmentComponent } from './components/comment-attachment/comment-attachment.component';

@NgModule({
  imports: [
    CommonModule,
    IonicModule,
    SharedModule,
    SocialCommentsModule,
    MetaModule,
    PersonModule,
    ReactiveFormsModule,
    RouterModule
  ],
  declarations: [
    CommentsComponent,
    CommentFormComponent,
    CommentComponent,
    CommentThreadComponent,
    CommentDraftReminderComponent,
    CommentAttachmentComponent,
  ],
  exports: [
    CommentsComponent,
  ]
})
export class CommentsModule {
}
