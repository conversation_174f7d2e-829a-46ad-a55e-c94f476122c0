import { Component, EventEmitter, HostBinding, Input, Output } from '@angular/core';

import { FileProgressService, FileTypes, IAttachment, IAttachmentUpload, Upload } from '@ip/social-core';
import { Observable } from 'rxjs';

@Component({
  selector: 'ips-comment-attachment',
  templateUrl: './comment-attachment.component.html',
  styleUrls: ['./comment-attachment.component.scss'],
})
export class CommentAttachmentComponent {
  @Input()
  editMode = false;

  @Input()
  imageFormatSize?: string;

  @Output()
  remove = new EventEmitter<IAttachment>();

  @HostBinding('class.ips-attachment-metadata')
  showMetaData = false;

  placeholder?: IAttachmentUpload;

  file?: IAttachment;

  progress$?: Observable<Upload<unknown> | undefined>;

  @Input()
  set attachment(attachment: IAttachment | IAttachmentUpload) {
    if (isUpload(attachment)) {
      this.showMetaData = true;
      this.placeholder = attachment;
      this.progress$ = this.fileProgressService.get$(attachment.fileId);
    }
    else {
      this.file = attachment;
      this.showMetaData = !FileTypes.image.includes(attachment.file.contentType);
    }
  }

  constructor(private fileProgressService: FileProgressService) { }
}

function isUpload(attachment: IAttachment | IAttachmentUpload): attachment is IAttachmentUpload {
  return (attachment as IAttachmentUpload).placeholder;
}
