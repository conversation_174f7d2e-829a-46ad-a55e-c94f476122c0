import { BehaviorSubject, Observable } from 'rxjs';
import { map } from 'rxjs/operators';

import { IReference } from '../../core/models';
import { Meta } from '../models/meta.model';

export class MetaCache {
  private readonly CACHE_SIZE = 100;

  private readonly EXPIRE_TIME = 300 * 1000;

  private readonly FRESH_DURATION = 30 * 1000;

  private cache = new BehaviorSubject<Meta[]>([]);

  private get state(): Meta[] {
    return this.cache.value;
  }

  private set state(value: Meta[]) {
    this.cache.next(value);
  }

  add(collection: Meta[]) {
    this.state = [
      ...collection,
      ...this.state.filter(m => !collection.some(c => c.reference.match(m.reference))),
    ].splice(0, this.CACHE_SIZE);
  }

  get$(reference: IReference): Observable<Meta | undefined> {
    return this.cache.pipe(
      map(cache => cache.find(m => m.reference.match(reference)))
    );
  }

  get(reference: IReference): Meta | undefined {
    return this.state.find(m => m.reference.match(reference));
  }

  clearExpired(): void {
    const now = new Date().getTime();
    this.state = this.state.filter(m => (now - m.timeStamp) < this.EXPIRE_TIME);
  }

  isFresh(reference: IReference, now: number): boolean {
    return (now - (this.get(reference)?.timeStamp ?? 0)) < this.FRESH_DURATION;
  }

  addVisit(reference: IReference) {
    const meta = this.get(reference);

    if (meta && meta.visitCount) {
      meta.visitCount++;
      this.add([meta]);
    }
  }
}
