import { NgModule } from '@angular/core';
import { RouterModule } from '@angular/router';
import { BlogPageComponent } from '../blog/components/blog-page/blog-page.component';
import { BlogResolver } from '../blog/services/blog.resolver';
import { PERSON_ROUTE } from '../person/person-route';

import { PersonBlogListPageComponent } from '../profile/components/person-blog-list-page/person-blog-list-page.component';
import { PersonResolver } from '../profile/resolver/person.resolver';
import { UserPageComponent } from './components/user-page/user-page.component';
import { UserSettingsComponent } from './components/user-settings/user-settings.component';
import { UserModule } from './user.module';

@NgModule({
  imports: [
    UserModule,
    RouterModule.forChild([
      {
        path: '',
        component: UserPageComponent,
        pathMatch: 'full',
      },
      {
        path: 'notificaties',
        component: UserSettingsComponent,
      },
      {
        path: 'blogs',
        component: PersonBlogListPageComponent,
        resolve: {
          person: PersonResolver,
        },
        data: {
          isOwnProfile: true,
        }
      },
      {
        path: 'blogs/:blogId',
        component: BlogPageComponent,
        resolve: {
          blogId: BlogResolver
        },
        pathMatch: 'full',
      },
      {
        path: 'person/:id',
        loadChildren: () => import('../profile/profile-route.module').then(m => m.ProfileRouteModule),
      },
    ]),
  ],
  providers: [
    {
      provide: PERSON_ROUTE,
      useValue: '/you/person'
    },
  ]
})
export class UserRouteModule {
}
