import { Reference } from '../../../core/models';
import { IAttachment, ICommentTree } from '../comment.model';
import { CommentsApi } from './api.models';

export const mapComment = (apiComment: CommentsApi.IComment): ICommentTree => ({
  userId: apiComment.userId,
  id: apiComment.id,
  reference: new Reference(apiComment.reference.id, apiComment.reference.collection),
  creationDate: new Date(apiComment.creationDate),
  publicationDate: apiComment.publicationDate ? new Date(apiComment.publicationDate) : null,
  lastPublicationDate: apiComment.lastPublicationDate ? new Date(apiComment.lastPublicationDate) : null,
  lastModifiedDate: apiComment.lastModifiedDate && apiComment.lastModifiedDate !== apiComment.publicationDate ? new Date(apiComment.lastModifiedDate) : null,
  body: apiComment.body,
  workingBody: apiComment.hasChanges ? apiComment.workingBody : apiComment.body,
  commentCount: apiComment.commentCount,
  comments: (apiComment.comments ?? []).map(comment => mapComment(comment)),
  draft: apiComment.draft ? mapComment(apiComment.draft) : undefined,
  attachments: apiComment.attachments?.map(mapAttachment) ?? [],
  workingAttachments: (apiComment.hasChanges ? apiComment.workingAttachments : apiComment.attachments)?.map(mapAttachment) ?? [],
  hasChanges: apiComment.hasChanges,
});

export const mapAttachment = (apiAttachment: CommentsApi.IAttachment): IAttachment => {
  return {
    id: apiAttachment.id,
    userId: apiAttachment.userId,
    reference: new Reference(apiAttachment.reference.id, apiAttachment.reference.collection),
    fileId: apiAttachment.fileId,
    file: {
      contentLength: apiAttachment.file.contentLength,
      contentType: apiAttachment.file.contentType,
      userId: apiAttachment.file.userId,
      fileName: apiAttachment.file.fileName,
      id: apiAttachment.file.id,
      owner: new Reference(apiAttachment.file.owner.id, apiAttachment.file.owner.collection),
    },
  };
};
