import { IReference } from '../../../core/models/reference.model';
import { ILikeCount } from '../like-count.model';
import { IOwner } from '../owner.model';
import { IUserLike } from '../user-like.model';

export namespace MetaApi {
  export type CountResponse = IMetaCount[];

  export interface IMetaCount {
    comments: {
      count: number;
    };
    visits: {
      count: number;
    };
    likes: ILikeCount[];
    userLikes: IUserLike[];
    reference: IReference;
  }

  export interface IRequestBody {
    visits: IOwner[];
    comments: IOwner[];
    likes: string[];
  }
}
