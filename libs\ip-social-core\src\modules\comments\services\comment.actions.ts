import { IReference, Reference } from '../../core/models';
import { ICommentCollection } from '../models/comment-collection.model';
import { IAttachment, IComment, ICommentTree, IWorkingBodyUpdate } from '../models/comment.model';

export abstract class Action {
  constructor(public reference: Reference) { }

  exec(collections: ICommentCollection[]): ICommentCollection[] {
    return collections;
  }
}

export namespace Actions {
  export class Add extends Action {
    constructor(reference: IReference, private totalCount: number, private comments: ICommentTree[], private draft?: IComment) {
      super(new Reference(reference.id, reference.collection));
    }

    exec(collections: ICommentCollection[]): ICommentCollection[] {
      const newCollections: ICommentCollection[] = [
        {
          reference: this.reference,
          count: this.totalCount,
          comments: this.comments.map(c => ({ ...c, comments: undefined, draft: undefined })),
          draft: this.draft,
        },
        ...this.transformToCollection(this.comments),
      ];

      return [
        ...collections,
        ...newCollections
      ];
    }

    private transformToCollection(comments: ICommentTree[]): ICommentCollection[] {
      return comments.reduce<ICommentCollection[]>((collections, comment) => {
        if (comment.commentCount > 0 || comment.draft) {
          return [
            ...collections,
            this.createCollection(comment),
            ...this.transformToCollection(comment.comments),
          ];
        }

        return collections;
      }, []);
    }

    private createCollection(comment: ICommentTree): ICommentCollection {
      return {
        reference: new Reference(comment.id, 'Comment'),
        count: comment.commentCount,
        comments: comment.comments.map(c => ({ ...c, comments: undefined, draft: undefined })),
        draft: comment.draft,
      };
    }
  }

  export class Insert extends Action {
    constructor(reference: IReference, private value: ICommentTree | ICommentTree[], private prepend = true, private incrementCommentCount = true) {
      super(new Reference(reference.id, reference.collection));
    }

    exec(collections: ICommentCollection[]): ICommentCollection[] {
      insertComments(collections, this.reference, Array.isArray(this.value) ? this.value : [this.value], this.prepend, this.incrementCommentCount);

      return collections;
    }
  }

  export class AddOrUpdate extends Action {
    constructor(private comments: IComment[]) {
      super(new Reference('', '')); // unused;
    }

    exec(collections: ICommentCollection[]): ICommentCollection[] {
      const comments = this.comments
        .filter((c): c is Omit<IComment, 'publicationDate'> & { publicationDate: Date; } => c.publicationDate !== null)
        .sort((a, b) => a.publicationDate.getTime() - b.publicationDate.getTime());

      const topLevelComments = comments.filter(c => c.reference.collection !== 'Comment');
      const replyComments = comments.filter(c => c.reference.collection === 'Comment');

      topLevelComments.forEach(comment => this.insertOrUpdateComment(collections, comment, true));
      replyComments.forEach(comment => this.insertOrUpdateComment(collections, comment, false));

      return collections;
    }

    private insertOrUpdateComment(collections: ICommentCollection[], comment: IComment, topLevelComment: boolean): void {
      const collection = collections.find(c => c.reference.match(comment.reference));
      const existingComment = collection?.comments.find(c => c.id === comment.id);

      if (existingComment) {
        return this.updateCommentProperties(existingComment, comment);
      }

      if (collection && collection.comments.length < collection.count) {
        // This collection has a load-more button. Do not insert comment;
        return;
      }

      insertComments(collections, comment.reference, [comment], topLevelComment);
    }

    private updateCommentProperties(existingComment: IComment, comment: IComment): void {
      const updateProperties: Partial<IComment> = {
        body: comment.workingBody,
        attachments: comment.attachments,
        lastPublicationDate: comment.lastPublicationDate,
        lastModifiedDate: comment.lastModifiedDate,
      };

      Object.assign(existingComment, updateProperties);
    }
  }

  export class Update extends Action {
    constructor(reference: IReference, private comment: IComment) {
      super(new Reference(reference.id, reference.collection));
    }

    exec(collections: ICommentCollection[]): ICommentCollection[] {
      const collection = collections.find(c => c.reference.match(this.reference));

      if (collection) {
        collection.comments = collection.comments.map(comment => comment.id === this.comment.id
          ? this.comment
          : comment
        );
      }

      return collections;
    }
  }

  export class Remove extends Action {
    constructor(reference: IReference, private comment: IComment) {
      super(new Reference(reference.id, reference.collection));
    }

    exec(collections: ICommentCollection[]): ICommentCollection[] {
      const collection = collections.find(c => c.reference.match(this.reference));

      if (collection) {
        collection.comments = collection.comments.filter(comment => comment.id !== this.comment.id);
        collection.count--;
      }

      return collections;
    }
  }

  export class CreateDraft extends Action {
    constructor(reference: IReference, private comment: IComment) {
      super(new Reference(reference.id, reference.collection));
    }

    exec(collections: ICommentCollection[]): ICommentCollection[] {
      const collection = collections.find(c => c.reference.match(this.reference));

      if (collection) {
        collection.draft = this.comment;

        return collections;
      }

      return [...collections, this.createCollection(this.comment)];
    }

    private createCollection(comment: IComment): ICommentCollection {
      return {
        reference: comment.reference,
        count: 0,
        comments: [],
        draft: comment,
      };
    }
  }

  export class RemoveDraft extends Action {
    constructor(reference: IReference) {
      super(new Reference(reference.id, reference.collection));
    }

    exec(collections: ICommentCollection[]): ICommentCollection[] {
      const collection = collections.find(c => c.reference.match(this.reference));

      if (collection) {
        collection.draft = undefined;
      }

      return collections;
    }
  }

  export class UpdateWorkingBody extends Action {
    constructor(reference: IReference, private commentId: string, private data: IWorkingBodyUpdate) {
      super(new Reference(reference.id, reference.collection));
    }

    exec(collections: ICommentCollection[]): ICommentCollection[] {
      const collection = collections.find(c => c.reference.match(this.reference));

      if (collection) {
        if (collection.draft?.id === this.commentId) {
          collection.draft = this.updateComment(collection.draft, this.data);
          return collections;
        }

        collection.comments = collection.comments.map(comment => comment.id === this.commentId
          ? this.updateComment(comment, this.data)
          : comment
        );
      }

      return collections;
    }

    private updateComment(comment: IComment, data: IWorkingBodyUpdate): IComment {
      return {
        ...comment,
        ...data,
      };
    }
  }

  export class AddAttachment extends Action {
    constructor(
      reference: IReference,
      private commentId: string,
      private trackingId: string,
      private fileName: string,
    ) {
      super(new Reference(reference.id, reference.collection));
    }

    exec(collections: ICommentCollection[]): ICommentCollection[] {
      const collection = collections.find(c => c.reference.match(this.reference));

      if (collection) {
        if (collection.draft?.id === this.commentId) {
          collection.draft = this.updateWorkingAttachment(collection.draft);
          return collections;
        }

        collection.comments = collection.comments.map(comment => comment.id === this.commentId
          ? this.updateWorkingAttachment(comment)
          : comment
        );
      }

      return collections;
    }

    private updateWorkingAttachment(comment: IComment): IComment {
      return {
        ...comment,
        workingAttachments: [
          ...comment.workingAttachments,
          {
            placeholder: true,
            fileId: this.trackingId,
            fileName: this.fileName
          }
        ],
      };
    }
  }

  export class AddAttachmentComplete extends Action {
    constructor(
      reference: IReference,
      private commentId: string,
      private trackingId: string,
      private attachment: IAttachment,
    ) {
      super(new Reference(reference.id, reference.collection));
    }
    exec(collections: ICommentCollection[]): ICommentCollection[] {
      const collection = collections.find(c => c.reference.match(this.reference));

      if (collection) {
        if (collection.draft?.id === this.commentId) {
          collection.draft = this.updateWorkingAttachment(collection.draft);
          return collections;
        }

        collection.comments = collection.comments.map(comment => comment.id === this.commentId
          ? this.updateWorkingAttachment(comment)
          : comment
        );
      }

      return collections;
    }

    private updateWorkingAttachment(comment: IComment): IComment {
      return {
        ...comment,
        workingAttachments: comment.workingAttachments.map(a => a.fileId === this.trackingId
          ? this.attachment
          : a),
      };
    }
  }

  export class RemoveAttachment extends Action {
    constructor(
      reference: IReference,
      private commentId: string,
      private attachment: IAttachment,
    ) {
      super(new Reference(reference.id, reference.collection));
    }

    exec(collections: ICommentCollection[]): ICommentCollection[] {
      const collection = collections.find(c => c.reference.match(this.reference));

      if (collection) {
        if (collection.draft?.id === this.commentId) {
          collection.draft = this.updateWorkingAttachment(collection.draft);
          return collections;
        }

        collection.comments = collection.comments.map(comment => comment.id === this.commentId
          ? this.updateWorkingAttachment(comment)
          : comment
        );
      }

      return collections;
    }

    private updateWorkingAttachment(comment: IComment): IComment {
      return {
        ...comment,
        workingAttachments: comment.workingAttachments.filter(attachment => attachment.fileId !== this.attachment.fileId),
      };
    }
  }
}

const insertComments = (collections: ICommentCollection[], reference: Reference, comments: Array<ICommentTree | IComment>, prepend: boolean, incrementCommentCount = true) => {
  const collection = collections.find(c => c.reference.match(reference));

  if (collection) {
    if (incrementCommentCount) {
      collection.count += comments.length;
    }

    if (prepend) {
      collection.comments.unshift(...comments);
    }
    else {
      collection.comments.push(...comments);
    }
  }
  else {
    collections.push({
      reference,
      count: comments.length,
      comments: comments,
    });
  }

  comments.forEach(c => {
    if ('comments' in c) {
      const ref = c.comments[0]?.reference;
      if (ref && c.comments?.length > 0) {
        /* eslint-disable-next-line no-unused-vars, @typescript-eslint/no-unused-vars */
        insertComments(collections, ref, c.comments, prepend);
      }
    }
  });
};
