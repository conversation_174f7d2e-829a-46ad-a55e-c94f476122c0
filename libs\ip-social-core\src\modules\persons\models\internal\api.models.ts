import { IPaginationPage } from '../../../core/models/paginated-results.model';
import { IPerson } from '../person.model';

export namespace PersonsApi {
  export interface IPaginatedResults {
    count: number;
    // eslint-disable-next-line @typescript-eslint/ban-types
    facets: {};
    itemCount: number;
    pageRange: number;
    pages: IPaginationPage[];
    query: null;
    start: number;
    totalCount: number;
  }

  export interface ISearchResponse extends IPaginatedResults {
    items: IPerson[];
  }

  // TODO: Type this model properly.
  /* eslint-disable @typescript-eslint/no-explicit-any */
  export interface IPersonApiModel {
    id: string;
    active: boolean;
    birthDate: string;
    status?: string;
    slug: string;
    fullName: string;
    loginName: string;
    profileImage: string | null;
    phoneNumbers: { type: 'Mobile' | 'Telephone'; number: string; }[];
    roomNumber: string;
    locationId?: string;
    dataFields?: { name: string; value: string; }[];
    jobFunctions: [];
    department?: any;
    departments: [];
    organisation?: any;
    locations: [];
    propertyList1: [];
    propertyList2: [];
    weblinks?: [];
    skills?: [];
    workHistory?: [];
    memberships?: [];
    email: string;
    workDays: any;
    social: any;
  }
}
