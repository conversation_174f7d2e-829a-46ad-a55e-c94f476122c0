import { Component, Input, OnInit } from '@angular/core';

import { IMetaConfig, Meta, MetaService } from '@ip/social-core';
import { Observable } from 'rxjs';

@Component({
  selector: 'ips-meta',
  templateUrl: './meta.component.html',
  styleUrls: ['./meta.component.scss'],
})
export class MetaComponent implements OnInit {
  @Input()
  config!: IMetaConfig;

  @Input()
  allowCommentNavigation = false;

  meta$!: Observable<Meta>;

  constructor(private metaService: MetaService) { }

  ngOnInit() {
    this.meta$ = this.metaService.get$(this.config);
  }
}
