import { HttpParams } from '@angular/common/http';

export interface ICommentParams {
  count: number;
  start: number;
  order: 'ascending' | 'descending',
  depth?: number;
  depthCount?: number;
}

export class CommentParams implements ICommentParams {
  count = 3;

  start = 0;

  order: 'ascending' | 'descending' = 'descending';

  depth?: number;

  depthCount?: number;

  constructor(params?: Partial<ICommentParams>) {
    Object.assign(this, params);
  }

  toHttpParams(): HttpParams {
    let params = new HttpParams({
      fromObject: {
        count: this.count.toString(),
        start: this.start.toString(),
        order: this.order,
      }
    });

    if (this.depth) {
      params = params.append('depth', this.depth.toString());
    }

    if (this.depthCount) {
      params = params.append('depthCount', this.depthCount.toString());
    }

    return params;
  }
}
