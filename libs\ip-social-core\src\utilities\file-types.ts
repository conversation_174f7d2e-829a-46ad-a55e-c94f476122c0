export type FileType =
  'csv' |
  'pdf' |
  'document' |
  'spreadsheet' |
  'powerpoint' |
  'zip' |
  '7zip' |
  'image' |
  'bmp' |
  'jpg' |
  'gif' |
  'png';

export class FileTypes {
  private static map = new Map<string, string[]>([
    ['csv', ['text/csv', '.csv']],
    ['pdf', ['application/pdf', '.pdf']],
    ['document', [
      'application/msword',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.template',
      'application/vnd.ms-word.document.macroEnabled.12',
      'application/vnd.ms-word.template.macroEnabled.12',
      '.doc',
      '.docx',
    ]],
    ['spreadsheet', [
      'application/vnd.ms-excel',
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      'application/vnd.openxmlformats-officedocument.spreadsheetml.template',
      'application/vnd.ms-excel.sheet.macroEnabled.12',
      'application/vnd.ms-excel.template.macroEnabled.12',
      'application/vnd.ms-excel.addin.macroEnabled.12',
      'application/vnd.ms-excel.sheet.binary.macroEnabled.12',
      '.xls',
      '.xlsx',
    ]],
    ['powerpoint', [
      'application/vnd.ms-powerpoint',
      'application/vnd.openxmlformats-officedocument.presentationml.presentation',
      'application/vnd.openxmlformats-officedocument.presentationml.template',
      'application/vnd.openxmlformats-officedocument.presentationml.slideshow',
      'application/vnd.ms-powerpoint.addin.macroEnabled.12',
      'application/vnd.ms-powerpoint.presentation.macroEnabled.12',
      'application/vnd.ms-powerpoint.slideshow.macroEnabled.12',
      '.ppt',
      '.pptx',
    ]],
    ['zip', ['application/zip', '.zip']],
    ['7zip', ['.7z']],
    // images
    ['bmp', ['image/bmp', 'image/x-windows-bmp', '.bmp']],
    ['jpg', ['image/jpg', 'image/jpeg', '.jpg', '.jpeg']],
    ['gif', ['image/gif', '.gif']],
    ['png', ['image/png', '.png']],
  ]);

  static get(types: FileType[]): string[] {
    const codes = new Set<string>();
    let typesToGet = types;

    if (typesToGet.includes('image')) {
      const imageTypes = ['image', 'bmp', 'jpg', 'gif', 'png'];
      typesToGet = typesToGet.filter(t => !imageTypes.includes(t));
      this.image.forEach(code => codes.add(code));
    }

    typesToGet.forEach(t => {
      (this.map.get(t) ?? []).forEach(code => codes.add(code));
    });

    return [...codes];
  }

  static get pdf(): string[] {
    return [
      ...this.map.get('pdf') ?? [],
    ];
  }

  static get image(): string[] {
    return [
      ...this.map.get('bmp') ?? [],
      ...this.map.get('jpg') ?? [],
      ...this.map.get('gif') ?? [],
      ...this.map.get('png') ?? [],
    ];
  }

  static get acceptsImage(): string {
    return this.image.join(', ');
  }
}
