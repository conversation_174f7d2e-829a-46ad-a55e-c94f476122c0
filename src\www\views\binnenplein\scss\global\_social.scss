.z-user-alerts,
.z-user-alerts-important,
.z-user-alerts-personal {
  @include large {
    width: calc(100% - #{$pzh-medium-gutter}) !important; // width calculate nodig vanwege 60px gutter
  }

  @include medium {
    width: calc(100% - #{$pzh-small-gutter}) !important; // width calculate nodig vanwege 48px gutter
  }

  @media screen and (min-width: $bl-mq-large) {
    width: calc(100% - #{$pzh-large-gutter}) !important; // width calculate nodig vanwege 75px gutter
  }

  > .grid-blok.type-social-user-alerts > .grid-element {
    padding-right: 0 !important;
  }

  ips-user-alerts {
    .ips-buttons {
      .btn {
        &,
        i {
          color: $pzh-dark-blue !important;
        }
      }
    }
  }
}

// Required by dashboard drag-and-drop.
.grid-dashboard {
  .type-carrousel.slider-loaded {
    animation: none !important;
  }

  .slider .slick-slide .slide img {
    height: inherit !important;
    max-height: inherit !important;
    max-width: 100% !important;
    width: 100% !important;
  }

  .slick-dots {
    position: relative;
  }
}

button {
  &.ips-indicator {
    color: $pzh-white;
    font-size: 100%;
    font-weight: normal;
  }

  &.activate-media {
    border: none;
  }
}

.ips-buttons button.ips-button {
  color: $pzh-white;
  font-size: 100%;
  font-weight: normal;
}
