<ion-list *transloco="let t;" lines="none">
  <ion-item-group>
    <form *ngIf="formGroup" [formGroup]="formGroup">
      <ion-item-divider lines>
        <ion-label>{{ t('settings.notifications.title') }}</ion-label>
      </ion-item-divider>

      <ion-item>
        <ion-label>{{ t('settings.notifications.push') }}</ion-label>
        <ion-toggle formControlName="enabled"></ion-toggle>
      </ion-item>

      <ion-item>
        <ion-label>{{ t('settings.notifications.outsideOfficeHours') }}</ion-label>
        <ion-toggle
          formControlName="outsideOfficeHours"
          [attr.disabled]="formGroup.value.enabled === false"
        ></ion-toggle>
      </ion-item>

      <ion-item-divider>
        <ion-label>{{ t('settings.notifications.activities.title') }}</ion-label>
      </ion-item-divider>

      <ion-item *ngFor="let activity of activities">
        <ion-label>{{ t('settings.notifications.activities.' + activity) }}</ion-label>
        <ion-toggle
          [formControlName]="activity"
          [attr.disabled]="formGroup.value.enabled === false"
        ></ion-toggle>
      </ion-item>
    </form>
  </ion-item-group>
</ion-list>
