import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { RouterModule } from '@angular/router';

import { IonicModule } from '@ionic/angular';
import { CoreModule, PersonsModule } from '@ip/social-core';

import { SharedModule } from '../shared/shared.module';
import { PersonChipComponent } from './components/person-chip/person-chip.component';
import { PersonItemComponent } from './components/person-item/person-item.component';
import { PersonComponent } from './components/person/person.component';
import { AvatarSrcDirective } from './directives/avatar-src.directive';

@NgModule({
  imports: [
    CommonModule,
    IonicModule,
    RouterModule,
    SharedModule,
    PersonsModule,
    CoreModule
  ],
  declarations: [
    PersonComponent,
    PersonChipComponent,
    PersonItemComponent,
    AvatarSrcDirective,
  ],
  providers: [
  ],
  exports: [
    PersonComponent,
    PersonChipComponent,
    PersonItemComponent,
    AvatarSrcDirective,
  ]
})
export class PersonModule {
}
