<ion-card [id]="'comment-' + comment.id" (ipsPressed)="pressed()">
  <ion-card-content>
    <div class="ips-comment-header">
      <ips-person [id]="comment.userId"></ips-person>
      <div class="ips-comment-date">
        <span *ngIf="comment.lastModifiedDate">{{ 'comment.edited' | transloco }} • </span>
        {{ comment.creationDate | timeago }}
      </div>
    </div>

    <div class="ips-comment-body" [class.ips-edit-mode]="editMode">
      <div *ngIf="!editMode" class="ips-comment-content ips-content-wrap" [innerHTML]="comment.body | linky"></div>

      <ips-comment-form
        *ngIf="editMode"
        [settings]="settings"
        [reference]="comment.reference"
        [comment]="comment"
        (saved)="editMode = false"
        (cancel)="editMode = false"
      ></ips-comment-form>

      <ng-container *ngIf="settings.enableAttachments && editMode === false && comment.attachments.length > 0">
        <div class="ips-attachments-divider"></div>
        <ul class="ips-attachments">
          <li *ngFor="let attachment of comment.attachments">
            <ips-comment-attachment [attachment]="attachment" [imageFormatSize]="settings.imageFormatSize"></ips-comment-attachment>
          </li>
        </ul>
      </ng-container>
    </div>

    <div class="ips-comment-footer">
      <div *ngIf="!editMode" class="ips-comment-actions">
        <ion-button
          *ngIf="canReply && ('Create' | cAuthorize: authorizer)"
          size="small"
          [attr.aria-label]="'comments.reply' | transloco"
          (click)="reply.emit(comment.id)"
        >
          <ion-icon name="arrow-undo"></ion-icon>
        </ion-button>
      </div>
      <div *ngIf="settings.enableLike && !editMode" class="ips-meta-container">
        <ips-meta
          class="ion-justify-content-end"
          [config]="{ reference: { collection: 'Comment', id: comment.id }, components: metaComponents }"
        ></ips-meta>
      </div>
    </div>

    <div *ngIf="comment.hasChanges && !editMode && ('Edit' | cAuthorize: authorizer:comment)" class="ips-comment-has-changes">
      <span>{{ 'comment.unsavedChanges' | transloco }}</span>
    </div>
  </ion-card-content>
  <ion-ripple-effect type="unbounded"></ion-ripple-effect>
</ion-card>
