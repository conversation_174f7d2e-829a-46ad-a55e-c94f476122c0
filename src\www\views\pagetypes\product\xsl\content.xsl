<?xml version="1.0" encoding="utf-8"?>

<xsl:stylesheet version="1.0"
                xmlns="http://www.w3.org/1999/xhtml"
                xmlns:xsl="http://www.w3.org/1999/XSL/Transform"
                xmlns:msxsl="urn:schemas-microsoft-com:xslt"
                xmlns:formatter="urn:formatter"
                extension-element-prefixes="msxsl formatter">

  <xsl:template match="zone[@Aka = 'titel']" mode="grid-row-start">
    <xsl:param name="context" select="."/>

    <xsl:variable name="producttype" select="$context/ancestor::content[1]/descendant::cluster[Nam = 'Meta']/veld[Nam = 'Type']/item/Trf" />
    <xsl:variable name="icoon" select="$context/ancestor::content[1]/product/iconen/icoon[@Wrd = $producttype and Txt/span/img]" />
    <xsl:if test="$icoon">
      <img class="library-icon" alt="">
        <xsl:attribute name="src">
          <xsl:choose>
            <xsl:when test="starts-with($icoon/Txt/span/img/@src, '/contents')">
              <xsl:text>/contents/</xsl:text>
            </xsl:when>
            <xsl:otherwise>
              <xsl:text>/publish/</xsl:text>
            </xsl:otherwise>
          </xsl:choose>
          <xsl:text>library/</xsl:text>
          <xsl:value-of select="$icoon/@LibCatIdt" />
          <xsl:text>/</xsl:text>
          <xsl:value-of select="$icoon/@Src" />
        </xsl:attribute>
      </img>
    </xsl:if>
  </xsl:template>

  <xsl:template match="content[page/@pagetype = 'product']" mode="grid-content-wrap">
    <xsl:apply-templates select="." mode="grid-element-wrapper">
      <xsl:with-param name="blok-rol" select="'productgegevens'"/>
      <xsl:with-param name="inside-template" select="document('')/*/xsl:template[@name='productgegevens']"/>
    </xsl:apply-templates>
  </xsl:template>

  <xsl:template match="xsl:template[@name='productgegevens']" name="productgegevens">
    <xsl:param name="context" select="null"/>

    <xsl:apply-templates select="$context/descendant::cluster[Nam = 'Product']/veld[Nam = 'Beschrijving']" mode="identify" />
    <xsl:apply-templates select="$context/descendant::cluster[Nam = 'Product']/cluster[Nam = 'Meer informatie']/descendant::veld[Nam = 'Link']" mode="identify" />
  </xsl:template>

  <xsl:template match="/data/content[page/@pagetype = 'product']/page//layout/zone[@Aka = 'seealso']" mode="grid-row-start">
    <xsl:apply-templates select="ancestor::content" mode="grid-element-wrapper">
      <xsl:with-param name="zone" select="."/>
      <xsl:with-param name="blok-rol" select="'productacties'"/>
      <xsl:with-param name="inside-template" select="document('')/*/xsl:template[@name='productacties']"/>
    </xsl:apply-templates>
  </xsl:template>

  <xsl:template match="xsl:template[@name='productacties']" name="productacties">
    <xsl:param name="context" select="null"/>

    <xsl:apply-templates select="$context/page/cluster[Nam = 'Product']/cluster[Nam = 'Regel nu']/descendant::veld[Nam = 'Link']" mode="identify">
      <xsl:with-param name="base_class" select="'regel-nu'"/>
    </xsl:apply-templates>
  </xsl:template>

</xsl:stylesheet>
