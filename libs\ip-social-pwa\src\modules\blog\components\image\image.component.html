<div class="ips-blog-image-wrapper">
  <div class="ips-blog-image-background-mask"></div>
  <img
    class="ips-blog-image-background"
    aria-hidden="true"
    ips-blog-image-src
    [blogId]="blogId"
    [image]="image"
  >
  <img
    class="ips-blog-image"
    ips-blog-image-src
    alt=""
    [blogId]="blogId"
    [image]="image"
    size="wide-large"
  >
  <ng-container *ngIf="editMode">
    <ion-button *ngIf="image" (click)="remove()" color="light">
      <ion-icon name="trash-outline"></ion-icon>
    </ion-button>
    <ion-button
      *ngIf="!image"
      (click)="hiddenInputElement.click()"
      [disabled]="progress !== undefined && progress.state !== 'READY'"
    >
      <ion-icon name="cloud-upload-outline"></ion-icon>
    </ion-button>
  </ng-container>

  <ips-blog-image-upload-progress [blogId]="blogId"></ips-blog-image-upload-progress>
</div>

<div [formGroup]="formGroup">
  <input
    #hiddenInputElement
    class="ion-hide"
    type="file"
    formControlName="image"
    [accept]="accepts"
  />
</div>
