<ips-error-page *ngIf="responseError" [error]="responseError"></ips-error-page>

<ng-container *ngIf="blog$ | async as blog;">
  <ion-grid *ngIf="formGroup !== undefined && !responseError" [class.ips-edit-mode]="editMode">

    <ion-row>
      <ion-col size="12">

        <ips-blog-image
          [editMode]="editMode"
          [blogId]="blog.id"
          [image]="blog.image"
          [formGroup]="formGroup"
        ></ips-blog-image>

      </ion-col>
    </ion-row>

    <ion-row>
      <ion-col size="12">

        <ips-blog-title
          [editMode]="editMode"
          [title]="blog.title"
          [formGroup]="formGroup"
        ></ips-blog-title>

        <ips-blog-published
          [editMode]="editMode"
          [blog]="blog"
          [canEdit]="blog | canEditBlog | async"
          [synchronizing]="synchronizing"
        ></ips-blog-published>

        <ips-blog-author
          [enableAuthors]="settings.enableAuthors"
          [editMode]="editMode"
          [userId]="blog.userId"
          [formGroup]="formGroup"
          [class.ips-display-none]="editMode && settings.enableAuthors === false"
        ></ips-blog-author>

      </ion-col>
    </ion-row>

    <ion-row>
      <ion-col size="12">

        <ips-blog-body
          [editMode]="editMode"
          [body]="blog.body"
          [workingBody]="blog.workingBody"
          [synchronizing$]="synchronizing$"
          [editorConfig]="editorConfig"
          [formGroup]="formGroup"
        ></ips-blog-body>

      </ion-col>
    </ion-row>

    <ion-row *ngIf="editMode === false">
      <ion-col size="12">

        <ips-meta class="ion-justify-content-end" [config]="{ reference: { collection: 'Blog', id: blog.id }, components: settings.metaComponents }"></ips-meta>

      </ion-col>
    </ion-row>

  </ion-grid>

  <ips-comments *ngIf="editMode === false" [reference]="{ collection: 'Blog', id: blog.id }"></ips-comments>

  <ips-visit-tracker *ngIf="blog.published" [reference]="{ collection: 'Blog', id: blog.id }"></ips-visit-tracker>

  <ips-blog-fab
    slot="fixed"
    *ngIf="blog | canEditBlog | async"
    [activated]="editMode"
    [canPublish]="blog.canPublish"
    [synchronizing]="synchronizing"
    (toggle)="toggleEditMode()"
    (remove)="removeBlog(blog.id)"
    (publish)="publishBlog(blog.id)"
  ></ips-blog-fab>

</ng-container>
