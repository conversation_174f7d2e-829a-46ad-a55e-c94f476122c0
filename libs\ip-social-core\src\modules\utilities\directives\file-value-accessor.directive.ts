import { Directive, forwardRef, HostListener, Provider } from '@angular/core';
import { ControlValueAccessor, NG_VALUE_ACCESSOR } from '@angular/forms';

@Directive({
  // eslint-disable-next-line @angular-eslint/directive-selector
  selector: 'input[type=file][formControlName]',
  providers: [
    { provide: NG_VALUE_ACCESSOR, useExisting: FileValueAccessorDirective, multi: true },
  ]
})
export class FileValueAccessorDirective implements ControlValueAccessor {

  @HostListener('change', ['$event'])
  change($event: Event) {
    this.onChange(($event.target as HTMLInputElement).files);
  }

  @HostListener('blur')
  blur() {
    this.onTouched();
  }

  /* eslint-disable @typescript-eslint/no-explicit-any */
  /* eslint-disable @typescript-eslint/no-empty-function */
  writeValue(_: any) { }

  onChange = (_: any) => { };
  onTouched = () => { };

  registerOnChange(fn: any) { this.onChange = fn; }
  registerOnTouched(fn: any) { this.onTouched = fn; }
  /* eslint-enable @typescript-eslint/no-explicit-any */
  /* eslint-enable @typescript-eslint/no-empty-function */
}

export const FILE_VALUE_ACCESSOR: Provider = {
  provide: NG_VALUE_ACCESSOR,
  useExisting: forwardRef(() => FileValueAccessorDirective),
  multi: true,
};
