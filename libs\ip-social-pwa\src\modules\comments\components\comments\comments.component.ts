import { Component, ElementRef } from '@angular/core';

import { CommentAuthorizerService, CommentsBaseComponent, CommentService, CommentSettings } from '@ip/social-core';

@Component({
  selector: 'ips-comments',
  templateUrl: './comments.component.html',
  styleUrls: ['./comments.component.scss'],
  providers: [CommentService]
})
export class CommentsComponent extends CommentsBaseComponent {
  constructor(
    private elem: ElementRef,
    commentService: CommentService,
    commentAuthorizerService: CommentAuthorizerService,
    commentSettings: CommentSettings,
  ) {
    super(commentService, commentAuthorizerService, commentSettings);
  }

  scrollToComment(commentId: string): void {
    setTimeout(() => {
      const element = this.elem.nativeElement.querySelector(`#comment-${commentId}`);
      if (element && element.getBoundingClientRect().bottom > window.innerHeight) {
        element.scrollIntoView({ behavior: 'smooth', block: 'end' });
      }
    }, 100);
  }
}
