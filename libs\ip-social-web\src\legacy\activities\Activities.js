(function (angular) {
  'use strict';

  angular.module('Intranet.Activities', [])

  .constant('activitiesSettings', {
    type: null,
    itemsPerPage: 10
  })
  .config(['UrlServiceProvider', 'config', 'iproxRedirectItem', function (UrlServiceProvider, config, iproxRedirectItem) {
    UrlServiceProvider.registerUrl('activity-context', function (context) {
      if (context.iproxId) {
        return iproxRedirectItem(config, context);
      }
      // context is een persoon

    });
  }]);
})(angular);
