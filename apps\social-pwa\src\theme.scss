/* https://ionicframework.com/docs/theming/color-generator */

:root {
  --ion-color-primary: #265077;
  --ion-color-primary-rgb: 38,80,119;
  --ion-color-primary-contrast: #ffffff;
  --ion-color-primary-contrast-rgb: 255,255,255;
  --ion-color-primary-shade: #214669;
  --ion-color-primary-tint: #3c6285;

  --ion-color-secondary: #8CA800;
  --ion-color-secondary-rgb: 140,168,0;
  --ion-color-secondary-contrast: #000000;
  --ion-color-secondary-contrast-rgb: 0,0,0;
  --ion-color-secondary-shade: #7b9400;
  --ion-color-secondary-tint: #98b11a;

  --ion-color-tertiary: #5260ff;
  --ion-color-tertiary-rgb: 82,96,255;
  --ion-color-tertiary-contrast: #ffffff;
  --ion-color-tertiary-contrast-rgb: 255,255,255;
  --ion-color-tertiary-shade: #4854e0;
  --ion-color-tertiary-tint: #6370ff;

  --ion-color-success: #2dd36f;
  --ion-color-success-rgb: 45,211,111;
  --ion-color-success-contrast: #ffffff;
  --ion-color-success-contrast-rgb: 255,255,255;
  --ion-color-success-shade: #28ba62;
  --ion-color-success-tint: #42d77d;

  --ion-color-warning: #9a6d00;
  --ion-color-warning-rgb: 154,109,0;
  --ion-color-warning-contrast: #ffffff;
  --ion-color-warning-contrast-rgb: 255,255,255;
  --ion-color-warning-shade: #886000;
  --ion-color-warning-tint: #a47c1a;

  --ion-color-danger: #eb445a;
  --ion-color-danger-rgb: 235,68,90;
  --ion-color-danger-contrast: #ffffff;
  --ion-color-danger-contrast-rgb: 255,255,255;
  --ion-color-danger-shade: #cf3c4f;
  --ion-color-danger-tint: #ed576b;

  --ion-color-dark: #222428;
  --ion-color-dark-rgb: 34,36,40;
  --ion-color-dark-contrast: #ffffff;
  --ion-color-dark-contrast-rgb: 255,255,255;
  --ion-color-dark-shade: #1e2023;
  --ion-color-dark-tint: #383a3e;

  --ion-color-medium: #74767E;
  --ion-color-medium-rgb: 116,118,126;
  --ion-color-medium-contrast: #ffffff;
  --ion-color-medium-contrast-rgb: 255,255,255;
  --ion-color-medium-shade: #66686f;
  --ion-color-medium-tint: #82848b;

  --ion-color-light: #f4f5f8;
  --ion-color-light-rgb: 244,245,248;
  --ion-color-light-contrast: #000000;
  --ion-color-light-contrast-rgb: 0,0,0;
  --ion-color-light-shade: #d7d8da;
  --ion-color-light-tint: #f5f6f9;

  --ion-text-color: #000000;
  --ion-text-color-rgb: 0,0,0;
  --ion-text-color-contrast: #ffffff;
  --ion-text-color-contrast-rgb: 255,255,255;
  --ion-text-color-shade: #000000;
  --ion-text-color-tint: #1a1a1a;

  --ripple-color: var(--ion-color-primary);

  --ion-font-family: 'Heebo', sans-serif;
}
