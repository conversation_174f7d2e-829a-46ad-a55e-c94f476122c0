import { HttpEvent, HttpEventType, HttpProgressEvent, HttpResponse } from '@angular/common/http';
import { Injectable } from '@angular/core';

import { BehaviorSubject, Observable, of, OperatorFunction, pipe } from 'rxjs';
import { delay, filter, map, scan, switchMap, tap, throttleTime } from 'rxjs/operators';

export interface Upload<T> {
  id: string;
  progress: number;
  state: 'READY' | 'PENDING' | 'IN_PROGRESS' | 'DONE';
  response: T | null;
}

export interface UploadComplete<T> extends Upload<T> {
  response: T;
  state: 'DONE';
}

export function isUpload<T>(value: Upload<T> | undefined): value is Upload<T> {
  return value !== undefined;
}

export function isUploadComplete<T>(value: Upload<T> | UploadComplete<T> | undefined): value is UploadComplete<T> {
  return value !== undefined && value.response !== null && value.state === 'DONE';
}

function isHttpResponse<T>(event: HttpEvent<T>): event is HttpResponse<T> {
  return event.type === HttpEventType.Response;
}

function isHttpProgressEvent(event: HttpEvent<unknown>): event is HttpProgressEvent {
  return (
    event.type === HttpEventType.DownloadProgress ||
    event.type === HttpEventType.UploadProgress
  );
}

const initialState = <T>(id: string): Upload<T> => ({
  id,
  state: 'PENDING',
  progress: 0,
  response: null,
});

const calculateState = <T>(upload: Upload<T>, event: HttpEvent<T>): Upload<T> => {
  if (isHttpProgressEvent(event)) {
    return {
      ...upload,
      progress: event.total
        ? Math.round((100 * event.loaded) / event.total)
        : upload.progress,
      state: 'IN_PROGRESS',
    };
  }

  if (isHttpResponse<T>(event)) {
    return {
      ...upload,
      progress: 100,
      state: 'DONE',
      response: event.body,
    };
  }

  return upload;
};

export const reportUploadProgress = <T>(id: string): OperatorFunction<HttpEvent<T>, T> => pipe(
  throttleTime(300, undefined, { leading: true, trailing: true }),
  switchMap(event => event.type === HttpEventType.Response ? of(event).pipe(delay(600)) : of(event)),
  scan(calculateState, initialState<T>(id)),
  tap((upload) => FileProgressService.report(upload)),
  filter(isUploadComplete),
  map(upload => upload.response),
);

type ProgressStorage = { [key: string]: Upload<unknown> };

@Injectable()
export class FileProgressService {
  private static data = new BehaviorSubject<ProgressStorage>({});

  private static get progressData() {
    return FileProgressService.data.value;
  }

  progress$ = FileProgressService.data.asObservable();

  public static report(upload: Upload<unknown>): void {
    FileProgressService.update(upload);

    if (upload.state === 'DONE') {
      setTimeout(() => FileProgressService.update({ ...upload, state: 'READY', progress: 0, response: null }), 50);
    }
  }

  private static update(upload: Upload<unknown>): void {
    FileProgressService.data.next({ ...FileProgressService.progressData, [upload.id]: upload });
  }

  get$(id: string): Observable<Upload<unknown> | undefined> {
    return this.progress$.pipe(
      map((data): Upload<unknown> | undefined => data[id]),
    );
  }

  getMultiple$(ids: string[]): Observable<Upload<unknown>[]> {
    return this.progress$.pipe(
      map((data): Upload<unknown>[] => ids.map(id => data[id] ?? initialState(id))),
    );
  }

  group$(tag: string) {
    return this.progress$.pipe(
      map((data): Upload<unknown>[] => Object.values(data).filter(upload => upload.id.startsWith(tag)))
    );
  }
}
