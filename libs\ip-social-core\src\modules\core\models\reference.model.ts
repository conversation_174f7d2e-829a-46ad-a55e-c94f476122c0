import { capitalize } from '../../../utilities/capitalize';

export interface IReference {
  id: string;
  collection: string;
}

export class Reference implements IReference {
  collection: string;

  constructor(public id: string, collection: string) {
    // SR: Niet leuk. 😐
    if (collection.toLowerCase() === 'calendarevent') {
      this.collection = 'CalendarEvent';
    }
    else {
      this.collection = capitalize(collection);
    }
  }

  public static convertFromString(value: string): Reference {
    const [collection, id] = value.split('/');
    return new Reference(id, capitalize(collection));
  }

  match(reference: IReference): boolean {
    return this.id === reference.id && this.collection === reference.collection;
  }

  toString(): string {
    return `${this.collection}/${this.id}`;
  }
}
