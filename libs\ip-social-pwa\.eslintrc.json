{"extends": ["../../.eslintrc.json"], "ignorePatterns": ["!**/*"], "overrides": [{"files": ["*.ts"], "extends": ["@infoprojects/eslint-config/angular-typescript"], "parserOptions": {"project": ["libs/ip-social-pwa/tsconfig.*?.json"]}, "rules": {"@angular-eslint/directive-selector": ["error", {"type": "attribute", "prefix": "ips", "style": "camelCase"}], "@angular-eslint/component-selector": ["error", {"type": "element", "prefix": "ips", "style": "kebab-case"}]}}, {"files": ["*.html"], "extends": ["plugin:@nrwl/nx/angular-template"], "rules": {}}]}