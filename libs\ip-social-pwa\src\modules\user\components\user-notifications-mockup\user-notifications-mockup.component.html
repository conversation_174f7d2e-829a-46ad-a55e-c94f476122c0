<h2>Je hebt 4 berichten</h2>

<ion-card *ngIf="view['view1']" class="ips-inbox-message ips-inbox-notification">
  <ion-card-content>
    <div class="ips-inbox-message-type">
      Nieuwe reacties
    </div>
    <div class="ips-inbox-message-content">
      <div class="ips-inbox-content-wrapper">
        <ips-person id="5cefa7f60779bc055872f491"></ips-person>
        <span> heeft samen met <strong>4 anderen</strong> op je blog 'Mijn eerste huis' gereageerd.</span>
      </div>
    </div>
    <div class="ips-inbox-message-actions">
      <ion-button class="ips-ion-text-button">Bekijk reacties</ion-button>
      <ion-button class="ips-ion-text-button" (click)="dismiss('view1')">Dismiss</ion-button>
    </div>
  </ion-card-content>
</ion-card>

<ion-card *ngIf="view['view2']" class="ips-inbox-message ips-inbox-notification">
  <ion-card-content>
    <div class="ips-inbox-message-type">
      Profiel
    </div>
    <div class="ips-inbox-message-content">
      <div class="ips-inbox-content-wrapper">
        <ips-person id="5cefa7f60779bc055872f491"></ips-person>
        <span> is het eens met je expertise 'Leuke dingen verzinnen'</span>
      </div>
    </div>
    <div class="ips-inbox-message-actions">
      <ion-button class="ips-ion-text-button" (click)="dismiss('view2')">Dismiss</ion-button>
    </div>
  </ion-card-content>
</ion-card>

<ion-card *ngIf="view['view3']" class="ips-inbox-message ips-inbox-notification-announcement">
  <ion-card-content>
    <div class="ips-inbox-message-type">
      Mededeling - persoonlijk
    </div>
    <div class="ips-inbox-message-content">
      <div class="ips-inbox-content-wrapper">
        Vul je sociale kanalen in, dan leren we je ook op een andere manier kennen.
      </div>
    </div>
    <div class="ips-inbox-message-actions">
      <ion-button class="ips-ion-text-button">Profiel bewerken</ion-button>
      <ion-button class="ips-ion-text-button" (click)="dismiss('view3')">Dismiss</ion-button>
    </div>
  </ion-card-content>
</ion-card>

<ion-card *ngIf="view['view4']" class="ips-inbox-message ips-inbox-notification-system">
  <ion-card-content>
    <div class="ips-inbox-message-type">
      Systeem
    </div>
    <div class="ips-inbox-message-content">
      <div class="ips-inbox-content-wrapper">
        Zouden wij een paar korte vragen mogen stellen om de app te kunnen verbeteren?
      </div>
    </div>
    <div class="ips-inbox-message-actions">
      <ion-button class="ips-ion-text-button">Naar enquete</ion-button>
      <ion-button class="ips-ion-text-button" (click)="dismiss('view4')">Dismiss</ion-button>
    </div>
  </ion-card-content>
</ion-card>
