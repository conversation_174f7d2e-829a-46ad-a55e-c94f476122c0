<div class="ips-timeline-image-holder" *ngIf="imageSrc">
  <div class="ips-timeline-image-ratio">
    <img
      class="ips-timeline-image ips-animate-image"
      [class.ips-loaded]="imageLoaded"
      [src]="imageSrc"
      (load)="imageLoaded = true"
    />
  </div>
</div>

<ips-timeline-page-link *ngIf="reference" [reference]="reference">
  <div class="ips-timeline-entry-content-block">
    <h2>{{ title }}</h2>
  </div>
</ips-timeline-page-link>

<div *ngIf="!reference" class="ips-timeline-entry-content-block">
  <h2>{{ title }}</h2>
</div>
