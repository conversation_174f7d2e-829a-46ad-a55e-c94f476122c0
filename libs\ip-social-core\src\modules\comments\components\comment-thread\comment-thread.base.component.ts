import { Component, Input } from '@angular/core';
import { Reference } from '../../../core/models';
import { ICommentSettings } from '../../comments.settings';
import { CommentAuthorizer } from '../../models/authorizer.model';
import { CommentParams } from '../../models/comment-params.model';
import { IComment, ICommentTree } from '../../models/comment.model';
import { CommentService } from '../../services/comment.service';

@Component({
  selector: 'ips-comment-thread-base',
  template: '',
})
export class CommentThreadBaseComponent {
  @Input()
  settings!: ICommentSettings;

  @Input()
  depth = 1;

  @Input()
  authorizer!: CommentAuthorizer;

  @Input()
  comment!: ICommentTree;

  openForm = false;

  constructor(private commentService: CommentService) { }

  trackByFn(index: number, comment: IComment): string {
    return comment.id;
  }

  reply() {
    this.openForm = true;
  }

  remove(comment: ICommentTree) {
    this.commentService.remove(comment)
      .subscribe();
  }

  loadMore(comment: ICommentTree) {
    this.commentService.loadMore(new Reference(comment.id, 'Comment'), new CommentParams({
      start: comment.comments.length,
      order: 'ascending'
    }))
      .subscribe();
  }
}
