/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable @typescript-eslint/no-empty-function */
import { CommonModule } from '@angular/common';
import { ModuleWithProviders, NgModule } from '@angular/core';
import { RouterModule } from '@angular/router';

import { IonicModule } from '@ionic/angular';
import { AuthenticationConfig, AuthGuard, CoreModule, MetaModule, UserService } from '@ip/social-core';
import { TranslocoModule } from '@ngneat/transloco';

import { LaunchScreenComponent } from './components/launch-screen/launch-screen.component';
import { UserMenuButtonComponent } from './components/user-menu-button/user-menu-button.component';
import { ModuleRouteProvidersFactory } from './route-providers';

@NgModule({
  declarations: [
    LaunchScreenComponent,
    UserMenuButtonComponent,
  ],
  imports: [
    CommonModule,
    RouterModule,
    IonicModule.forRoot({
      mode: 'md',
    }),
    CoreModule.forRoot(),
    MetaModule.forRoot(),
    TranslocoModule,
  ],
  exports: [
  ]
})
export class IpSocialPwaBootstrapperModule {
  constructor(userService: UserService) { }

  static forRoot(authentication: AuthenticationConfig): ModuleWithProviders<IpSocialPwaBootstrapperModule> {
    return {
      ngModule: IpSocialPwaBootstrapperModule,
      providers: [
        AuthGuard,
        ...ModuleRouteProvidersFactory(),
        ...authentication.getProviders()
      ]
    };
  }
}
