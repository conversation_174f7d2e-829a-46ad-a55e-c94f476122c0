import { Injectable } from '@angular/core';
import { ActivatedRouteSnapshot, Resolve, RouterStateSnapshot } from '@angular/router';

import { Observable, of } from 'rxjs';

@Injectable()
export class BlogResolver implements Resolve<string> {
  resolve(route: ActivatedRouteSnapshot, _state: RouterStateSnapshot): Observable<string> {
    const id = route.paramMap.get('blogId');
    if (!id) {
      throw new Error('Resolver can only be used on routes with :id param');
    }

    return of(id);
  }
}
