<ips-timeline-title
  class="ips-title-small"
  [title]="data.content.title"
  [reference]="{ collection: data.collection, id: data.id }"
></ips-timeline-title>

<div class="ips-timeline-entry-content-block">
  <p class="ips-comment-label">{{ 'timeline.commentLabel' | transloco }}</p>
  <div
    class="ips-preview-comment"
    (click)="expand()"
    [ips-html-content]="comment.body | truncate:{ limit: expanded ? 0 : previewLimit, suffix: 'timeline.commentMore' | transloco }"
  ></div>
</div>

<ips-meta
  class="ion-justify-content-end"
  [config]="{ reference: { collection: 'Comment', id: comment.id }, components: metaComponents }"
></ips-meta>
