import { Injectable } from '@angular/core';
import { ActivatedRouteSnapshot, Resolve, RouterStateSnapshot } from '@angular/router';

import { UserService } from '@ip/social-core';
import { Observable } from 'rxjs';
import { map, take } from 'rxjs/operators';

@Injectable()
export class IsOwnProfileResolver implements Resolve<boolean> {
  constructor(private userService: UserService) { }

  resolve(route: ActivatedRouteSnapshot, _state: RouterStateSnapshot): Observable<boolean> {
    const id = route.paramMap.get('id');

    return this.userService.currentUser$
      .pipe(
        map(user => !id || id === 'self' || id === user.id),
        take(1)
      );
  }
}
