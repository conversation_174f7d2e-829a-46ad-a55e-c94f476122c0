.type-carrousel {
  .navigation-container .buttons .toggle {
    @include button-focus();

    background-color: $bl-button-background-color;
    background-image: none;
    border-radius: $pzh-border-radius;
    color: $bl-button-color;
    opacity: 1;
    padding: 0;
    display: flex;
    justify-content: center;
    align-items: center;

    &:hover {
      background-color: $bl-button-hover-background-color;
    }

    &::before {
      display: inline-block;
      font-size: 1.5rem;
    }

    &.playing {
      @include icon($bl-slider-pause-icon, $bl-icon-type: $bl-slider-pause-icon-type);
    }

    &.pausing {
      @include icon($bl-slider-play-icon, $bl-icon-type: $bl-slider-play-icon-type);
    }
  }
}
