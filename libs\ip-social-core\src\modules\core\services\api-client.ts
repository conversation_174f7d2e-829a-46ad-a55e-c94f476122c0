import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';

import { CoreSettings } from '../core.settings';

/* eslint-disable */
function SocialHttpClientProxyFactory(apiUrl: string): ProxyHandler<HttpClient> {
  return {
    get(target: HttpClient, p: string | number | symbol, receiver: any): any {
      switch (p) {
        case 'request':
          return function() {
            arguments[1] = apiUrl + arguments[1];

            // @ts-ignore
            return target[p].apply(this, arguments as any);
          };

        default:
          return Reflect.get(target, p, receiver);
      }
    }
  };
}

export function SocialClientHttpFactory(http: HttpClient, settings: CoreSettings): SocialHttpClient {
  return new Proxy(http, SocialHttpClientProxyFactory(settings.apiUrl));
}

@Injectable()
export class SocialHttpClient extends HttpClient { }
