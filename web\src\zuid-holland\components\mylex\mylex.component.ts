import { HttpClient, HttpParams } from '@angular/common/http';
import { Component, ElementRef, HostBinding, OnDestroy, OnInit } from '@angular/core';
import { Router } from '@angular/router';

import { ISearchConfig } from '@pzh-local/mylex-search';

@Component({
  selector: 'ips-mylex-search',
  template: `<pzh-mylex-search-page *ngIf="searchConfig" [searchConfig]="searchConfig"></pzh-mylex-search-page>`
})
export class MylexComponent implements OnInit {
  searchConfig?: ISearchConfig;

  constructor(private element: ElementRef, private http: HttpClient, private router: Router) { }

  ngOnInit() {
    const configJson = this.element.nativeElement.getAttribute('data-config');
    const config = JSON.parse(configJson ?? '{}');

    if (config.contentApi) {
      this.http.get<IPageData>(config.contentApi)
        .subscribe(({ page }) => {
          if (!page.inhoud.mylexurl) {
            throw new Error('[mylex] - apiUrl is undefined.');
          }

          this.searchConfig = {
            apiUrl: page.inhoud.mylexurl,
            sisUrl: page.inhoud['stateninformatie-systeem-url'],
            noResultText: page.inhoud['tekst-bij-geen-resultaten'] ?? '<p>Er zijn geen resultaten die aan uw zoekopdracht voldoen.</p>',
            facets: page.facets?.map(f => ({ facetName: f.facetname })) ?? [],
          };

          /* Weird bug: possibly because component is not instantiated by the router
           * QueryParams is empty the first emit in the search.service.ts by renavigating on init with location.search
           * the search application will get the correct queryParams.
           */
          const search = new URLSearchParams(window.location.search);
          const queryParams: Record<string, string[]> = {};

          search.forEach((val, key) => queryParams[key] = queryParams[key] ? [...queryParams[key], val] : [val]);

          this.router.navigate([], {
            queryParams,
            queryParamsHandling: 'merge'
          });
        });

      return;
    }
  }
}

interface IPageData {
  page: {
    inhoud: {
      'tekst-bij-geen-resultaten'?: string;
      mylexurl?: string;
      'stateninformatie-systeem-url'?: string;
    };
    facets?: Array<{ facetname: string; }>;
  };
}
