.grid-blok.rol-social,
.grid-blok {
  a.btn,
  button.btn {
    .button-size(.5rem, .75rem, 1.25rem, 1.375rem, @pzh-border-radius);

    &.btn-sm {
      .button-size(.375rem, .75rem, 1rem, 1.125rem, @pzh-border-radius);
    }

    &.btn-xs {
      .button-size(.25rem, .5rem, .75rem, 1.125rem, @pzh-border-radius);
    }

    color: @btn-default-color;

    &.btn-link {
      color: @linkkleur;

      > i {
        color: @linkkleur;
      }

      &:hover {
        color: @pzh-dark-green;

        > i {
          color: @pzh-dark-green;
        }
      }
    }

    &.ips-button.ips-button.ips-button,
    &:not(.btn-link) {
      &:hover,
      &:active {
        background-color: @pzh-green;
        border-color: @pzh-green;
      }

      &:focus {
        border-color: @btn-focus-border-color;
        outline: 2px auto @btn-focus-outline-color;
        outline-offset: 3px;
      }

      &[disabled] {
        background-color: @pzh-grey-6;
        border-color: @pzh-grey-6;
      }

      > i {
        color: @btn-default-color;
      }
    }

    > .caret,
    > i {
      color: @btn-default-color;
    }

    &.ips-primary {
      > i {
        color: @btn-default-color;
      }
    }
  }
}

.grid-title {
  position: relative;

  .title-buttons {
    float: right;
    margin-top: -.5rem;

    a,
    button {
      float: inherit;

      &.btn {
        border-radius: 0 !important;
        vertical-align: top;

        &:first-child {
          border-bottom-left-radius: @pzh-border-radius !important;
        }

        &:last-child {
          border-bottom-right-radius: @pzh-border-radius !important;
        }

        +.btn {
          margin-left: 2px;
        }
      }
    }
  }
}

.z-social-edit-button {
  margin-bottom: 2 * @pzh-outer-padding;
  margin-top: 2 * @pzh-outer-padding;
}

.modal-dialog {
  .modal-header {
    button.close {
      color: @btn-default-color;
      padding: @pzh-unit / 4 @pzh-unit;
      opacity: 1;
      text-shadow: none;
    }
  }
}
