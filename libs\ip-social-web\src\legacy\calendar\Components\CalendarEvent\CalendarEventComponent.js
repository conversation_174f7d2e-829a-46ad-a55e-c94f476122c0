import template from '!html-loader?minimize=true&conservativeCollapse=false&collapseInlineTagWhitespace=true!./CalendarEvent.html';

(function (angular, undefined) {
  'use strict';

  angular.module('Intranet.Calendar').component('ipsCalendarEvent', {
    bindings: {
      calendarEvent: '<',
      settings: '<',
      listModeFn: '&?listMode'
    },
    template: template,
    controller: ['$location', '$ngRedux', '$q', 'CalendarActions', 'CalendarAuthorizer', 'DialogsService', 'IproxPageService', 'VisitCountService', 'OwnerToEntity', 'User',
      function ($location, $ngRedux, $q, CalendarActions, CalendarAuthorizer, DialogsService, IproxPageService, VisitCountService, OwnerToEntity, User) {
      var self = this;
      var store = {};

      self.$onInit = function () {
        self.listMode = !!self.listModeFn && self.listModeFn();

        self.$onDestroy = $ngRedux.connect(null, CalendarActions)(store);

        var authorizer = CalendarAuthorizer(self.calendarEvent.owner);

        $q.all({
          user: User.user.$promise,
          entity: OwnerToEntity(self.calendarEvent.owner)
        }).then(function (results) {
          self.permissions = {
            canEdit: authorizer.canEditCalendarEvent(results.user, results.entity, self.calendarEvent),
            canDelete: authorizer.canDeleteCalendarEvent(results.user, results.entity, self.calendarEvent)
          };
        });
      };

      self.$onChanges = function () {
        self.multiDayEvent = self.calendarEvent.startDate &&
          self.calendarEvent.endDate &&
          self.calendarEvent.startDate.toDateString() !== self.calendarEvent.endDate.toDateString();
      };

      self.toggleOpen = function ($event) {
        store.toggleOpen(self.calendarEvent);


        if (!self.calendarEvent.$$open) {
          VisitCountService.registerInlineVisit('CalendarEvent', self.calendarEvent.id);
        }
        else {
          VisitCountService.cancelRegisterInlineVisit('CalendarEvent', self.calendarEvent.id);
        }

        if (self.settings.documentCallback) {
          IproxPageService.initPageLoadFunctions('CalendarEvent');
        }
      };

      self.toggleEdit = function (mode) {
        self.$$editMode = mode === undefined ? !self.$$editMode : mode;

        IproxPageService.initPageLoadFunctions('CalendarEvent');
      };

      self.update = function ($draft) {
        store.updateCalendarEvent(self.calendarEvent, $draft.title, $draft.location, $draft.description, $draft.startDate, $draft.endDate).then(function () {
          self.toggleEdit(false);
        });
      };

      self.delete = function () {
        DialogsService.confirm('calendar.calendarEvent.delete', 'calendar.calendarEvent.delete.confirmMessage').then(function () {
          store.deleteCalendarEvent(self.calendarEvent);

          IproxPageService.initPageLoadFunctions('CalendarEvent');

          if (!self.listMode) {
            $location.path('/');
          }
        });
      };
    }],
    controllerAs: 'CalendarEventCtrl'
  });
})(angular);
