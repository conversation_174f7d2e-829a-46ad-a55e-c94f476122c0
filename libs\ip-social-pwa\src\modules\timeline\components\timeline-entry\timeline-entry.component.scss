@import "../../../../scss/variables";
@import "../../../../scss/mixins";

$ips-timeline-entry-padding: $ips-unit * 2;
$ips-timeline-entry-margin: $ips-unit * 2;

:host {
  display: block;
  // margin overlaps with eachother, this makes the virtual scroll unstable. So the wrapper is used with padding instead.
  padding-bottom: $ips-timeline-entry-margin;
}

.ips-timeago {
  @include font-size(-2);

  color: var(--ion-color-medium);

  @media(min-width: 720px) {
    text-align: right;
  }
}

.ips-timeline-entry-holder {
  background-color: #fff;
}

.ips-timeline-entry-header {
  align-items: center;
  border-bottom: 1px solid var(--ion-color-light-shade);
  display: flex;
  flex-wrap: wrap;
  min-height: calc(var(--ips-avatar-size) + 32px);
  padding-left: 80px;
  padding-bottom: $ips-timeline-entry-padding;
  padding-right: $ips-timeline-entry-padding;
  padding-top: $ips-timeline-entry-padding;
  position: relative;
}

@media(max-width: 720px) {
  ips-timeline-activity,
  .ips-timeago {
    flex: 0 0 100%;
  }
}

@media(min-width: 720px) {
  .ips-timeline-entry-header {
    justify-content: space-between;
  }
}

.ips-timeline-entry-content {
  overflow: hidden;
  position: relative;
}

.ips-timeline-entry-footer {
  border-top: 1px solid var(--ion-color-light-shade);
  height: $ips-meta-height;
}

::ng-deep {
  .ips-timeline-entry-content-block {
    padding: $ips-timeline-entry-padding calc(var(--ion-grid-padding) + var(--ion-grid-column-padding));

    + .ips-timeline-entry-content-block {
      padding-top: 0;
    }
  }

  ips-timeline-title + .ips-timeline-entry-content-block {
    padding-top: 0;
  }

  .ips-timeline-entry-content ips-meta {
    border-top: 1px solid var(--ion-color-light-shade);
  }
}
