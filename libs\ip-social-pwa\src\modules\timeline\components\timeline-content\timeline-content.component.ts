import { Component, Input, OnInit, ViewChild, ViewContainerRef } from '@angular/core';

import { ITimelineEntry } from '../../models';
import { TimelineContentFactoryService } from '../../services/timeline-content-factory.service';

@Component({
  templateUrl: './timeline-content.component.html',
  selector: 'ips-timeline-content',
  styleUrls: ['./timeline-content.component.scss'],
})
export class TimelineContentComponent implements OnInit {
  @ViewChild('outlet', { static: true, read: ViewContainerRef })
  outlet!: ViewContainerRef;

  @Input()
  entry!: ITimelineEntry;

  constructor(private factory: TimelineContentFactoryService) { }

  ngOnInit() {
    this.factory.createComponent(this.entry, this.outlet);
  }
}
