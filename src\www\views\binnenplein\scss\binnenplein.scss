@import "./include/mq-vars";
@import "./include/baseline-vars";
@import "./include/paths-vars";

@import "./typography/fonts";
@import "./typography/functions";
@import "./typography/mixins";

@import "./include/local-vars";

@import "@infoprojects-local/baseline-styling/src/baseline-base";

@import "./typography/typography";

@import "./baseline-components";

@import "./global/controls";
@import "./global/dna";
@import "./global/entries";
@import "./global/footer";
@import "./global/forms";
@import "./global/frontend-edit";
@import "./global/header";
@import "./global/index";
@import "./global/navigation";
@import "./global/social";
@import "./global/spacing";

@import "./typography/lists";
@import "./typography/blockquote";
@import "./typography/calendar";
@import "./typography/foto";

@import "./components/carrousel";
@import "./components/entry";
@import "./components/group-mailer";
@import "./components/lightbox";
@import "./components/media";

@media screen and (min-width: 1440px) and (max-width: 1560px) {
  body {
    zoom: $pzh-large-zoom-level;
  }

  .media-content.mediatype-mp3 {
    zoom: calc(1 / #{$pzh-large-zoom-level});
  }
}

@media screen and (min-width: 1280px) and (max-width: 1441px) {
  body {
    zoom: $pzh-medium-zoom-level;
  }

  .media-content.mediatype-mp3 {
    zoom: calc(1 / #{$pzh-medium-zoom-level});
  }
}

@media screen and (min-width: $bl-mq-medium) and (max-width: 1281px) {
  body {
    zoom: $pzh-small-zoom-level;
  }

  .media-content.mediatype-mp3 {
    zoom: calc(1 / #{$pzh-small-zoom-level});
  }
}
