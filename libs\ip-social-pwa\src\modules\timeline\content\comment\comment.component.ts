import { Component, Input } from '@angular/core';

import { IComment, mapComment, MetaComponent } from '@ip/social-core';

import { IBlogSummary } from '../../../blog/models';
import { ITimelineEntry } from '../../models';
import { TimelineApi } from '../../models/internal/api.models';
import { TimelineService } from '../../services/timeline.service';

@Component({
  templateUrl: './comment.component.html',
  selector: 'ips-timeline-comment',
  styleUrls: ['./comment.component.scss'],
})
export class TimelineCommentComponent {
  @Input()
  data!: TimelineApi.IContentEntity<IBlogSummary>;

  @Input()
  set entry(value: ITimelineEntry) {
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    this.comment = mapComment(value.primaryValue as any);
    this.expanded = typeof value.props.expanded ==='boolean' ? value.props.expanded : false;
    this.entryId = value.id;
  }

  previewLimit = 200;

  metaComponents = [MetaComponent.Likes];

  comment!: IComment;

  entryId!: string;

  expanded?: boolean;

  constructor(private timelineService: TimelineService) { }

  expand() {
    if (!this.expanded) {
      this.timelineService.setProp(this.entryId, { expanded: true });
    }
  }
}
