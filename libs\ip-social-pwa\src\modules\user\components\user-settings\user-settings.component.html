<ion-header>
  <ip-header [title]="'user.settings.PageTitle' | transloco"></ip-header>
</ion-header>

<ion-content *transloco="let t;">
  <ion-grid *ngIf="userAgentService.userAgent$ | async as userAgent;">
    <ion-row *ngIf="(device.state$ | async) as state">
      <ng-container [ngSwitch]="state.status">

        <ion-col *ngIf="!userAgent.platform.pushSupported" size="12" class="ion-text-center">
          <div [innerHTML]="t('device.pushNotSupported')"></div>
        </ion-col>

        <ion-col *ngIf="userAgent.notificationPermission === 'denied'" size="12" class="ion-text-center">
          <div [innerHTML]="t('device.notificationsDisabled')"></div>
        </ion-col>

        <ng-container *ngSwitchCase="deviceStatus.Initializing">
          <ion-col *ngIf="userAgent.platform.pushSupported" size="12" class="ion-text-center">
            <ion-spinner></ion-spinner>
          </ion-col>
        </ng-container>

        <ng-container *ngSwitchCase="deviceStatus.Registered">
          <!-- <ion-col size="12">
            <ips-device-name [name]="state.device?.deviceName"></ips-device-name>
          </ion-col> -->

          <ion-col *ngIf="state.subscription" size="12">
            <ips-push-settings *ngIf="state.device" [userAgent]="userAgent" [settings]="state.device!.pushSettings"></ips-push-settings>
          </ion-col>

          <ion-col *ngIf="!state.subscription && userAgent.platform.pushSupported && userAgent.notificationPermission !== 'denied'" size="12">
            <ion-button expand="full" (click)="device.subscribe()">
              {{ t('device.connect.btn') }}
            </ion-button>
          </ion-col>
        </ng-container>

        <ion-col size="12">
          <ion-button expand="full" (click)="reload()">
            {{ t('reload.btn') }}
          </ion-button>
        </ion-col>

      </ng-container>
    </ion-row>
  </ion-grid>
</ion-content>
