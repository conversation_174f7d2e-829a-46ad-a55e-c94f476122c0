import { NgModule } from '@angular/core';
import { RouterModule } from '@angular/router';

import { BlogModule } from './blog.module';
import { BlogPageComponent } from './components/blog-page/blog-page.component';
import { BlogResolver } from './services/blog.resolver';
import { NewBlogResolver } from './services/new-blog.resolver';

@NgModule({
  imports: [
    BlogModule,
    RouterModule.forChild([
      {
        path: 'new',
        component: BlogPageComponent,
        resolve: {
          blog: NewBlogResolver
        },
      },
      {
        path: ':blogId',
        component: BlogPageComponent,
        resolve: {
          blogId: BlogResolver
        },
        pathMatch: 'full',
      },
    ]),
  ],
})
export class BlogRouteModule { }
