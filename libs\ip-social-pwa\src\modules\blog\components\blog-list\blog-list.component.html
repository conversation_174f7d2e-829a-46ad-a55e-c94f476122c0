<ng-container *transloco="let t">
  <ng-container *ngIf="list$ | async as list">

    <ion-list *ngIf="list">
      <div class="ips-blog-list-heading">
        <ion-list-header>
          {{ t('blog.list') }}
        </ion-list-header>
      </div>

      <ion-item *ngFor="let blog of (fullList ? list : (list | slice:0:highlightCount))" [routerLink]="[route + blog.id]">
        <ion-icon *ngIf="isOwnProfile && blog.workInProgress" name="alert-circle-outline" class="ips-blog-status ips-blog-wip"></ion-icon>
        <ion-thumbnail>
          <img
            ips-blog-image-src
            [blogId]="blog.id"
            [image]="blog.image"
            size="wide-small"
          />
        </ion-thumbnail>
        <ion-label>
          <h2>{{ blog.title ? blog.title : t('noTitle') }}</h2>
          <p>{{ blog.published ? t('blog.status.publishedDate', { date: blog.publishDate | timeago }) : t('blog.status.unpublished') }}</p>
        </ion-label>
      </ion-item>
    </ion-list>

    <ion-button
      *ngIf="!fullList && list && list.length > 3"
      color="light"
      [routerLink]="['./blogs']"
    >
      {{ t('blog.list.showMore') }}
    </ion-button>

  </ng-container>
</ng-container>
