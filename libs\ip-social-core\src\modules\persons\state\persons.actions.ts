import { IPerson } from '../models/person.model';

export namespace Persons {
  export class PreCache {
    static readonly type = '[Person] pre-cache';

    constructor(public persons: IPerson[]) { }
  }

  export class Cache {
    static readonly type = '[Person] cache persons';

    constructor(public persons: <PERSON>erson[]) { }
  }

  export class Request {
    static readonly type = '[Person] request person';

    constructor(public id: string) { }
  }
}
