import { ComponentFactoryResolver, Injectable } from '@angular/core';

import { TimelineContentFactory } from '../content-factory';
import { TimelinePreviewBirthdayComponent } from './birthday-preview.component';

@Injectable()
export class BirthdayPreviewFactory extends TimelineContentFactory {
  component = TimelinePreviewBirthdayComponent;

  type = 'birthday-preview';

  constructor(componentFactoryResolver: ComponentFactoryResolver) {
    super(componentFactoryResolver);
  }
}
