import { Component } from '@angular/core';
import { ActivatedRoute } from '@angular/router';

import { IPerson } from '@ip/social-core';
import { Observable } from 'rxjs';

import { PersonMenuService } from '../../services/person-menu.service';

@Component({
  selector: 'ips-person-page',
  templateUrl: './person-blog-list-page.component.html',
  styleUrls: ['./person-blog-list-page.component.scss'],
})
export class PersonBlogListPageComponent {
  data$: Observable<IProfilePageData>;

  constructor(
    public menu: PersonMenuService,
    activatedRoute: ActivatedRoute,
  ) {
    this.data$ = activatedRoute.data as Observable<IProfilePageData>;
  }
}

interface IProfilePageData {
  person: IPerson;
  isOwnProfile: boolean;
}
