import { Injectable } from '@angular/core';
import { ActivatedRouteSnapshot, Resolve, RouterStateSnapshot } from '@angular/router';

import { IPerson, PersonService, User, UserService } from '@ip/social-core';
import { Observable } from 'rxjs';
import { filter, switchMap, take } from 'rxjs/operators';

@Injectable()
export class PersonResolver implements Resolve<IPerson> {
  constructor(private personService: PersonService, private userService: UserService) { }

  resolve(route: ActivatedRouteSnapshot, _state: RouterStateSnapshot): Observable<IPerson> {
    const id = route.paramMap.get('id');
    if (!id || id === 'self') {
      return this.userService.user$
        .pipe(
          filter((user): user is User => user !== undefined),
          switchMap(user => this.personService.person$(user.id)),
          take(1)
        );
    }

    return this.personService.person$(id)
      .pipe(take(1));
  }
}
