import { Component, Input } from '@angular/core';

import { saveAs } from 'file-saver';

import { IEvent } from '../../models';
import { Ics } from '../../utilities/ics';

@Component({
  selector: 'ips-event-ics',
  templateUrl: './event-ics.component.html',
})
export class EventIcsComponent {
  @Input()
  event!: IEvent;

  download(event: IEvent) {
    const ics = new Ics();

    ics.addEvent({
      start: event.date.start,
      end: event.date.end,
      subject: event.title,
      description: '',
      location: event.location
    });

    const blob = new Blob([ics.generate()], { type: 'text/calendar' });

    saveAs(blob, `${event.title}.ics`);
  }
}
