<?xml version="1.0" encoding="utf-8"?>
<node name="data">
  <node name="veld">
    <table name="VldDefTab" alias="VldDef" />
    <table name="PagVldTab" alias="PagVld" joincondition="PagVld.VldDefIdt = VldDef.VldDefIdt" />
    <table name="ItmPagVldVew" alias="ItmPagVld" joincondition="ItmPagVld.PagVldIdt = PagVld.PagVldIdt" />
    <where condition="VldDef.Nam = 'Afbeelding voor index'" />
    <field table="ItmPagVld" name="ItmIdt" type="attribute" />
    <field table="PagVld" name="PagVldIdt" type="attribute" />
    <field table="PagVld" name="Wrd" type="attribute" />
    <field table="PagVld" name="Txt" type="tag" />
  </node>
</node>