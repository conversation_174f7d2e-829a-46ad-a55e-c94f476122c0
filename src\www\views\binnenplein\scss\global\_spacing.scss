.grid-title:not(:only-child) {
  padding-bottom: .5rem;
  padding-top: .5rem;
}

.grid-blok:not(.type-meta):not(.rol-inleiding):not(.type-zoeken) {
  .grid-inside:only-child {
    padding-bottom: $pzh-inner-padding;
    padding-top: $pzh-inner-padding;
  }

  .grid-inside:last-child {
    padding-bottom: $pzh-inner-padding;
  }

  .grid-inside {
    .iprox-rich-content > *:first-child {
      margin-top: 0;
    }

    + .grid-inside {
      padding-top: 0;
    }
  }

  .grid-nesting {
    .grid-title + .grid-inside {
      padding-top: 0;
    }
  }
}

.grid-blok.has-bgcolor {
  margin-bottom: 2rem;
}

.grid-blok.rol-social-elevated {
  z-index: 10;
}
