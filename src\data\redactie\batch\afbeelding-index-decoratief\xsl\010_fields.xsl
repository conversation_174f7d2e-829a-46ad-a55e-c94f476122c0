<?xml version="1.0" encoding="utf-8"?>
<xsl:stylesheet xmlns:xsl="http://www.w3.org/1999/XSL/Transform" version="1.0">

  <xsl:output method="xml" version="1.0" encoding="utf-8" indent="yes" />

  <xsl:template match="/*">
    <data>
      <xsl:apply-templates select="/data/veld" />
    </data>
  </xsl:template>

  <xsl:template match="veld">
    <table>
      <xsl:attribute name="name">PagVldTab.<xsl:value-of select="@PagVldIdt" /></xsl:attribute>
      <prop name="$action">edit</prop>
      <prop name="$key">PagVldIdt</prop>
      <prop name="$id"><xsl:value-of select="@PagVldIdt" /></prop>
      <prop name="Wrd"></prop>
      <prop name="Txt">
        <xsl:value-of select="'&lt;![CDATA['" disable-output-escaping="yes" />
        <xsl:apply-templates select="Txt/*|text()" />
        <xsl:value-of select="']]&gt;'" disable-output-escaping="yes" />
      </prop>
      <prop name="Vrs">-1</prop>
    </table>
  </xsl:template>

  <xsl:template match="@alt">
    <xsl:attribute name="alt" />
  </xsl:template>

  <xsl:template match="@*|*">
    <xsl:copy>
      <xsl:apply-templates select="@*|*|text()" />
    </xsl:copy>
  </xsl:template>

</xsl:stylesheet>