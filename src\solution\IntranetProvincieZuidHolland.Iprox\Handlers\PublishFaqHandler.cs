﻿namespace IntranetProvincieZuidHolland.Iprox.Handlers {
  using InfoProjects.Dxe.Process;
  using InfoProjects.Iprox.Model;
  using InfoProjects.Iprox.Model.Util;

  using IntranetProvincieZuidHolland.Iprox.Model;

  /// <summary>
  /// Handles publish FAQ
  /// </summary>
  public class PublishFaqHandler : PublishNewPageHandler<Faq> {
    #region Methods

    /// <summary>
    /// Sets content of page.
    /// </summary>
    /// <param name="unit">
    /// The process unit.
    /// </param>
    /// <param name="page">
    /// The iprox page.
    /// </param>
    protected override void SetContent(ProcessUnit unit, Faq page) {
      unit.SetValueOf(page.VraagEnAntwoord.Antwoord, "Antwoord");
    }

    #endregion
  }
}