@import "functions";

@mixin aspect-ratio-thumbnail($width: 16, $height: 9) {
  position: relative;

  &:before {
    display: block;
    content: "";
    width: 100%;
    padding-top: ($height / $width) * 100%;
  }

  > img {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
  }
}

@mixin font-size($step) {
  font-size: font-size-value($step);
}

@mixin cancel-grid-padding($top: false) {
  $__negative-ion-grid-padding: calc((var(--ion-grid-padding) + var(--ion-grid-column-padding)) * -1);

  @if($top) {
    margin-top: $__negative-ion-grid-padding;
  }

  margin-left: $__negative-ion-grid-padding;
  margin-right: $__negative-ion-grid-padding;
}

@mixin line-clamp($lines) {
  display: -webkit-box;
  -webkit-line-clamp: $lines;
  -webkit-box-orient: vertical;
  overflow: hidden;
  max-height: $lines * 1.5em;

  @supports (-webkit-line-clamp: 1) {
    max-height: none;
  }
}
