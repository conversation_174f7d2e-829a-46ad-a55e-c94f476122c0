<?xml version="1.0" encoding="utf-8"?>

<xsl:stylesheet version="1.0"
                xmlns="http://www.w3.org/1999/xhtml"
                xmlns:xsl="http://www.w3.org/1999/XSL/Transform"
                xmlns:mediawidgetmanager="urn:mediawidgetmanager"
                xmlns:formatter="urn:formatter"
                extension-element-prefixes="mediawidgetmanager formatter">

  <xsl:template match="content[page/@pagetype = 'zoekenmylex']/page/layout/zone[@Aka = 'facet']" mode="grid-zone" />
  <xsl:template match="content[page/@pagetype = 'zoekenmylex']/page/layout/zone[@Aka = 'content']" mode="grid-zone" />

  <xsl:template match="content[page/@pagetype = 'zoekenmylex']/page/layout/zone[@Aka = 'zoeken']" mode="grid-zone">
    <ips-mylex-search>
      <xsl:attribute name="data-config">
        <xsl:text>{</xsl:text>
          <xsl:text>"searchApi" : </xsl:text>
          <xsl:value-of select="mediawidgetmanager:JsonQuote(formatter:SetPropInHref($pathitem/@Url, 'AppIdt', 'mylex-search'))" />
          <xsl:text>,"contentApi" : </xsl:text>
          <xsl:value-of select="mediawidgetmanager:JsonQuote(formatter:SetPropInHref($pathitem/@Url, 'AppIdt', 'page-data-static'))" />
        <xsl:text>}</xsl:text>
      </xsl:attribute>
    </ips-mylex-search>
  </xsl:template>
</xsl:stylesheet>
