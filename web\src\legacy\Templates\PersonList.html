﻿<form class="formulier" novalidate>
  <div class="grid-zone grid_12 z-titel is-fullwidth-zone">
    <div class="grid-blok grid_12 rol-paginatitel type-titel has-elt-breakpoints">
      <div class="grid-element">
        <div class="grid-edge">
          <h1 class="grid-title">{{ 'personList.search.title' | ipRefText }}</h1>
        </div>
      </div>
    </div>
    <br class="end-of-zone">
  </div>
  <div class="grid-zone grid_12 z-results has-no-link-icons has-no-list-icons has-no-button-icons">
    <div class="grid-blok grid_12 rol-social-white type-formulier ips-person-search has-elt-breakpoints">
      <div class="grid-element">
        <div class="grid-edge">
          <div class="grid-box">
            <div class="input-group">
              <div class="input-group-btn" ng-if="PersonListCtrl.showTextFieldSelector" uib-dropdown>
                <button type="button" class="btn btn-default" uib-dropdown-toggle>
                  {{ 'person.search.field.' + PersonListCtrl.getSelectedTextField() | ipRefText }}
                  <span class="caret"></span>
                </button>
                <ul class="dropdown-menu" role="menu" uib-dropdown-menu>
                  <li ng-repeat="field in PersonListCtrl.availableTextFields">
                    <button type="button" class="btn btn-link btn-sm" ng-click="PersonListCtrl.setTextField(field)">
                      {{ 'person.search.field.' + field.toLowerCase() | ipRefText }}
                    </button>
                  </li>
                </ul>
              </div>
              <input type="text" class="form-control" placeholder="{{:: 'person.search.searchFor' | ipRefText }}" aria-label="{{ 'person.search.searchField' | ipRefText }}"
                     ng-model="PersonListCtrl.textField.value"
                     ng-change="PersonListCtrl.search(0)"
                     ng-model-options="{ debounce: {'default': 500, 'blur': 0} }">
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="row-clear clear"></div>
    <div class="grid-blok grid_12 type-person-list-result-meta rol-social-white has-elt-breakpoints" ng-if="PersonListCtrl.persons.length > 0">
      <div class="grid-element">
        <div class="grid-edge">
          <div class="grid-box">
            <div class="row">
              <div ng-class="{ 'col-xs-18': PersonListCtrl.enablePrintView && PersonListCtrl.searchResults.itemCount > 0, 'col-xs-24': !PersonListCtrl.enablePrintView || PersonListCtrl.searchResults.itemCount === 0 }">
                <results-text data-start="{{PersonListCtrl.searchResults.start + 1}}" data-end="{{PersonListCtrl.searchResults.start + PersonListCtrl.searchResults.itemCount}}" data-total-count="{{PersonListCtrl.searchResults.totalCount}}" data-context="persons"></results-text>
              </div>
              <div ng-show="PersonListCtrl.enablePrintView && PersonListCtrl.searchResults.itemCount > 0" class="col-xs-6">
                <button type="button" class="btn btn-default pull-right" ng-click="PersonListCtrl.printView()">
                  <span>{{'person.search.print' | ipRefText }}</span>
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="row-clear clear"></div>
    <div class="grid-blok grid_12 type-person-list-results rol-social has-bgcolor type-galerij">
      <div class="grid-element">
        <div class="grid-edge">
          <div class="grid-inside" ng-if="PersonListCtrl.persons.length === 0">
            <div class="iprox-rich-content iprox-content">
              <p>{{ 'persons.noResults' | ipRefText }}</p>
            </div>
          </div>
          <div class="grid-nesting" ng-if="PersonListCtrl.persons.length > 0">
            <div class="ips-person-card-container">
              <div class="grid-blok grid_3 rol-social-white has-bgcolor has-elt-breakpoints" ng-repeat="person in PersonListCtrl.persons track by person.id">
                <div class="grid-element">
                  <div class="grid-edge">
                    <div class="grid-box">
                      <ips-person-card data-person="person"></ips-person-card>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="grid-blok grid_9 rol-resultaten-pager has-elt-breakpoints" ng-if="PersonListCtrl.persons.length > 0">
      <div class="grid-element">
        <div class="grid-edge">
          <div class="grid-box">
            <div class="bs-form-grid">
              <results-pager data-pages="PersonListCtrl.searchResults.pages" data-goto-page="PersonListCtrl.search" data-context="persons"></results-pager>
            </div>
          </div>
        </div>
      </div>
    </div>
    <br class="end-of-zone">
  </div>
</form>
