import { Injectable } from '@angular/core';

import { TranslocoService } from '@ngneat/transloco';

import { IEvent, IEventDate, IEventProperty } from '../models';

// SR: Consider moving all IPROX related stuff into their own module?
@Injectable()
export class IproxEventMapper {
  constructor(private transloco: TranslocoService) { }

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  parseEvent(page: any): IEvent {
    const content = page.inhoud;
    const data: IEventProperty[] = [];

    if (content.doelgroep) {
      data.push({
        label: this.transloco.translate('event.targetAudience'),
        html: content.doelgroep
      });
    }

    if (content.omschrijving) {
      data.push({
        label: this.transloco.translate('event.description'),
        html: content.omschrijving
      });
    }

    if (content.kosten) {
      data.push({
        label: this.transloco.translate('event.costs'),
        html: content.kosten
      });
    }

    if (content['meer-informatie']) {
      data.push({
        label: this.transloco.translate('event.additionalInformation'),
        html: content['meer-informatie']
      });
    }

    if (content.aanmelden) {
      data.push({
        label: this.transloco.translate('event.registration'),
        html: content.aanmelden
      });
    }

    if (content.bijzonderheden) {
      data.push({
        label: this.transloco.translate('event.note'),
        html: content.bijzonderheden
      });
    }

    const startTime = content.starttijd || content.eindtijd;
    const endTime = startTime === content.eindtijd ? undefined : content.eindtijd;

    return {
      title: page.title,
      date: this.parseIproxEventDate(
        { date: content.startdatum, time: startTime },
        { date: content.einddatum, time: endTime },
      ),
      location: content?.locatie,
      data
    };
  }

  private parseIproxEventDate(start: { date: string; time?: string }, end: { date?: string; time?: string }): IEventDate {
    const startDate = this.parseIproxDate(start.date, start.time);
    const endDate = end.date
      ? this.parseIproxDate(end.date, end.time)
      : end.time
        ? this.parseIproxDate(start.date, end.time)
        : undefined;

    return {
      start: startDate,
      startHasTime: start.time !== undefined,
      end: endDate,
      endHasTime: end.time !== undefined && start.time !== undefined,
      endsOnSameDay: endDate ? this.endsOnSameDay(startDate, endDate) : false
    };
  }

  private parseIproxDate(input: string, time?: string): Date {
    if (!/^(\d){8}$/.test(input)) {
      throw new Error(`invalid date '${input}`);
    }

    const y = parseInt(input.substr(0, 4), 10);
    const m = parseInt(input.substr(4, 2), 10) - 1;
    const d = parseInt(input.substr(6, 2), 10);

    if (time) {
      const hh = time ? parseInt(time.substr(0, 2), 10) : undefined;
      const mm = time ? parseInt(time.substr(2, 2), 10) : undefined;

      return new Date(y, m, d, hh, mm);
    }

    return new Date(y, m, d);
  }

  private endsOnSameDay(start: Date, end: Date) {
    return start.getFullYear() === end.getFullYear() &&
      start.getMonth() === end.getMonth() &&
      start.getDate() === end.getDate();
  }
}
