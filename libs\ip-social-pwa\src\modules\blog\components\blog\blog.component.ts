import { HttpErrorResponse } from '@angular/common/http';
import { Component, Input, OnChanges, OnDestroy, OnInit, SimpleChanges } from '@angular/core';
import { FormGroup } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';

import { FileProgressService, UserService } from '@ip/social-core';
import { EMPTY, Observable, Subscription } from 'rxjs';
import { catchError, distinctUntilChanged, map, take } from 'rxjs/operators';

import { EditorConfig } from '../../../forms/services/tinymce.service';
import { BlogSettings } from '../../blog.settings';
import { IBlog } from '../../models';
import { BlogService } from '../../services/blog.service';

@Component({
  selector: 'ips-blog',
  templateUrl: './blog.component.html',
  styleUrls: ['./blog.component.scss'],
  providers: [BlogService]
})
export class BlogComponent implements OnChanges, OnInit, OnDestroy {
  private subscription?: Subscription;

  @Input()
  blogId!: string;

  blog$!: Observable<IBlog>;

  editMode = false;

  editorConfig!: EditorConfig;

  formGroup!: FormGroup;

  responseError?: HttpErrorResponse;

  synchronizing$?: Observable<boolean>;

  synchronizing = false;

  constructor(
    public settings: BlogSettings,
    private blogService: BlogService,
    private activatedRoute: ActivatedRoute,
    private userService: UserService,
    private router: Router,
    private fileProgressService: FileProgressService,
  ) { }

  ngOnInit() {
    this.blog$ = this.blogService.blog$;

    this.init(this.blogId);
  }

  ngOnChanges(changes: SimpleChanges) {
    if (changes.blogId.currentValue !== changes.blogId.previousValue && changes.blogId.isFirstChange() === false) {
      this.init(changes.blogId.currentValue);
    }
  }

  ngOnDestroy() {
    this.subscription?.unsubscribe();
  }

  toggleEditMode() {
    setTimeout(() => this.editMode = !this.editMode);
  }

  publishBlog(blogId: string) {
    this.blogService.publish(blogId)
      .subscribe(() => this.toggleEditMode());
  }

  removeBlog(blogId: string) {
    this.blogService.remove(blogId)
      // TODO NavigationService zodat routes dynamisch kunnen zijn.
      .subscribe(() => this.router.navigate(['/you/blogs']));
  }

  private init(blogId: string) {
    this.initialEditMode();
    this.initializeService(blogId);
    this.subscription?.unsubscribe();
    this.subscription = this.synchronizingListener(blogId);
  }

  private initialEditMode() {
    if (this.activatedRoute.snapshot.queryParamMap.get('editMode') === 'true') {
      this.blog$
        .pipe(take(1))
        .subscribe(blog => this.editMode = blog.userId === this.userService.currentUser()?.id);
    }
  }

  private initializeService(blogId: string) {
    this.blogService.init(blogId)
      .pipe(
        catchError(error => {
          this.responseError = error;
          return EMPTY;
        })
      )
      .subscribe(({ editorConfig, formGroup }) => {
        this.editorConfig = editorConfig;
        this.formGroup = formGroup;
        this.responseError = undefined;
      });
  }

  private synchronizingListener(blogId: string) {
    this.synchronizing$ = this.fileProgressService.group$(`blog-${blogId}`)
      .pipe(
        map(progress => progress.some(p => p.state !== 'READY')),
        distinctUntilChanged(),
      );

    return this.synchronizing$.subscribe(synchronizing => this.synchronizing = synchronizing);
  }
}
