import { HttpErrorResponse } from '@angular/common/http';
import { Component, EventEmitter, Inject, Input, OnInit, Optional, Output } from '@angular/core';

import { IMetaConfig, IReference, MetaComponent, SocialHttpClient } from '@ip/social-core';
import { EMPTY } from 'rxjs';
import { catchError } from 'rxjs/operators';

import { IEvent } from '../../../shared/models';
import { IproxEventMapper } from '../../../shared/utilities/iprox-event.mapper';
import { IIproxBlock } from '../../models/iprox-block.model';
import { IproxSeeAlsoMapperFn } from '../../models/iprox-see-also-mapper-fn.model';
import { IPROX_SEE_ALSO_MAPPER } from '../../providers/iprox-see-also-mapper';
import { defaultDataMapper } from '../../utilities/iprox-see-also-mappers';

@Component({
  selector: 'ips-iprox-content',
  templateUrl: './iprox-content.component.html',
  styleUrls: ['./iprox-content.component.scss'],
})
export class IproxContentComponent implements OnInit {
  @Input()
  reference!: IReference;

  @Output()
  pageTitle = new EventEmitter<string>();

  metaConfig: IMetaConfig | undefined;

  enableComments = false;

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  data: any;

  seeAlsoBlocks: IIproxBlock[] = [];

  iproxEvent?: IEvent;

  responseError?: HttpErrorResponse;

  constructor(
    private socialApi: SocialHttpClient,
    private iproxEventMapper: IproxEventMapper,
    @Optional()
    @Inject(IPROX_SEE_ALSO_MAPPER)
    private mapper?: IproxSeeAlsoMapperFn,
  ) { }

  ngOnInit(): void {
    this.socialApi.get(`iprox/page/${this.reference.id}`)
      .pipe(
        catchError(error => {
          this.responseError = error;
          return EMPTY;
        })
      )
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      .subscribe((data: any) => {
        this.responseError = undefined;

        this.data = data;
        this.metaConfig = data?.metaConfig;
        this.enableComments = this.metaConfig?.components.includes(MetaComponent.Comments) === true;

        this.seeAlsoBlocks = this.mapSeeAlso(data);

        this.pageTitle.emit('page.pageTitle.iprox' + data.pagetype);

        if (data.pagetype === 'evenement') {
          this.iproxEvent = this.iproxEventMapper.parseEvent(this.data.content.page);
        }
      });
  }

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  private mapSeeAlso(data: any): IIproxBlock[] {
    try {
      return data.content?.page?.['zie-ook'] !== undefined
        ? this.mapper ? this.mapper(data.content.page['zie-ook']) : defaultDataMapper(data.content.page['zie-ook'])
        : [];
    }
    catch (error) {
      console.error('[iprox-content] Mapping see-also iprox-content failed', error);

      return [];
    }
  }
}
