﻿namespace IntranetProvincieZuidHolland.Iprox.Events.EventModel {
  using System.Diagnostics.CodeAnalysis;
  using System.Linq;

  using InfoProjects.Dxe.Linq;

  /// <summary>
  /// Event data for published item in group.
  /// </summary>
  [SuppressMessage("StyleCop.CSharp.DocumentationRules", "SA1600:ElementsMustBeDocumented", 
    Justification = "Reviewed. Suppression is OK here.")]
  public class GroupItemPublishEvent {
    #region Public Properties

    public bool Added { get; set; }

    public Item GroupItem { get; set; }

    public Item IndexItem { get; set; }

    public Item PublishedItem { get; set; }

    public User User { get; set; }

    #endregion

    /// <summary>
    /// Returns a string that represents the current object.
    /// </summary>
    /// <returns>
    /// A string that represents the current object.
    /// </returns>
    public override string ToString() {
      return (from prop in this.GetType().GetProperties()
              let value = prop.GetValue(this).ToIproxString()
              where value != null
              select string.Format("{0}: {1}", prop.Name, value)).ToCsvString("\r\n");
    }
  }
}