import { Component } from '@angular/core';

import { Select } from '@ngxs/store';
import { Observable } from 'rxjs';

import { ITimelineEntry } from '../../models';
import { TimelineService } from '../../services/timeline.service';
import { TimelineState } from '../../state/timeline.state';

@Component({
  selector: 'ips-timeline-new-entries-toast',
  templateUrl: './timeline-new-entries-toast.component.html',
  styleUrls: ['./timeline-new-entries-toast.component.scss'],
})
export class TimelineNewEntriesToastComponent {
  @Select(TimelineState.unseenEntries)
  unseenEntries$!: Observable<ITimelineEntry[]>;

  constructor(private timelineService: TimelineService) { }

  dismiss() {
    this.timelineService.dismissToast();
  }

  refresh() {
    this.timelineService.refresh();
  }
}
