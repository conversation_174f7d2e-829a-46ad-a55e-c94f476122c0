@import "../../../../scss/variables";
@import "../../../../scss/mixins";

:host {
  @include cancel-grid-padding();

  border-top: 1px solid var(--ion-color-light-shade);
  border-bottom: 1px solid var(--ion-color-light-shade);
  display: flex;
  flex-wrap: wrap;
  padding-left: calc(var(--ion-grid-padding) +  var(--ion-grid-column-padding));
  padding-right: calc(var(--ion-grid-padding) +  var(--ion-grid-column-padding));
}

:host-context(.ips-edit-mode) {
  background-color: #fff;
  margin-bottom: 5px;
  padding-bottom: $ips-unit * 2;
  padding-top: calc(var(--ion-grid-padding) +  var(--ion-grid-column-padding));

  ips-person:not(:only-child) {
    padding-bottom: calc(var(--ion-grid-padding) +  var(--ion-grid-column-padding));
    padding-top: 0;
  }
}

ips-person {
  flex: 0 0 100%;
  padding-bottom: $ips-unit;
  padding-top: $ips-unit;

  + ips-person {
    margin-left: 0;
  }
}

ion-chip {
  margin: 0 4px 4px 0;
}
