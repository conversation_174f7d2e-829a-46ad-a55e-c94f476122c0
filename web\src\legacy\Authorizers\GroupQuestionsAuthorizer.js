(function (angular) {
  'use strict';

  angular.module('Intranet').factory('GroupQuestionsAuthorizer', ['GroupService', function (GroupService) {
    function getGroup(entity) {
      return entity.userIsMemberOrOwner
        ? entity
        : GroupService.getGroup(entity.id);
    }

    return {
      canCreateQuestion: function (user, entity) {
        var group = getGroup(entity);
        return group.public || group.userIsMemberOrOwner(user.id) || user.isCommunityManager();
      },
      canEditQuestion: function (user, entity, question) {
        var group = getGroup(entity);
        return user.id === question.userId || group.userIsMemberOrOwner(user.id) || user.isCommunityManager();
      },
      canMoveQuestion: function (user, entity, question) {
        return false;
      },
      canDeleteQuestion: function (user, entity, question) {
        var group = getGroup(entity);
        return user.id === question.userId || group.userIsMemberOrOwner(user.id) || user.isCommunityManager();
      },
      canEditAnswer: function (user, entity, question, answer) {
        return user.id === answer.userId || user.isCommunityManager();
      },
      canDeleteAnswer: function (user, entity, question, answer) {
        return user.id === answer.userId || user.isCommunityManager();
      },
      canAcceptAnswer: function (user, entity, question, answer) {
        return user.id === question.userId || user.isCommunityManager();
      },
      canEditComment: function (user, group, question, answer, comment) {
        return user.id === comment.userId || user.isCommunityManager();
      },
      canDeleteComment: function (user, group, question, answer, comment) {
        return user.id === comment.userId || user.isCommunityManager();
      }
    };
  }]);
})(angular);
