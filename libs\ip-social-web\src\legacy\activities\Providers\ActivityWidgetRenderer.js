import { escape, escapeRegExp, kebabCase, find } from 'lodash';

(function (angular) {
  'use strict';

  angular.module('Intranet.Activities').provider('ActivityWidgetRenderer', [function () {
    var provider = this;
    var widgets = [];

    provider.register = function (name, widgetFn) {
      widgets.push({
        name: name,
        fn: widgetFn
      });
    };

    provider.startSymbol = '<';
    provider.endSymbol = '>';

    provider.$get = [function () {
      var startSymbol = escape(provider.startSymbol);
      var endSymbol = escape(provider.endSymbol);

      var widgetNames = widgets.map(function (widget) {
        return widget.name;
      });
      var widgetRegex = new RegExp(escapeRegExp(startSymbol) + '(' + widgetNames.join('|') + ')(?:(?::)([a-zA-Z0-9.]+))?' + escapeRegExp(endSymbol), 'g');

      return function (text) {
        return escape(text).replace(widgetRegex, function () {
          var matches = Array.prototype.slice.call(arguments);

          matches.pop();
          matches.pop();
          matches.shift();

          var widget = matches[0];
          var propertyName = matches[1] || matches[0];

          if (propertyName === 'avatar') {
            return '<ips-person-avatar data-person="userInfo"></ips-person-avatar>';
          }

          return '<span class="ips-widget ips-widget-' + kebabCase(propertyName) +'">' + find(widgets, { name: widget }).fn(propertyName) + '</span>';
        });
      };
    }];
  }]);
})(angular);
