﻿<?xml version="1.0" encoding="utf-8"?>
<data>
  <prop name="iprox_apppath" value="intranetprvzuidholland_shared" append=";" />

  <prop name="trigger_path" value="IntranetProvincieZuidHolland.Modules.Triggers, IntranetProvincieZuidHolland.Modules" prepend=";" level="2" />
  <prop name="handler_path" value="IntranetProvincieZuidHolland.Modules.Handlers, IntranetProvincieZuidHolland.Modules" prepend=";" level="2" />

  <prop name="cookies_allowed" value="true"  />
  <prop name="use_allow_cookies" value="false"  />

  <prop name="mediawidget_youtube_options" value="&amp;rel=0&amp;disablekb=1" />

  <!-- Settings voor stijlset 'binnenplein' -->
  <prop name="functional-styling-only-binnenplein" value="true" />
  <prop name="minimal-subblock-width-binnenplein" value="300" />

  <prop name="grid-mq-small-binnenplein" value="768" />
  <prop name="grid-mq-medium-binnenplein" value="960" />

  <prop name="lijst-image-width-binnenplein" value="300" />
  <prop name="initial-image-width-binnenplein" value="128" />
  <prop name="lazy-loading-images-binnenplein" value="true" />

  <prop name="navigation_maxlevel-binnenplein" value="2" />
  <prop name="navigation-expanding-binnenplein" value="small medium" />

  <prop name="titel_zones-binnenplein" value="titel,content" />
  <prop name="bootstrap_conflict-binnenplein" value="true" />
  <prop name="pager_label_type-binnenplein" value="page" />
  <prop name="formfields_singlecheckbox_format-binnenplein" value="together" />
  <prop name="formfields_singleradio_format-binnenplein" value="together" />
  <!-- // 'binnenplein' -->

  <prop name="primary-nav-megamenu-colcount" value="6" />
  <prop name="primary-nav-type" value="uitgebreid" />

  <!-- om styling van mails goed door te laten komen (zie ook iprox bugid#6826)-->
  <prop name="variant_cache" value="{EnvIdt}/{VarIdt}/{mode}" />

  <prop name="search_item_cache" value="{EnvIdt}/{VarIdt}" />
  <prop name="search_item_cacheduration" value="{frontend_cacheduration}" />

  <prop name="pager_use_list" value="true" />

  <prop name="share_this_zones" value="none" />
  <prop name="share_this_pagetypes" value="none" />

  <prop name="mask_email_format" value="[\w{0}]+(?:[.-][\w{0}]+)*@{1}(,[\w{0}]+(?:[.-][\w{0}]+)*@{1})*?" />

  <prop name="jquery_migrate" value="false" />
</data>
