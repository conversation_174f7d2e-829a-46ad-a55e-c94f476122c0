<ion-header>
  <ip-header></ip-header>
</ion-header>

<ion-content *transloco="let t;">
  <ion-grid *ngIf="userService.currentUser$ | async as user">
    <ion-row>
      <ion-col size="12">
        <ips-person [id]="user.id"></ips-person>
      </ion-col>
    </ion-row>
    <ion-row>
      <ion-col size="12">
        <ion-list>

          <ion-item button [routerLink]="['/blog', 'new']">
            <ion-icon slot="start" name="add-outline"></ion-icon>
            <ion-label>{{ t('blog.create') }}</ion-label>
          </ion-item>

          <ion-item [routerLink]="['./blogs']">
            <ion-icon slot="start" name="list-circle-outline"></ion-icon>
            <ion-label>{{ t('blog.myList') }}</ion-label>
          </ion-item>

          <ion-item button [routerLink]="['./person', 'self']">
            <ion-icon slot="start" name="person-outline"></ion-icon>
            <ion-label>{{ t('user.viewProfile') }}</ion-label>
          </ion-item>

          <ng-container *ngIf="appInstall.installed$ | async as status;">
            <ion-item
              *ngIf="status !== 'installed'"
              (click)="appInstall.prompt()"
              [disabled]="status === 'rejected'"
              button
            >
              <ion-icon slot="start" name="download-outline"></ion-icon>
              <ion-label>{{ t('app.install') }}</ion-label>
            </ion-item>
          </ng-container>

          <ion-item [routerLink]="['./notificaties']">
            <ion-icon slot="start" name="notifications-outline"></ion-icon>
            <ion-label>{{ t('app.notificationSettings') }}</ion-label>
          </ion-item>

          <ion-item button (click)="logout()">
            <ion-icon slot="start" name="log-out-outline"></ion-icon>
            <ion-label>{{ t('logout') }}</ion-label>
          </ion-item>

        </ion-list>
      </ion-col>
    </ion-row>

    <!-- <ion-row>
      <ion-col size="12">

        <ips-user-notifications-mockup></ips-user-notifications-mockup>

      </ion-col>
    </ion-row> -->
  </ion-grid>
</ion-content>
