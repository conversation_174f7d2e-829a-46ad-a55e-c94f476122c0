import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';

import { DefaultCommentAuthorizerFactory } from './authorizers/default.authorizer';
import { CommentSettings } from './comments.settings';
import { CommentAuthorizerPipe } from './pipes/comment-authorizer.pipe';
import { AttachmentDownloadHrefPipe } from './pipes/attachment-download-href.pipe';
import { CommentApiService } from './services/comment-api.service';
import { COMMENT_AUTHORIZER, CommentAuthorizerService } from './services/comment-authorizer.service';

@NgModule({
  imports: [
    CommonModule,
  ],
  declarations: [
    AttachmentDownloadHrefPipe,
    CommentAuthorizerPipe,
  ],
  providers: [
    CommentApiService,
    CommentSettings,
    CommentAuthorizerService,
    CommentAuthorizerPipe,
    {
      provide: COMMENT_AUTHORIZER,
      useFactory: () => new DefaultCommentAuthorizerFactory(),
      deps: [],
      multi: true,
    },
  ],
  exports: [
    AttachmentDownloadHrefPipe,
    CommentAuthorizerPipe,
  ]
})
export class CommentsModule {
}
