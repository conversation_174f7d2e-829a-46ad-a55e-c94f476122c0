import { of } from 'rxjs';

import { IReference, User } from '../../core/models';
import { ICommentSettings } from '../comments.settings';
import { CommentAuthorizer } from '../models/authorizer.model';
import { IComment } from '../models/comment.model';
import { ICommentAuthorizerFactory } from '../services/comment-authorizer.service';

export class DefaultCommentAuthorizerFactory implements ICommentAuthorizerFactory {
  entities = ['default'];

  authorizer$(reference: IReference, settings: ICommentSettings, user: User) {
    return of(new DefaultCommentAuthorizer(user, reference, settings));
  }
}

export class DefaultCommentAuthorizer extends CommentAuthorizer {
  // TODO nalopen iedere permission
  fn = {
    canView: (_comment: IComment | undefined): boolean => {
      return true;
    },
    canCreate: (_comment: IComment | undefined): boolean => {
      return true;
    },
    canEdit: (comment: IComment | undefined): boolean => {
      return this.user.id === comment?.userId;
    },
    canRemove: (comment: IComment | undefined): boolean => {
      return this.user.id === comment?.userId;
    },
    canAddAttachment: (_comment: IComment | undefined): boolean => {
      return true;
    },
    canRemoveAttachment: (_comment: IComment | undefined): boolean => {
      return true;
    },
  };

  constructor(
    private user: User,
    private reference: IReference,
    private settings: ICommentSettings,
  ) {
    super();
  }
}
