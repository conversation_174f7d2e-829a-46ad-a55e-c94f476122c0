<?xml version="1.0" encoding="utf-8"?>
<xsl:stylesheet xmlns:xsl="http://www.w3.org/1999/XSL/Transform" version="1.0" xmlns:resources="urn:resources" extension-element-prefixes="resources">

  <xsl:import href="include/StartPage.xsl"/>

  <xsl:param name="RePublishFoto.done"/>
  <xsl:param name="PagClsIdt"/>

  <!-- abstract variables -->
  <xsl:variable name="done" select="$RePublishFoto.done" />
  <xsl:variable name="pagetype">fotoalbum</xsl:variable>

  <xsl:variable name="handler" select="'RePublishFoto'" />

  <xsl:template match="*" mode="body_inside_form">
    <xsl:if test="string($error = '')">
      <xsl:call-template name="editor"/>
      <form enctype="multipart/form-data">
        <xsl:apply-templates select="." mode="form_lib">
          <xsl:with-param name="form_AppIdt" select="$exitAppIdt"/>
        </xsl:apply-templates>
        <input type="hidden" name="{$handler}.$action" value="run"/>
        <input type="hidden" name="{$handler}.$id" value="{/data/site/item/@ItmIdt}"/>
        <input type="hidden" name="{$handler}.PagClsIdt" value="{$PagClsIdt}"/>
        <table class="list">
          <xsl:call-template name="startpage-velden" />
        </table>
      </form>
    </xsl:if>
  </xsl:template>

  <xsl:template match="*" mode="remove-button">
    <button class="btn btn-warning" id="RemoveBtn">
      <xsl:attribute name="onclick">
        <xsl:text/>if (confirm('Weet u zeker dat u deze foto wilt verwijderen?')) { submitForm('<xsl:value-of select="$handler" />.$action', 'none', 'RemoveFoto.$action', 'run', 'RemoveFoto.$id', '<xsl:value-of select="$ItmIdt" />', 'RemoveFoto.PagClsIdt', '<xsl:value-of select="$PagClsIdt" />', 'exit', 'publish', 'exitItmIdt', '<xsl:value-of select="$exitItmIdtForPublish" />', 'exitUrl', '<xsl:value-of select="$exitUrl" />'); }<xsl:text/>
      </xsl:attribute>
      <i class="trash"></i>
      <span>
        <xsl:value-of select="resources:GetText('buttons','delete')" />
      </span>
    </button>
  </xsl:template>

  <xsl:template name="startpage-velden">
    <xsl:variable name="inhoud-velden" select="/data/site/item/page//clusterdefinition[Nam='Foto']/veld[site and not(Nam='Copyright')]" />
    <xsl:apply-templates select="$inhoud-velden" mode="startpage-veld"/>
  </xsl:template>

  <xsl:template match="veld" mode="startpage-veld">
    <xsl:param name="Nam" select="translate(DefNam, ' ,/', '___')" />
    <xsl:call-template name="veld">
      <xsl:with-param name="definition" select="."/>
      <xsl:with-param name="curFieldName" select="concat($handler, '.', $Nam)"/>
      <xsl:with-param name="curClusterName" select="concat($handler, '_', $Nam)"/>
      <xsl:with-param name="curPath" select="concat('temp/', $User.GebIdt)"/>
      <xsl:with-param name="curSitIdt" select="$SitIdt"/>
      <xsl:with-param name="this" select="/data/site/item/page//veld[@VldDefIdt=current()/@VldDefIdt and @PagClsIdt = $PagClsIdt]"/>
    </xsl:call-template>
  </xsl:template>

</xsl:stylesheet>