import { Component, Input } from '@angular/core';
import { FormGroup } from '@angular/forms';

import { ModalController } from '@ionic/angular';
import { Observable } from 'rxjs';

import { EditorConfig } from '../../../forms/services/tinymce.service';

@Component({
  selector: 'ips-blog-editor',
  templateUrl: './editor.component.html',
  styleUrls: ['./editor.component.scss'],
})
export class BlogEditorComponent {
  @Input()
  synchronizing$!: Observable<boolean>;

  @Input()
  editorConfig!: EditorConfig;

  @Input()
  formGroup!: FormGroup;

  constructor(public modalController: ModalController) { }
}
