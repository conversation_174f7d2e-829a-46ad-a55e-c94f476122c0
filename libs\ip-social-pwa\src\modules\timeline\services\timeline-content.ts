import { InjectionToken } from '@angular/core';

import { TimelineContentFactory } from '../content/content-factory';

export const TIMELINE_CONTENT_MAP = new InjectionToken<ITimelineContentMap>('TIMELINE_CONTENT_MAP');

export const TIMELINE_CONTENT_FACTORY = new InjectionToken<TimelineContentFactory>('TIMELINE_CONTENT_FACTORY');

export interface ITimelineContentMap {
  type: string;
  factoryType: string;
}
