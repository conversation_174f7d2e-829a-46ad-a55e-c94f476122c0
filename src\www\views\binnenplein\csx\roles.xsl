<?xml version="1.0" encoding="utf-8"?>

<xsl:stylesheet xmlns:xsl="http://www.w3.org/1999/XSL/Transform" version="1.0">
  <xsl:import href="/views/baseline/csx/roles_minimal.xsl" />

  <xsl:template match="role/@linkcolor" mode="rol-style">
    <xsl:param name="role" select="parent::role" />
    <xsl:param name="css-prefix" />
    <xsl:param name="color" select="$role/@linkcolor" />

    <xsl:choose>
      <xsl:when test="$color = '#281f6b'">
        <xsl:call-template name="linkcolors">
          <xsl:with-param name="role" select="$role" />
          <xsl:with-param name="css-prefix" select="$css-prefix" />
          <xsl:with-param name="color" select="$color" />
          <xsl:with-param name="hovercolor" select="'#16113b'" />
        </xsl:call-template>
      </xsl:when>
      <xsl:when test="$color = '#00804d'">
        <xsl:call-template name="linkcolors">
          <xsl:with-param name="role" select="$role" />
          <xsl:with-param name="css-prefix" select="$css-prefix" />
          <xsl:with-param name="color" select="$color" />
          <xsl:with-param name="hovercolor" select="'#004d2e'" />
        </xsl:call-template>
      </xsl:when>
      <xsl:otherwise>
        <xsl:call-template name="linkcolor">
          <xsl:with-param name="role" select="$role" />
          <xsl:with-param name="css-prefix" select="$css-prefix" />
          <xsl:with-param name="color" select="$color" />
        </xsl:call-template>
      </xsl:otherwise>
    </xsl:choose>
  </xsl:template>

  <xsl:template name="linkcolors">
    <xsl:param name="role" select="parent::role" />
    <xsl:param name="css-prefix" />
    <xsl:param name="color" select="$role/@linkcolor" />
    <xsl:param name="visitedcolor" select="$color" />
    <xsl:param name="hovercolor" select="$color" />
    <xsl:param name="activecolor" select="$hovercolor" />

    <xsl:variable name="rule">
      <xsl:call-template name="rule">
        <xsl:with-param name="prefix" select="$css-prefix" />
        <xsl:with-param name="name" select="$role/Aka" />
      </xsl:call-template>
    </xsl:variable>

    <xsl:value-of select="$rule" /> a {
      <xsl:apply-templates select="." mode="color-declaration">
        <xsl:with-param name="propname" select="'color'" />
        <xsl:with-param name="propvalue" select="$color" />
      </xsl:apply-templates>
    }

    <xsl:value-of select="$rule" /> a:visited {
      <xsl:apply-templates select="." mode="color-declaration">
        <xsl:with-param name="propname" select="'color'" />
        <xsl:with-param name="propvalue" select="$visitedcolor" />
      </xsl:apply-templates>
    }

    <xsl:value-of select="$rule" /> a:hover {
      <xsl:apply-templates select="." mode="color-declaration">
        <xsl:with-param name="propname" select="'color'" />
        <xsl:with-param name="propvalue" select="$hovercolor" />
      </xsl:apply-templates>
    }

    <xsl:value-of select="$rule" /> a:active {
      <xsl:apply-templates select="." mode="color-declaration">
        <xsl:with-param name="propname" select="'color'" />
        <xsl:with-param name="propvalue" select="$activecolor" />
      </xsl:apply-templates>
    }

    <xsl:value-of select="$rule" /> a:hover {
      <xsl:apply-templates select="." mode="color-declaration">
        <xsl:with-param name="propname" select="'color'" />
        <xsl:with-param name="propvalue" select="$hovercolor" />
      </xsl:apply-templates>
    }
  </xsl:template>

</xsl:stylesheet>
