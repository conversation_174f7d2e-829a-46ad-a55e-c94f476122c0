import { ChangeDetectionStrategy, Component, Input } from '@angular/core';

import { IIproxContentBlock } from '../../models/iprox-content-block.model';

@Component({
  selector: 'ips-iprox-content-block',
  templateUrl: './iprox-content-block.component.html',
  styleUrls: ['./iprox-content-block.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class IproxContentBlockComponent {
  @Input()
  block!: IIproxContentBlock;
}
