import { Injectable } from '@angular/core';
import { ActivatedRouteSnapshot, Resolve, RouterStateSnapshot } from '@angular/router';

import { IReference, Reference } from '@ip/social-core';
import { Observable, of } from 'rxjs';

@Injectable()
export class PageResolver implements Resolve<IReference> {
  resolve(route: ActivatedRouteSnapshot, _state: RouterStateSnapshot): Observable<IReference> {
    const collection = route.paramMap.get('collection');
    const id = route.paramMap.get('id');
    if (!id || !collection) {
      throw new Error('Resolver can only be used on routes with :collection and :id params.');
    }


    return of(Reference.convertFromString(`${collection}/${id}`));
  }
}
