import { AfterViewInit, Component, ViewChild, ViewEncapsulation } from '@angular/core';

import { IonTabs } from '@ionic/angular';
import { Observable } from 'rxjs';
import { map, startWith, switchMap } from 'rxjs/operators';

import { HeaderService } from '../../../header/services/header.service';
import { Menu } from '../../models/menu.model';
import { SettingsService } from '../../providers/settings/settings.service';
import { StructureService } from '../../providers/structure/structure.service';
import { MenuService } from '../../services/menu.service';

@Component({
  selector: 'ip-root',
  styleUrls: ['./root.component.scss'],
  templateUrl: './root.component.html',
  encapsulation: ViewEncapsulation.None
})
export class RootComponent implements AfterViewInit {
  @ViewChild(IonTabs)
  ionTabs!: IonTabs;

  menu$: Observable<Menu>;

  fullscreen$?: Observable<boolean>;

  constructor(
    public menuService: MenuService,
    public headerService: HeaderService,
    private settings: SettingsService,
    structureService: StructureService,
  ) {
    this.menu$ = structureService.menu$;
  }

  ngAfterViewInit() {
    this.fullscreen$ = this.ionTabs.ionTabsDidChange
      .pipe(
        startWith(''),
        switchMap(() => this.ionTabs.outlet.activateEvents),
        map(() => {
          const url = this.ionTabs.outlet.activatedView?.url;
          return url ? this.isFullscreenUrl(url) : false;
        }),
      );
  }

  private isFullscreenUrl(url: string): boolean {
    return this.settings.get().fullscreenUrls?.some(u => u === url) ?? false;
  }
}
