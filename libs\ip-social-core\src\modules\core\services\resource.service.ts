import { Injectable, Renderer2, RendererFactory2 } from '@angular/core';

import { saveAs } from 'file-saver';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';

import { ResourceTokenService } from '../../authentication/base/resource-token.service';
import { CoreSettings } from '../core.settings';
import { SocialImageSize } from '../models';

@Injectable()
export class ResourceService {
  private renderer: Renderer2;

  constructor(
    private settings: CoreSettings,
    private resourceTokenService: ResourceTokenService,
    rendererFactory: RendererFactory2,
  ) {
    this.renderer = rendererFactory.createRenderer(null, null);
  }

  avatarSrc(personId: string, imageId: string | null, size: 'small' | 'large' = 'small'): Observable<string> {
    return this.resourceTokenService.token$
      .pipe(
        map(token => imageId
          ? `${this.settings.apiUrl}person/${personId}/image/${imageId}/${size}?access-token=${token}`
          : `${this.settings.apiUrl}person/image`
        )
      );
  }

  blogImageSrc(blogId: string, imageId: string | null, size: SocialImageSize = 'wide-small'): Observable<string> {
    return this.resourceTokenService.token$
      .pipe(
        map(token => imageId
          ? `${this.settings.apiUrl}blog/${blogId}/image/${imageId}/${size}?access-token=${token}`
          : `${this.settings.apiUrl}blog/image`
        )
      );
  }

  setImageAccessToken(input: NodeListOf<HTMLImageElement> | HTMLImageElement): void {
    const images: HTMLImageElement[] = input instanceof NodeList
      ? Array.from(input)
      : [input];

    this.resourceTokenService.token$
      .subscribe(token => images.forEach(el => this.setImageSrc(el, token)));
  }

  appendAccessToken(input: string): string {
    const isSecuredImage = input.toLowerCase().startsWith(this.settings.apiUrl.toLowerCase());

    if (!isSecuredImage) {
      return input;
    }

    const url = new URL(input);

    if (this.resourceTokenService.currentToken) {
      url.searchParams.set('access-token', this.resourceTokenService.currentToken);
    }

    return url.toString();
  }

  /** Pass a fileName to save the file using FileSaver */
  downloadFile(fileUrl: string, fileName?: string): void {
    this.resourceTokenService.token$
      .pipe(map(token => {
        const url = new URL(fileUrl);
        url.searchParams.set('access-token', token);
        return url.toString();
      }))
      .subscribe(url => fileName
        ? saveAs(url, fileName)
        : window.open(url, '_blank')
      );
  }

  private setImageSrc(imageElement: HTMLImageElement, token: string): void {
    const url = new URL(imageElement.src);
    const isSecuredImage = imageElement.src.toLowerCase().startsWith(this.settings.apiUrl.toLowerCase());

    if (isSecuredImage && !url.searchParams.get('access-token')) {
      if (imageElement.srcset) {
        this.setImageSrcset(imageElement, token);
      }

      url.searchParams.set('access-token', token);
      this.renderer.setAttribute(imageElement, 'src', url.toString());
    }
  }

  private setImageSrcset(imageElement: HTMLImageElement, token: string): void {
    const newSrcset = imageElement.srcset
      .split(', ')
      .map((src): [URL, string] => {
        const [urlString, format] = src.split(' ');
        return [new URL(urlString), format];
      })
      .map(([url, format]) => {
        url.searchParams.set('access-token', token);
        return `${url.toString()} ${format}`;
      })
      .join(', ');

    this.renderer.setAttribute(imageElement, 'srcset', newSrcset);
  }
}
