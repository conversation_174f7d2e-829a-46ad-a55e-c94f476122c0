import { find } from 'lodash';

(function (angular) {
  'use strict';

  angular.module('Intranet').factory('CalendarCommentsAuthorizer', ['$ngRedux', 'GroupCalendarCommentsAuthorizer', function ($ngRedux, GroupCalendarCommentsAuthorizer) {
    var store = {};

    $ngRedux.connect(function (state) {
      return {
        items: state.calendar.items
      };
    })(store);

    return {
      canCreateComment: function (user, commentOwner) {
        var calendarEvent = find(store.items, { id: commentOwner.id });

        return GroupCalendarCommentsAuthorizer.canCreateComment(user, calendarEvent.owner);
      },
      canEditComment: function (user, comment, commentOwner) {
        return comment.userId === user.id || user.isCommunityManager();
      },
      canDeleteComment: function (user, comment, commentOwner) {
        return comment.userId === user.id || user.isCommunityManager();
      }
    };
  }]);
})(angular);
