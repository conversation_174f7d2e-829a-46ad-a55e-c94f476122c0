(function (angular) {
  'use strict';

  angular.module('Intranet.Activities').config(['ActivityWidgetRendererProvider', function (ActivityWidgetRendererProvider) {
    ActivityWidgetRendererProvider.register('userInfo', function (propertyName) {
      return '<a ip-person-url="' + propertyName + '">{{ ' + propertyName + '.fullName }}</a>';
    });

    ActivityWidgetRendererProvider.register('avatar', function (propertyName) {
      return '<ips-person-avatar data-person="' + propertyName + '"></ips-person-avatar>';
    });

    ActivityWidgetRendererProvider.register('group', function (propertyName) {
      return '<a ip-iprox-redirect-item-url="' + propertyName + '" target="_self">{{ ' + propertyName + '.label }}</a>';
    });

    ActivityWidgetRendererProvider.register('context', function (propertyName) {
      return '<a ip-activity-context-url="' + propertyName + '[0]" target="_self">{{ ' + propertyName + '[0].label }}</a>';
    });

    ActivityWidgetRendererProvider.register('memberRole', function (propertyName) {
      return '{{:: \'activities.membership.\' + member.role | ipRefText }}';
    });

    ActivityWidgetRendererProvider.register('post', function (propertyName) {
      return '<a ip-post-url="' + propertyName + '" target="_self">{{ ' + propertyName + '.title }}</a>';
    });

    ActivityWidgetRendererProvider.register('question', function (propertyName) {
      return '<a ip-question-url="' + propertyName + '" target="_self">{{ ' + propertyName + '.title }}</a>';
    });

    ActivityWidgetRendererProvider.register('document', function (propertyName) {
      return '<a ip-file-url="' + propertyName + '" target="_self">{{ ' + propertyName + '.title }}</a>';
    });

    ActivityWidgetRendererProvider.register('file', function (propertyName) {
      return '<a ip-file-url="' + propertyName + '" target="_self">{{ ' + propertyName + '.fileName }}</a>';
    });

    ActivityWidgetRendererProvider.register('calendarEvent', function (propertyName) {
      return '<a ip-calendar-event-url="' + propertyName + '" target="_self">{{ ' + propertyName + '.title }}</a>';
    });

    ActivityWidgetRendererProvider.register('emailAddress', function (propertyName) {
      return '<a ng-href="mailto:{{ ' + propertyName + '}}" target="_self">{{' + propertyName + '}}</a>';
    });

    ActivityWidgetRendererProvider.register('blogPost', function (propertyName) {
      return '<a ip-blog-url="' + propertyName + '">{{ ' + propertyName + '.title }}</a>';
    });

    ActivityWidgetRendererProvider.register('dataField', function (propertyName) {
      return '{{ \'dataField.\' + ' + propertyName + '.name + \'.label\' | ipRefText }}';
    });

    ActivityWidgetRendererProvider.register('weblink', function (propertyName) {
      return '<a href="{{ ' + propertyName + '.url }}" target="_blank">{{ ' + propertyName + '.label }}</a>';
    });

    ActivityWidgetRendererProvider.register('summary', function (propertyName) {
      return '{{ ' + propertyName + ' | ipTruncate: 20 }}';
    });

    ActivityWidgetRendererProvider.register('workDays', function (propertyName) {
      return '{{ ' + propertyName + ' | personWorkDays }}';
    });

    ActivityWidgetRendererProvider.register('phoneNumberType', function (propertyName) {
      return '{{ \'phone.\' + ' + propertyName + '.type | ipRefText }}';
    });

    ActivityWidgetRendererProvider.register('phoneNumber', function (propertyName) {
      return '<a ng-href="tel:{{ ' + propertyName + '.number }}">{{ ' + propertyName + '.number }}</a>';
    });

    ActivityWidgetRendererProvider.register('expertLevel', function (propertyName) {
      return '{{ ' + propertyName + ' }}';
    });

    ActivityWidgetRendererProvider.register('person', function (propertyName) {
      return '<a ip-person-url="' + propertyName + '">{{ ' + propertyName + '.fullName }}</a>';
    });

    ActivityWidgetRendererProvider.register('skill', function (propertyName) {
      return '<a ip-person-url="person" data-url-query="{ highlight: \'skills\' }">{{ ' + propertyName + '.label }}</a>';
    });

    ActivityWidgetRendererProvider.register('microblogPost', function (propertyName) {
      return '{{ ' + propertyName + '.body }}';
    });

    ActivityWidgetRendererProvider.register('microblogComment', function (propertyName) {
      return '{{ ' + propertyName + '.body }}';
    });

    ActivityWidgetRendererProvider.register('iproxGroupPoll', function (propertyName) {
      return '{{ title }}';
    });
  }])

  .filter('personWorkDays', [function () {
    var workDaysMap = {
      'ma': 'maandag',
      'ma-1': 'maandagochtend',
      'ma-2': 'maandagmiddag',
      'di': 'dinsdag',
      'di-1': 'dinsdagochtend',
      'di-2': 'dinsdagmiddag',
      'wo': 'woensdag',
      'wo-1': 'woensdagochtend',
      'wo-2': 'woensdagmiddag',
      'do': 'donderdag',
      'do-1': 'donderdagochtend',
      'do-2': 'donderdagmiddag',
      'vr': 'vrijdag',
      'vr-1': 'vrijdagochtend',
      'vr-2': 'vrijdagmiddag'
    };

    return function (workDays) {
      if (!workDays || !angular.isArray(workDays)) {
        return workDays;
      }

      var interpreted = interpretWorkDaysData(workDays);
      var interpretedWorkdays = interpreted.interpretedWorkdays;
      var weeks = interpreted.enabledWeeks
        .map(function (week) {
          var days = interpretedWorkdays
            .filter(function (workDay) { return workDay.week === week; })
            .map(function (workDay) { return workDaysMap[workDay.day]; });

          return prettyWorkDays(days);
        });

      if (weeks.length > 1) {
        return weeks
          .map(function (weekStr, index) {
            var prefix = index === 0 ? 'even weken: ' : 'oneven weken: ';

            return prefix + weekStr;
          })
          .join(', ');
      }

      return weeks[0];
    };

    function prettyWorkDays(workDays) {
      if (workDays.length === 0) {
        return 'nooit meer';
      }
      else if (workDays.length === 1) {
        return workDays[0];
      }
      else if (workDays.length === 2) {
        return workDays.join(' en ');
      }

      return workDays.slice(0, workDays.length - 1).join(', ') + ' en ' + workDays.slice(-1)[0];
    }

    function interpretWorkDaysData(workDays) {
      var regex = new RegExp(/^([0-9])?-?(\w{2})(-[0-9])?/);

      var enabledWeeks = workDays
        .filter(function (day) {
          return day.indexOf('enabledWeek-') === 0;
        })
        .map(function (enabledWeek) {
          return parseInt(enabledWeek.replace('enabledWeek-', ''), 10);
        });

      enabledWeeks.unshift(0);

      var interpretedWorkdays = workDays
        .filter(function (day) {
          return day.indexOf('enabledWeek-') !== 0;
        })
        .map(function (day) {
          var result = regex.exec(day);

          return {
            week: result[1] ? parseInt(result[1], 10) : 0,
            day: result[2] + (result[3] || ''),
          };
        });

      return {
        enabledWeeks: enabledWeeks,
        interpretedWorkdays: interpretedWorkdays
      };
    }
  }]);
})(angular);
