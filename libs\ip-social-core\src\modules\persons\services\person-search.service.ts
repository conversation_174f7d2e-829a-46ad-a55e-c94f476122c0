import { HttpParams } from '@angular/common/http';
import { Injectable } from '@angular/core';

import { BehaviorSubject, forkJoin, Observable, of, Subject } from 'rxjs';
import { filter, scan, switchMap, takeUntil, tap } from 'rxjs/operators';

import { IPagination } from '../../core/models/paginated-results.model';
import { SocialHttpClient } from '../../core/services/api-client';
import { PersonsApi } from '../models/internal/api.models';
import { IPersonSearchParamsAction, isSearchParamsAction, PersonSearchAction } from '../models/internal/person-search-action.model';
import { IPerson } from '../models/person.model';
import { ISearchParams, SearchParams } from '../models/search-params.model';
import { PersonService } from './person.service';

export interface IPersonSearchState extends IPagination {
  persons: IPerson[];
  hasMore?: boolean;
  query?: string;
}

@Injectable()
export class PersonSearchService {
  private readonly state = new BehaviorSubject<IPersonSearchState>(this.initialState());

  private readonly action = new Subject<PersonSearchAction>();

  readonly destroyed = new Subject();

  state$ = this.state.asObservable();

  constructor(private socialApi: SocialHttpClient, private personService: PersonService) {
    this.actionListener();
  }

  clear(): void {
    this.state.next(this.initialState());
  }

  // TODO: Append verhuizen naar de personfinder. Dit is een zoekService, het 'appenden' naar 1 lijst kan beter bij de gebruiker van deze service.
  search(params: SearchParams, append: boolean): void;

  search(partial: Partial<ISearchParams>): void;

  search(params: SearchParams, append = false): void {
    this.action.next({ append, params });
  }

  loadMore(count: number): void {
    if (this.state.value.query === undefined) {
      return;
    }

    this.search(new SearchParams({
      searchText: this.state.value.query,
      start: this.state.value.start + count
    }), true);
  }

  recentlyVisited(): Observable<IPerson[]> {
    // TODO: Count als parameter, en settings voor personFinder.
    return this.socialApi.get<IPerson[]>('user/visitedpersons', { params: new HttpParams().append('count', '8') })
      .pipe(
        tap(persons => this.personService.cache(persons))
      );
  }

  private actionListener() {
    this.action
      .pipe(
        scan((previousAction: IPersonSearchParamsAction | undefined, action) => {
          if (isSearchParamsAction(action)) {
            return action;
          }
          else {
            previousAction?.params.update(action.params);
          }

          return previousAction;
        }, undefined),
        filter(isSearchParamsAction),
        switchMap(action => forkJoin([
          of(action),
          this.socialApi.get<PersonsApi.ISearchResponse>('person/search', { params: action.params.toHttpParams() })
        ])),
        tap(([_action, result]) => this.personService.cache(result.items)),
        takeUntil(this.destroyed)
      )
      .subscribe(([action, result]) => {
        const persons = action.append && this.state.value.persons
          ? [...this.state.value.persons, ...result.items]
          : result.items;

        this.state.next({
          persons,
          start: action.params.start,
          totalCount: result.totalCount,
          hasMore: result.totalCount > persons.length,
          query: action.params.searchText,
          pages: result.pages,
          count: result.count,
          itemCount: result.itemCount,
        });
      });
  }

  private initialState(): IPersonSearchState {
    return {
      persons: [],
      start: 0,
      totalCount: 0,
      pages: [],
      itemCount: 0,
      count: 0,
    };
  }
}
