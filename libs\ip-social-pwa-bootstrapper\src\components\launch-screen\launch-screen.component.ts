import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';

import { StructureService } from '@ip/pwa';
import { AppInstallService, AuthenticationService, AuthStatus } from '@ip/social-core';
import { filter, switchMap, take } from 'rxjs/operators';

@Component({
  templateUrl: './launch-screen.component.html'
})
export class LaunchScreenComponent implements OnInit {
  public authStatus = AuthStatus;

  constructor(
    public auth: AuthenticationService,
    public appInstall: AppInstallService,
    private router: Router,
    private structureService: StructureService,
  ) { }

  ngOnInit() {
    this.auth.auth$
      .pipe(
        filter(status => status === AuthStatus.Authenticated),
        take(1),
        switchMap(() => this.structureService.defaultRoute$),
      )
      .subscribe(defaultRoute => {
        if (defaultRoute) {
          this.router.navigate([defaultRoute.path]);
        }
      });
  }
}
