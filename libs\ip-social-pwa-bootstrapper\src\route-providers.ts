import { MODULE_ROUTE, ModuleRouteProvider } from '@ip/pwa';
import { AuthGuard } from '@ip/social-core';

import { LaunchScreenComponent } from './components/launch-screen/launch-screen.component';
import { UserMenuButtonComponent } from './components/user-menu-button/user-menu-button.component';

// eslint-disable-next-line @typescript-eslint/naming-convention
export function ModuleRouteProvidersFactory(): ModuleRouteProvider[] {
  return [
    {
      provide: MODULE_ROUTE,
      multi: true,
      useValue: {
        type: 'social-pwa-timeline',
        icon: 'home-outline',
        route: {
          loadChildren: () => import('@ip/social-pwa').then(m => m.TimelineRouteModule),
          canActivate: [AuthGuard]
        }
      },
    },
    {
      provide: MODULE_ROUTE,
      multi: true,
      useValue: {
        type: 'social-pwa-persons',
        icon: 'people-outline',
        route: {
          loadChildren: () => import('@ip/social-pwa').then(m => m.PersonFinderRouteModule),
          canActivate: [AuthGuard]
        }
      },
    },
    {
      provide: MODULE_ROUTE,
      multi: true,
      useValue: {
        type: 'social-pwa-profile',
        icon: 'person-outline',
        route: {
          loadChildren: () => import('@ip/social-pwa').then(m => m.ProfileRouteModule),
          canActivate: [AuthGuard]
        }
      },
    },
    {
      provide: MODULE_ROUTE,
      multi: true,
      useValue: {
        type: 'social-pwa-user',
        component: UserMenuButtonComponent,
        route: {
          loadChildren: () => import('@ip/social-pwa').then(m => m.UserRouteModule),
          canActivate: [AuthGuard]
        }
      },
    },
    {
      provide: MODULE_ROUTE,
      multi: true,
      useValue: {
        type: 'social-pwa-launch-screen',
        route: {
          path: '',
          component: LaunchScreenComponent
        },
        applicationRoute: true,
      },
    },
    {
      provide: MODULE_ROUTE,
      multi: true,
      useValue: {
        type: 'social-pwa-blog',
        route: {
          path: 'blog',
          loadChildren: () => import('@ip/social-pwa').then(m => m.BlogRouteModule),
        },
        applicationRoute: true,
      },
    },
  ];
}
