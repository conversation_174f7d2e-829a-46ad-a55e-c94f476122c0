import { Directive, HostBinding, HostListener, Input, OnInit } from '@angular/core';

import { IPerson, ResourceService } from '@ip/social-core';

@Directive({
  // eslint-disable-next-line @angular-eslint/directive-selector
  selector: 'img[ips-avatar-src]',
})
export class AvatarSrcDirective implements OnInit {
  // eslint-disable-next-line @angular-eslint/no-input-rename
  @Input('ips-avatar-src')
  data!: Pick<IPerson, 'id' | 'profileImage'>;

  @Input()
  size: 'small' | 'large' = 'small';

  @HostBinding('src')
  securedSrc?: string;

  @HostBinding('class.ips-animate-image')
  animateImage = true;

  @HostBinding('class.ips-loaded')
  loaded = false;

  @HostBinding('class.ips-error')
  errored = false;

  constructor(private resourceService: ResourceService) { }

  @HostListener('load', ['$event'])
  onLoad(_event: Event) {
    this.loaded = true;
    this.errored = false;
  }

  @HostListener('error', ['$event'])
  onError(_event: Event) {
    this.errored = true;
    this.getSrc();
  }

  ngOnInit() {
    this.getSrc();
  }

  private getSrc() {
    this.resourceService.avatarSrc(this.data.id, this.data.profileImage, this.size)
      .subscribe(src => this.securedSrc = src);
  }
}
