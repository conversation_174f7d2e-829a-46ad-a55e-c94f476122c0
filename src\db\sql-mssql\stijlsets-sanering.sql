-- Plustab
-- 'Icoon' veld verwijderen
DELETE FROM SitDefTab
WHERE Nam = 'Icoon'
GO

-- Stijlsets
-- 'Achtergrond body'
-- 'Body repeterend'
-- 'Achtergrond canvas'
-- 'Canvas repeterend'
-- 'Achtergrond header'
-- 'Logo'
-- 'Gerelateerde artikelen'
-- 'Trefwoorden'
-- 'Delen met sociale media'
-- 'Iconen bij links'
-- Verwijderen (beide stijlsets)
DELETE FROM VarVldTab
WHERE VewDefIdt IN (SELECT
    VewDefIdt
  FROM VewDefTab
  WHERE Nam IN (
  'Achtergrond body',
  'Body repeterend',
  'Achtergrond canvas',
  'Canvas repeterend',
  'Achtergrond header',
  'Logo',
  'Gerelateerde artikelen',
  'Trefwoorden',
  'Delen met sociale media',
  'Iconen bij links'
  ))
GO

DELETE FROM VewDefTab
WHERE Nam IN (
'Achtergrond body',
'Body repeterend',
'Achtergrond canvas',
'Canvas repeterend',
'Achtergrond header',
'Logo',
'Gerelateerde artikelen',
'Trefwoorden',
'Delen met sociale media',
'Iconen bij links'
)
GO

-- 'Regelhoogte'
-- 'CSS sprite (donker)'
-- 'CSS sprite (licht)'
-- 'DCTERMS.creator'
-- 'OVERHEID.authority'
-- 'Splash overlay'
-- 'Splash overlay tekst'
-- 'Afmeting ronding'
-- 'Afmeting schaduw'
-- Verwijderen (nieuwe stijlset)
DELETE FROM VarVldTab
WHERE VewDefIdt IN (SELECT
    VewDefIdt
  FROM VewDefTab
  WHERE Nam IN (
  'Regelhoogte',
  'CSS sprite (donker)',
  'CSS sprite (licht)',
  'DCTERMS.creator',
  'OVERHEID.authority',
  'Splash overlay',
  'Splash overlay tekst',
  'Afmeting ronding',
  'Afmeting schaduw'
  )
  AND VewIdt NOT IN (SELECT
    VewIdt
  FROM VewTab
  WHERE Map = 'intranetzuidholland'))
GO

DELETE FROM VewDefTab
WHERE Nam IN (
  'Regelhoogte',
  'CSS sprite (donker)',
  'CSS sprite (licht)',
  'DCTERMS.creator',
  'OVERHEID.authority',
  'Splash overlay',
  'Splash overlay tekst',
  'Afmeting ronding',
  'Afmeting schaduw'
  )
  AND VewIdt NOT IN (SELECT
    VewIdt
  FROM VewTab
  WHERE Map = 'intranetzuidholland')
GO

-- 'Aantal lay-out kolommen'
-- Default moet 12 worden (beide)
UPDATE VewDefTab
SET Def = '12'
WHERE Nam = 'Aantal lay-out kolommen'
GO

-- 'Google analytics sid'
-- In nieuwe stijlset hernoemen naar Google universal analytics sid
UPDATE VewDefTab
SET Nam = 'Google universal analytics sid'
WHERE Nam = 'Google analytics sid'
AND VewIdt IN (SELECT
  VewIdt
FROM VewTab
WHERE Map = 'intranetzuidholland')
GO
