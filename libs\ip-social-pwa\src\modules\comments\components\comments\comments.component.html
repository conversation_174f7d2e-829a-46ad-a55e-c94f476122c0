<ng-container *ngIf="settings && authorizer$ | async as authorizer">
  <ion-grid *ngIf="comments$ | async as data">
    <ion-row>
      <ion-col size="12" ipsScrollTo="comments" [scrollDelay]="700">
        <h2>{{ 'comments.heading' | transloco }}</h2>

        <ng-container *ngIf="'Create' | cAuthorize: authorizer">
          <ips-comment-draft-reminder
            *ngIf="data.draft && !openDraft"
            class="ips-comment-draft-reminder"
            (openDraft)="openDraft = true"
          ></ips-comment-draft-reminder>

          <ips-comment-form
            *ngIf="!data.draft || openDraft"
            [settings]="settings"
            [reference]="reference"
            [comment]="data.draft"
            (saved)="scrollToComment($event)"
            (change)="openDraft = true"
          ></ips-comment-form>
        </ng-container>

        <ips-comment-thread
          *ngFor="let comment of data.comments; trackBy: trackByFn"
          [settings]="settings"
          [authorizer]="authorizer"
          [comment]="comment"
        ></ips-comment-thread>
      </ion-col>

      <ion-col size="12" class="ion-text-center">
        <ion-button
          *ngIf="data.comments.length < data.commentCount"
          (click)="loadMore(data.comments.length)"
          [disabled]="inProgress"
          size="small"
        >
          {{ 'comments.more' | transloco: { count: data.commentCount - data.comments.length } }}
        </ion-button>
      </ion-col>
    </ion-row>
  </ion-grid>
</ng-container>
