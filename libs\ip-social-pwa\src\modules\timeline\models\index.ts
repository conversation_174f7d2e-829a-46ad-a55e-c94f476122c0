// TODO: Split into files when models are more definitive.

import { IMetaConfig } from '@ip/social-core';

import { ActivityParts } from '../activities/models';

export interface ITimelineEntry {
  timestamp: number;
  id: string;
  date: Date;
  action: string;
  activity: ActivityParts;
  content: ITimelineContent;
  primaryValue: unknown; // SR: kan dit beter?
  metaConfig?: IMetaConfig;
  mutation: number;
  props: Record<string, unknown>;
  // props: { [key: string]: any };
}

export interface ITimelineContent {
  type: string;
  reference: string;
  data: Record<string, unknown>;
}

export interface IContentComponent {
  entry?: ITimelineEntry;
  data?: Record<string, unknown>;
}
