.ips-timeline-toast-container {
  border-radius: var(--border-radius);
  bottom: calc(8px + var(--ion-safe-area-bottom, 0px));
  box-shadow: rgba(0, 0, 0, 0.2) 0px 3px 5px -1px, rgba(0, 0, 0, 0.14) 0px 6px 10px 0px, rgba(0, 0, 0, 0.12) 0px 1px 18px 0px;
  left: 50%;
  position: fixed;
  transform: translateX(-50%) translateY(0);
  transition: transform 300ms cubic-bezier(0.4, 0, 0.2, 1);
  visibility: visible;
  z-index: 100;

  &.ips-timeline-toast-hidden {
    transform: translateX(-50%) translateY(80px);
    visibility: hidden;
  }
}

.ips-timeline-toast {
  display: flex;
  justify-content: space-between;
}

.ips-timeline-toast-btn {
  --border-radius: 0;

  margin: 0;
}
