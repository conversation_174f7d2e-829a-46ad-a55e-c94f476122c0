import { HTTP_INTERCEPTORS } from '@angular/common/http';
import { Provider } from '@angular/core';

import { AuthenticationConfig } from '../base/authentication.config';
import { AuthenticationService } from '../base/authentication.service';
import { IproxAccessTokenInterceptor } from './iprox-authentication.interceptor';
import { IproxAuthenticationService } from './iprox-authentication.service';

export class IproxAuthenticationConfig extends AuthenticationConfig {
  constructor() {
    super();
  }

  getProviders(): Provider[] {
    return [
      {
        provide: HTTP_INTERCEPTORS,
        useClass: IproxAccessTokenInterceptor,
        multi: true,
      },
      {
        provide: AuthenticationService,
        useClass: IproxAuthenticationService,
      },
    ];
  }
}
