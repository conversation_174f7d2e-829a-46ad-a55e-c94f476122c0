@import "../../../../scss/variables";
@import "../../../../scss/mixins";

ion-list-header {
  @include font-size(-2);

  margin-top: $ips-unit * 2;
  min-height: auto;

  + ion-item {
    ion-label {
      margin-top: $ips-unit;
    }
  }
}

ion-item {
  --inner-border-width: 0;
  --min-height: auto;

  ion-label {
    margin-bottom: 0;
    margin-top: 0;
  }

  &:last-child {
    --inner-border-width: 0 0 1px 0;

    ion-label {
      margin-bottom: $ips-unit;
    }
  }
}
