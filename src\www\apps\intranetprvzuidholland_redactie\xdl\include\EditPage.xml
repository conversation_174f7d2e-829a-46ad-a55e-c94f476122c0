<?xml version="1.0" encoding="utf-8"?>
<node name="data">
  <node name="site" alias="current" anchor="site_current">
    <table name="ItmTab" alias="Itm" />
    <table name="SitTab" alias="Sit" joincondition="Sit.SitIdt=Itm.SitIdt"/>
    <where condition="Itm.ItmIdt=$$ItmIdt$$" />
    <field table="Sit" name="SitIdt" type="attribute,set" />
    <field table="Sit" name="WcaTyp" type="attribute" />
    <field table="Sit" name="GryLst" type="attribute" />
    <field table="Sit" name="Int" type="attribute" />
    <field table="Sit" name="Sys" type="attribute" />
    <field table="Sit" name="Nam" type="tag" />
    <node name="item" alias="real" anchor="connect" distinct="true">
      <table name="ItmTab" alias="Itm" />
      <table name="PagTab" alias="Pag" jointype="left" joincondition="Itm.ItmIdt = Pag.ItmIdt" />
      <table name="PagTypTab" alias="PagTyp" jointype="left" joincondition="PagTyp.PagTypIdt = Itm.PagTypIdt" />
      <join node="site.current" field="SitIdt" condition="Itm.SitIdt=$$" />
      <where condition="Itm.ItmIdt=$$ItmIdt$$" />
      <field table="Itm" name="Vrs" type="attribute" />
      <field table="Itm" name="ItmIdt" type="attribute" />
      <field table="Pag" name="RedDtm" type="attribute" />
      <field table="Pag" name="RedTyd" type="attribute" />
      <field table="Itm" name="LstPubDtm" type="attribute" />
      <field table="Itm" name="LstPubTyd" type="attribute" />
      <field table="Itm" name="PubSttDtm" type="attribute" />
      <field table="Itm" name="PubEndDtm" type="attribute" />
      <field table="Itm" name="PubTyd" type="attribute" />
      <field table="Itm" name="PagTypIdt" type="attribute" />
      <field table="Itm" name="ItmTyp" type="attribute" />
      <field table="Itm" name="Sts" type="attribute" />
      <field table="Itm" name="Cmp" type="attribute" />
      <field table="Itm" name="Wca" type="attribute" />
      <field table="Itm" name="Gry" type="attribute" />
      <field table="Itm" name="Lbl" type="tag" />
      <field table="PagTyp" name="Aka" alias="PagTypAka" type="tag" />
      <field table="PagTyp" name="Hid" type="attribute" />

      <node name="page" alias="current" anchor="currentpage">
        <table name="PagTab" alias="Pag" />
        <join node="connect" field="ItmIdt" condition="Pag.ItmIdt=$$" />
        <field table="Pag" name="PagIdt" type="attribute,set" />
        <field table="Pag" name="ItmIdt" type="attribute" />
        <field table="Pag" name="Vrs" type="attribute" />
        <field table="Pag" name="Tit" type="tag" />
        <field table="Pag" name="RedGebIdt" type="attribute" />
        <field table="Pag" name="RedDtm" type="tag" />
        <field table="Pag" name="RedTyd" type="tag" />
        <table name="GebTab" alias="RedGeb" jointype="left" joincondition="RedGeb.GebIdt = Pag.RedGebIdt" />
        <field table="RedGeb" name="Nam" alias="RedGebNam" type="tag" />

        <node name="content" xsl="/xsl/include/Form_content" collapse="cluster">
          <node name="clusters" transform="AllActiveCluster">
            <node name="cluster" order="Cls.VlgNum ASC, Def.VlgNum ASC" tree="ParIdt,PagClsIdt">
              <table name="PagClsTab" alias="Cls" />
              <table name="ClsTab" alias="Def" joincondition="Cls.ClsIdt=Def.ClsIdt" />
              <join node="currentpage" field="PagIdt" condition="Cls.PagIdt = $$" />
              <field table="Def" name="ClsIdt" type="attribute" />
              <field table="Def" name="ParIdt" alias="DefParIdt" type="attribute" />
              <field table="Def" name="ProTypIdt" type="attribute" />
              <field table="Def" name="RepTyp" type="attribute" />
              <field table="Def" name="Opt" type="attribute" />
              <field table="Def" name="Rev" type="attribute" />
              <field table="Def" name="Skp" type="attribute" />
              <field table="Def" name="Stt" type="attribute" />
              <field table="Def" name="VlgNum" alias="DefVlgNum" type="attribute" />
              <field table="Def" name="Nam" type="tag" />
              <field table="Def" name="Toe" type="tag" />
              <field table="Cls" name="Vrs" type="attribute" />
              <field table="Cls" name="PagClsIdt" type="attribute" />
              <field table="Cls" name="ParIdt" type="attribute" />
              <field table="Cls" name="VlgNum" type="attribute" />
              <field table="Cls" name="Cmp" type="attribute" />
              <field table="Cls" name="Wca" type="attribute" />
              <field table="Cls" name="Gry" type="attribute" />
            </node>
          </node>
          <node name="velden" xdl="/xdl/include/Page_cluster_veld" collapse="data/veld" />
        </node>

        <node name="include_clusterdef" session="$$site.current::SitIdt$$_$$connect::PagTypIdt$$" xdl="include/Page_clusterdefinition" collapse="data/clusterdefinition"/>

        <node name="bookmarks" run="Bookmarks" collapse="bookmarks">
          <if xpath=".//clusterdefinition[@ClsIdt=$$ActClsIdt$$]//veld[@GgvTyp='19'][site]/@GgvTyp|.//clusterdefinition[@ClsIdt=$$ActClsIdt$$]//veld[@GgvTyp='24'][site]/@GgvTyp" expr="." />
        </node>
      </node>
    </node>
  </node>
</node>