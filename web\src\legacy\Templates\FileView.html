﻿<p class="ips-loading grid-realign" ng-if="FileViewCtrl.isLoading"><i class="fa-solid fa-spin fa-spinner"></i></p>

<div class="grid-zone grid_12 z-file-view z-file-not-found" ng-if="!FileViewCtrl.isLoading && FileViewCtrl.store.file === undefined">
  <div class="grid-blok grid_12 rol-social-group-components has-elt-breakpoints">
    <div class="grid-element">
      <div class="grid-edge">
        <div class="grid-box">
          <ips-component data-component="not-found">
            <content>
              <ips-not-found></ips-not-found>
            </content>
          </ips-component>
        </div>
      </div>
    </div>
  </div>
</div>

<div class="grid-zone grid_12 z-backbuttons z-backbuttons-static" ng-if=":: FileViewCtrl.store.file">
  <div class="grid-blok grid_12 rol-backbuttons type-backbuttons has-elt-breakpoints">
    <div class="grid-element">
      <div class="grid-edge">
        <div class="grid-box">
          <button type="button" class="btn ips-button ips-primary" ng-click="FileViewCtrl.back()">
            <i class="fa-solid fa-angle-double-left" aria-hidden="true"></i>
            <span>{{:: 'view.files.file.back' | ipRefText }}</span>
          </button>
        </div>
      </div>
    </div>
  </div>
</div>

<div class="grid-zone grid_12 z-file-view" ng-if=":: FileViewCtrl.store.file">
  <div class="grid-row single-elt-row has-fullwidth-elt rol-rij-social-group-components has-elt-breakpoints has-no-list-icons has-no-link-icons has-no-button-icons">
    <div class="grid-blok grid_12 rol-social-group-components type-social-files has-elt-breakpoints has-no-list-icons has-no-link-icons has-no-button-icons">
      <div class="grid-element">
        <div class="grid-edge">
          <div class="grid-box">
            <ips-component data-actions="FileViewCtrl.actions" data-component="files" ng-if="FileViewCtrl.fileTarget.entity && FileViewCtrl.config">
              <content>
                <ips-file
                  class="ips-component-item"
                  data-file="FileViewCtrl.store.file"
                  data-config="FileViewCtrl.config"
                  data-file-target="FileViewCtrl.fileTarget"
                ></ips-file>
              </content>
            </ips-component>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div class="grid-row single-elt-row has-fullwidth-elt rol-rij-social-group-components has-elt-breakpoints has-no-list-icons has-no-link-icons has-no-button-icons" ng-if="FileViewCtrl.store.settings.enableComments && FileViewCtrl.commentsConfig">
    <div class="grid-blok grid_12 rol-social-group-components type-social-files has-elt-breakpoints has-no-list-icons has-no-link-icons has-no-button-icons">
      <div class="grid-element">
        <div class="grid-edge">
          <div class="grid-box">
            <ips-comments data-owner="{{ FileViewCtrl.commentOwner }}" data-config="{{ FileViewCtrl.commentsConfig }}"></ips-comments>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
