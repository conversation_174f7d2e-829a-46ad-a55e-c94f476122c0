import { Component, Input, OnDestroy, OnInit } from '@angular/core';

import { Subject, timer } from 'rxjs';
import { switchMap, takeUntil } from 'rxjs/operators';

import { IReference } from '../../../core/models';
import { MetaService } from '../../services/meta.service';
import { VisitService } from '../../services/visit.service';

@Component({
  selector: 'ips-visit-tracker',
  template: ''
})
export class VisitTrackerComponent implements OnInit, OnDestroy {
  private readonly TIMER = 10000; // 10 seconds.

  private destroyed = new Subject();

  @Input()
  reference!: IReference;

  constructor(private metaService: MetaService, private visitService: VisitService) { }

  ngOnInit() {
    this.visitTimer();
  }

  ngOnDestroy() {
    this.destroyed.next();
    this.destroyed.complete();
  }

  private visitTimer() {
    timer(this.TIMER)
      .pipe(
        takeUntil(this.destroyed),
        switchMap(() => this.visitService.registerVisit(this.reference)),
      )
      .subscribe(() => {
        this.metaService.addVisit(this.reference);
      });
  }
}
