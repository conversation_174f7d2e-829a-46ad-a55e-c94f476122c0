@import "../../../../../../ip-social-pwa/src/scss/mixins";
@import "../../../../../../ip-social-pwa/src/scss/variables";

$ion-iphone-safe-area: 18px;

:root {
  // In this app we do NOT use --ion-grid-paddings with viewports (eg. --ion-grid-padding-sm);
  --ion-grid-padding: 10px;
  --ion-grid-column-padding: 5px;

  // Social
  --ips-avatar-size: #{$ips-unit * 6};
}

body {
  line-height: 1.5;
}

$ips-timeline-background-color: $ion-content-background;

ion-app {
  margin-left: auto;
  margin-right: auto;
  max-width: 768px;
}

.ips-display-none {
  display: none !important;
}

virtual-scroller {
  background-color: var(--ips-timeline-background-color, #{$ips-timeline-background-color});
}

ion-toolbar {
  --min-height: var(--ip-header-height, #{$ips-header-height});
}

.plt-iphone.plt-pwa {
  --ion-safe-area-bottom: $ion-iphone-safe-area;
}

.plt-iphone.plt-pwa ion-toast {
  --ion-safe-area-bottom: 60px + $ion-iphone-safe-area;
}

ion-toast {
  --ion-safe-area-bottom: 60px;
}

.ips-animate-image {
  opacity: 0;
  transition: opacity 200ms cubic-bezier(0.4,0.0,0.2,1);

  &.ips-loaded {
    opacity: 1;
  }
}

.ips-ion-text-button {
  --background: transparent;
  --color: var(--ion-color-primary);

  &.ips-px-0 {
    --padding-start: 0;
    --padding-end: 0;
  }

  letter-spacing: normal;
}

ion-list-header {
  padding-left: 0;
}

ion-col ion-item-divider,
ion-col ion-item {
  --padding-start: 0;
}

ion-header {
  &.header-md::after {
    background-image: none;
    bottom: 0;
  }

  border-bottom: 1px solid var(--ion-color-primary-tint);

  ion-title img {
    vertical-align: middle;
    max-height: var(--ip-header-height, #{$ips-header-height});
    padding-bottom: $ips-unit;
    padding-top: $ips-unit;
  }

  ion-title {
    bottom: 0;
    left: 0;
    position: absolute;
    right: 0;
    text-align: center;
    top: 0;
  }
}

.ips-timeline-content {
  clear: both;
}

.ips-side-menu {
  justify-content: flex-end;

  > .ion-overlay-wrapper {
    --max-width: 300px;
  }
}

ips-person {
  font-weight: 500;

  + ips-person {
    margin-left: $ips-unit;
  }
}

ion-ripple-effect {
  color: var(--ripple-color);
}

.ips-ion-button-text {
  padding-left: 8px;
}

.ips-ion-button-wrapper {
  height: 44px; // A standard button is 44px;
}

ion-tab-bar {
  --background: var(--ion-color-primary);
  --color: var(--ion-color-primary-contrast);

  ion-tab-button {
    --color: var(--ion-color-primary-contrast);
    --color-selected: var(--ion-color-primary-contrast);
  }
}

ion-tab-button {
  &.tab-selected {
    ion-icon:not(.ip-tab-selected-icon) {
      display: none;
    }

    .ip-tab-selected-icon {
      display: block;
    }
  }

  ion-icon.ip-tab-selected-icon {
    display: none;
  }
}

ion-item ion-thumbnail + ion-label {
  margin-left: $ips-unit * 2;
}

ion-thumbnail {
  @include aspect-ratio-thumbnail;

  height: auto;
  width: 100px;
}

ion-fab {
  --ion-safe-area-right: 5px;
}

ion-icon {
  pointer-events: none;
}

.ips-rich-content {
  // p {
  //   @include font-size(0);
  // }

  img {
    height: auto;
  }
}

.ips-text-italic {
  font-style: italic;
}

// Form
ion-button {
  --box-shadow: none;

  letter-spacing: 0;
  text-transform: inherit;
}

ion-input {
  background-color: #fff !important;
  margin-top: $ips-unit;

  .native-input {
    --padding-start: #{$ips-unit};
    --padding-end: #{$ips-unit};
    --padding-bottom: #{$ips-unit};
    --padding-top: #{$ips-unit};
  }
}

// Fab button
ion-fab-button.fab-button-disabled::part(native) {
  --ion-color-base: var(--ion-color-light-shade);
  --ion-color-contrast: var(--ion-color-medium);
}
