import { Injectable } from '@angular/core';

import { ModalController } from '@ionic/angular';
import { IMetaConfig } from '@ip/social-core';

import { LikeActionSheetComponent } from '../components/like-action-sheet/like-action-sheet.component';

@Injectable()
export class LikeActionSheetService {
  constructor(public modalController: ModalController) { }

  // TODO
  // eslint-disable-next-line no-undef
  async open(metaConfig: IMetaConfig): Promise<HTMLIonModalElement> {
    const modal = await this.modalController.create({
      component: LikeActionSheetComponent,
      cssClass: 'ips-like-action-modal',
      componentProps: {
        metaConfig,
      }
    });

    await modal.present();

    return modal;
  }
}
