import { Component, Input, OnInit } from '@angular/core';

import { ResourceService } from '@ip/social-core';

import { IBlogSummary } from '../../../blog/models';
import { ITimelineEntry } from '../../models';
import { TimelineApi } from '../../models/internal/api.models';

@Component({
  templateUrl: './blog.component.html',
  selector: 'ips-timeline-blog',
  styles: [':host { display: block; }']
})
export class TimelineBlogComponent implements OnInit {
  @Input()
  data!: TimelineApi.IContentEntity<IBlogSummary>;

  @Input()
  entry!: ITimelineEntry;

  imageSrc?: string;

  constructor(private resourceService: ResourceService) { }

  ngOnInit() {
    this.resourceService.blogImageSrc(this.data.id, this.data.content.image, 'wide')
      .subscribe(src => this.imageSrc = src);
  }
}
