import { map, tap } from 'rxjs/operators';
import { Reference } from '../../core/models';

import { MetaService } from '../services/meta.service';
import { ILikeCollection } from './like-collection.model';
import { MetaComponent } from './meta-component.model';
import { IMostLiked } from './most-liked.model';

export interface IMetaConstructor {
  reference: Reference;
  timeStamp: number;
  visitCount: number | undefined;
  commentCount: number | undefined;
  likes: ILikeCollection | undefined;
  components: MetaComponent[];
}

/* eslint-disable @typescript-eslint/no-non-null-assertion */
export class Meta {
  working = false;

  reference: Reference;

  timeStamp: number;

  visitCount: number | undefined;

  commentCount: number | undefined;

  likes: ILikeCollection | undefined;

  components: MetaComponent[];

  constructor(private metaService: MetaService, data: IMetaConstructor) {
    this.reference = data.reference;
    this.timeStamp = data.timeStamp;
    this.visitCount = data.visitCount;
    this.commentCount = data.commentCount;
    this.likes = data.likes;
    this.components = data.components;

    if (data.likes) {
      this.updateMostLiked();
    }
  }

  like(type: string): void {
    if (this.working) {
      return;
    }

    this.working = true;

    this.metaService.like(this, type)
      .pipe(
        map(instructions => {
          instructions.remove.forEach(like => this.likes!.data[like.type].count--);
          instructions.add.forEach(like => {
            if (this.likes!.data[like.type]) {
              this.likes!.data[like.type].count++;
            }
            else {
              this.likes!.data[like.type] = { count: 1 };
            }

            // SR: Quick and dirty. For multi-like support this needs looking at.
            this.likes!.userLikes = [{ id: like.id, type: like.type }];
          });

          this.updateMostLiked();

          if (this.likes) {
            this.likes = { ...this.likes };
          }
        }),
        tap(() => this.working = false)
      )
      .subscribe(() => this.metaService.updateMeta(this));
  }

  delete(id: string, type: string): void {
    if (this.working) {
      return;
    }

    this.working = true;

    this.metaService.deleteLike(id)
      .pipe(
        map(() => {
          if (this.likes) {
            this.likes.data[type].count--;
            this.updateMostLiked();
            this.likes.userLikes = this.likes.userLikes.filter(l => l.id !== id);

            this.likes = { ...this.likes };
          }
        }),
        tap(() => this.working = false)
      )
      .subscribe(() => this.metaService.updateMeta(this));
  }

  private updateMostLiked() {
    this.likes!.mostLiked = Object.entries(this.likes!.data).reduce((mostLiked: IMostLiked, [type, { count }]): IMostLiked => {
      mostLiked.totalLikes = mostLiked.totalLikes + count;

      if (count > mostLiked?.count) {
        mostLiked.type = type;
        mostLiked.count = count;
      }

      return mostLiked;
    }, { type: '', count: 0, totalLikes: 0 });

    this.likes!.count = this.likes!.mostLiked.totalLikes;
  }
}
