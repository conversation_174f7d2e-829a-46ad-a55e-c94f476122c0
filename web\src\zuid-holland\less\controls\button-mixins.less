.follow-background-image(@icon-color) {
  @__encodedColor: escape(@icon-color);

  background-image: url("data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 20.6 20.8'%3E%3Cpath fill='none' stroke='@{__encodedColor}' stroke-linecap='round' stroke-linejoin='round' stroke-miterlimit='10' stroke-width='4' d='M17.6 2.9L7.9 17.8 2.9 11.5'/%3E%3C/svg%3E");
}

.follow-btn() {
  position: relative;

  &:not(.active) {
    background-color: @pzh-white;
    box-shadow: @pzh-box-shadow;

    > i {
      color: @pzh-blue !important;
    }

    &:hover > i.volg-dit,
    > i.volg-dit {
      .follow-background-image(@pzh-blue);
    }
  }

  &.active {
    background-color: @pzh-blue;

    > i {
      color: @pzh-white;
    }

    &:hover,
    &:active {
      background-color: @pzh-blue !important;

      > i {
        color: @pzh-white !important;
      }
    }
  }

  &:hover,
  &:active {
    background-color: @pzh-white !important;

    > i {
      color: @pzh-blue !important;
    }

    > i.volg-dit {
      .follow-background-image(@pzh-white);

      background-size: 60%;
    }
  }

  > i.volg-dit {
    .follow-background-image(@pzh-white);

    background-position: 50% 50%;
    background-repeat: no-repeat;
    background-size: 50%;
    height: @ips-round-button-size;
    left: 0;
    position: absolute;
    top: 0;
    transition: background-size 0.5s ease-in-out;
    width: @ips-round-button-size;
  }
}
