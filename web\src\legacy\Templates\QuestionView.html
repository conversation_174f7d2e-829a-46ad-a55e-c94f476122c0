﻿<p class="ips-loading grid-realign" ng-if="QuestionViewCtrl.isLoading"><i class="fa-solid fa-spin fa-spinner"></i></p>

<div class="grid-zone grid_12 z-question-view z-question-not-found" ng-if="!QuestionViewCtrl.isLoading && QuestionViewCtrl.question === undefined">
  <div class="grid-blok grid_12 rol-social-group-components has-elt-breakpoints">
    <div class="grid-element">
      <div class="grid-edge">
        <div class="grid-box">
          <ips-component data-component="not-found">
            <content>
              <ips-not-found></ips-not-found>
            </content>
          </ips-component>
        </div>
      </div>
    </div>
  </div>
</div>

<div class="grid-zone grid_12 z-backbuttons z-backbuttons-static" ng-if=":: QuestionViewCtrl.question">
  <div class="grid-blok grid_12 rol-backbuttons type-backbuttons has-elt-breakpoints">
    <div class="grid-element">
      <div class="grid-edge">
        <div class="grid-box">
          <button type="button" class="btn ips-button ips-primary" ng-click="QuestionViewCtrl.back()">
            <i class="fa-solid fa-angle-double-left" aria-hidden="true"></i>
            <span>{{:: 'view.questions.question.back' | ipRefText }}</span>
          </button>
        </div>
      </div>
    </div>
  </div>
</div>

<div class="grid-zone grid_12 z-question-view" ng-if=":: QuestionViewCtrl.question">
  <div class="grid-row single-elt-row has-fullwidth-elt rol-rij-social-group-components has-no-list-icons has-no-link-icons has-no-button-icons">
    <div class="grid-blok grid_12 rol-social-group-components has-elt-breakpoints">
      <div class="grid-element">
        <div class="grid-edge">
          <div class="grid-box">
            <ips-component data-component="questions">
              <content>
                <ips-question
                  class="ips-component-item"
                  data-question="QuestionViewCtrl.question"
                  data-settings="QuestionViewCtrl.settings"
                ></ips-question>
              </content>
            </ips-component>
          </div>
        </div>
      </div>
    </div>
  </div>

  <div class="grid-row single-elt-row has-fullwidth-elt rol-rij-social-group-components has-no-list-icons has-no-link-icons has-no-button-icons"
       ng-repeat="answer in QuestionViewCtrl.question.answers | orderBy: ['-isAcceptedAnswer', 'creationDate'] track by answer.id">
    <div class="grid-blok grid_12 rol-social-group-components type-social-question has-elt-breakpoints" id="QuestionAnswer_{{ answer.id }}">
      <div class="grid-element">
        <div class="grid-edge">
          <div class="grid-box">
            <ips-component data-component="questions">
              <content>
                <ips-answer
                  class="ips-component-item"
                  data-answer="answer"
                  data-settings="QuestionViewCtrl.settings"
                  ng-attr-id="{{ $first ? 'answers' : undefined }}"
                ></ips-answer>
              </content>
            </ips-component>
          </div>
        </div>
      </div>
    </div>
  </div>

  <div class="grid-row single-elt-row has-fullwidth-elt rol-rij-social-group-components has-no-list-icons has-no-link-icons has-no-button-icons">
    <div class="grid-blok grid_12 rol-social-group-components has-elt-breakpoints">
      <div class="grid-element">
        <div class="grid-edge">
          <div class="grid-box">
            <ips-component data-component="questions">
              <content>
                <ips-answer-form
                  class="ips-component-item"
                  data-settings="QuestionViewCtrl.settings"
                  data-save="QuestionViewCtrl.addAnswer($model)"
                ></ips-answer-form>
              </content>
            </ips-component>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
