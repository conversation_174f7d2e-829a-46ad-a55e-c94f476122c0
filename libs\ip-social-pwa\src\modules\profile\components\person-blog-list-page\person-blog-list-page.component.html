<ion-header>
  <ip-header [title]="'blog.list.pageTitle' | transloco"></ip-header>
</ion-header>

<ion-content fullscreen>
  <ion-grid *ngIf="data$ | async as data;">
    <ion-row>
      <ion-col size="12" class="ion-text-center">
        <ips-person-header [person]="data.person"></ips-person-header>
        <h1 class="ips-person-page-title" [routerLink]="['/persons', data.person.id]">{{ data.person.fullName }}</h1>
      </ion-col>

      <ion-col size="12">
        <ips-blog-list [userId]="data.person.id" [fullList]="true" [isOwnProfile]="data.isOwnProfile"></ips-blog-list>
      </ion-col>
    </ion-row>
  </ion-grid>
</ion-content>
