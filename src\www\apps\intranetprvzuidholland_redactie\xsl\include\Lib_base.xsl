<?xml version="1.0" encoding="utf-8"?>
<xsl:stylesheet xmlns:xsl="http://www.w3.org/1999/XSL/Transform" version="1.0" xmlns:resources="urn:resources" extension-element-prefixes="resources">
  <xsl:import href="/xsl/include/Lib_base.xsl" />

  <xsl:template match="*" mode="head_lib_css_core">
    <xsl:if test="site/item/@PagTypIdt = pagetype[Aka = 'workgroup']/@PagTypIdt or site/item/PagTypAka = 'workgroup' or site/item[not(@ParIdt)]">
      <meta name="hideversions" content="true" />
    </xsl:if>

    <link rel="stylesheet" href="{$iprox_client_home}css/iprox.css" type="text/css"/>
    <link rel="stylesheet" href="{$iprox_home}css/colors.css" type="text/css"/>
  </xsl:template>

</xsl:stylesheet>
