import { Component, ComponentFactoryResolver, Input, OnInit, ViewChild, ViewContainerRef } from '@angular/core';
import { ActivatedRoute } from '@angular/router';

import { IReference } from '@ip/social-core';
import { take } from 'rxjs/operators';

import { BlogComponent } from '../../../blog/components/blog/blog.component';
import { AnniversaryEventComponent } from '../../../anniversary/components/anniversary-event/anniversary-event.component';
import { IproxContentComponent } from '../iprox-content/iprox-content.component';

@Component({
  selector: 'ips-page',
  template: `
    <ion-header>
      <ip-header [title]="pageTitle ? (pageTitle | transloco) : ' '"></ip-header>
    </ion-header>
    <ion-content><ng-template #outlet></ng-template></ion-content>
  `,
})
export class PageComponent implements OnInit {
  @Input()
  reference?: IReference;

  @ViewChild('outlet', { static: true, read: ViewContainerRef })
  outlet!: ViewContainerRef;

  pageTitle?: string;

  constructor(
    private componentFactoryResolver: ComponentFactoryResolver,
    private activatedRoute: ActivatedRoute
  ) { }

  ngOnInit() {
    const reference: IReference | undefined = this.reference || this.activatedRoute.snapshot.data.reference;

    if (reference === undefined) {
      throw new Error('Page instantiated without a reference');
    }

    this.page(this.outlet, reference);
  }

  private page(outlet: ViewContainerRef, reference: IReference) {
    // TODO double check wether component actually gets destroyed.
    outlet.clear();

    switch (reference.collection.toLowerCase()) {
      case 'iproxcontent':
        this.createIproxContent(outlet, reference);
        break;
      case 'blog':
        this.createBlogContent(outlet, reference);
        break;
      case 'anniversaryevent':
        this.createAnniversaryContent(outlet, reference);
        break;
      default:
        throw new Error(`[Page Resolver] Unable to find component for collection: '${reference.collection}'`);
    }
  }

  // TODO: Move these to providers, group them together for better maintainability.
  private createIproxContent(outlet: ViewContainerRef, reference: IReference): void {
    const factory = this.componentFactoryResolver.resolveComponentFactory(IproxContentComponent);
    const component = outlet.createComponent(factory);
    component.instance.reference = reference;
    component.instance.pageTitle
      .pipe(take(1))
      .subscribe(pageTitle => this.pageTitle = pageTitle);
  }

  private createBlogContent(outlet: ViewContainerRef, reference: IReference) {
    this.pageTitle = 'page.pageTitle.blog';

    const factory = this.componentFactoryResolver.resolveComponentFactory(BlogComponent);
    const component = outlet.createComponent(factory);
    component.instance.blogId = reference.id;
  }

  private createAnniversaryContent(outlet: ViewContainerRef, reference: IReference) {
    this.pageTitle = 'page.pageTitle.anniversary';

    const factory = this.componentFactoryResolver.resolveComponentFactory(AnniversaryEventComponent);
    const component = outlet.createComponent(factory);
    component.instance.reference = reference;
  }
}
