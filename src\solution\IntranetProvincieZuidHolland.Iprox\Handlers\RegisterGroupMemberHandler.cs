﻿namespace IntranetProvincieZuidHolland.Iprox.Handlers {
  using System;
  using System.Collections.Generic;
  using System.Linq;

  using InfoProjects.Dxe.Linq;
  using InfoProjects.Dxe.Process;
  using InfoProjects.Dxe.Process.Handler;
  using InfoProjects.Dxe.Sql;
  using InfoProjects.Dxe.Util;
  using InfoProjects.Iprox.Modules.Frontend.Shared;

  using Action = InfoProjects.Dxe.Process.Action;

  /// <summary>
  /// The group member roles.
  /// </summary>
  public enum GroupMemberRole {
    /// <summary>
    /// Group member.
    /// </summary>
    Lid, 

    /// <summary>
    /// Group manager.
    /// </summary>
    Beheerder
  }

  /// <summary>
  /// Handler to register group member.
  /// </summary>
  public class RegisterGroupMemberHandler : IContextProcessHandler {
    #region Static Fields

    /// <summary>
    /// The group member role ID
    /// </summary>
    internal static readonly RobustLazy<IDictionary<GroupMemberProfile, int>> GroupMemberRoleId =
      new RobustLazy<IDictionary<GroupMemberProfile, int>>(
        () => {
          using (var sql = SqlConnection.GetSqlConnection()) {
            var query = sql.GetSqlWriter();
            query.AddTable("TakTab", "Rol", JoinType.FIRST, null);
            query.AddCondition("Rol.ParIdt IS NULL");
            query.AddCondition("Rol.FunIdt IS NULL");
            query.AddCondition("Rol.PrfIdt", new[] { 3, 6 });
            query.AddCondition("Rol.Nam", GroupMemberProfile.All.Select(p => p.Name));
            query.AddField("Rol", "TakIdt", "TakIdt");
            query.AddField("Rol", "Nam", "Nam");
            return query.GetRawRows()
              .ToDictionary(row => GroupMemberProfile.Get(row.Get<string>("Nam")), row => row.Get<int>("TakIdt"));
          }
        });

    #endregion

    #region Public Methods and Operators

    /// <summary>
    /// Processes the unit.
    /// </summary>
    /// <param name="unit">
    /// Process unit.
    /// </param>
    /// <param name="context">
    /// Process context.
    /// </param>
    /// <returns>
    /// Result props with Success = true.
    /// </returns>
    public ResultProps Process(ProcessUnit unit, ProcessContext context) {
      var itmIdt = ParseUnitProperty<int>(unit, "GroupItemId", true);
      var log = ParseUnitProperty<string>(unit, "Login", true);
      var dmn = ParseUnitProperty<string>(unit, "Domain");
      var nam = ParseUnitProperty<string>(unit, "Name", true);
      var eml = ParseUnitProperty<string>(unit, "Email", true);
      var rol = unit["Role"].To<GroupMemberRole?>() ?? GroupMemberRole.Lid;
      var profile = GroupMemberProfile.Get(
        rol, 
        rol == GroupMemberRole.Beheerder ? null : (bool?)GetProjectMembersActive(itmIdt, context));

      ValidateItemId(itmIdt);
      AuthorizeUser(GetUserId(log, dmn, nam, eml, context), GetFunctionIds(itmIdt, context, profile), profile, context);

      var result = new ResultProps();
      result["Success"] = "true";
      return result;
    }

    #endregion

    #region Methods

    /// <summary>
    /// Gets (a proxy for) the function IDs, creates a function if necessary.
    /// </summary>
    /// <param name="itmIdt">
    /// The group item ID.
    /// </param>
    /// <param name="context">
    /// The process context.
    /// </param>
    /// <param name="profile">
    /// The required profile.
    /// </param>
    /// <returns>
    /// The (proxy for the) function ID.
    /// </returns>
    internal static IDictionary<GroupMemberProfile, string> GetFunctionIds(
      int itmIdt, 
      ProcessContext context, 
      GroupMemberProfile profile = null) {
      var result = new Dictionary<GroupMemberProfile, string>();

      // Get existing functions
      var taskQuery = context.Sql.GetSqlWriter();
      taskQuery.AddTable("TakTab", "Tak", JoinType.FIRST, null);
      taskQuery.AddCondition("Tak.ParIdt", GroupMemberRoleId.Value.Values);
      taskQuery.AddTable("TakScpTab", "TakScp", JoinType.INNER, "Tak.TakIdt = TakScp.TakIdt");
      taskQuery.AddCondition("TakScp.ScpIdt", 3);
      taskQuery.AddCondition("TakScp.Wrd", itmIdt);
      taskQuery.AddField("Tak", "ParIdt", "ParIdt");
      taskQuery.AddField("Tak", "FunIdt", "FunIdt");
      foreach (var function in from task in taskQuery.GetRawRows()
                               join kvp in GroupMemberRoleId.Value on task.Get<int>("ParIdt") equals kvp.Value
                               select new { Role = kvp.Key, Id = task.Get<string>("FunIdt") }) {
        result[function.Role] = function.Id;
      }

      if (profile != null && !result.ContainsKey(profile)) {
        // Create a new function
        var funUnit = new ProcessUnit("FunTab") { Action = Action.ADD };
        funUnit["Nam"] = string.Format(
          "Groeps{0} {1}", 
          profile.Role.ToString().ToLowerInvariant(), 
          context.Sql.DetermineFieldValue("ItmTab", "Lbl", "ItmIdt", itmIdt.ToIproxString()));
        result[profile] = context.Schedule(funUnit) + ".CurIdt";

        // Add a task to the function
        var takUnit = new ProcessUnit("TakTab") { Action = Action.ADD };
        takUnit["Nam"] = funUnit["Nam"];
        takUnit["FunIdt"] = result[profile];
        takUnit["ParIdt"] = GroupMemberRoleId.Value[profile].ToIproxString();
        takUnit["PrfIdt"] = profile.Active == false ? "3" : "6";
        var takIdt = context.Schedule(takUnit) + ".CurIdt";

        // Add item scope to the task
        var itmScpUnit = new ProcessUnit("TakScpTab") { Action = Action.ADD };
        itmScpUnit["TakIdt"] = takIdt;
        itmScpUnit["ScpIdt"] = "3";
        itmScpUnit["Wrd"] = itmIdt.ToIproxString();
        itmScpUnit["Erf"] = "1";
        context.Schedule(itmScpUnit);

        // Add variant scope(s) to the task if the item is in a container
        var variantQuery = context.Sql.GetSqlWriter();
        variantQuery.AddTable("ItmTab", "Itm", JoinType.FIRST, null);
        variantQuery.AddCondition("Itm.ItmIdt", itmIdt);
        variantQuery.AddTable("SitRelTab", "Rel", JoinType.INNER, "Itm.SitIdt = Rel.NarSitIdt");
        variantQuery.AddTable(
          "VarTab", 
          "Var", 
          JoinType.INNER, 
          "Rel.VanSitIdt = Var.SitIdt AND Itm.SitIdt <> Var.SitIdt");
        variantQuery.AddField("Var", "VarIdt", "VarIdt");
        foreach (var varIdt in variantQuery.GetValues<int>()) {
          var varScpUnit = new ProcessUnit("TakScpTab") { Action = Action.ADD };
          varScpUnit["TakIdt"] = takIdt;
          varScpUnit["ScpIdt"] = "14";
          varScpUnit["Wrd"] = varIdt.ToIproxString();
          varScpUnit["Erf"] = "0";
          context.Schedule(varScpUnit);
        }
      }

      return result;
    }

    /// <summary>
    /// Gets a value indicating whether project members are active.
    /// </summary>
    /// <param name="projectItemId">
    /// The project item ID.
    /// </param>
    /// <param name="context">
    /// The process context.
    /// </param>
    /// <returns>
    /// A value indicating whether project members are active.
    /// </returns>
    internal static bool GetProjectMembersActive(int projectItemId, ProcessContext context) {
      var staticPage = StaticPage.Get(projectItemId.ToIproxString(), true);
      return staticPage == null || staticPage.SelectSingleNode(".//veld[Nam = 'Actieve leden' and Wrd = '0']") == null;
    }

    /// <summary>
    /// Parses a unit property.
    /// </summary>
    /// <param name="unit">
    /// The process unit.
    /// </param>
    /// <param name="propertyName">
    /// The property name.
    /// </param>
    /// <param name="required">
    /// Whether a non-empty value for the property is required.
    /// </param>
    /// <typeparam name="TResult">
    /// The result type.
    /// </typeparam>
    /// <returns>
    /// The unit property value.
    /// </returns>
    internal static TResult ParseUnitProperty<TResult>(ProcessUnit unit, string propertyName, bool required = false) {
      if (required ? !unit.IsSet(propertyName) : !unit.IsSupplied(propertyName)) {
        throw new ArgumentException(
          string.Format("Property {0} should be {1}.", propertyName, required ? "set" : "supplied"), 
          "unit");
      }

      var value = unit[propertyName];
      try {
        return value.To<TResult>();
      }
      catch {
        throw new ArgumentException(
          string.Format("Invalid value '{0}' for property '{1}'", value, propertyName), 
          "unit");
      }
    }

    /// <summary>
    /// Validates item ID as the ID of a published workgroup.
    /// </summary>
    /// <param name="itmIdt">
    /// The item ID.
    /// </param>
    internal static void ValidateItemId(int itmIdt) {
      using (var sql = SqlConnection.GetSqlConnection()) {
        var query = sql.GetSqlWriter();
        query.AddTable("SitItmVew", "Itm", JoinType.FIRST, null);
        query.AddCondition("Itm.ItmIdt", itmIdt);
        query.AddCondition("Itm.pagetype", "workgroup");
        query.AddTable("EnvTab", "Env", JoinType.INNER, "Itm.EnvIdt = Env.EnvIdt");
        query.AddCondition("Env.Prv", 0);
        if (!query.Exists()) {
          throw new ArgumentException("Item ID does not belong to a published workgroup", "itmIdt");
        }
      }
    }

    /// <summary>
    /// Gets the ProcessUnit to authorize the user.
    /// </summary>
    /// <param name="userId">
    /// The user ID proxy.
    /// </param>
    /// <param name="functionIds">
    /// The function IDs by role.
    /// </param>
    /// <param name="profile">
    /// The required profile.
    /// </param>
    /// <param name="context">
    /// The process context.
    /// </param>
    private static void AuthorizeUser(
      string userId, 
      IDictionary<GroupMemberProfile, string> functionIds, 
      GroupMemberProfile profile, 
      ProcessContext context) {
      int parsedUserId;
      if (int.TryParse(userId, out parsedUserId)) {
        UnRegisterGroupMemberHandler.DeAuthorizeUser(
          parsedUserId, 
          from kvp in functionIds where kvp.Key != profile select kvp.Value, 
          context);
      }

      var unit = new ProcessUnit("GebFunTab") { Action = Action.MAKE };
      unit[Reserved.KEY] = "GebIdt";
      unit[Reserved.ID] = userId;
      unit[Reserved.GROUP] = "FunIdt";
      unit["FunIdt"] = functionIds[profile];
      context.Schedule(unit);
    }

    /// <summary>
    /// Gets (a proxy for) the user ID, creates user if necessary.
    /// </summary>
    /// <param name="log">
    /// The user login name.
    /// </param>
    /// <param name="dmn">
    /// The user domain.
    /// </param>
    /// <param name="nam">
    /// The user name.
    /// </param>
    /// <param name="eml">
    /// The user e-mail.
    /// </param>
    /// <param name="context">
    /// The process context.
    /// </param>
    /// <returns>
    /// The (proxy for the) the user ID.
    /// </returns>
    private static string GetUserId(string log, string dmn, string nam, string eml, ProcessContext context) {
      var gebIdt = context.Sql.DetermineFieldValue("GebTab", "GebIdt", "Log", log, "Dmn", dmn);
      if (!string.IsNullOrEmpty(gebIdt)) {
        return gebIdt;
      }

      var unit = new ProcessUnit("GebTab") { Action = Action.ADD };
      unit["Log"] = log;
      unit["Dmn"] = dmn;
      unit["Nam"] = nam;
      unit["Eml"] = eml;
      unit["Act"] = "1";
      unit["Cng"] = "0";
      return context.Schedule(unit) + ".CurIdt";
    }

    #endregion

    /// <summary>
    /// The group member profile.
    /// </summary>
    internal class GroupMemberProfile {
      #region Static Fields

      /// <summary>
      /// All possible group member profiles.
      /// </summary>
      public static readonly IEnumerable<GroupMemberProfile> All =
        new[] {
                new GroupMemberProfile { Role = GroupMemberRole.Lid, Active = true }, 
                new GroupMemberProfile { Role = GroupMemberRole.Lid, Active = false }, 
                new GroupMemberProfile { Role = GroupMemberRole.Beheerder }
              }.AsEnumerable();

      #endregion

      #region Constructors and Destructors

      /// <summary>
      /// Prevents a default instance of the <see cref="GroupMemberProfile"/> class from being created.
      /// </summary>
      private GroupMemberProfile() {
      }

      #endregion

      #region Public Properties

      /// <summary>
      /// Gets the active.
      /// </summary>
      public bool? Active { get; private set; }

      /// <summary>
      /// Gets the profile name.
      /// </summary>
      public string Name {
        get {
          return string.Concat(
            "Groeps", 
            this.Role.ToString().ToLowerInvariant(), 
            this.Active == true ? " (actief)" : this.Active == false ? " (passief)" : null);
        }
      }

      /// <summary>
      /// Gets the role.
      /// </summary>
      public GroupMemberRole Role { get; private set; }

      #endregion

      #region Public Methods and Operators

      /// <summary>
      /// Gets the instance for the specified values.
      /// </summary>
      /// <param name="role">
      /// The group member role.
      /// </param>
      /// <param name="active">
      /// The active.
      /// </param>
      /// <returns>
      /// The <see cref="GroupMemberProfile"/>.
      /// </returns>
      public static GroupMemberProfile Get(GroupMemberRole role, bool? active = null) {
        return All.Single(p => p.Role == role && p.Active == active);
      }

      /// <summary>
      /// Gets the instance by name.
      /// </summary>
      /// <param name="name">
      /// The profile name.
      /// </param>
      /// <returns>
      /// The <see cref="GroupMemberProfile"/>.
      /// </returns>
      public static GroupMemberProfile Get(string name) {
        return All.Single(p => p.Name == name);
      }

      #endregion
    }
  }
}