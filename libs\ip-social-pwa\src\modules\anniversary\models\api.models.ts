import { IPerson } from '@ip/social-core';

export namespace AnniversaryApi {
  export interface IAnniversaryResponse {
    endDay?: {
      day: number;
      month: number;
      year: number;
      dayOfYear: number;
    };
    id: string;
    startDay?: {
      day: number;
      month: number;
      year: number;
      dayOfYear: number;
    };
    type?: string;
    userIds?: string[];
    users?: IPerson[];
  }
}
