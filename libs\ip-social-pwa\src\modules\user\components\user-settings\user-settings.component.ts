import { Component } from '@angular/core';

import { DeviceService, DeviceStatus, UserAgentService } from '@ip/social-core';

@Component({
  selector: 'ips-user-settings',
  templateUrl: './user-settings.component.html',
})
export class UserSettingsComponent {
  public deviceStatus = DeviceStatus;

  constructor(
    public device: DeviceService,
    public userAgentService: UserAgentService,
  ) { }

  reload() {
    if ('caches' in window) {
      caches.keys().then((names) => {
        names.forEach(async (name) => {
          await caches.delete(name);
        });
      });
    }

    window.location.reload();
  }
}
