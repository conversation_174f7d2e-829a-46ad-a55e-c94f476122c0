import { Directive, ElementRef, Input, Renderer2 } from '@angular/core';

import { map } from 'rxjs/operators';

import { ResourceTokenService } from '../../authentication/base/resource-token.service';

@Directive({
  // eslint-disable-next-line @angular-eslint/directive-selector
  selector: 'img[ips-image-src]',
})
export class SecureImageSrcDirective {
  // eslint-disable-next-line @angular-eslint/no-input-rename
  @Input('ips-image-src')
  set path(path: string) {
    this.resourceTokenService.token$
      .pipe(map(token => {
        const url = new URL(path);
        url.searchParams.set('access-token', token);
        return url.toString();
      }))
      .subscribe(src => {
        this.renderer.setAttribute(this.element.nativeElement, 'src', src);
      });
  }

  constructor(
    private resourceTokenService: ResourceTokenService,
    private element: ElementRef,
    private renderer: Renderer2,
  ) { }
}
