<ng-container *ngIf="editMode === false">
  <ng-container *ngTemplateOutlet="template"></ng-container>
</ng-container>

<ng-container *ngIf="editMode === true" >
  <ion-item lines="none">
    <ng-container *ngTemplateOutlet="template"></ng-container>
  </ion-item>
  <ion-item lines="none">
    <div class="ips-synchronize-blog">
      <ng-container *ngIf="synchronizing">
        <ion-text>{{ 'blog.synchronizing' | transloco }}</ion-text>
        <ion-spinner></ion-spinner>
      </ng-container>
      <ion-text *ngIf="!synchronizing">{{ 'blog.synchronized' | transloco }}</ion-text>
    </div>
  </ion-item>
</ng-container>

<ng-template #template>
  <div class="ips-published-wrapper">
    <div class="ips-published-date">
      <div>{{ 'blog.publishedOn' | transloco }}</div>
      <div class="ips-date">{{ blog.published ? (blog.publishDate | timeago) : (status | transloco) }}</div>
    </div>
    <div *ngIf="canEdit === true && blog.workInProgress" class="ips-blog-wip">
      <span>{{ 'blog.unpublishedChanges' | transloco }}</span>
      <ion-icon name="alert-circle-outline"></ion-icon>
    </div>
  </div>
</ng-template>
