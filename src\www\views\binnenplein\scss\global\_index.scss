$pzh-product-card-follow-icon-size: 30px;
$pzh-product-card-follow-icon-box-shadow: 0 0 5px 2px rgba(0, 0, 0, .15);

.ipx-groepen,
.ipx-loket {
  .type-resultsortering .rij.pulldown .label,
  .type-resultcount {
    font-weight: 500;
  }

  .type-formulier {
    > .grid-element > .grid-edge > .grid-inside {
      padding-bottom: 0 !important;
      padding-top: 0 !important;
    }

    form.formulier {
      fieldset {
        position: relative;

        .label {
          font-weight: 500;
        }

        .invoer > input {
          padding-left: 3rem;
        }

        .knoppen {
          bottom: 0;
          padding: 0;
          position: absolute;
          z-index: 1;

          > .zoeken {
            @include icon($bl-icon-search, $bl-icon-type: light);

            background-color: transparent;
            color: $elementkleur;
            padding: .75rem;

            > span {
              @include visuallyhidden;
            }
          }
        }
      }
    }
  }
}

.ipx-loket {
  .z-results {
    > .type-galerij {
      > .grid-element > .grid-edge {
        @include small-up {
          padding: $pzh-inner-padding;
        }

        @include small {
          padding: $pzh-inner-padding #{$pzh-inner-padding + $pzh-outer-padding};
        }
      }
    }

    .product-card-container {
      .product-card-height {
        display: flex;
        min-height: $pzh-product-card-image-size;
        position: relative;
      }

      .card-wrapper {
        display: inline-block;
        flex: 1;
        overflow: hidden;
        padding-left: 0;
        text-overflow: ellipsis;
        white-space: nowrap;

        .card-title,
        .product-card-image {
          vertical-align: middle;
        }

        .card-title {
          font-size: 1.25rem;
          font-weight: 500;
        }

        .product-card-image {
          margin-right: 1rem;
        }

        &.linking {
          + .product-card-follow-container {
            margin-right: 1rem;
            margin-top: 1rem;
          }
        }
      }

      .product-card-follow-container {
        .regel-nu {
          margin: 0;

          a {
            @include icon($fa-var-file-alt);

            background-color: $pzh-white;
            border-radius: 50%;
            box-shadow: $pzh-product-card-follow-icon-box-shadow;
            display: inline-block;
            height: $pzh-product-card-follow-icon-size;
            position: relative;
            text-indent: -9999px;
            width: $pzh-product-card-follow-icon-size;

            &::before {
              display: inline-block;
              font-size: 1rem;
              left: 50%;
              position: absolute;
              text-indent: 0;
              top: 50%;
              transform: translate(-50%, -50%);
            }

            &:hover,
            &:active,
            &:focus {
              background-color: $pzh-blue;

              &::before {
                color: $pzh-white;
              }
            }
          }
        }
      }
    }
  }
}
