(() => {
  'use strict';

  window.ProcessPageOptions.external = '.externLink';

  if (window.IproxLightbox !== undefined) {
    window.IproxLightbox.useIcon('add-article', 'add');
    window.IproxLightbox.useIcon('add-faq', 'add');
    window.IproxLightbox.useIcon('add-event', 'add');
    window.IproxLightbox.useIcon('edit-article', 'edit');
    window.IproxLightbox.useIcon('edit-faq', 'edit');
    window.IproxLightbox.useIcon('add-album', 'add');
    window.IproxLightbox.useIcon('edit-photo', 'edit');
    window.IproxLightbox.useIcon('edit-content', 'edit');
    window.IproxLightbox.useIcon('edit-event', 'edit');
  }

  $(window).smartresize(function() {
    const hasFullscreenIframes = $('.mediawidget iframe').toArray().some(iframe => {
      const $iframe = $(iframe);

      return $iframe.width() === $(window).width() && $iframe.height() === $(window).height();
    });

    if (hasFullscreenIframes) {
      return;
    }

    $(document).redoMediaWidgets();
  });
})();
