window.social = {
  "serviceWorker" : {
    "apiUrl" : "https://intra-demo-social.iproxtest.nl/Social/api/public"
  },
  "core": {
    "apiUrl": "https://intra-demo-social.iproxtest.nl/Social/api",
    "baseUrl": "http://localhost:4210",
    "authentication": {
      "azure": {
        "authorityId": "ee21013a-99e5-4c19-a55a-72e16cc0e4fc",
        "clientId": "bea03e2e-63ba-48f6-a706-245b5c864835"
      }
    }
  },
  "blogs": {
    "enableAuthors": false
  }
}
