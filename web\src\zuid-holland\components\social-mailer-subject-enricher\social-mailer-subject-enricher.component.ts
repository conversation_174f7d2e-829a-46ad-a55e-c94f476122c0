import { Component, Inject, OnInit } from '@angular/core';
import { RefTextPipe } from '../../pipes/ref-text/ref-text.pipe';

@Component({
  selector: 'pzh-social-mailer-subject-enricher',
  template: '',
  styles: [`
    :host {
      display: none;
    }
  `],
})
export class SocialMailerSubjectEnricherComponent implements OnInit {
  constructor(@Inject('LegacyGroupService') private groupService: any, private refTextPipe: RefTextPipe) { }

  ngOnInit() {
    const input = document.querySelector<HTMLInputElement>('input[name="Formulier.Subject"]');
    const groupId = new URLSearchParams(window.location.search).get('groupId');

    if (input && groupId) {
      this.groupService.getGroup(groupId).$promise
        .then((group: any) => {
          input.value = this.refTextPipe.getText('Groepsmail vanuit de groep [groepnaam]', { groepnaam: group.label });
        });
    }
    else {
      console.warn('No groupId or input found. Unable to enrich subject with groupname.')
    }
  }
}
