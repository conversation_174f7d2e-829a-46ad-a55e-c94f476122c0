﻿namespace IntranetProvincieZuidHolland.Modules.Handlers {
  using System.Collections.Generic;
  using InfoProjects.Dxe.Linq;
  using InfoProjects.Dxe.Prop;
  using InfoProjects.Iprox.Security.Handler;
  using InfoProjects.Iprox.Security.Lightbox;

  /// <summary>
  /// Front-end editing fotoalbum
  /// </summary>
  public static class FrontendEditFoto {
    /// <summary>
    /// Registers plug
    /// </summary>
    public static void Register() {
      var pagClsIdtPos = LightboxHandler.KeyProperties.IndexOf("PagClsIdt");
      LightboxHandler.KeyProperties.Insert(pagClsIdtPos, "EdtFotClsIdt");
      LightboxHandler.RegisterMappingDelegate("EdtFotClsIdt", "PagClsIdt", GetEdtFotClsIdt2PagClsIdtMapping);
      LightboxHandler.RegisterContextDelegate("EdtFotClsIdt", GetPagClsIdtContext);
    }

    /// <summary>
    /// Gets mapping from EdtFotClsIdt to PagClsIdt
    /// </summary>
    /// <param name="lightbox">Lightbox handler</param>
    /// <returns>Mapping from EdtFotClsIdt to PagClsIdt</returns>
    private static IDictionary<int, int> GetEdtFotClsIdt2PagClsIdtMapping(ILightbox lightbox) {
      return lightbox.GetMapping("PagClsIdt", lightbox.GetValues("EdtFotClsIdt"), "PagClsTab");
    }

    /// <summary>
    /// Gets cluster context
    /// </summary>
    /// <param name="lightbox">Lightbox handler</param>
    /// <param name="clusterId">Cluster identity</param>
    /// <returns>Cluster context</returns>
    private static PropCollection GetPagClsIdtContext(ILightbox lightbox, int clusterId) {
      int itemId;
      Item item;
      if (!lightbox.Mappings["PagClsIdt", "ItmIdt"].TryGetValue(clusterId, out itemId)
          || !lightbox.Mappings.ItemMapping.TryGetValue(itemId, out item) || item.Sts != 4) {
        return null;
      }

      var context = new PropCollection();
      context["ItmIdt"] = itemId.ToIproxString();
      context["Sts"] = item.Sts.ToIproxString();
      context["CorDtm"] = item.CorDtm.ToIproxString();
      context["CorTyd"] = item.CorTyd.ToIproxString();
      context["LstPubDtm"] = item.LstPubDtm.ToIproxString();
      context["LstPubTyd"] = item.LstPubTyd.ToIproxString();
      context["ItmTyp"] = item.ItmTyp.ToIproxString();
      context["PagTypIdt"] = item.PagTypIdt.ToIproxString();
      context["SitIdt"] = item.SitIdt.ToIproxString();
      context["PagClsIdt"] = clusterId.ToIproxString();
      return context;
    }
  }
}
