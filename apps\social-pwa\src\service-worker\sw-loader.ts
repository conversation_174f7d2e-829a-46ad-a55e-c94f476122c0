import { Workbox } from 'workbox-window';

export function loadServiceWorker(serviceWorkerUrl: string | undefined): void {
  console.log('[core] Service worker url: ', serviceWorkerUrl);

  if (serviceWorkerUrl && 'serviceWorker' in navigator) {
    console.log('[core] Device supports service worker');

    const wb = new Workbox(serviceWorkerUrl);

    console.log('[core] Workbox created');

    wb.addEventListener('activated', (event) => {
      if (!event.isUpdate) {
        console.log('[core] Service worker activated for the first time!');
      }
      else {
        console.log('[core] Service worker activated!');
      }
    });

    wb.addEventListener('waiting', (event) => {
      console.log(`[core] A new service worker has installed, but it can't activate` +
        `until all tabs running the current version have fully unloaded.`);
    });

    wb.addEventListener('installed', (event) => {
      if (!event.isUpdate) {
        console.log('[core] Service worker installed for the first time!');
      }
      else {
        console.log('[core] Service worker installed');
      }
    });

    wb.register();
  }
}
