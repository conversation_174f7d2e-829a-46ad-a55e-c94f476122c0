import { ComponentFactoryResolver, Injectable } from '@angular/core';

import { TimelineContentFactory } from '../content-factory';
import { TimelineBlogComponent } from './blog.component';

@Injectable()
export class BlogFactory extends TimelineContentFactory {
  component = TimelineBlogComponent;

  type = 'blog';

  constructor(componentFactoryResolver: ComponentFactoryResolver) {
    super(componentFactoryResolver);
  }
}
