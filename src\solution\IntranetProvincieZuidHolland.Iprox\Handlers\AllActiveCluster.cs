﻿namespace IntranetProvincieZuidHolland.Iprox.Handlers {
  using System;
  using System.Collections.Generic;
  using System.Linq;
  using System.Xml;
  using InfoProjects.Dxe.Linq;
  using InfoProjects.Dxe.Xdl;
  using XdlTransform = InfoProjects.Dxe.Xdl.XdlBuilder.XdlTransform;

  /// <summary>
  /// Find active cluster in page (in this scenario: all of them)
  /// </summary>
  public static class AllActiveClusterTransform {
    /// <summary>
    /// Gets XDL handler delegate.
    /// </summary>
    public static XdlTransform XdlTransform {
      get {
        return AllActiveClusterTransform.Transform;
      }
    }

    /// <summary>Finds active cluster in page</summary>
    /// <param name="node">XDL output node</param>
    /// <param name="context">XDL context</param>
    /// <returns>Node with active cluster marked</returns>
    private static XmlElement Transform(XmlElement node, XdlContext context) {
      var pagClsIdt = new HashSet<string>();
      foreach (var root in AllActiveClusterTransform.GetRootClusters(node)) {
        foreach (var id in from cluster in AllActiveClusterTransform.GetClusters(root)
                            select cluster.GetAttribute("PagClsIdt")) {
          pagClsIdt.Add(id);
        }
      }

      context.SetProp("ActPagClsIdt", pagClsIdt.ToCsvString());
      return node;
    }

    /// <summary>
    /// Gets root clusters under element
    /// </summary>
    /// <param name="root">Root element</param>
    /// <returns>Sequence of root clusters</returns>
    private static IEnumerable<XmlElement> GetRootClusters(XmlElement root) {
      foreach (XmlElement cluster in root.SelectNodes("cluster")) {
        if (cluster.GetAttribute("Skp") == "1") {
          foreach (var result in GetRootClusters(cluster)) {
            yield return result;
          }
        }
        else {
          yield return cluster;
        }
      }
    }

    /// <summary>
    /// Gets all clusters from an element
    /// </summary>
    /// <param name="root">Root element</param>
    /// <returns>Sequence of clusters</returns>
    private static IEnumerable<XmlElement> GetClusters(XmlElement root) {
      var self = Enumerable.Repeat(root, 1);
      var descendants = from cluster in root.SelectNodes("cluster").Cast<XmlElement>()
                        from child in AllActiveClusterTransform.GetClusters(cluster)
                        select child;
      return self.Concat(descendants);
    }
  }
}
