// <auto-generated>IPROX model generator</auto-generated>
[assembly: InfoProjects.Iprox.Model.IproxModel(InfoProjects.Iprox.Model.ModelLevel.LocallyGenerated)]

namespace IntranetProvincieZuidHolland.Iprox.Model {
  using global::InfoProjects.Iprox.Model;
  using global::InfoProjects.Iprox.Model.Clusters;
  using global::InfoProjects.Iprox.Model.Fields;
  
  /// <summary>Meta (class)</summary>
  [IproxPagetype(Alias = "meta", Prototype = true)]
  public partial class Meta : IproxCluster {
    /// <summary>Datum tonen (singular)</summary>
    [IproxCluster(Alias = "Meta")]
    [IproxField(Alias = "Datum tonen", SequenceId = 1)]
    private readonly SelectionField fieldDatumTonen = new SelectionField();
    
    /// <summary>Samenvatting (singular)</summary>
    [IproxCluster(Alias = "Meta")]
    [IproxField(Alias = "Samenvatting", SequenceId = 1)]
    private readonly HtmlField fieldSamenvatting = new HtmlField();
    
    /// <summary>Auteur (singular)</summary>
    [IproxCluster(Alias = "Meta")]
    [IproxField(Alias = "Auteur", SequenceId = 1)]
    private readonly PlainField fieldAuteur = new PlainField();
    
    /// <summary>Informatietype (singular)</summary>
    [IproxCluster(Alias = "Meta")]
    [IproxField(Alias = "Informatietype", SequenceId = 1)]
    private readonly SelectionField fieldInformatietype = new SelectionField();
    
    /// <summary>Artikeltype (singular)</summary>
    [IproxCluster(Alias = "Meta")]
    [IproxField(Alias = "Artikeltype", SequenceId = 1)]
    private readonly PicklistField fieldArtikeltype = new PicklistField();
    
    /// <summary>Ordeningsdatum (singular)</summary>
    [IproxCluster(Alias = "Meta")]
    [IproxField(Alias = "Ordeningsdatum", SequenceId = 1)]
    private readonly DateField fieldOrdeningsdatum = new DateField();
    
    /// <summary>Afbeelding voor index (singular)</summary>
    [IproxCluster(Alias = "Meta")]
    [IproxField(Alias = "Afbeelding voor index", SequenceId = 1)]
    private readonly ImageField fieldAfbeeldingVoorIndex = new ImageField();
    
    /// <summary>Ordeningstijd (singular)</summary>
    [IproxCluster(Alias = "Meta")]
    [IproxField(Alias = "Ordeningstijd", SequenceId = 1)]
    private readonly TimeField fieldOrdeningstijd = new TimeField();
    
    /// <summary>Thema (singular)</summary>
    [IproxCluster(Alias = "Meta")]
    [IproxField(Alias = "Thema", SequenceId = 1)]
    private readonly KeywordsField fieldThema = new KeywordsField();
    
    /// <summary>Type (singular)</summary>
    [IproxCluster(Alias = "Meta")]
    [IproxField(Alias = "Type", SequenceId = 1)]
    private readonly KeywordsField fieldType = new KeywordsField();
    
    /// <summary>Trefwoord (singular)</summary>
    [IproxCluster(Alias = "Meta")]
    [IproxField(Alias = "Trefwoord", SequenceId = 1)]
    private readonly KeywordsField fieldTrefwoord = new KeywordsField();
    
    /// <summary>Datum laatste sociale wijziging (singular)</summary>
    [IproxCluster(Alias = "Meta")]
    [IproxField(Alias = "Datum laatste sociale wijziging", SequenceId = 1)]
    private readonly DateTimeField fieldDatumLaatsteSocialeWijziging = new DateTimeField();
    
    /// <summary>Gets or sets Datum tonen</summary>
    [IproxProperty(SequenceId = 0)]
    public SelectionField DatumTonen {
      get {
        return this.fieldDatumTonen;
      }
      
      set {
        this.fieldDatumTonen.Value = (value ?? new SelectionField()).Value;
      }
    }
    
    /// <summary>Gets or sets Samenvatting</summary>
    [IproxProperty(SequenceId = 1)]
    public HtmlField Samenvatting {
      get {
        return this.fieldSamenvatting;
      }
      
      set {
        this.fieldSamenvatting.Value = (value ?? new HtmlField()).Value;
      }
    }
    
    /// <summary>Gets or sets Auteur</summary>
    [IproxProperty(SequenceId = 2)]
    public PlainField Auteur {
      get {
        return this.fieldAuteur;
      }
      
      set {
        this.fieldAuteur.Value = (value ?? new PlainField()).Value;
      }
    }
    
    /// <summary>Gets or sets Informatietype</summary>
    /// <remarks>dcterms.type</remarks>
    [IproxProperty(SequenceId = 3)]
    public SelectionField Informatietype {
      get {
        return this.fieldInformatietype;
      }
      
      set {
        this.fieldInformatietype.Value = (value ?? new SelectionField()).Value;
      }
    }
    
    /// <summary>Gets or sets Artikeltype</summary>
    /// <remarks>Voor selectiefilter</remarks>
    [IproxProperty(SequenceId = 4)]
    public PicklistField Artikeltype {
      get {
        return this.fieldArtikeltype;
      }
      
      set {
        this.fieldArtikeltype.Value = (value ?? new PicklistField()).Value;
      }
    }
    
    /// <summary>Gets or sets Ordeningsdatum</summary>
    /// <remarks>Wordt gebruikt voor de ordening in indexen</remarks>
    [IproxProperty(SequenceId = 5)]
    public DateField Ordeningsdatum {
      get {
        return this.fieldOrdeningsdatum;
      }
      
      set {
        this.fieldOrdeningsdatum.Value = (value ?? new DateField()).Value;
      }
    }
    
    /// <summary>Gets or sets Afbeelding voor index</summary>
    /// <remarks>maat: 960*576</remarks>
    [IproxProperty(SequenceId = 6)]
    public ImageField AfbeeldingVoorIndex {
      get {
        return this.fieldAfbeeldingVoorIndex;
      }
      
      set {
        this.fieldAfbeeldingVoorIndex.Value = (value ?? new ImageField()).Value;
      }
    }
    
    /// <summary>Gets or sets Ordeningstijd</summary>
    /// <remarks>Wordt gebruikt voor de ordening in indexen</remarks>
    [IproxProperty(SequenceId = 7)]
    public TimeField Ordeningstijd {
      get {
        return this.fieldOrdeningstijd;
      }
      
      set {
        this.fieldOrdeningstijd.Value = (value ?? new TimeField()).Value;
      }
    }
    
    /// <summary>Gets or sets Thema</summary>
    [IproxProperty(SequenceId = 8)]
    public KeywordsField Thema {
      get {
        return this.fieldThema;
      }
      
      set {
        this.fieldThema.Value = (value ?? new KeywordsField()).Value;
      }
    }
    
    /// <summary>Gets or sets Type</summary>
    /// <remarks>Eén keuze mogelijk</remarks>
    [IproxProperty(SequenceId = 9)]
    public KeywordsField Type {
      get {
        return this.fieldType;
      }
      
      set {
        this.fieldType.Value = (value ?? new KeywordsField()).Value;
      }
    }
    
    /// <summary>Gets or sets Trefwoord</summary>
    [IproxProperty(SequenceId = 10)]
    public KeywordsField Trefwoord {
      get {
        return this.fieldTrefwoord;
      }
      
      set {
        this.fieldTrefwoord.Value = (value ?? new KeywordsField()).Value;
      }
    }
    
    /// <summary>Gets or sets Datum laatste sociale wijziging</summary>
    [IproxProperty(SequenceId = 11)]
    public DateTimeField DatumLaatsteSocialeWijziging {
      get {
        return this.fieldDatumLaatsteSocialeWijziging;
      }
      
      set {
        this.fieldDatumLaatsteSocialeWijziging.Value = (value ?? new DateTimeField()).Value;
      }
    }
  }
  
  /// <summary>Verwijzingen (interface)</summary>
  /// <see cref="InterneVerwijzingCluster" />
  /// <see cref="ExterneVerwijzingCluster" />
  /// <see cref="DownloadCluster" />
  [IproxPagetype(Alias = "verwijzingen", Prototype = true)]
  [IproxCluster(Alias = "Verwijzing", SequenceId = 1)]
  public partial interface IVerwijzingen : IIproxCluster {
  }
  
  /// <summary>Verwijzingen (static class)</summary>
  [IproxPagetype(Alias = "verwijzingen", Prototype = true)]
  [IproxCluster(Alias = "Verwijzing", SequenceId = 1)]
  public static class VerwijzingenClusters {
    /// <summary>Interne verwijzing (class)</summary>
    [IproxCluster(Alias = "Interne verwijzing")]
    public partial class InterneVerwijzingCluster : IproxCluster {
      /// <summary>Link (singular)</summary>
      [IproxField(Alias = "Link")]
      private readonly LinkField fieldLink = new LinkField();
      
      /// <summary>Openen in nieuw venster (singular)</summary>
      [IproxField(Alias = "Openen in nieuw venster")]
      private readonly BooleanField fieldOpenenInNieuwVenster = new BooleanField();
      
      /// <summary>Gets or sets Link</summary>
      [IproxProperty(SequenceId = 0)]
      public LinkField Link {
        get {
          return this.fieldLink;
        }
        
        set {
          this.fieldLink.Value = (value ?? new LinkField()).Value;
        }
      }
      
      /// <summary>Gets or sets Openen in nieuw venster</summary>
      [IproxProperty(SequenceId = 1)]
      public BooleanField OpenenInNieuwVenster {
        get {
          return this.fieldOpenenInNieuwVenster;
        }
        
        set {
          this.fieldOpenenInNieuwVenster.Value = (value ?? new BooleanField()).Value;
        }
      }
    }
    
    /// <content>InterneVerwijzingCluster can be used for IVerwijzingen</content>
    public partial class InterneVerwijzingCluster : IVerwijzingen {
    }
    
    /// <summary>Externe verwijzing (class)</summary>
    [IproxCluster(Alias = "Externe verwijzing")]
    public partial class ExterneVerwijzingCluster : IproxCluster {
      /// <summary>Link (singular)</summary>
      [IproxField(Alias = "Link")]
      private readonly AddressField fieldLink = new AddressField();
      
      /// <summary>Openen in nieuw venster (singular)</summary>
      [IproxField(Alias = "Openen in nieuw venster")]
      private readonly BooleanField fieldOpenenInNieuwVenster = new BooleanField();
      
      /// <summary>Gets or sets Link</summary>
      [IproxProperty(SequenceId = 0)]
      public AddressField Link {
        get {
          return this.fieldLink;
        }
        
        set {
          this.fieldLink.Value = (value ?? new AddressField()).Value;
        }
      }
      
      /// <summary>Gets or sets Openen in nieuw venster</summary>
      [IproxProperty(SequenceId = 1)]
      public BooleanField OpenenInNieuwVenster {
        get {
          return this.fieldOpenenInNieuwVenster;
        }
        
        set {
          this.fieldOpenenInNieuwVenster.Value = (value ?? new BooleanField()).Value;
        }
      }
    }
    
    /// <content>ExterneVerwijzingCluster can be used for IVerwijzingen</content>
    public partial class ExterneVerwijzingCluster : IVerwijzingen {
    }
    
    /// <summary>Download (class)</summary>
    [IproxCluster(Alias = "Download")]
    public partial class DownloadCluster : IproxCluster {
      /// <summary>Bestand (singular)</summary>
      [IproxField(Alias = "Bestand")]
      private readonly DocumentField fieldBestand = new DocumentField();
      
      /// <summary>Gets or sets Bestand</summary>
      [IproxProperty(SequenceId = 0)]
      public DocumentField Bestand {
        get {
          return this.fieldBestand;
        }
        
        set {
          this.fieldBestand.Value = (value ?? new DocumentField()).Value;
        }
      }
    }
    
    /// <content>DownloadCluster can be used for IVerwijzingen</content>
    public partial class DownloadCluster : IVerwijzingen {
    }
  }
  
  /// <summary>Verwijzing (interface)</summary>
  /// <see cref="InterneVerwijzingCluster" />
  /// <see cref="ExterneVerwijzingCluster" />
  /// <see cref="DownloadCluster" />
  [IproxPagetype(Alias = "verwijzing", Prototype = true)]
  [IproxCluster(Alias = "Verwijzing", SequenceId = 1)]
  public partial interface IVerwijzing : IIproxCluster {
  }
  
  /// <summary>Verwijzing (static class)</summary>
  [IproxPagetype(Alias = "verwijzing", Prototype = true)]
  [IproxCluster(Alias = "Verwijzing", SequenceId = 1)]
  public static class VerwijzingClusters {
    /// <summary>Interne verwijzing (class)</summary>
    [IproxCluster(Alias = "Interne verwijzing")]
    public partial class InterneVerwijzingCluster : IproxCluster {
      /// <summary>Link (singular)</summary>
      [IproxField(Alias = "Link")]
      private readonly LinkField fieldLink = new LinkField();
      
      /// <summary>Openen in nieuw venster (singular)</summary>
      [IproxField(Alias = "Openen in nieuw venster")]
      private readonly BooleanField fieldOpenenInNieuwVenster = new BooleanField();
      
      /// <summary>Gets or sets Link</summary>
      [IproxProperty(SequenceId = 0)]
      public LinkField Link {
        get {
          return this.fieldLink;
        }
        
        set {
          this.fieldLink.Value = (value ?? new LinkField()).Value;
        }
      }
      
      /// <summary>Gets or sets Openen in nieuw venster</summary>
      [IproxProperty(SequenceId = 1)]
      public BooleanField OpenenInNieuwVenster {
        get {
          return this.fieldOpenenInNieuwVenster;
        }
        
        set {
          this.fieldOpenenInNieuwVenster.Value = (value ?? new BooleanField()).Value;
        }
      }
    }
    
    /// <content>InterneVerwijzingCluster can be used for IVerwijzing</content>
    public partial class InterneVerwijzingCluster : IVerwijzing {
    }
    
    /// <summary>Externe verwijzing (class)</summary>
    [IproxCluster(Alias = "Externe verwijzing")]
    public partial class ExterneVerwijzingCluster : IproxCluster {
      /// <summary>Link (singular)</summary>
      [IproxField(Alias = "Link")]
      private readonly AddressField fieldLink = new AddressField();
      
      /// <summary>Openen in nieuw venster (singular)</summary>
      [IproxField(Alias = "Openen in nieuw venster")]
      private readonly BooleanField fieldOpenenInNieuwVenster = new BooleanField();
      
      /// <summary>Gets or sets Link</summary>
      [IproxProperty(SequenceId = 0)]
      public AddressField Link {
        get {
          return this.fieldLink;
        }
        
        set {
          this.fieldLink.Value = (value ?? new AddressField()).Value;
        }
      }
      
      /// <summary>Gets or sets Openen in nieuw venster</summary>
      [IproxProperty(SequenceId = 1)]
      public BooleanField OpenenInNieuwVenster {
        get {
          return this.fieldOpenenInNieuwVenster;
        }
        
        set {
          this.fieldOpenenInNieuwVenster.Value = (value ?? new BooleanField()).Value;
        }
      }
    }
    
    /// <content>ExterneVerwijzingCluster can be used for IVerwijzing</content>
    public partial class ExterneVerwijzingCluster : IVerwijzing {
    }
    
    /// <summary>Download (class)</summary>
    [IproxCluster(Alias = "Download")]
    public partial class DownloadCluster : IproxCluster {
      /// <summary>Bestand (singular)</summary>
      [IproxField(Alias = "Bestand")]
      private readonly DocumentField fieldBestand = new DocumentField();
      
      /// <summary>Gets or sets Bestand</summary>
      [IproxProperty(SequenceId = 0)]
      public DocumentField Bestand {
        get {
          return this.fieldBestand;
        }
        
        set {
          this.fieldBestand.Value = (value ?? new DocumentField()).Value;
        }
      }
    }
    
    /// <content>DownloadCluster can be used for IVerwijzing</content>
    public partial class DownloadCluster : IVerwijzing {
    }
  }
  
  /// <summary>Hotspot (class)</summary>
  /// <remarks>Speciaal voor het plaatsen van hotspots</remarks>
  [IproxPagetype(Alias = "hotspot", Prototype = true)]
  [IproxCluster(Alias = "Popup", SequenceId = 1)]
  public partial class Hotspot : IproxCluster {
    /// <summary>Algemeen (singular)</summary>
    [IproxCluster(Alias = "Algemeen")]
    private readonly Hotspot.AlgemeenCluster fieldAlgemeen = new Hotspot.AlgemeenCluster();
    
    /// <summary>Verwijzing (collection)</summary>
    [IproxCluster(Alias = "Verwijzing")]
    private readonly IproxClustersCollection<Hotspot.IVerwijzing> fieldVerwijzing = new IproxClustersCollection<Hotspot.IVerwijzing>();
    
    /// <summary>Gets Algemeen</summary>
    [IproxProperty(SequenceId = 0)]
    public Hotspot.AlgemeenCluster Algemeen {
      get {
        return this.fieldAlgemeen;
      }
    }
    
    /// <summary>Gets collection of Verwijzing</summary>
    [IproxProperty(SequenceId = 1)]
    public IproxClustersCollection<Hotspot.IVerwijzing> Verwijzing {
      get {
        return this.fieldVerwijzing;
      }
    }
    
    /// <summary>Algemeen (class)</summary>
    [IproxCluster(Alias = "Algemeen")]
    public partial class AlgemeenCluster : IproxCluster {
      /// <summary>Titel (singular)</summary>
      [IproxField(Alias = "Titel")]
      private readonly PlainField fieldTitel = new PlainField();
      
      /// <summary>Tekst (singular)</summary>
      [IproxField(Alias = "Tekst")]
      private readonly HtmlField fieldTekst = new HtmlField();
      
      /// <summary>Gets or sets Titel</summary>
      [IproxProperty(SequenceId = 0)]
      public PlainField Titel {
        get {
          return this.fieldTitel;
        }
        
        set {
          this.fieldTitel.Value = (value ?? new PlainField()).Value;
        }
      }
      
      /// <summary>Gets or sets Tekst</summary>
      [IproxProperty(SequenceId = 1)]
      public HtmlField Tekst {
        get {
          return this.fieldTekst;
        }
        
        set {
          this.fieldTekst.Value = (value ?? new HtmlField()).Value;
        }
      }
    }
    
    /// <summary>Verwijzing (interface)</summary>
    /// <see cref="InternCluster" />
    /// <see cref="ExternCluster" />
    /// <see cref="DownloadCluster" />
    [IproxCluster(Alias = "Verwijzing")]
    public partial interface IVerwijzing : IIproxCluster {
    }
    
    /// <summary>Verwijzing (static class)</summary>
    [IproxCluster(Alias = "Verwijzing")]
    public static class VerwijzingClusters {
      /// <summary>Intern (class)</summary>
      [IproxCluster(Alias = "Intern")]
      public partial class InternCluster : IproxCluster {
        /// <summary>Link (singular)</summary>
        [IproxField(Alias = "Link")]
        private readonly LinkField fieldLink = new LinkField();
        
        /// <summary>Gets or sets Link</summary>
        [IproxProperty(SequenceId = 0)]
        public LinkField Link {
          get {
            return this.fieldLink;
          }
          
          set {
            this.fieldLink.Value = (value ?? new LinkField()).Value;
          }
        }
      }
      
      /// <content>InternCluster can be used for IVerwijzing</content>
      public partial class InternCluster : IVerwijzing {
      }
      
      /// <summary>Extern (class)</summary>
      [IproxCluster(Alias = "Extern")]
      public partial class ExternCluster : IproxCluster {
        /// <summary>Link (singular)</summary>
        [IproxField(Alias = "Link")]
        private readonly AddressField fieldLink = new AddressField();
        
        /// <summary>Gets or sets Link</summary>
        [IproxProperty(SequenceId = 0)]
        public AddressField Link {
          get {
            return this.fieldLink;
          }
          
          set {
            this.fieldLink.Value = (value ?? new AddressField()).Value;
          }
        }
      }
      
      /// <content>ExternCluster can be used for IVerwijzing</content>
      public partial class ExternCluster : IVerwijzing {
      }
      
      /// <summary>Download (class)</summary>
      [IproxCluster(Alias = "Download")]
      public partial class DownloadCluster : IproxCluster {
        /// <summary>Download (singular)</summary>
        [IproxField(Alias = "Download")]
        private readonly DocumentField fieldDownload = new DocumentField();
        
        /// <summary>Gets or sets Download</summary>
        [IproxProperty(SequenceId = 0)]
        public DocumentField Download {
          get {
            return this.fieldDownload;
          }
          
          set {
            this.fieldDownload.Value = (value ?? new DocumentField()).Value;
          }
        }
      }
      
      /// <content>DownloadCluster can be used for IVerwijzing</content>
      public partial class DownloadCluster : IVerwijzing {
      }
    }
  }
  
  /// <summary>Social - Posts (class)</summary>
  [IproxPagetype(Alias = "social-posts", Prototype = true)]
  public partial class SocialPosts : IproxCluster {
    /// <summary>title (singular)</summary>
    [IproxCluster(Alias = "Blok")]
    [IproxField(Alias = "title", SequenceId = 1)]
    private readonly PlainField fieldTitle = new PlainField();
    
    /// <summary>type (singular)</summary>
    [IproxCluster(Alias = "Blok")]
    [IproxField(Alias = "type", SequenceId = 1)]
    private readonly PlainField fieldType = new PlainField();
    
    /// <summary>itemsPerPage (singular)</summary>
    [IproxCluster(Alias = "Blok")]
    [IproxField(Alias = "itemsPerPage", SequenceId = 1)]
    private readonly PlainField fieldItemsPerPage = new PlainField();
    
    /// <summary>enableLike (singular)</summary>
    [IproxCluster(Alias = "Blok")]
    [IproxField(Alias = "enableLike", SequenceId = 1)]
    private readonly BooleanField fieldEnableLike = new BooleanField();
    
    /// <summary>enableTagging (singular)</summary>
    [IproxCluster(Alias = "Blok")]
    [IproxField(Alias = "enableTagging", SequenceId = 1)]
    private readonly BooleanField fieldEnableTagging = new BooleanField();
    
    /// <summary>enableComments (singular)</summary>
    [IproxCluster(Alias = "Blok")]
    [IproxField(Alias = "enableComments", SequenceId = 1)]
    private readonly BooleanField fieldEnableComments = new BooleanField();
    
    /// <summary>enableMoveQuestion (singular)</summary>
    [IproxCluster(Alias = "Blok")]
    [IproxField(Alias = "enableMoveQuestion", SequenceId = 1)]
    private readonly BooleanField fieldEnableMoveQuestion = new BooleanField();
    
    /// <summary>Blokrol (singular)</summary>
    [IproxCluster(Alias = "Blok")]
    [IproxField(Alias = "Blokrol", SequenceId = 1)]
    private readonly SelectionField fieldBlokrol = new SelectionField();
    
    /// <summary>allowCommentAsUser (singular)</summary>
    [IproxCluster(Alias = "Blok")]
    [IproxField(Alias = "allowCommentAsUser", SequenceId = 1)]
    private readonly BooleanField fieldAllowCommentAsUser = new BooleanField();
    
    /// <summary>Gets or sets title</summary>
    [IproxProperty(SequenceId = 0)]
    public PlainField Title {
      get {
        return this.fieldTitle;
      }
      
      set {
        this.fieldTitle.Value = (value ?? new PlainField()).Value;
      }
    }
    
    /// <summary>Gets or sets type</summary>
    [IproxProperty(SequenceId = 1)]
    public PlainField Type {
      get {
        return this.fieldType;
      }
      
      set {
        this.fieldType.Value = (value ?? new PlainField()).Value;
      }
    }
    
    /// <summary>Gets or sets itemsPerPage</summary>
    [IproxProperty(SequenceId = 2)]
    public PlainField ItemsPerPage {
      get {
        return this.fieldItemsPerPage;
      }
      
      set {
        this.fieldItemsPerPage.Value = (value ?? new PlainField()).Value;
      }
    }
    
    /// <summary>Gets or sets enableLike</summary>
    [IproxProperty(SequenceId = 3)]
    public BooleanField EnableLike {
      get {
        return this.fieldEnableLike;
      }
      
      set {
        this.fieldEnableLike.Value = (value ?? new BooleanField()).Value;
      }
    }
    
    /// <summary>Gets or sets enableTagging</summary>
    [IproxProperty(SequenceId = 4)]
    public BooleanField EnableTagging {
      get {
        return this.fieldEnableTagging;
      }
      
      set {
        this.fieldEnableTagging.Value = (value ?? new BooleanField()).Value;
      }
    }
    
    /// <summary>Gets or sets enableComments</summary>
    [IproxProperty(SequenceId = 5)]
    public BooleanField EnableComments {
      get {
        return this.fieldEnableComments;
      }
      
      set {
        this.fieldEnableComments.Value = (value ?? new BooleanField()).Value;
      }
    }
    
    /// <summary>Gets or sets enableMoveQuestion</summary>
    [IproxProperty(SequenceId = 6)]
    public BooleanField EnableMoveQuestion {
      get {
        return this.fieldEnableMoveQuestion;
      }
      
      set {
        this.fieldEnableMoveQuestion.Value = (value ?? new BooleanField()).Value;
      }
    }
    
    /// <summary>Gets or sets Blokrol</summary>
    [IproxProperty(SequenceId = 7)]
    public SelectionField Blokrol {
      get {
        return this.fieldBlokrol;
      }
      
      set {
        this.fieldBlokrol.Value = (value ?? new SelectionField()).Value;
      }
    }
    
    /// <summary>Gets or sets allowCommentAsUser</summary>
    [IproxProperty(SequenceId = 8)]
    public BooleanField AllowCommentAsUser {
      get {
        return this.fieldAllowCommentAsUser;
      }
      
      set {
        this.fieldAllowCommentAsUser.Value = (value ?? new BooleanField()).Value;
      }
    }
  }
  
  /// <summary>Zoeken (class)</summary>
  /// <remarks>grid-blok</remarks>
  [IproxPagetype(Alias = "zoeken", Prototype = true)]
  public partial class ZoekenPrototype : IproxCluster {
    /// <summary>Titel (singular)</summary>
    [IproxCluster(Alias = "Blok")]
    [IproxField(Alias = "Titel", SequenceId = 1)]
    private readonly PlainField fieldTitel = new PlainField();
    
    /// <summary>Gets or sets Titel</summary>
    [IproxProperty(SequenceId = 0)]
    public PlainField Titel {
      get {
        return this.fieldTitel;
      }
      
      set {
        this.fieldTitel.Value = (value ?? new PlainField()).Value;
      }
    }
  }
  
  /// <summary>Sitepad (class)</summary>
  /// <remarks>grid-blok</remarks>
  [IproxPagetype(Alias = "sitepad", Prototype = true)]
  public partial class Sitepad : IproxCluster {
    /// <summary>Titel (singular)</summary>
    [IproxCluster(Alias = "Blok")]
    [IproxField(Alias = "Titel", SequenceId = 1)]
    private readonly PlainField fieldTitel = new PlainField();
    
    /// <summary>Gets or sets Titel</summary>
    [IproxProperty(SequenceId = 0)]
    public PlainField Titel {
      get {
        return this.fieldTitel;
      }
      
      set {
        this.fieldTitel.Value = (value ?? new PlainField()).Value;
      }
    }
  }
  
  /// <summary>Fotoalbum (class)</summary>
  /// <remarks>grid-blok</remarks>
  [IproxPagetype(Alias = "fotoalbum", Prototype = true)]
  public partial class FotoalbumPrototype : IproxCluster {
    /// <summary>Titel (singular)</summary>
    [IproxCluster(Alias = "Blok")]
    [IproxField(Alias = "Titel", SequenceId = 1)]
    private readonly PlainField fieldTitel = new PlainField();
    
    /// <summary>Blokrol (singular)</summary>
    [IproxCluster(Alias = "Blok")]
    [IproxField(Alias = "Blokrol", SequenceId = 1)]
    private readonly SelectionField fieldBlokrol = new SelectionField();
    
    /// <summary>Fotoalbum (singular)</summary>
    [IproxCluster(Alias = "Blok")]
    [IproxField(Alias = "Fotoalbum", SequenceId = 1)]
    private readonly LinkField fieldFotoalbum = new LinkField();
    
    /// <summary>Presentatie (singular)</summary>
    [IproxCluster(Alias = "Blok")]
    [IproxField(Alias = "Presentatie", SequenceId = 1)]
    private readonly SelectionField fieldPresentatie = new SelectionField();
    
    /// <summary>Gets or sets Titel</summary>
    [IproxProperty(SequenceId = 0)]
    public PlainField Titel {
      get {
        return this.fieldTitel;
      }
      
      set {
        this.fieldTitel.Value = (value ?? new PlainField()).Value;
      }
    }
    
    /// <summary>Gets or sets Blokrol</summary>
    [IproxProperty(SequenceId = 1)]
    public SelectionField Blokrol {
      get {
        return this.fieldBlokrol;
      }
      
      set {
        this.fieldBlokrol.Value = (value ?? new SelectionField()).Value;
      }
    }
    
    /// <summary>Gets or sets Fotoalbum</summary>
    [IproxProperty(SequenceId = 2)]
    public LinkField Fotoalbum {
      get {
        return this.fieldFotoalbum;
      }
      
      set {
        this.fieldFotoalbum.Value = (value ?? new LinkField()).Value;
      }
    }
    
    /// <summary>Gets or sets Presentatie</summary>
    [IproxProperty(SequenceId = 3)]
    public SelectionField Presentatie {
      get {
        return this.fieldPresentatie;
      }
      
      set {
        this.fieldPresentatie.Value = (value ?? new SelectionField()).Value;
      }
    }
  }
  
  /// <summary>Trefwoord (class)</summary>
  /// <remarks>grid-blok</remarks>
  [IproxPagetype(Alias = "trefwoord", Prototype = true)]
  public partial class Trefwoord : IproxCluster {
    /// <summary>Algemeen (singular)</summary>
    [IproxCluster(Alias = "Blok")]
    [IproxCluster(Alias = "Algemeen", SequenceId = 1)]
    private readonly Trefwoord.AlgemeenCluster fieldAlgemeen = new Trefwoord.AlgemeenCluster();
    
    /// <summary>Link (collection)</summary>
    [IproxCluster(Alias = "Blok")]
    [IproxCluster(Alias = "Startpunt", SequenceId = 1)]
    [IproxField(Alias = "Link", SequenceId = 2)]
    private readonly IproxFieldsCollection<LinkField> fieldLink = new IproxFieldsCollection<LinkField>();
    
    /// <summary>Gets Algemeen</summary>
    [IproxProperty(SequenceId = 0)]
    public Trefwoord.AlgemeenCluster Algemeen {
      get {
        return this.fieldAlgemeen;
      }
    }
    
    /// <summary>Gets collection of Link</summary>
    [IproxProperty(SequenceId = 1)]
    public IproxFieldsCollection<LinkField> Link {
      get {
        return this.fieldLink;
      }
    }
    
    /// <summary>Algemeen (class)</summary>
    [IproxCluster(Alias = "Blok")]
    [IproxCluster(Alias = "Algemeen", SequenceId = 1)]
    public partial class AlgemeenCluster : IproxCluster {
      /// <summary>Titel (singular)</summary>
      [IproxField(Alias = "Titel")]
      private readonly PlainField fieldTitel = new PlainField();
      
      /// <summary>Tekst (singular)</summary>
      [IproxField(Alias = "Tekst")]
      private readonly HtmlField fieldTekst = new HtmlField();
      
      /// <summary>Blokrol (singular)</summary>
      [IproxField(Alias = "Blokrol")]
      private readonly SelectionField fieldBlokrol = new SelectionField();
      
      /// <summary>Trefwoord (singular)</summary>
      [IproxField(Alias = "Trefwoord")]
      private readonly KeywordsField fieldTrefwoord = new KeywordsField();
      
      /// <summary>Gets or sets Titel</summary>
      [IproxProperty(SequenceId = 0)]
      public PlainField Titel {
        get {
          return this.fieldTitel;
        }
        
        set {
          this.fieldTitel.Value = (value ?? new PlainField()).Value;
        }
      }
      
      /// <summary>Gets or sets Tekst</summary>
      [IproxProperty(SequenceId = 1)]
      public HtmlField Tekst {
        get {
          return this.fieldTekst;
        }
        
        set {
          this.fieldTekst.Value = (value ?? new HtmlField()).Value;
        }
      }
      
      /// <summary>Gets or sets Blokrol</summary>
      [IproxProperty(SequenceId = 2)]
      public SelectionField Blokrol {
        get {
          return this.fieldBlokrol;
        }
        
        set {
          this.fieldBlokrol.Value = (value ?? new SelectionField()).Value;
        }
      }
      
      /// <summary>Gets or sets Trefwoord</summary>
      [IproxProperty(SequenceId = 3)]
      public KeywordsField Trefwoord {
        get {
          return this.fieldTrefwoord;
        }
        
        set {
          this.fieldTrefwoord.Value = (value ?? new KeywordsField()).Value;
        }
      }
    }
  }
  
  /// <summary>Banner (class)</summary>
  /// <remarks>grid-blok</remarks>
  [IproxPagetype(Alias = "banner", Prototype = true)]
  public partial class Banner : IproxCluster {
    /// <summary>Afbeelding (singular)</summary>
    [IproxCluster(Alias = "Banner")]
    [IproxCluster(Alias = "Algemeen", SequenceId = 1)]
    [IproxField(Alias = "Afbeelding", SequenceId = 2)]
    private readonly ImageField fieldAfbeelding = new ImageField();
    
    /// <summary>Verwijzing (collection)</summary>
    [IproxCluster(Alias = "Banner")]
    [IproxCluster(Alias = "Verwijzing", Prototype = true, SequenceId = 1)]
    [IproxPagetype(Alias = "verwijzing", Prototype = true, SequenceId = 2)]
    [IproxCluster(Alias = "Verwijzing", SequenceId = 3)]
    private readonly IproxClustersCollection<IVerwijzing> fieldVerwijzing = new IproxClustersCollection<IVerwijzing>();
    
    /// <summary>Gets or sets Afbeelding</summary>
    [IproxProperty(SequenceId = 0)]
    public ImageField Afbeelding {
      get {
        return this.fieldAfbeelding;
      }
      
      set {
        this.fieldAfbeelding.Value = (value ?? new ImageField()).Value;
      }
    }
    
    /// <summary>Gets collection of Verwijzing</summary>
    [IproxProperty(SequenceId = 1)]
    public IproxClustersCollection<IVerwijzing> Verwijzing {
      get {
        return this.fieldVerwijzing;
      }
    }
  }
  
  /// <summary>Social - Vraag en Antwoord (class)</summary>
  [IproxPagetype(Alias = "social-questions", Prototype = true)]
  public partial class SocialQuestions : IproxCluster {
    /// <summary>title (singular)</summary>
    [IproxCluster(Alias = "Blok")]
    [IproxField(Alias = "title", SequenceId = 1)]
    private readonly PlainField fieldTitle = new PlainField();
    
    /// <summary>itemsPerPage (singular)</summary>
    [IproxCluster(Alias = "Blok")]
    [IproxField(Alias = "itemsPerPage", SequenceId = 1)]
    private readonly PlainField fieldItemsPerPage = new PlainField();
    
    /// <summary>listOrder (singular)</summary>
    [IproxCluster(Alias = "Blok")]
    [IproxField(Alias = "listOrder", SequenceId = 1)]
    private readonly SelectionField fieldListOrder = new SelectionField();
    
    /// <summary>enableLike (singular)</summary>
    [IproxCluster(Alias = "Blok")]
    [IproxField(Alias = "enableLike", SequenceId = 1)]
    private readonly BooleanField fieldEnableLike = new BooleanField();
    
    /// <summary>enableTagging (singular)</summary>
    [IproxCluster(Alias = "Blok")]
    [IproxField(Alias = "enableTagging", SequenceId = 1)]
    private readonly BooleanField fieldEnableTagging = new BooleanField();
    
    /// <summary>enableComments (singular)</summary>
    [IproxCluster(Alias = "Blok")]
    [IproxField(Alias = "enableComments", SequenceId = 1)]
    private readonly BooleanField fieldEnableComments = new BooleanField();
    
    /// <summary>enableAcceptedAnswer (singular)</summary>
    [IproxCluster(Alias = "Blok")]
    [IproxField(Alias = "enableAcceptedAnswer", SequenceId = 1)]
    private readonly BooleanField fieldEnableAcceptedAnswer = new BooleanField();
    
    /// <summary>inlineAcceptedAnswer (singular)</summary>
    [IproxCluster(Alias = "Blok")]
    [IproxField(Alias = "inlineAcceptedAnswer", SequenceId = 1)]
    private readonly BooleanField fieldInlineAcceptedAnswer = new BooleanField();
    
    /// <summary>Blokrol (singular)</summary>
    [IproxCluster(Alias = "Blok")]
    [IproxField(Alias = "Blokrol", SequenceId = 1)]
    private readonly SelectionField fieldBlokrol = new SelectionField();
    
    /// <summary>Gets or sets title</summary>
    [IproxProperty(SequenceId = 0)]
    public PlainField Title {
      get {
        return this.fieldTitle;
      }
      
      set {
        this.fieldTitle.Value = (value ?? new PlainField()).Value;
      }
    }
    
    /// <summary>Gets or sets itemsPerPage</summary>
    [IproxProperty(SequenceId = 1)]
    public PlainField ItemsPerPage {
      get {
        return this.fieldItemsPerPage;
      }
      
      set {
        this.fieldItemsPerPage.Value = (value ?? new PlainField()).Value;
      }
    }
    
    /// <summary>Gets or sets listOrder</summary>
    [IproxProperty(SequenceId = 2)]
    public SelectionField ListOrder {
      get {
        return this.fieldListOrder;
      }
      
      set {
        this.fieldListOrder.Value = (value ?? new SelectionField()).Value;
      }
    }
    
    /// <summary>Gets or sets enableLike</summary>
    [IproxProperty(SequenceId = 3)]
    public BooleanField EnableLike {
      get {
        return this.fieldEnableLike;
      }
      
      set {
        this.fieldEnableLike.Value = (value ?? new BooleanField()).Value;
      }
    }
    
    /// <summary>Gets or sets enableTagging</summary>
    [IproxProperty(SequenceId = 4)]
    public BooleanField EnableTagging {
      get {
        return this.fieldEnableTagging;
      }
      
      set {
        this.fieldEnableTagging.Value = (value ?? new BooleanField()).Value;
      }
    }
    
    /// <summary>Gets or sets enableComments</summary>
    [IproxProperty(SequenceId = 5)]
    public BooleanField EnableComments {
      get {
        return this.fieldEnableComments;
      }
      
      set {
        this.fieldEnableComments.Value = (value ?? new BooleanField()).Value;
      }
    }
    
    /// <summary>Gets or sets enableAcceptedAnswer</summary>
    [IproxProperty(SequenceId = 6)]
    public BooleanField EnableAcceptedAnswer {
      get {
        return this.fieldEnableAcceptedAnswer;
      }
      
      set {
        this.fieldEnableAcceptedAnswer.Value = (value ?? new BooleanField()).Value;
      }
    }
    
    /// <summary>Gets or sets inlineAcceptedAnswer</summary>
    [IproxProperty(SequenceId = 7)]
    public BooleanField InlineAcceptedAnswer {
      get {
        return this.fieldInlineAcceptedAnswer;
      }
      
      set {
        this.fieldInlineAcceptedAnswer.Value = (value ?? new BooleanField()).Value;
      }
    }
    
    /// <summary>Gets or sets Blokrol</summary>
    [IproxProperty(SequenceId = 8)]
    public SelectionField Blokrol {
      get {
        return this.fieldBlokrol;
      }
      
      set {
        this.fieldBlokrol.Value = (value ?? new SelectionField()).Value;
      }
    }
  }
  
  /// <summary>Primaire navigatie (class)</summary>
  /// <remarks>grid-blok</remarks>
  [IproxPagetype(Alias = "primaire-navigatie", Prototype = true)]
  public partial class PrimaireNavigatie : IproxCluster {
    /// <summary>Uitvalmenu (singular)</summary>
    [IproxCluster(Alias = "Blok")]
    [IproxField(Alias = "Uitvalmenu", SequenceId = 1)]
    private readonly SelectionField fieldUitvalmenu = new SelectionField();
    
    /// <summary>Starten met home (singular)</summary>
    [IproxCluster(Alias = "Blok")]
    [IproxField(Alias = "Starten met home", SequenceId = 1)]
    private readonly BooleanField fieldStartenMetHome = new BooleanField();
    
    /// <summary>Titel (singular)</summary>
    [IproxCluster(Alias = "Blok")]
    [IproxField(Alias = "Titel", SequenceId = 1)]
    private readonly PlainField fieldTitel = new PlainField();
    
    /// <summary>Gets or sets Uitvalmenu</summary>
    [IproxProperty(SequenceId = 0)]
    public SelectionField Uitvalmenu {
      get {
        return this.fieldUitvalmenu;
      }
      
      set {
        this.fieldUitvalmenu.Value = (value ?? new SelectionField()).Value;
      }
    }
    
    /// <summary>Gets or sets Starten met home</summary>
    [IproxProperty(SequenceId = 1)]
    public BooleanField StartenMetHome {
      get {
        return this.fieldStartenMetHome;
      }
      
      set {
        this.fieldStartenMetHome.Value = (value ?? new BooleanField()).Value;
      }
    }
    
    /// <summary>Gets or sets Titel</summary>
    [IproxProperty(SequenceId = 2)]
    public PlainField Titel {
      get {
        return this.fieldTitel;
      }
      
      set {
        this.fieldTitel.Value = (value ?? new PlainField()).Value;
      }
    }
  }
  
  /// <summary>Logo (class)</summary>
  /// <remarks>grid-blok</remarks>
  [IproxPagetype(Alias = "logo", Prototype = true)]
  public partial class Logo : IproxCluster {
    /// <summary>Afbeelding (singular)</summary>
    [IproxCluster(Alias = "Logo")]
    [IproxCluster(Alias = "Algemeen", SequenceId = 1)]
    [IproxField(Alias = "Afbeelding", SequenceId = 2)]
    private readonly ImageField fieldAfbeelding = new ImageField();
    
    /// <summary>Responsive (singular)</summary>
    [IproxCluster(Alias = "Logo")]
    [IproxCluster(Alias = "Algemeen", SequenceId = 1)]
    [IproxField(Alias = "Responsive", SequenceId = 2)]
    private readonly BooleanField fieldResponsive = new BooleanField();
    
    /// <summary>Afbeelding (svg) (singular)</summary>
    [IproxCluster(Alias = "Logo")]
    [IproxCluster(Alias = "Algemeen", SequenceId = 1)]
    [IproxField(Alias = "Afbeelding (svg)", SequenceId = 2)]
    private readonly DocumentField fieldAfbeeldingSvg = new DocumentField();
    
    /// <summary>Gets or sets Afbeelding</summary>
    [IproxProperty(SequenceId = 0)]
    public ImageField Afbeelding {
      get {
        return this.fieldAfbeelding;
      }
      
      set {
        this.fieldAfbeelding.Value = (value ?? new ImageField()).Value;
      }
    }
    
    /// <summary>Gets or sets Responsive</summary>
    [IproxProperty(SequenceId = 1)]
    public BooleanField Responsive {
      get {
        return this.fieldResponsive;
      }
      
      set {
        this.fieldResponsive.Value = (value ?? new BooleanField()).Value;
      }
    }
    
    /// <summary>Gets or sets Afbeelding (svg)</summary>
    [IproxProperty(SequenceId = 2)]
    public DocumentField AfbeeldingSvg {
      get {
        return this.fieldAfbeeldingSvg;
      }
      
      set {
        this.fieldAfbeeldingSvg.Value = (value ?? new DocumentField()).Value;
      }
    }
  }
  
  /// <summary>Foto (class)</summary>
  /// <remarks>grid-blok</remarks>
  [IproxPagetype(Alias = "foto", Prototype = true)]
  public partial class Foto : IproxCluster {
    /// <summary>Titel (singular)</summary>
    [IproxCluster(Alias = "Foto")]
    [IproxCluster(Alias = "Algemeen", SequenceId = 1)]
    [IproxField(Alias = "Titel", SequenceId = 2)]
    private readonly PlainField fieldTitel = new PlainField();
    
    /// <summary>Afbeelding (singular)</summary>
    [IproxCluster(Alias = "Foto")]
    [IproxCluster(Alias = "Algemeen", SequenceId = 1)]
    [IproxField(Alias = "Afbeelding", SequenceId = 2)]
    private readonly ImageField fieldAfbeelding = new ImageField();
    
    /// <summary>Tekst (singular)</summary>
    [IproxCluster(Alias = "Foto")]
    [IproxCluster(Alias = "Algemeen", SequenceId = 1)]
    [IproxField(Alias = "Tekst", SequenceId = 2)]
    private readonly HtmlField fieldTekst = new HtmlField();
    
    /// <summary>Blokrol (singular)</summary>
    [IproxCluster(Alias = "Foto")]
    [IproxCluster(Alias = "Algemeen", SequenceId = 1)]
    [IproxField(Alias = "Blokrol", SequenceId = 2)]
    private readonly SelectionField fieldBlokrol = new SelectionField();
    
    /// <summary>Gets or sets Titel</summary>
    [IproxProperty(SequenceId = 0)]
    public PlainField Titel {
      get {
        return this.fieldTitel;
      }
      
      set {
        this.fieldTitel.Value = (value ?? new PlainField()).Value;
      }
    }
    
    /// <summary>Gets or sets Afbeelding</summary>
    [IproxProperty(SequenceId = 1)]
    public ImageField Afbeelding {
      get {
        return this.fieldAfbeelding;
      }
      
      set {
        this.fieldAfbeelding.Value = (value ?? new ImageField()).Value;
      }
    }
    
    /// <summary>Gets or sets Tekst</summary>
    [IproxProperty(SequenceId = 2)]
    public HtmlField Tekst {
      get {
        return this.fieldTekst;
      }
      
      set {
        this.fieldTekst.Value = (value ?? new HtmlField()).Value;
      }
    }
    
    /// <summary>Gets or sets Blokrol</summary>
    [IproxProperty(SequenceId = 3)]
    public SelectionField Blokrol {
      get {
        return this.fieldBlokrol;
      }
      
      set {
        this.fieldBlokrol.Value = (value ?? new SelectionField()).Value;
      }
    }
  }
  
  /// <summary>Secundaire navigatie (class)</summary>
  /// <remarks>grid-blok</remarks>
  [IproxPagetype(Alias = "secundaire-navigatie", Prototype = true)]
  public partial class SecundaireNavigatie : IproxCluster {
    /// <summary>Startpunt (singular)</summary>
    [IproxCluster(Alias = "Blok")]
    [IproxField(Alias = "Startpunt", SequenceId = 1)]
    private readonly LinkField fieldStartpunt = new LinkField();
    
    /// <summary>Presentatievorm (singular)</summary>
    [IproxCluster(Alias = "Blok")]
    [IproxField(Alias = "Presentatievorm", SequenceId = 1)]
    private readonly SelectionField fieldPresentatievorm = new SelectionField();
    
    /// <summary>Gets or sets Startpunt</summary>
    /// <remarks>Het eerste niveau onder het aangewezen item, worden getoond</remarks>
    [IproxProperty(SequenceId = 0)]
    public LinkField Startpunt {
      get {
        return this.fieldStartpunt;
      }
      
      set {
        this.fieldStartpunt.Value = (value ?? new LinkField()).Value;
      }
    }
    
    /// <summary>Gets or sets Presentatievorm</summary>
    [IproxProperty(SequenceId = 1)]
    public SelectionField Presentatievorm {
      get {
        return this.fieldPresentatievorm;
      }
      
      set {
        this.fieldPresentatievorm.Value = (value ?? new SelectionField()).Value;
      }
    }
  }
  
  /// <summary>Inhoud (class)</summary>
  /// <remarks>grid-blok</remarks>
  [IproxPagetype(Alias = "inhoud", Prototype = true)]
  public partial class Inhoud : IproxCluster {
    /// <summary>Algemeen (singular)</summary>
    [IproxCluster(Alias = "Blok")]
    [IproxCluster(Alias = "Algemeen", SequenceId = 1)]
    private readonly Inhoud.AlgemeenCluster fieldAlgemeen = new Inhoud.AlgemeenCluster();
    
    /// <summary>Verwijzingen (collection)</summary>
    [IproxCluster(Alias = "Blok")]
    [IproxCluster(Alias = "Verwijzingen", Prototype = true, SequenceId = 1)]
    [IproxPagetype(Alias = "verwijzingen", Prototype = true, SequenceId = 2)]
    [IproxCluster(Alias = "Verwijzing", SequenceId = 3)]
    private readonly IproxClustersCollection<IVerwijzingen> fieldVerwijzingen = new IproxClustersCollection<IVerwijzingen>();
    
    /// <summary>Gets Algemeen</summary>
    [IproxProperty(SequenceId = 0)]
    public Inhoud.AlgemeenCluster Algemeen {
      get {
        return this.fieldAlgemeen;
      }
    }
    
    /// <summary>Gets collection of Verwijzingen</summary>
    [IproxProperty(SequenceId = 1)]
    public IproxClustersCollection<IVerwijzingen> Verwijzingen {
      get {
        return this.fieldVerwijzingen;
      }
    }
    
    /// <summary>Algemeen (class)</summary>
    [IproxCluster(Alias = "Blok")]
    [IproxCluster(Alias = "Algemeen", SequenceId = 1)]
    public partial class AlgemeenCluster : IproxCluster {
      /// <summary>Titel (singular)</summary>
      [IproxField(Alias = "Titel")]
      private readonly PlainField fieldTitel = new PlainField();
      
      /// <summary>Foto (singular)</summary>
      [IproxField(Alias = "Foto")]
      private readonly ImageField fieldFoto = new ImageField();
      
      /// <summary>Tekst (singular)</summary>
      [IproxField(Alias = "Tekst")]
      private readonly HtmlField fieldTekst = new HtmlField();
      
      /// <summary>Blokrol (singular)</summary>
      [IproxField(Alias = "Blokrol")]
      private readonly SelectionField fieldBlokrol = new SelectionField();
      
      /// <summary>Lijstweergave (singular)</summary>
      [IproxField(Alias = "Lijstweergave")]
      private readonly BooleanField fieldLijstweergave = new BooleanField();
      
      /// <summary>Gets or sets Titel</summary>
      [IproxProperty(SequenceId = 0)]
      public PlainField Titel {
        get {
          return this.fieldTitel;
        }
        
        set {
          this.fieldTitel.Value = (value ?? new PlainField()).Value;
        }
      }
      
      /// <summary>Gets or sets Foto</summary>
      /// <remarks>maat: 960*576</remarks>
      [IproxProperty(SequenceId = 1)]
      public ImageField Foto {
        get {
          return this.fieldFoto;
        }
        
        set {
          this.fieldFoto.Value = (value ?? new ImageField()).Value;
        }
      }
      
      /// <summary>Gets or sets Tekst</summary>
      [IproxProperty(SequenceId = 2)]
      public HtmlField Tekst {
        get {
          return this.fieldTekst;
        }
        
        set {
          this.fieldTekst.Value = (value ?? new HtmlField()).Value;
        }
      }
      
      /// <summary>Gets or sets Blokrol</summary>
      [IproxProperty(SequenceId = 3)]
      public SelectionField Blokrol {
        get {
          return this.fieldBlokrol;
        }
        
        set {
          this.fieldBlokrol.Value = (value ?? new SelectionField()).Value;
        }
      }
      
      /// <summary>Gets or sets Lijstweergave</summary>
      [IproxProperty(SequenceId = 4)]
      public BooleanField Lijstweergave {
        get {
          return this.fieldLijstweergave;
        }
        
        set {
          this.fieldLijstweergave.Value = (value ?? new BooleanField()).Value;
        }
      }
    }
  }
  
  /// <summary>Navigatie (class)</summary>
  /// <remarks>grid-blok</remarks>
  [IproxPagetype(Alias = "navigatie", Prototype = true)]
  public partial class Navigatie : IproxCluster {
    /// <summary>Inhoud (singular)</summary>
    [IproxCluster(Alias = "Blok")]
    [IproxCluster(Alias = "Inhoud", SequenceId = 1)]
    private readonly Navigatie.InhoudCluster fieldInhoud = new Navigatie.InhoudCluster();
    
    /// <summary>Instellingen (singular)</summary>
    [IproxCluster(Alias = "Blok")]
    [IproxCluster(Alias = "Instellingen", SequenceId = 1)]
    private readonly Navigatie.InstellingenCluster fieldInstellingen = new Navigatie.InstellingenCluster();
    
    /// <summary>Gets Inhoud</summary>
    [IproxProperty(SequenceId = 0)]
    public Navigatie.InhoudCluster Inhoud {
      get {
        return this.fieldInhoud;
      }
    }
    
    /// <summary>Gets Instellingen</summary>
    [IproxProperty(SequenceId = 1)]
    public Navigatie.InstellingenCluster Instellingen {
      get {
        return this.fieldInstellingen;
      }
    }
    
    /// <summary>Inhoud (class)</summary>
    [IproxCluster(Alias = "Blok")]
    [IproxCluster(Alias = "Inhoud", SequenceId = 1)]
    public partial class InhoudCluster : IproxCluster {
      /// <summary>Titel (singular)</summary>
      [IproxField(Alias = "Titel")]
      private readonly PlainField fieldTitel = new PlainField();
      
      /// <summary>Tekst (singular)</summary>
      [IproxField(Alias = "Tekst")]
      private readonly HtmlField fieldTekst = new HtmlField();
      
      /// <summary>Blokrol (singular)</summary>
      [IproxField(Alias = "Blokrol")]
      private readonly SelectionField fieldBlokrol = new SelectionField();
      
      /// <summary>Gets or sets Titel</summary>
      [IproxProperty(SequenceId = 0)]
      public PlainField Titel {
        get {
          return this.fieldTitel;
        }
        
        set {
          this.fieldTitel.Value = (value ?? new PlainField()).Value;
        }
      }
      
      /// <summary>Gets or sets Tekst</summary>
      [IproxProperty(SequenceId = 1)]
      public HtmlField Tekst {
        get {
          return this.fieldTekst;
        }
        
        set {
          this.fieldTekst.Value = (value ?? new HtmlField()).Value;
        }
      }
      
      /// <summary>Gets or sets Blokrol</summary>
      [IproxProperty(SequenceId = 2)]
      public SelectionField Blokrol {
        get {
          return this.fieldBlokrol;
        }
        
        set {
          this.fieldBlokrol.Value = (value ?? new SelectionField()).Value;
        }
      }
    }
    
    /// <summary>Instellingen (class)</summary>
    [IproxCluster(Alias = "Blok")]
    [IproxCluster(Alias = "Instellingen", SequenceId = 1)]
    public partial class InstellingenCluster : IproxCluster {
      /// <summary>Startpunt (singular)</summary>
      [IproxField(Alias = "Startpunt")]
      private readonly LinkField fieldStartpunt = new LinkField();
      
      /// <summary>Diepte (singular)</summary>
      [IproxField(Alias = "Diepte")]
      private readonly SelectionField fieldDiepte = new SelectionField();
      
      /// <summary>Bovenliggende pagina tonen (singular)</summary>
      [IproxField(Alias = "Bovenliggende pagina tonen")]
      private readonly BooleanField fieldBovenliggendePaginaTonen = new BooleanField();
      
      /// <summary>Gets or sets Startpunt</summary>
      /// <remarks>Leeg = huidige pagina</remarks>
      [IproxProperty(SequenceId = 0)]
      public LinkField Startpunt {
        get {
          return this.fieldStartpunt;
        }
        
        set {
          this.fieldStartpunt.Value = (value ?? new LinkField()).Value;
        }
      }
      
      /// <summary>Gets or sets Diepte</summary>
      [IproxProperty(SequenceId = 1)]
      public SelectionField Diepte {
        get {
          return this.fieldDiepte;
        }
        
        set {
          this.fieldDiepte.Value = (value ?? new SelectionField()).Value;
        }
      }
      
      /// <summary>Gets or sets Bovenliggende pagina tonen</summary>
      /// <remarks>Aan = toont het startpunt</remarks>
      [IproxProperty(SequenceId = 2)]
      public BooleanField BovenliggendePaginaTonen {
        get {
          return this.fieldBovenliggendePaginaTonen;
        }
        
        set {
          this.fieldBovenliggendePaginaTonen.Value = (value ?? new BooleanField()).Value;
        }
      }
    }
  }
  
  /// <summary>Servicebalk (class)</summary>
  /// <remarks>grid-blok</remarks>
  [IproxPagetype(Alias = "servicebalk", Prototype = true)]
  public partial class Servicebalk : IproxCluster {
    /// <summary>Sitepad (singular)</summary>
    [IproxCluster(Alias = "Blok")]
    [IproxField(Alias = "Sitepad", SequenceId = 1)]
    private readonly BooleanField fieldSitepad = new BooleanField();
    
    /// <summary>Zoekveld (singular)</summary>
    [IproxCluster(Alias = "Blok")]
    [IproxField(Alias = "Zoekveld", SequenceId = 1)]
    private readonly BooleanField fieldZoekveld = new BooleanField();
    
    /// <summary>Gets or sets Sitepad</summary>
    /// <remarks>Toon het sitepad</remarks>
    [IproxProperty(SequenceId = 0)]
    public BooleanField Sitepad {
      get {
        return this.fieldSitepad;
      }
      
      set {
        this.fieldSitepad.Value = (value ?? new BooleanField()).Value;
      }
    }
    
    /// <summary>Gets or sets Zoekveld</summary>
    /// <remarks>Toon het zoekveld; tevens moet er een pagina met alias 'zoeken' in de structuur aanwezig zijn</remarks>
    [IproxProperty(SequenceId = 1)]
    public BooleanField Zoekveld {
      get {
        return this.fieldZoekveld;
      }
      
      set {
        this.fieldZoekveld.Value = (value ?? new BooleanField()).Value;
      }
    }
  }
  
  /// <summary>Mediawidget (class)</summary>
  /// <remarks>grid-blok</remarks>
  [IproxPagetype(Alias = "mediawidget", Prototype = true)]
  public partial class Mediawidget : IproxCluster {
    /// <summary>Titel (singular)</summary>
    [IproxCluster(Alias = "Blok")]
    [IproxField(Alias = "Titel", SequenceId = 1)]
    private readonly PlainField fieldTitel = new PlainField();
    
    /// <summary>Inleiding (singular)</summary>
    [IproxCluster(Alias = "Blok")]
    [IproxField(Alias = "Inleiding", SequenceId = 1)]
    private readonly HtmlField fieldInleiding = new HtmlField();
    
    /// <summary>Widget (singular)</summary>
    [IproxCluster(Alias = "Blok")]
    [IproxField(Alias = "Widget", SequenceId = 1)]
    private readonly UnknownField fieldWidget = new UnknownField();
    
    /// <summary>Blokrol (singular)</summary>
    [IproxCluster(Alias = "Blok")]
    [IproxField(Alias = "Blokrol", SequenceId = 1)]
    private readonly SelectionField fieldBlokrol = new SelectionField();
    
    /// <summary>Alias (singular)</summary>
    [IproxCluster(Alias = "Blok")]
    [IproxField(Alias = "Alias", SequenceId = 1)]
    private readonly PlainField fieldAlias = new PlainField();
    
    /// <summary>Gets or sets Titel</summary>
    [IproxProperty(SequenceId = 0)]
    public PlainField Titel {
      get {
        return this.fieldTitel;
      }
      
      set {
        this.fieldTitel.Value = (value ?? new PlainField()).Value;
      }
    }
    
    /// <summary>Gets or sets Inleiding</summary>
    [IproxProperty(SequenceId = 1)]
    public HtmlField Inleiding {
      get {
        return this.fieldInleiding;
      }
      
      set {
        this.fieldInleiding.Value = (value ?? new HtmlField()).Value;
      }
    }
    
    /// <summary>Gets or sets Widget</summary>
    [IproxProperty(SequenceId = 2)]
    public UnknownField Widget {
      get {
        return this.fieldWidget;
      }
      
      set {
        this.fieldWidget.Value = (value ?? new UnknownField()).Value;
      }
    }
    
    /// <summary>Gets or sets Blokrol</summary>
    [IproxProperty(SequenceId = 3)]
    public SelectionField Blokrol {
      get {
        return this.fieldBlokrol;
      }
      
      set {
        this.fieldBlokrol.Value = (value ?? new SelectionField()).Value;
      }
    }
    
    /// <summary>Gets or sets Alias</summary>
    /// <remarks>Nodig voor hulpvideo's dashboard</remarks>
    [IproxProperty(SequenceId = 4)]
    public PlainField Alias {
      get {
        return this.fieldAlias;
      }
      
      set {
        this.fieldAlias.Value = (value ?? new PlainField()).Value;
      }
    }
  }
  
  /// <summary>Opiniepeiling (class)</summary>
  /// <remarks>grid-blok</remarks>
  [IproxPagetype(Alias = "opiniepeiling-blok", Prototype = true)]
  public partial class OpiniepeilingBlok : IproxCluster {
    /// <summary>Titel (singular)</summary>
    [IproxCluster(Alias = "Blok")]
    [IproxField(Alias = "Titel", SequenceId = 1)]
    private readonly PlainField fieldTitel = new PlainField();
    
    /// <summary>Blokrol (singular)</summary>
    [IproxCluster(Alias = "Blok")]
    [IproxField(Alias = "Blokrol", SequenceId = 1)]
    private readonly SelectionField fieldBlokrol = new SelectionField();
    
    /// <summary>Opiniepeiling (singular)</summary>
    [IproxCluster(Alias = "Blok")]
    [IproxField(Alias = "Opiniepeiling", SequenceId = 1)]
    private readonly LinkField fieldOpiniepeiling = new LinkField();
    
    /// <summary>Gets or sets Titel</summary>
    [IproxProperty(SequenceId = 0)]
    public PlainField Titel {
      get {
        return this.fieldTitel;
      }
      
      set {
        this.fieldTitel.Value = (value ?? new PlainField()).Value;
      }
    }
    
    /// <summary>Gets or sets Blokrol</summary>
    [IproxProperty(SequenceId = 1)]
    public SelectionField Blokrol {
      get {
        return this.fieldBlokrol;
      }
      
      set {
        this.fieldBlokrol.Value = (value ?? new SelectionField()).Value;
      }
    }
    
    /// <summary>Gets or sets Opiniepeiling</summary>
    [IproxProperty(SequenceId = 2)]
    public LinkField Opiniepeiling {
      get {
        return this.fieldOpiniepeiling;
      }
      
      set {
        this.fieldOpiniepeiling.Value = (value ?? new LinkField()).Value;
      }
    }
  }
  
  /// <summary>Artikel (class)</summary>
  [IproxPagetype(Alias = "artikel", Prototype = true)]
  [IproxCluster(Alias = "Inhoud", SequenceId = 1)]
  public partial class ArtikelPrototype : IproxCluster {
    /// <summary>Inleiding (singular)</summary>
    [IproxField(Alias = "Inleiding")]
    private readonly HtmlField fieldInleiding = new HtmlField();
    
    /// <summary>Inhoud (singular)</summary>
    [IproxField(Alias = "Inhoud")]
    private readonly HtmlField fieldInhoud = new HtmlField();
    
    /// <summary>Imagemap hotspot toont bij onclick (singular)</summary>
    [IproxField(Alias = "Imagemap hotspot toont bij onclick")]
    private readonly BooleanField fieldImagemapHotspotToontBijOnclick = new BooleanField();
    
    /// <summary>Gedrag imagemap (singular)</summary>
    [IproxField(Alias = "Gedrag imagemap")]
    private readonly SelectionField fieldGedragImagemap = new SelectionField();
    
    /// <summary>Imagemap (singular)</summary>
    [IproxField(Alias = "Imagemap")]
    private readonly HtmlField fieldImagemap = new HtmlField();
    
    /// <summary>Gets or sets Inleiding</summary>
    [IproxProperty(SequenceId = 0)]
    public HtmlField Inleiding {
      get {
        return this.fieldInleiding;
      }
      
      set {
        this.fieldInleiding.Value = (value ?? new HtmlField()).Value;
      }
    }
    
    /// <summary>Gets or sets Inhoud</summary>
    [IproxProperty(SequenceId = 1)]
    public HtmlField Inhoud {
      get {
        return this.fieldInhoud;
      }
      
      set {
        this.fieldInhoud.Value = (value ?? new HtmlField()).Value;
      }
    }
    
    /// <summary>Gets or sets Imagemap hotspot toont bij onclick</summary>
    /// <remarks>false: hotspot toont bij onmouseover</remarks>
    [IproxProperty(SequenceId = 2)]
    public BooleanField ImagemapHotspotToontBijOnclick {
      get {
        return this.fieldImagemapHotspotToontBijOnclick;
      }
      
      set {
        this.fieldImagemapHotspotToontBijOnclick.Value = (value ?? new BooleanField()).Value;
      }
    }
    
    /// <summary>Gets or sets Gedrag imagemap</summary>
    [IproxProperty(SequenceId = 3)]
    public SelectionField GedragImagemap {
      get {
        return this.fieldGedragImagemap;
      }
      
      set {
        this.fieldGedragImagemap.Value = (value ?? new SelectionField()).Value;
      }
    }
    
    /// <summary>Gets or sets Imagemap</summary>
    [IproxProperty(SequenceId = 4)]
    public HtmlField Imagemap {
      get {
        return this.fieldImagemap;
      }
      
      set {
        this.fieldImagemap.Value = (value ?? new HtmlField()).Value;
      }
    }
  }
  
  /// <summary>Reactiemogelijkheid (class)</summary>
  /// <remarks>Feedback mogelijkheid toevoegen aan paginatypen</remarks>
  [IproxPagetype(Alias = "feedback", Prototype = true)]
  public partial class Feedback : IproxCluster {
    /// <summary>Aan (singular)</summary>
    [IproxCluster(Alias = "Reactiemogelijkheid")]
    [IproxField(Alias = "Aan", SequenceId = 1)]
    private readonly BooleanField fieldAan = new BooleanField();
    
    /// <summary>Actief (singular)</summary>
    [IproxCluster(Alias = "Reactiemogelijkheid")]
    [IproxField(Alias = "Actief", SequenceId = 1)]
    private readonly BooleanField fieldActief = new BooleanField();
    
    /// <summary>Modereren (singular)</summary>
    [IproxCluster(Alias = "Reactiemogelijkheid")]
    [IproxField(Alias = "Modereren", SequenceId = 1)]
    private readonly BooleanField fieldModereren = new BooleanField();
    
    /// <summary>Omgekeerd chronologisch sorteren (singular)</summary>
    [IproxCluster(Alias = "Reactiemogelijkheid")]
    [IproxField(Alias = "Omgekeerd chronologisch sorteren", SequenceId = 1)]
    private readonly BooleanField fieldOmgekeerdChronologischSorteren = new BooleanField();
    
    /// <summary>Geen titel (singular)</summary>
    [IproxCluster(Alias = "Reactiemogelijkheid")]
    [IproxField(Alias = "Geen titel", SequenceId = 1)]
    private readonly BooleanField fieldGeenTitel = new BooleanField();
    
    /// <summary>Emailadres verplicht (singular)</summary>
    [IproxCluster(Alias = "Reactiemogelijkheid")]
    [IproxField(Alias = "Emailadres verplicht", SequenceId = 1)]
    private readonly BooleanField fieldEmailadresVerplicht = new BooleanField();
    
    /// <summary>E-mailadres optioneel verbergen (singular)</summary>
    [IproxCluster(Alias = "Reactiemogelijkheid")]
    [IproxField(Alias = "E-mailadres optioneel verbergen", SequenceId = 1)]
    private readonly BooleanField fieldEMailadresOptioneelVerbergen = new BooleanField();
    
    /// <summary>Aantal reacties per pagina (singular)</summary>
    [IproxCluster(Alias = "Reactiemogelijkheid")]
    [IproxField(Alias = "Aantal reacties per pagina", SequenceId = 1)]
    private readonly PlainField fieldAantalReactiesPerPagina = new PlainField();
    
    /// <summary>Captcha gebruiken (singular)</summary>
    [IproxCluster(Alias = "Reactiemogelijkheid")]
    [IproxField(Alias = "Captcha gebruiken", SequenceId = 1)]
    private readonly BooleanField fieldCaptchaGebruiken = new BooleanField();
    
    /// <summary>Bericht na verzending (singular)</summary>
    [IproxCluster(Alias = "Reactiemogelijkheid")]
    [IproxField(Alias = "Bericht na verzending", SequenceId = 1)]
    private readonly HtmlField fieldBerichtNaVerzending = new HtmlField();
    
    /// <summary>Afzender adres attendering (singular)</summary>
    [IproxCluster(Alias = "Reactiemogelijkheid")]
    [IproxField(Alias = "Afzender adres attendering", SequenceId = 1)]
    private readonly AddressField fieldAfzenderAdresAttendering = new AddressField();
    
    /// <summary>E-mail attendering (singular)</summary>
    [IproxCluster(Alias = "Reactiemogelijkheid")]
    [IproxField(Alias = "E-mail attendering", SequenceId = 1)]
    private readonly AddressField fieldEMailAttendering = new AddressField();
    
    /// <summary>Omschrijving rss feed (singular)</summary>
    [IproxCluster(Alias = "Reactiemogelijkheid")]
    [IproxField(Alias = "Omschrijving rss feed", SequenceId = 1)]
    private readonly HtmlField fieldOmschrijvingRssFeed = new HtmlField();
    
    /// <summary>Gets or sets Aan</summary>
    [IproxProperty(SequenceId = 0)]
    public BooleanField Aan {
      get {
        return this.fieldAan;
      }
      
      set {
        this.fieldAan.Value = (value ?? new BooleanField()).Value;
      }
    }
    
    /// <summary>Gets or sets Actief</summary>
    [IproxProperty(SequenceId = 1)]
    public BooleanField Actief {
      get {
        return this.fieldActief;
      }
      
      set {
        this.fieldActief.Value = (value ?? new BooleanField()).Value;
      }
    }
    
    /// <summary>Gets or sets Modereren</summary>
    [IproxProperty(SequenceId = 2)]
    public BooleanField Modereren {
      get {
        return this.fieldModereren;
      }
      
      set {
        this.fieldModereren.Value = (value ?? new BooleanField()).Value;
      }
    }
    
    /// <summary>Gets or sets Omgekeerd chronologisch sorteren</summary>
    [IproxProperty(SequenceId = 3)]
    public BooleanField OmgekeerdChronologischSorteren {
      get {
        return this.fieldOmgekeerdChronologischSorteren;
      }
      
      set {
        this.fieldOmgekeerdChronologischSorteren.Value = (value ?? new BooleanField()).Value;
      }
    }
    
    /// <summary>Gets or sets Geen titel</summary>
    /// <remarks>er is alleen een reactieveld beschikbaar</remarks>
    [IproxProperty(SequenceId = 4)]
    public BooleanField GeenTitel {
      get {
        return this.fieldGeenTitel;
      }
      
      set {
        this.fieldGeenTitel.Value = (value ?? new BooleanField()).Value;
      }
    }
    
    /// <summary>Gets or sets Emailadres verplicht</summary>
    /// <remarks>er dient een emailadres ingevoerd te worden.</remarks>
    [IproxProperty(SequenceId = 5)]
    public BooleanField EmailadresVerplicht {
      get {
        return this.fieldEmailadresVerplicht;
      }
      
      set {
        this.fieldEmailadresVerplicht.Value = (value ?? new BooleanField()).Value;
      }
    }
    
    /// <summary>Gets or sets E-mailadres optioneel verbergen</summary>
    [IproxProperty(SequenceId = 6)]
    public BooleanField EMailadresOptioneelVerbergen {
      get {
        return this.fieldEMailadresOptioneelVerbergen;
      }
      
      set {
        this.fieldEMailadresOptioneelVerbergen.Value = (value ?? new BooleanField()).Value;
      }
    }
    
    /// <summary>Gets or sets Aantal reacties per pagina</summary>
    [IproxProperty(SequenceId = 7)]
    public PlainField AantalReactiesPerPagina {
      get {
        return this.fieldAantalReactiesPerPagina;
      }
      
      set {
        this.fieldAantalReactiesPerPagina.Value = (value ?? new PlainField()).Value;
      }
    }
    
    /// <summary>Gets or sets Captcha gebruiken</summary>
    [IproxProperty(SequenceId = 8)]
    public BooleanField CaptchaGebruiken {
      get {
        return this.fieldCaptchaGebruiken;
      }
      
      set {
        this.fieldCaptchaGebruiken.Value = (value ?? new BooleanField()).Value;
      }
    }
    
    /// <summary>Gets or sets Bericht na verzending</summary>
    [IproxProperty(SequenceId = 9)]
    public HtmlField BerichtNaVerzending {
      get {
        return this.fieldBerichtNaVerzending;
      }
      
      set {
        this.fieldBerichtNaVerzending.Value = (value ?? new HtmlField()).Value;
      }
    }
    
    /// <summary>Gets or sets Afzender adres attendering</summary>
    [IproxProperty(SequenceId = 10)]
    public AddressField AfzenderAdresAttendering {
      get {
        return this.fieldAfzenderAdresAttendering;
      }
      
      set {
        this.fieldAfzenderAdresAttendering.Value = (value ?? new AddressField()).Value;
      }
    }
    
    /// <summary>Gets or sets E-mail attendering</summary>
    [IproxProperty(SequenceId = 11)]
    public AddressField EMailAttendering {
      get {
        return this.fieldEMailAttendering;
      }
      
      set {
        this.fieldEMailAttendering.Value = (value ?? new AddressField()).Value;
      }
    }
    
    /// <summary>Gets or sets Omschrijving rss feed</summary>
    /// <remarks>Bij leeg wordt de samenvatting van de pagina gebruikt</remarks>
    [IproxProperty(SequenceId = 12)]
    public HtmlField OmschrijvingRssFeed {
      get {
        return this.fieldOmschrijvingRssFeed;
      }
      
      set {
        this.fieldOmschrijvingRssFeed.Value = (value ?? new HtmlField()).Value;
      }
    }
  }
  
  /// <summary>Formulier (class)</summary>
  [IproxPagetype(Alias = "formulier", Prototype = true)]
  public partial class Formulier : IproxCluster {
    /// <summary>Instellingen (singular)</summary>
    [IproxCluster(Alias = "Instellingen")]
    private readonly Formulier.InstellingenCluster fieldInstellingen = new Formulier.InstellingenCluster();
    
    /// <summary>Inhoud (singular)</summary>
    [IproxCluster(Alias = "Inhoud")]
    private readonly Formulier.InhoudCluster fieldInhoud = new Formulier.InhoudCluster();
    
    /// <summary>Standaardvelden (singular)</summary>
    [IproxCluster(Alias = "Standaardvelden")]
    private readonly Formulier.StandaardveldenCluster fieldStandaardvelden = new Formulier.StandaardveldenCluster();
    
    /// <summary>Element (collection)</summary>
    [IproxCluster(Alias = "Element")]
    private readonly IproxClustersCollection<Formulier.IElement> fieldElement = new IproxClustersCollection<Formulier.IElement>();
    
    /// <summary>Gets Instellingen</summary>
    [IproxProperty(SequenceId = 0)]
    public Formulier.InstellingenCluster Instellingen {
      get {
        return this.fieldInstellingen;
      }
    }
    
    /// <summary>Gets Inhoud</summary>
    [IproxProperty(SequenceId = 1)]
    public Formulier.InhoudCluster Inhoud {
      get {
        return this.fieldInhoud;
      }
    }
    
    /// <summary>Gets Standaardvelden</summary>
    [IproxProperty(SequenceId = 2)]
    public Formulier.StandaardveldenCluster Standaardvelden {
      get {
        return this.fieldStandaardvelden;
      }
    }
    
    /// <summary>Gets collection of Element</summary>
    [IproxProperty(SequenceId = 3)]
    public IproxClustersCollection<Formulier.IElement> Element {
      get {
        return this.fieldElement;
      }
    }
    
    /// <summary>Instellingen (class)</summary>
    [IproxCluster(Alias = "Instellingen")]
    public partial class InstellingenCluster : IproxCluster {
      /// <summary>Bevestigingsscherm tonen (singular)</summary>
      [IproxField(Alias = "Bevestigingsscherm tonen")]
      private readonly BooleanField fieldBevestigingsschermTonen = new BooleanField();
      
      /// <summary>Sla het formulier op (singular)</summary>
      [IproxField(Alias = "Sla het formulier op")]
      private readonly BooleanField fieldSlaHetFormulierOp = new BooleanField();
      
      /// <summary>Mail het formulier (singular)</summary>
      [IproxField(Alias = "Mail het formulier")]
      private readonly BooleanField fieldMailHetFormulier = new BooleanField();
      
      /// <summary>Print het formulier (singular)</summary>
      [IproxField(Alias = "Print het formulier")]
      private readonly BooleanField fieldPrintHetFormulier = new BooleanField();
      
      /// <summary>Bijlagen niet meesturen in mail (singular)</summary>
      [IproxField(Alias = "Bijlagen niet meesturen in mail")]
      private readonly BooleanField fieldBijlagenNietMeesturenInMail = new BooleanField();
      
      /// <summary>Opt-in (singular)</summary>
      [IproxField(Alias = "Opt-in")]
      private readonly BooleanField fieldOptIn = new BooleanField();
      
      /// <summary>Cookie plaatsen (singular)</summary>
      [IproxField(Alias = "Cookie plaatsen")]
      private readonly BooleanField fieldCookiePlaatsen = new BooleanField();
      
      /// <summary>Onderwerp (singular)</summary>
      [IproxField(Alias = "Onderwerp")]
      private readonly PlainField fieldOnderwerp = new PlainField();
      
      /// <summary>Bezoeker mag onderwerp invullen (singular)</summary>
      [IproxField(Alias = "Bezoeker mag onderwerp invullen")]
      private readonly BooleanField fieldBezoekerMagOnderwerpInvullen = new BooleanField();
      
      /// <summary>Bestemming (singular)</summary>
      [IproxField(Alias = "Bestemming")]
      private readonly AddressField fieldBestemming = new AddressField();
      
      /// <summary>Kopie naar (singular)</summary>
      [IproxField(Alias = "Kopie naar")]
      private readonly AddressField fieldKopieNaar = new AddressField();
      
      /// <summary>Bezoeker mag Kopie naar invullen (singular)</summary>
      [IproxField(Alias = "Bezoeker mag Kopie naar invullen")]
      private readonly BooleanField fieldBezoekerMagKopieNaarInvullen = new BooleanField();
      
      /// <summary>Bcc (singular)</summary>
      [IproxField(Alias = "Bcc")]
      private readonly AddressField fieldBcc = new AddressField();
      
      /// <summary>Afzender (singular)</summary>
      [IproxField(Alias = "Afzender")]
      private readonly AddressField fieldAfzender = new AddressField();
      
      /// <summary>Afschrift versturen (singular)</summary>
      [IproxField(Alias = "Afschrift versturen")]
      private readonly BooleanField fieldAfschriftVersturen = new BooleanField();
      
      /// <summary>Afschrift op verzoek versturen (singular)</summary>
      [IproxField(Alias = "Afschrift op verzoek versturen")]
      private readonly BooleanField fieldAfschriftOpVerzoekVersturen = new BooleanField();
      
      /// <summary>Verzonden gegevens downloaden (singular)</summary>
      [IproxField(Alias = "Verzonden gegevens downloaden")]
      private readonly BooleanField fieldVerzondenGegevensDownloaden = new BooleanField();
      
      /// <summary>Gets or sets Bevestigingsscherm tonen</summary>
      [IproxProperty(SequenceId = 0)]
      public BooleanField BevestigingsschermTonen {
        get {
          return this.fieldBevestigingsschermTonen;
        }
        
        set {
          this.fieldBevestigingsschermTonen.Value = (value ?? new BooleanField()).Value;
        }
      }
      
      /// <summary>Gets or sets Sla het formulier op</summary>
      [IproxProperty(SequenceId = 1)]
      public BooleanField SlaHetFormulierOp {
        get {
          return this.fieldSlaHetFormulierOp;
        }
        
        set {
          this.fieldSlaHetFormulierOp.Value = (value ?? new BooleanField()).Value;
        }
      }
      
      /// <summary>Gets or sets Mail het formulier</summary>
      [IproxProperty(SequenceId = 2)]
      public BooleanField MailHetFormulier {
        get {
          return this.fieldMailHetFormulier;
        }
        
        set {
          this.fieldMailHetFormulier.Value = (value ?? new BooleanField()).Value;
        }
      }
      
      /// <summary>Gets or sets Print het formulier</summary>
      [IproxProperty(SequenceId = 3)]
      public BooleanField PrintHetFormulier {
        get {
          return this.fieldPrintHetFormulier;
        }
        
        set {
          this.fieldPrintHetFormulier.Value = (value ?? new BooleanField()).Value;
        }
      }
      
      /// <summary>Gets or sets Bijlagen niet meesturen in mail</summary>
      /// <remarks>Werkt alleen als de mail ook opgeslagen wordt.</remarks>
      [IproxProperty(SequenceId = 4)]
      public BooleanField BijlagenNietMeesturenInMail {
        get {
          return this.fieldBijlagenNietMeesturenInMail;
        }
        
        set {
          this.fieldBijlagenNietMeesturenInMail.Value = (value ?? new BooleanField()).Value;
        }
      }
      
      /// <summary>Gets or sets Opt-in</summary>
      [IproxProperty(SequenceId = 5)]
      public BooleanField OptIn {
        get {
          return this.fieldOptIn;
        }
        
        set {
          this.fieldOptIn.Value = (value ?? new BooleanField()).Value;
        }
      }
      
      /// <summary>Gets or sets Cookie plaatsen</summary>
      /// <remarks>het formulier mag maar 1x ingevuld worden</remarks>
      [IproxProperty(SequenceId = 6)]
      public BooleanField CookiePlaatsen {
        get {
          return this.fieldCookiePlaatsen;
        }
        
        set {
          this.fieldCookiePlaatsen.Value = (value ?? new BooleanField()).Value;
        }
      }
      
      /// <summary>Gets or sets Onderwerp</summary>
      [IproxProperty(SequenceId = 7)]
      public PlainField Onderwerp {
        get {
          return this.fieldOnderwerp;
        }
        
        set {
          this.fieldOnderwerp.Value = (value ?? new PlainField()).Value;
        }
      }
      
      /// <summary>Gets or sets Bezoeker mag onderwerp invullen</summary>
      [IproxProperty(SequenceId = 8)]
      public BooleanField BezoekerMagOnderwerpInvullen {
        get {
          return this.fieldBezoekerMagOnderwerpInvullen;
        }
        
        set {
          this.fieldBezoekerMagOnderwerpInvullen.Value = (value ?? new BooleanField()).Value;
        }
      }
      
      /// <summary>Gets or sets Bestemming</summary>
      /// <remarks>de mail wordt hiernaartoe gestuurd</remarks>
      [IproxProperty(SequenceId = 9)]
      public AddressField Bestemming {
        get {
          return this.fieldBestemming;
        }
        
        set {
          this.fieldBestemming.Value = (value ?? new AddressField()).Value;
        }
      }
      
      /// <summary>Gets or sets Kopie naar</summary>
      /// <remarks>komt in het cc-veld van de mail</remarks>
      [IproxProperty(SequenceId = 10)]
      public AddressField KopieNaar {
        get {
          return this.fieldKopieNaar;
        }
        
        set {
          this.fieldKopieNaar.Value = (value ?? new AddressField()).Value;
        }
      }
      
      /// <summary>Gets or sets Bezoeker mag Kopie naar invullen</summary>
      [IproxProperty(SequenceId = 11)]
      public BooleanField BezoekerMagKopieNaarInvullen {
        get {
          return this.fieldBezoekerMagKopieNaarInvullen;
        }
        
        set {
          this.fieldBezoekerMagKopieNaarInvullen.Value = (value ?? new BooleanField()).Value;
        }
      }
      
      /// <summary>Gets or sets Bcc</summary>
      [IproxProperty(SequenceId = 12)]
      public AddressField Bcc {
        get {
          return this.fieldBcc;
        }
        
        set {
          this.fieldBcc.Value = (value ?? new AddressField()).Value;
        }
      }
      
      /// <summary>Gets or sets Afzender</summary>
      /// <remarks>komt in het afzender-veld van de mail</remarks>
      [IproxProperty(SequenceId = 13)]
      public AddressField Afzender {
        get {
          return this.fieldAfzender;
        }
        
        set {
          this.fieldAfzender.Value = (value ?? new AddressField()).Value;
        }
      }
      
      /// <summary>Gets or sets Afschrift versturen</summary>
      [IproxProperty(SequenceId = 14)]
      public BooleanField AfschriftVersturen {
        get {
          return this.fieldAfschriftVersturen;
        }
        
        set {
          this.fieldAfschriftVersturen.Value = (value ?? new BooleanField()).Value;
        }
      }
      
      /// <summary>Gets or sets Afschrift op verzoek versturen</summary>
      [IproxProperty(SequenceId = 15)]
      public BooleanField AfschriftOpVerzoekVersturen {
        get {
          return this.fieldAfschriftOpVerzoekVersturen;
        }
        
        set {
          this.fieldAfschriftOpVerzoekVersturen.Value = (value ?? new BooleanField()).Value;
        }
      }
      
      /// <summary>Gets or sets Verzonden gegevens downloaden</summary>
      [IproxProperty(SequenceId = 16)]
      public BooleanField VerzondenGegevensDownloaden {
        get {
          return this.fieldVerzondenGegevensDownloaden;
        }
        
        set {
          this.fieldVerzondenGegevensDownloaden.Value = (value ?? new BooleanField()).Value;
        }
      }
    }
    
    /// <summary>Inhoud (class)</summary>
    [IproxCluster(Alias = "Inhoud")]
    public partial class InhoudCluster : IproxCluster {
      /// <summary>Formulier (singular)</summary>
      [IproxCluster(Alias = "Formulier")]
      private readonly Formulier.InhoudCluster.FormulierCluster fieldFormulier = new Formulier.InhoudCluster.FormulierCluster();
      
      /// <summary>Bevestiging (singular)</summary>
      [IproxCluster(Alias = "Bevestiging")]
      private readonly Formulier.InhoudCluster.BevestigingCluster fieldBevestiging = new Formulier.InhoudCluster.BevestigingCluster();
      
      /// <summary>Verzending (singular)</summary>
      [IproxCluster(Alias = "Verzending")]
      private readonly Formulier.InhoudCluster.VerzendingCluster fieldVerzending = new Formulier.InhoudCluster.VerzendingCluster();
      
      /// <summary>Opt-in (singular)</summary>
      [IproxCluster(Alias = "Opt-in")]
      private readonly Formulier.InhoudCluster.OptInCluster fieldOptIn = new Formulier.InhoudCluster.OptInCluster();
      
      /// <summary>E-mail (singular)</summary>
      [IproxCluster(Alias = "E-mail")]
      private readonly Formulier.InhoudCluster.EMailCluster fieldEMail = new Formulier.InhoudCluster.EMailCluster();
      
      /// <summary>Afschrift (singular)</summary>
      [IproxCluster(Alias = "Afschrift")]
      private readonly Formulier.InhoudCluster.AfschriftCluster fieldAfschrift = new Formulier.InhoudCluster.AfschriftCluster();
      
      /// <summary>Verzendingsrapport (singular)</summary>
      [IproxCluster(Alias = "Verzendingsrapport")]
      private readonly Formulier.InhoudCluster.VerzendingsrapportCluster fieldVerzendingsrapport = new Formulier.InhoudCluster.VerzendingsrapportCluster();
      
      /// <summary>Gets Formulier</summary>
      [IproxProperty(SequenceId = 0)]
      public Formulier.InhoudCluster.FormulierCluster Formulier {
        get {
          return this.fieldFormulier;
        }
      }
      
      /// <summary>Gets Bevestiging</summary>
      [IproxProperty(SequenceId = 1)]
      public Formulier.InhoudCluster.BevestigingCluster Bevestiging {
        get {
          return this.fieldBevestiging;
        }
      }
      
      /// <summary>Gets Verzending</summary>
      [IproxProperty(SequenceId = 2)]
      public Formulier.InhoudCluster.VerzendingCluster Verzending {
        get {
          return this.fieldVerzending;
        }
      }
      
      /// <summary>Gets Opt-in</summary>
      [IproxProperty(SequenceId = 3)]
      public Formulier.InhoudCluster.OptInCluster OptIn {
        get {
          return this.fieldOptIn;
        }
      }
      
      /// <summary>Gets E-mail</summary>
      [IproxProperty(SequenceId = 4)]
      public Formulier.InhoudCluster.EMailCluster EMail {
        get {
          return this.fieldEMail;
        }
      }
      
      /// <summary>Gets Afschrift</summary>
      [IproxProperty(SequenceId = 5)]
      public Formulier.InhoudCluster.AfschriftCluster Afschrift {
        get {
          return this.fieldAfschrift;
        }
      }
      
      /// <summary>Gets Verzendingsrapport</summary>
      [IproxProperty(SequenceId = 6)]
      public Formulier.InhoudCluster.VerzendingsrapportCluster Verzendingsrapport {
        get {
          return this.fieldVerzendingsrapport;
        }
      }
      
      /// <summary>Formulier (class)</summary>
      [IproxCluster(Alias = "Formulier")]
      public partial class FormulierCluster : IproxCluster {
        /// <summary>Inleiding (singular)</summary>
        [IproxField(Alias = "Inleiding")]
        private readonly HtmlField fieldInleiding = new HtmlField();
        
        /// <summary>Afsluiter (singular)</summary>
        [IproxField(Alias = "Afsluiter")]
        private readonly HtmlField fieldAfsluiter = new HtmlField();
        
        /// <summary>Gets or sets Inleiding</summary>
        [IproxProperty(SequenceId = 0)]
        public HtmlField Inleiding {
          get {
            return this.fieldInleiding;
          }
          
          set {
            this.fieldInleiding.Value = (value ?? new HtmlField()).Value;
          }
        }
        
        /// <summary>Gets or sets Afsluiter</summary>
        [IproxProperty(SequenceId = 1)]
        public HtmlField Afsluiter {
          get {
            return this.fieldAfsluiter;
          }
          
          set {
            this.fieldAfsluiter.Value = (value ?? new HtmlField()).Value;
          }
        }
      }
      
      /// <summary>Bevestiging (class)</summary>
      [IproxCluster(Alias = "Bevestiging")]
      public partial class BevestigingCluster : IproxCluster {
        /// <summary>Inleiding (singular)</summary>
        [IproxField(Alias = "Inleiding")]
        private readonly HtmlField fieldInleiding = new HtmlField();
        
        /// <summary>Afsluiter (singular)</summary>
        [IproxField(Alias = "Afsluiter")]
        private readonly HtmlField fieldAfsluiter = new HtmlField();
        
        /// <summary>Gets or sets Inleiding</summary>
        [IproxProperty(SequenceId = 0)]
        public HtmlField Inleiding {
          get {
            return this.fieldInleiding;
          }
          
          set {
            this.fieldInleiding.Value = (value ?? new HtmlField()).Value;
          }
        }
        
        /// <summary>Gets or sets Afsluiter</summary>
        [IproxProperty(SequenceId = 1)]
        public HtmlField Afsluiter {
          get {
            return this.fieldAfsluiter;
          }
          
          set {
            this.fieldAfsluiter.Value = (value ?? new HtmlField()).Value;
          }
        }
      }
      
      /// <summary>Verzending (class)</summary>
      [IproxCluster(Alias = "Verzending")]
      public partial class VerzendingCluster : IproxCluster {
        /// <summary>Bericht na verzending (singular)</summary>
        [IproxField(Alias = "Bericht na verzending")]
        private readonly HtmlField fieldBerichtNaVerzending = new HtmlField();
        
        /// <summary>Bericht na fout (singular)</summary>
        [IproxField(Alias = "Bericht na fout")]
        private readonly HtmlField fieldBerichtNaFout = new HtmlField();
        
        /// <summary>Gets or sets Bericht na verzending</summary>
        [IproxProperty(SequenceId = 0)]
        public HtmlField BerichtNaVerzending {
          get {
            return this.fieldBerichtNaVerzending;
          }
          
          set {
            this.fieldBerichtNaVerzending.Value = (value ?? new HtmlField()).Value;
          }
        }
        
        /// <summary>Gets or sets Bericht na fout</summary>
        [IproxProperty(SequenceId = 1)]
        public HtmlField BerichtNaFout {
          get {
            return this.fieldBerichtNaFout;
          }
          
          set {
            this.fieldBerichtNaFout.Value = (value ?? new HtmlField()).Value;
          }
        }
      }
      
      /// <summary>Opt-in (class)</summary>
      [IproxCluster(Alias = "Opt-in")]
      public partial class OptInCluster : IproxCluster {
        /// <summary>Inleiding (singular)</summary>
        [IproxField(Alias = "Inleiding")]
        private readonly HtmlField fieldInleiding = new HtmlField();
        
        /// <summary>Afsluiter (singular)</summary>
        [IproxField(Alias = "Afsluiter")]
        private readonly HtmlField fieldAfsluiter = new HtmlField();
        
        /// <summary>Mail tekst (singular)</summary>
        [IproxField(Alias = "Mail tekst")]
        private readonly HtmlField fieldMailTekst = new HtmlField();
        
        /// <summary>Gets or sets Inleiding</summary>
        [IproxProperty(SequenceId = 0)]
        public HtmlField Inleiding {
          get {
            return this.fieldInleiding;
          }
          
          set {
            this.fieldInleiding.Value = (value ?? new HtmlField()).Value;
          }
        }
        
        /// <summary>Gets or sets Afsluiter</summary>
        [IproxProperty(SequenceId = 1)]
        public HtmlField Afsluiter {
          get {
            return this.fieldAfsluiter;
          }
          
          set {
            this.fieldAfsluiter.Value = (value ?? new HtmlField()).Value;
          }
        }
        
        /// <summary>Gets or sets Mail tekst</summary>
        [IproxProperty(SequenceId = 2)]
        public HtmlField MailTekst {
          get {
            return this.fieldMailTekst;
          }
          
          set {
            this.fieldMailTekst.Value = (value ?? new HtmlField()).Value;
          }
        }
      }
      
      /// <summary>E-mail (class)</summary>
      [IproxCluster(Alias = "E-mail")]
      public partial class EMailCluster : IproxCluster {
        /// <summary>Inleiding (singular)</summary>
        [IproxField(Alias = "Inleiding")]
        private readonly HtmlField fieldInleiding = new HtmlField();
        
        /// <summary>Afsluiter (singular)</summary>
        [IproxField(Alias = "Afsluiter")]
        private readonly HtmlField fieldAfsluiter = new HtmlField();
        
        /// <summary>Gets or sets Inleiding</summary>
        [IproxProperty(SequenceId = 0)]
        public HtmlField Inleiding {
          get {
            return this.fieldInleiding;
          }
          
          set {
            this.fieldInleiding.Value = (value ?? new HtmlField()).Value;
          }
        }
        
        /// <summary>Gets or sets Afsluiter</summary>
        [IproxProperty(SequenceId = 1)]
        public HtmlField Afsluiter {
          get {
            return this.fieldAfsluiter;
          }
          
          set {
            this.fieldAfsluiter.Value = (value ?? new HtmlField()).Value;
          }
        }
      }
      
      /// <summary>Afschrift (class)</summary>
      [IproxCluster(Alias = "Afschrift")]
      public partial class AfschriftCluster : IproxCluster {
        /// <summary>Inleiding (singular)</summary>
        [IproxField(Alias = "Inleiding")]
        private readonly HtmlField fieldInleiding = new HtmlField();
        
        /// <summary>Afsluiter (singular)</summary>
        [IproxField(Alias = "Afsluiter")]
        private readonly HtmlField fieldAfsluiter = new HtmlField();
        
        /// <summary>Geen formuliervelden (singular)</summary>
        [IproxField(Alias = "Geen formuliervelden")]
        private readonly BooleanField fieldGeenFormuliervelden = new BooleanField();
        
        /// <summary>Gets or sets Inleiding</summary>
        [IproxProperty(SequenceId = 0)]
        public HtmlField Inleiding {
          get {
            return this.fieldInleiding;
          }
          
          set {
            this.fieldInleiding.Value = (value ?? new HtmlField()).Value;
          }
        }
        
        /// <summary>Gets or sets Afsluiter</summary>
        [IproxProperty(SequenceId = 1)]
        public HtmlField Afsluiter {
          get {
            return this.fieldAfsluiter;
          }
          
          set {
            this.fieldAfsluiter.Value = (value ?? new HtmlField()).Value;
          }
        }
        
        /// <summary>Gets or sets Geen formuliervelden</summary>
        [IproxProperty(SequenceId = 2)]
        public BooleanField GeenFormuliervelden {
          get {
            return this.fieldGeenFormuliervelden;
          }
          
          set {
            this.fieldGeenFormuliervelden.Value = (value ?? new BooleanField()).Value;
          }
        }
      }
      
      /// <summary>Verzendingsrapport (class)</summary>
      [IproxCluster(Alias = "Verzendingsrapport")]
      public partial class VerzendingsrapportCluster : IproxCluster {
        /// <summary>Inleiding (singular)</summary>
        [IproxField(Alias = "Inleiding")]
        private readonly PlainField fieldInleiding = new PlainField();
        
        /// <summary>Afsluiter (singular)</summary>
        [IproxField(Alias = "Afsluiter")]
        private readonly PlainField fieldAfsluiter = new PlainField();
        
        /// <summary>Gets or sets Inleiding</summary>
        [IproxProperty(SequenceId = 0)]
        public PlainField Inleiding {
          get {
            return this.fieldInleiding;
          }
          
          set {
            this.fieldInleiding.Value = (value ?? new PlainField()).Value;
          }
        }
        
        /// <summary>Gets or sets Afsluiter</summary>
        [IproxProperty(SequenceId = 1)]
        public PlainField Afsluiter {
          get {
            return this.fieldAfsluiter;
          }
          
          set {
            this.fieldAfsluiter.Value = (value ?? new PlainField()).Value;
          }
        }
      }
    }
    
    /// <summary>Standaardvelden (class)</summary>
    [IproxCluster(Alias = "Standaardvelden")]
    public partial class StandaardveldenCluster : IproxCluster {
      /// <summary>Naam (singular)</summary>
      [IproxCluster(Alias = "Naam")]
      private readonly Formulier.StandaardveldenCluster.NaamCluster fieldNaam = new Formulier.StandaardveldenCluster.NaamCluster();
      
      /// <summary>E-mail (singular)</summary>
      [IproxCluster(Alias = "E-mail")]
      private readonly Formulier.StandaardveldenCluster.EMailCluster fieldEMail = new Formulier.StandaardveldenCluster.EMailCluster();
      
      /// <summary>Organisatie (singular)</summary>
      [IproxCluster(Alias = "Organisatie")]
      private readonly Formulier.StandaardveldenCluster.OrganisatieCluster fieldOrganisatie = new Formulier.StandaardveldenCluster.OrganisatieCluster();
      
      /// <summary>Functie (singular)</summary>
      [IproxCluster(Alias = "Functie")]
      private readonly Formulier.StandaardveldenCluster.FunctieCluster fieldFunctie = new Formulier.StandaardveldenCluster.FunctieCluster();
      
      /// <summary>Adres (singular)</summary>
      [IproxCluster(Alias = "Adres")]
      private readonly Formulier.StandaardveldenCluster.AdresCluster fieldAdres = new Formulier.StandaardveldenCluster.AdresCluster();
      
      /// <summary>Postcode (singular)</summary>
      [IproxCluster(Alias = "Postcode")]
      private readonly Formulier.StandaardveldenCluster.PostcodeCluster fieldPostcode = new Formulier.StandaardveldenCluster.PostcodeCluster();
      
      /// <summary>Plaats (singular)</summary>
      [IproxCluster(Alias = "Plaats")]
      private readonly Formulier.StandaardveldenCluster.PlaatsCluster fieldPlaats = new Formulier.StandaardveldenCluster.PlaatsCluster();
      
      /// <summary>Land (singular)</summary>
      [IproxCluster(Alias = "Land")]
      private readonly Formulier.StandaardveldenCluster.LandCluster fieldLand = new Formulier.StandaardveldenCluster.LandCluster();
      
      /// <summary>Telefoon (singular)</summary>
      [IproxCluster(Alias = "Telefoon")]
      private readonly Formulier.StandaardveldenCluster.TelefoonCluster fieldTelefoon = new Formulier.StandaardveldenCluster.TelefoonCluster();
      
      /// <summary>Fax (singular)</summary>
      [IproxCluster(Alias = "Fax")]
      private readonly Formulier.StandaardveldenCluster.FaxCluster fieldFax = new Formulier.StandaardveldenCluster.FaxCluster();
      
      /// <summary>Uw bericht (singular)</summary>
      [IproxCluster(Alias = "Uw bericht")]
      private readonly Formulier.StandaardveldenCluster.UwBerichtCluster fieldUwBericht = new Formulier.StandaardveldenCluster.UwBerichtCluster();
      
      /// <summary>Captcha (singular)</summary>
      [IproxCluster(Alias = "Captcha")]
      private readonly Formulier.StandaardveldenCluster.CaptchaCluster fieldCaptcha = new Formulier.StandaardveldenCluster.CaptchaCluster();
      
      /// <summary>Gets Naam</summary>
      [IproxProperty(SequenceId = 0)]
      public Formulier.StandaardveldenCluster.NaamCluster Naam {
        get {
          return this.fieldNaam;
        }
      }
      
      /// <summary>Gets E-mail</summary>
      [IproxProperty(SequenceId = 1)]
      public Formulier.StandaardveldenCluster.EMailCluster EMail {
        get {
          return this.fieldEMail;
        }
      }
      
      /// <summary>Gets Organisatie</summary>
      [IproxProperty(SequenceId = 2)]
      public Formulier.StandaardveldenCluster.OrganisatieCluster Organisatie {
        get {
          return this.fieldOrganisatie;
        }
      }
      
      /// <summary>Gets Functie</summary>
      [IproxProperty(SequenceId = 3)]
      public Formulier.StandaardveldenCluster.FunctieCluster Functie {
        get {
          return this.fieldFunctie;
        }
      }
      
      /// <summary>Gets Adres</summary>
      [IproxProperty(SequenceId = 4)]
      public Formulier.StandaardveldenCluster.AdresCluster Adres {
        get {
          return this.fieldAdres;
        }
      }
      
      /// <summary>Gets Postcode</summary>
      [IproxProperty(SequenceId = 5)]
      public Formulier.StandaardveldenCluster.PostcodeCluster Postcode {
        get {
          return this.fieldPostcode;
        }
      }
      
      /// <summary>Gets Plaats</summary>
      [IproxProperty(SequenceId = 6)]
      public Formulier.StandaardveldenCluster.PlaatsCluster Plaats {
        get {
          return this.fieldPlaats;
        }
      }
      
      /// <summary>Gets Land</summary>
      [IproxProperty(SequenceId = 7)]
      public Formulier.StandaardveldenCluster.LandCluster Land {
        get {
          return this.fieldLand;
        }
      }
      
      /// <summary>Gets Telefoon</summary>
      [IproxProperty(SequenceId = 8)]
      public Formulier.StandaardveldenCluster.TelefoonCluster Telefoon {
        get {
          return this.fieldTelefoon;
        }
      }
      
      /// <summary>Gets Fax</summary>
      [IproxProperty(SequenceId = 9)]
      public Formulier.StandaardveldenCluster.FaxCluster Fax {
        get {
          return this.fieldFax;
        }
      }
      
      /// <summary>Gets Uw bericht</summary>
      [IproxProperty(SequenceId = 10)]
      public Formulier.StandaardveldenCluster.UwBerichtCluster UwBericht {
        get {
          return this.fieldUwBericht;
        }
      }
      
      /// <summary>Gets Captcha</summary>
      /// <remarks>Completely Automated Turing Test To Tell Computers and Humans Apart</remarks>
      [IproxProperty(SequenceId = 11)]
      public Formulier.StandaardveldenCluster.CaptchaCluster Captcha {
        get {
          return this.fieldCaptcha;
        }
      }
      
      /// <summary>Naam (class)</summary>
      [IproxCluster(Alias = "Naam")]
      public partial class NaamCluster : IproxCluster {
        /// <summary>Plaatsen (singular)</summary>
        [IproxField(Alias = "Plaatsen")]
        private readonly BooleanField fieldPlaatsen = new BooleanField();
        
        /// <summary>Verplicht (singular)</summary>
        [IproxField(Alias = "Verplicht")]
        private readonly BooleanField fieldVerplicht = new BooleanField();
        
        /// <summary>Placeholder (singular)</summary>
        [IproxField(Alias = "Placeholder")]
        private readonly PlainField fieldPlaceholder = new PlainField();
        
        /// <summary>Gets or sets Plaatsen</summary>
        [IproxProperty(SequenceId = 0)]
        public BooleanField Plaatsen {
          get {
            return this.fieldPlaatsen;
          }
          
          set {
            this.fieldPlaatsen.Value = (value ?? new BooleanField()).Value;
          }
        }
        
        /// <summary>Gets or sets Verplicht</summary>
        [IproxProperty(SequenceId = 1)]
        public BooleanField Verplicht {
          get {
            return this.fieldVerplicht;
          }
          
          set {
            this.fieldVerplicht.Value = (value ?? new BooleanField()).Value;
          }
        }
        
        /// <summary>Gets or sets Placeholder</summary>
        [IproxProperty(SequenceId = 2)]
        public PlainField Placeholder {
          get {
            return this.fieldPlaceholder;
          }
          
          set {
            this.fieldPlaceholder.Value = (value ?? new PlainField()).Value;
          }
        }
      }
      
      /// <summary>E-mail (class)</summary>
      [IproxCluster(Alias = "E-mail")]
      public partial class EMailCluster : IproxCluster {
        /// <summary>Plaatsen (singular)</summary>
        [IproxField(Alias = "Plaatsen")]
        private readonly BooleanField fieldPlaatsen = new BooleanField();
        
        /// <summary>Verplicht (singular)</summary>
        [IproxField(Alias = "Verplicht")]
        private readonly BooleanField fieldVerplicht = new BooleanField();
        
        /// <summary>Placeholder (singular)</summary>
        [IproxField(Alias = "Placeholder")]
        private readonly PlainField fieldPlaceholder = new PlainField();
        
        /// <summary>Gets or sets Plaatsen</summary>
        [IproxProperty(SequenceId = 0)]
        public BooleanField Plaatsen {
          get {
            return this.fieldPlaatsen;
          }
          
          set {
            this.fieldPlaatsen.Value = (value ?? new BooleanField()).Value;
          }
        }
        
        /// <summary>Gets or sets Verplicht</summary>
        [IproxProperty(SequenceId = 1)]
        public BooleanField Verplicht {
          get {
            return this.fieldVerplicht;
          }
          
          set {
            this.fieldVerplicht.Value = (value ?? new BooleanField()).Value;
          }
        }
        
        /// <summary>Gets or sets Placeholder</summary>
        [IproxProperty(SequenceId = 2)]
        public PlainField Placeholder {
          get {
            return this.fieldPlaceholder;
          }
          
          set {
            this.fieldPlaceholder.Value = (value ?? new PlainField()).Value;
          }
        }
      }
      
      /// <summary>Organisatie (class)</summary>
      [IproxCluster(Alias = "Organisatie")]
      public partial class OrganisatieCluster : IproxCluster {
        /// <summary>Plaatsen (singular)</summary>
        [IproxField(Alias = "Plaatsen")]
        private readonly BooleanField fieldPlaatsen = new BooleanField();
        
        /// <summary>Verplicht (singular)</summary>
        [IproxField(Alias = "Verplicht")]
        private readonly BooleanField fieldVerplicht = new BooleanField();
        
        /// <summary>Placeholder (singular)</summary>
        [IproxField(Alias = "Placeholder")]
        private readonly PlainField fieldPlaceholder = new PlainField();
        
        /// <summary>Gets or sets Plaatsen</summary>
        [IproxProperty(SequenceId = 0)]
        public BooleanField Plaatsen {
          get {
            return this.fieldPlaatsen;
          }
          
          set {
            this.fieldPlaatsen.Value = (value ?? new BooleanField()).Value;
          }
        }
        
        /// <summary>Gets or sets Verplicht</summary>
        [IproxProperty(SequenceId = 1)]
        public BooleanField Verplicht {
          get {
            return this.fieldVerplicht;
          }
          
          set {
            this.fieldVerplicht.Value = (value ?? new BooleanField()).Value;
          }
        }
        
        /// <summary>Gets or sets Placeholder</summary>
        [IproxProperty(SequenceId = 2)]
        public PlainField Placeholder {
          get {
            return this.fieldPlaceholder;
          }
          
          set {
            this.fieldPlaceholder.Value = (value ?? new PlainField()).Value;
          }
        }
      }
      
      /// <summary>Functie (class)</summary>
      [IproxCluster(Alias = "Functie")]
      public partial class FunctieCluster : IproxCluster {
        /// <summary>Plaatsen (singular)</summary>
        [IproxField(Alias = "Plaatsen")]
        private readonly BooleanField fieldPlaatsen = new BooleanField();
        
        /// <summary>Verplicht (singular)</summary>
        [IproxField(Alias = "Verplicht")]
        private readonly BooleanField fieldVerplicht = new BooleanField();
        
        /// <summary>Placeholder (singular)</summary>
        [IproxField(Alias = "Placeholder")]
        private readonly PlainField fieldPlaceholder = new PlainField();
        
        /// <summary>Gets or sets Plaatsen</summary>
        [IproxProperty(SequenceId = 0)]
        public BooleanField Plaatsen {
          get {
            return this.fieldPlaatsen;
          }
          
          set {
            this.fieldPlaatsen.Value = (value ?? new BooleanField()).Value;
          }
        }
        
        /// <summary>Gets or sets Verplicht</summary>
        [IproxProperty(SequenceId = 1)]
        public BooleanField Verplicht {
          get {
            return this.fieldVerplicht;
          }
          
          set {
            this.fieldVerplicht.Value = (value ?? new BooleanField()).Value;
          }
        }
        
        /// <summary>Gets or sets Placeholder</summary>
        [IproxProperty(SequenceId = 2)]
        public PlainField Placeholder {
          get {
            return this.fieldPlaceholder;
          }
          
          set {
            this.fieldPlaceholder.Value = (value ?? new PlainField()).Value;
          }
        }
      }
      
      /// <summary>Adres (class)</summary>
      [IproxCluster(Alias = "Adres")]
      public partial class AdresCluster : IproxCluster {
        /// <summary>Plaatsen (singular)</summary>
        [IproxField(Alias = "Plaatsen")]
        private readonly BooleanField fieldPlaatsen = new BooleanField();
        
        /// <summary>Verplicht (singular)</summary>
        [IproxField(Alias = "Verplicht")]
        private readonly BooleanField fieldVerplicht = new BooleanField();
        
        /// <summary>Placeholder (singular)</summary>
        [IproxField(Alias = "Placeholder")]
        private readonly PlainField fieldPlaceholder = new PlainField();
        
        /// <summary>Gets or sets Plaatsen</summary>
        [IproxProperty(SequenceId = 0)]
        public BooleanField Plaatsen {
          get {
            return this.fieldPlaatsen;
          }
          
          set {
            this.fieldPlaatsen.Value = (value ?? new BooleanField()).Value;
          }
        }
        
        /// <summary>Gets or sets Verplicht</summary>
        [IproxProperty(SequenceId = 1)]
        public BooleanField Verplicht {
          get {
            return this.fieldVerplicht;
          }
          
          set {
            this.fieldVerplicht.Value = (value ?? new BooleanField()).Value;
          }
        }
        
        /// <summary>Gets or sets Placeholder</summary>
        [IproxProperty(SequenceId = 2)]
        public PlainField Placeholder {
          get {
            return this.fieldPlaceholder;
          }
          
          set {
            this.fieldPlaceholder.Value = (value ?? new PlainField()).Value;
          }
        }
      }
      
      /// <summary>Postcode (class)</summary>
      [IproxCluster(Alias = "Postcode")]
      public partial class PostcodeCluster : IproxCluster {
        /// <summary>Plaatsen (singular)</summary>
        [IproxField(Alias = "Plaatsen")]
        private readonly BooleanField fieldPlaatsen = new BooleanField();
        
        /// <summary>Verplicht (singular)</summary>
        [IproxField(Alias = "Verplicht")]
        private readonly BooleanField fieldVerplicht = new BooleanField();
        
        /// <summary>Placeholder (singular)</summary>
        [IproxField(Alias = "Placeholder")]
        private readonly PlainField fieldPlaceholder = new PlainField();
        
        /// <summary>Gets or sets Plaatsen</summary>
        [IproxProperty(SequenceId = 0)]
        public BooleanField Plaatsen {
          get {
            return this.fieldPlaatsen;
          }
          
          set {
            this.fieldPlaatsen.Value = (value ?? new BooleanField()).Value;
          }
        }
        
        /// <summary>Gets or sets Verplicht</summary>
        [IproxProperty(SequenceId = 1)]
        public BooleanField Verplicht {
          get {
            return this.fieldVerplicht;
          }
          
          set {
            this.fieldVerplicht.Value = (value ?? new BooleanField()).Value;
          }
        }
        
        /// <summary>Gets or sets Placeholder</summary>
        [IproxProperty(SequenceId = 2)]
        public PlainField Placeholder {
          get {
            return this.fieldPlaceholder;
          }
          
          set {
            this.fieldPlaceholder.Value = (value ?? new PlainField()).Value;
          }
        }
      }
      
      /// <summary>Plaats (class)</summary>
      [IproxCluster(Alias = "Plaats")]
      public partial class PlaatsCluster : IproxCluster {
        /// <summary>Plaatsen (singular)</summary>
        [IproxField(Alias = "Plaatsen")]
        private readonly BooleanField fieldPlaatsen = new BooleanField();
        
        /// <summary>Verplicht (singular)</summary>
        [IproxField(Alias = "Verplicht")]
        private readonly BooleanField fieldVerplicht = new BooleanField();
        
        /// <summary>Placeholder (singular)</summary>
        [IproxField(Alias = "Placeholder")]
        private readonly PlainField fieldPlaceholder = new PlainField();
        
        /// <summary>Gets or sets Plaatsen</summary>
        [IproxProperty(SequenceId = 0)]
        public BooleanField Plaatsen {
          get {
            return this.fieldPlaatsen;
          }
          
          set {
            this.fieldPlaatsen.Value = (value ?? new BooleanField()).Value;
          }
        }
        
        /// <summary>Gets or sets Verplicht</summary>
        [IproxProperty(SequenceId = 1)]
        public BooleanField Verplicht {
          get {
            return this.fieldVerplicht;
          }
          
          set {
            this.fieldVerplicht.Value = (value ?? new BooleanField()).Value;
          }
        }
        
        /// <summary>Gets or sets Placeholder</summary>
        [IproxProperty(SequenceId = 2)]
        public PlainField Placeholder {
          get {
            return this.fieldPlaceholder;
          }
          
          set {
            this.fieldPlaceholder.Value = (value ?? new PlainField()).Value;
          }
        }
      }
      
      /// <summary>Land (class)</summary>
      [IproxCluster(Alias = "Land")]
      public partial class LandCluster : IproxCluster {
        /// <summary>Plaatsen (singular)</summary>
        [IproxField(Alias = "Plaatsen")]
        private readonly BooleanField fieldPlaatsen = new BooleanField();
        
        /// <summary>Verplicht (singular)</summary>
        [IproxField(Alias = "Verplicht")]
        private readonly BooleanField fieldVerplicht = new BooleanField();
        
        /// <summary>Placeholder (singular)</summary>
        [IproxField(Alias = "Placeholder")]
        private readonly PlainField fieldPlaceholder = new PlainField();
        
        /// <summary>Gets or sets Plaatsen</summary>
        [IproxProperty(SequenceId = 0)]
        public BooleanField Plaatsen {
          get {
            return this.fieldPlaatsen;
          }
          
          set {
            this.fieldPlaatsen.Value = (value ?? new BooleanField()).Value;
          }
        }
        
        /// <summary>Gets or sets Verplicht</summary>
        [IproxProperty(SequenceId = 1)]
        public BooleanField Verplicht {
          get {
            return this.fieldVerplicht;
          }
          
          set {
            this.fieldVerplicht.Value = (value ?? new BooleanField()).Value;
          }
        }
        
        /// <summary>Gets or sets Placeholder</summary>
        [IproxProperty(SequenceId = 2)]
        public PlainField Placeholder {
          get {
            return this.fieldPlaceholder;
          }
          
          set {
            this.fieldPlaceholder.Value = (value ?? new PlainField()).Value;
          }
        }
      }
      
      /// <summary>Telefoon (class)</summary>
      [IproxCluster(Alias = "Telefoon")]
      public partial class TelefoonCluster : IproxCluster {
        /// <summary>Plaatsen (singular)</summary>
        [IproxField(Alias = "Plaatsen")]
        private readonly BooleanField fieldPlaatsen = new BooleanField();
        
        /// <summary>Verplicht (singular)</summary>
        [IproxField(Alias = "Verplicht")]
        private readonly BooleanField fieldVerplicht = new BooleanField();
        
        /// <summary>Placeholder (singular)</summary>
        [IproxField(Alias = "Placeholder")]
        private readonly PlainField fieldPlaceholder = new PlainField();
        
        /// <summary>Gets or sets Plaatsen</summary>
        [IproxProperty(SequenceId = 0)]
        public BooleanField Plaatsen {
          get {
            return this.fieldPlaatsen;
          }
          
          set {
            this.fieldPlaatsen.Value = (value ?? new BooleanField()).Value;
          }
        }
        
        /// <summary>Gets or sets Verplicht</summary>
        [IproxProperty(SequenceId = 1)]
        public BooleanField Verplicht {
          get {
            return this.fieldVerplicht;
          }
          
          set {
            this.fieldVerplicht.Value = (value ?? new BooleanField()).Value;
          }
        }
        
        /// <summary>Gets or sets Placeholder</summary>
        [IproxProperty(SequenceId = 2)]
        public PlainField Placeholder {
          get {
            return this.fieldPlaceholder;
          }
          
          set {
            this.fieldPlaceholder.Value = (value ?? new PlainField()).Value;
          }
        }
      }
      
      /// <summary>Fax (class)</summary>
      [IproxCluster(Alias = "Fax")]
      public partial class FaxCluster : IproxCluster {
        /// <summary>Plaatsen (singular)</summary>
        [IproxField(Alias = "Plaatsen")]
        private readonly BooleanField fieldPlaatsen = new BooleanField();
        
        /// <summary>Verplicht (singular)</summary>
        [IproxField(Alias = "Verplicht")]
        private readonly BooleanField fieldVerplicht = new BooleanField();
        
        /// <summary>Placeholder (singular)</summary>
        [IproxField(Alias = "Placeholder")]
        private readonly PlainField fieldPlaceholder = new PlainField();
        
        /// <summary>Gets or sets Plaatsen</summary>
        [IproxProperty(SequenceId = 0)]
        public BooleanField Plaatsen {
          get {
            return this.fieldPlaatsen;
          }
          
          set {
            this.fieldPlaatsen.Value = (value ?? new BooleanField()).Value;
          }
        }
        
        /// <summary>Gets or sets Verplicht</summary>
        [IproxProperty(SequenceId = 1)]
        public BooleanField Verplicht {
          get {
            return this.fieldVerplicht;
          }
          
          set {
            this.fieldVerplicht.Value = (value ?? new BooleanField()).Value;
          }
        }
        
        /// <summary>Gets or sets Placeholder</summary>
        [IproxProperty(SequenceId = 2)]
        public PlainField Placeholder {
          get {
            return this.fieldPlaceholder;
          }
          
          set {
            this.fieldPlaceholder.Value = (value ?? new PlainField()).Value;
          }
        }
      }
      
      /// <summary>Uw bericht (class)</summary>
      [IproxCluster(Alias = "Uw bericht")]
      public partial class UwBerichtCluster : IproxCluster {
        /// <summary>Plaatsen (singular)</summary>
        [IproxField(Alias = "Plaatsen")]
        private readonly BooleanField fieldPlaatsen = new BooleanField();
        
        /// <summary>Verplicht (singular)</summary>
        [IproxField(Alias = "Verplicht")]
        private readonly BooleanField fieldVerplicht = new BooleanField();
        
        /// <summary>Placeholder (singular)</summary>
        [IproxField(Alias = "Placeholder")]
        private readonly PlainField fieldPlaceholder = new PlainField();
        
        /// <summary>Gets or sets Plaatsen</summary>
        [IproxProperty(SequenceId = 0)]
        public BooleanField Plaatsen {
          get {
            return this.fieldPlaatsen;
          }
          
          set {
            this.fieldPlaatsen.Value = (value ?? new BooleanField()).Value;
          }
        }
        
        /// <summary>Gets or sets Verplicht</summary>
        [IproxProperty(SequenceId = 1)]
        public BooleanField Verplicht {
          get {
            return this.fieldVerplicht;
          }
          
          set {
            this.fieldVerplicht.Value = (value ?? new BooleanField()).Value;
          }
        }
        
        /// <summary>Gets or sets Placeholder</summary>
        [IproxProperty(SequenceId = 2)]
        public PlainField Placeholder {
          get {
            return this.fieldPlaceholder;
          }
          
          set {
            this.fieldPlaceholder.Value = (value ?? new PlainField()).Value;
          }
        }
      }
      
      /// <summary>Captcha (class)</summary>
      /// <remarks>Completely Automated Turing Test To Tell Computers and Humans Apart</remarks>
      [IproxCluster(Alias = "Captcha")]
      public partial class CaptchaCluster : IproxCluster {
        /// <summary>Plaatsen (singular)</summary>
        [IproxField(Alias = "Plaatsen")]
        private readonly BooleanField fieldPlaatsen = new BooleanField();
        
        /// <summary>Verplicht (singular)</summary>
        [IproxField(Alias = "Verplicht")]
        private readonly BooleanField fieldVerplicht = new BooleanField();
        
        /// <summary>Placeholder (singular)</summary>
        [IproxField(Alias = "Placeholder")]
        private readonly PlainField fieldPlaceholder = new PlainField();
        
        /// <summary>Gets or sets Plaatsen</summary>
        [IproxProperty(SequenceId = 0)]
        public BooleanField Plaatsen {
          get {
            return this.fieldPlaatsen;
          }
          
          set {
            this.fieldPlaatsen.Value = (value ?? new BooleanField()).Value;
          }
        }
        
        /// <summary>Gets or sets Verplicht</summary>
        [IproxProperty(SequenceId = 1)]
        public BooleanField Verplicht {
          get {
            return this.fieldVerplicht;
          }
          
          set {
            this.fieldVerplicht.Value = (value ?? new BooleanField()).Value;
          }
        }
        
        /// <summary>Gets or sets Placeholder</summary>
        [IproxProperty(SequenceId = 2)]
        public PlainField Placeholder {
          get {
            return this.fieldPlaceholder;
          }
          
          set {
            this.fieldPlaceholder.Value = (value ?? new PlainField()).Value;
          }
        }
      }
    }
    
    /// <summary>Element (interface)</summary>
    /// <see cref="SelectievraagCluster" />
    /// <see cref="TekstveldCluster" />
    /// <see cref="TussentekstCluster" />
    /// <see cref="BijlageCluster" />
    /// <see cref="DatumCluster" />
    /// <see cref="TijdCluster" />
    /// <see cref="EditorCluster" />
    [IproxCluster(Alias = "Element")]
    public partial interface IElement : IIproxCluster {
    }
    
    /// <summary>Element (static class)</summary>
    [IproxCluster(Alias = "Element")]
    public static class ElementClusters {
      /// <summary>Selectievraag (class)</summary>
      [IproxCluster(Alias = "Selectievraag")]
      public partial class SelectievraagCluster : IproxCluster {
        /// <summary>Vraag (singular)</summary>
        [IproxCluster(Alias = "Vraag")]
        private readonly Formulier.ElementClusters.SelectievraagCluster.VraagCluster fieldVraag = new Formulier.ElementClusters.SelectievraagCluster.VraagCluster();
        
        /// <summary>Antwoord (collection)</summary>
        [IproxCluster(Alias = "Antwoord")]
        [IproxField(Alias = "Antwoord", SequenceId = 1)]
        private readonly IproxFieldsCollection<PlainField> fieldAntwoord = new IproxFieldsCollection<PlainField>();
        
        /// <summary>Gets Vraag</summary>
        [IproxProperty(SequenceId = 0)]
        public Formulier.ElementClusters.SelectievraagCluster.VraagCluster Vraag {
          get {
            return this.fieldVraag;
          }
        }
        
        /// <summary>Gets collection of Antwoord</summary>
        [IproxProperty(SequenceId = 1)]
        public IproxFieldsCollection<PlainField> Antwoord {
          get {
            return this.fieldAntwoord;
          }
        }
        
        /// <summary>Vraag (class)</summary>
        [IproxCluster(Alias = "Vraag")]
        public partial class VraagCluster : IproxCluster {
          /// <summary>Label (singular)</summary>
          [IproxField(Alias = "Label")]
          private readonly PlainField fieldLabel = new PlainField();
          
          /// <summary>Verplicht (singular)</summary>
          [IproxField(Alias = "Verplicht")]
          private readonly BooleanField fieldVerplicht = new BooleanField();
          
          /// <summary>Type (singular)</summary>
          [IproxField(Alias = "Type")]
          private readonly SelectionField fieldType = new SelectionField();
          
          /// <summary>Toelichting (singular)</summary>
          [IproxField(Alias = "Toelichting")]
          private readonly HtmlField fieldToelichting = new HtmlField();
          
          /// <summary>Gets or sets Label</summary>
          [IproxProperty(SequenceId = 0)]
          public PlainField Label {
            get {
              return this.fieldLabel;
            }
            
            set {
              this.fieldLabel.Value = (value ?? new PlainField()).Value;
            }
          }
          
          /// <summary>Gets or sets Verplicht</summary>
          [IproxProperty(SequenceId = 1)]
          public BooleanField Verplicht {
            get {
              return this.fieldVerplicht;
            }
            
            set {
              this.fieldVerplicht.Value = (value ?? new BooleanField()).Value;
            }
          }
          
          /// <summary>Gets or sets Type</summary>
          [IproxProperty(SequenceId = 2)]
          public SelectionField Type {
            get {
              return this.fieldType;
            }
            
            set {
              this.fieldType.Value = (value ?? new SelectionField()).Value;
            }
          }
          
          /// <summary>Gets or sets Toelichting</summary>
          [IproxProperty(SequenceId = 3)]
          public HtmlField Toelichting {
            get {
              return this.fieldToelichting;
            }
            
            set {
              this.fieldToelichting.Value = (value ?? new HtmlField()).Value;
            }
          }
        }
      }
      
      /// <content>SelectievraagCluster can be used for IElement</content>
      public partial class SelectievraagCluster : IElement {
      }
      
      /// <summary>Tekstveld (class)</summary>
      [IproxCluster(Alias = "Tekstveld")]
      public partial class TekstveldCluster : IproxCluster {
        /// <summary>Label (singular)</summary>
        [IproxField(Alias = "Label")]
        private readonly PlainField fieldLabel = new PlainField();
        
        /// <summary>Verplicht (singular)</summary>
        [IproxField(Alias = "Verplicht")]
        private readonly BooleanField fieldVerplicht = new BooleanField();
        
        /// <summary>Veldtype (singular)</summary>
        [IproxField(Alias = "Veldtype")]
        private readonly SelectionField fieldVeldtype = new SelectionField();
        
        /// <summary>Automatisch invullen (singular)</summary>
        [IproxField(Alias = "Automatisch invullen")]
        private readonly SelectionField fieldAutomatischInvullen = new SelectionField();
        
        /// <summary>Rijen (singular)</summary>
        [IproxField(Alias = "Rijen")]
        private readonly PlainField fieldRijen = new PlainField();
        
        /// <summary>Maximale lengte invoer (singular)</summary>
        [IproxField(Alias = "Maximale lengte invoer")]
        private readonly PlainField fieldMaximaleLengteInvoer = new PlainField();
        
        /// <summary>Placeholder (singular)</summary>
        [IproxField(Alias = "Placeholder")]
        private readonly PlainField fieldPlaceholder = new PlainField();
        
        /// <summary>Gets or sets Label</summary>
        [IproxProperty(SequenceId = 0)]
        public PlainField Label {
          get {
            return this.fieldLabel;
          }
          
          set {
            this.fieldLabel.Value = (value ?? new PlainField()).Value;
          }
        }
        
        /// <summary>Gets or sets Verplicht</summary>
        [IproxProperty(SequenceId = 1)]
        public BooleanField Verplicht {
          get {
            return this.fieldVerplicht;
          }
          
          set {
            this.fieldVerplicht.Value = (value ?? new BooleanField()).Value;
          }
        }
        
        /// <summary>Gets or sets Veldtype</summary>
        [IproxProperty(SequenceId = 2)]
        public SelectionField Veldtype {
          get {
            return this.fieldVeldtype;
          }
          
          set {
            this.fieldVeldtype.Value = (value ?? new SelectionField()).Value;
          }
        }
        
        /// <summary>Gets or sets Automatisch invullen</summary>
        [IproxProperty(SequenceId = 3)]
        public SelectionField AutomatischInvullen {
          get {
            return this.fieldAutomatischInvullen;
          }
          
          set {
            this.fieldAutomatischInvullen.Value = (value ?? new SelectionField()).Value;
          }
        }
        
        /// <summary>Gets or sets Rijen</summary>
        [IproxProperty(SequenceId = 4)]
        public PlainField Rijen {
          get {
            return this.fieldRijen;
          }
          
          set {
            this.fieldRijen.Value = (value ?? new PlainField()).Value;
          }
        }
        
        /// <summary>Gets or sets Maximale lengte invoer</summary>
        [IproxProperty(SequenceId = 5)]
        public PlainField MaximaleLengteInvoer {
          get {
            return this.fieldMaximaleLengteInvoer;
          }
          
          set {
            this.fieldMaximaleLengteInvoer.Value = (value ?? new PlainField()).Value;
          }
        }
        
        /// <summary>Gets or sets Placeholder</summary>
        [IproxProperty(SequenceId = 6)]
        public PlainField Placeholder {
          get {
            return this.fieldPlaceholder;
          }
          
          set {
            this.fieldPlaceholder.Value = (value ?? new PlainField()).Value;
          }
        }
      }
      
      /// <content>TekstveldCluster can be used for IElement</content>
      public partial class TekstveldCluster : IElement {
      }
      
      /// <summary>Tussentekst (class)</summary>
      [IproxCluster(Alias = "Tussentekst")]
      public partial class TussentekstCluster : IproxCluster {
        /// <summary>Tekst (singular)</summary>
        [IproxField(Alias = "Tekst")]
        private readonly HtmlField fieldTekst = new HtmlField();
        
        /// <summary>Tonen in mail (singular)</summary>
        [IproxField(Alias = "Tonen in mail")]
        private readonly BooleanField fieldTonenInMail = new BooleanField();
        
        /// <summary>Gets or sets Tekst</summary>
        [IproxProperty(SequenceId = 0)]
        public HtmlField Tekst {
          get {
            return this.fieldTekst;
          }
          
          set {
            this.fieldTekst.Value = (value ?? new HtmlField()).Value;
          }
        }
        
        /// <summary>Gets or sets Tonen in mail</summary>
        /// <remarks>Als dit veld AAN staat, wordt de tussentekst in de verzonden mail opgenomen.</remarks>
        [IproxProperty(SequenceId = 1)]
        public BooleanField TonenInMail {
          get {
            return this.fieldTonenInMail;
          }
          
          set {
            this.fieldTonenInMail.Value = (value ?? new BooleanField()).Value;
          }
        }
      }
      
      /// <content>TussentekstCluster can be used for IElement</content>
      public partial class TussentekstCluster : IElement {
      }
      
      /// <summary>Bijlage (class)</summary>
      [IproxCluster(Alias = "Bijlage")]
      public partial class BijlageCluster : IproxCluster {
        /// <summary>Label (singular)</summary>
        [IproxField(Alias = "Label")]
        private readonly PlainField fieldLabel = new PlainField();
        
        /// <summary>Verplicht (singular)</summary>
        [IproxField(Alias = "Verplicht")]
        private readonly BooleanField fieldVerplicht = new BooleanField();
        
        /// <summary>Gets or sets Label</summary>
        [IproxProperty(SequenceId = 0)]
        public PlainField Label {
          get {
            return this.fieldLabel;
          }
          
          set {
            this.fieldLabel.Value = (value ?? new PlainField()).Value;
          }
        }
        
        /// <summary>Gets or sets Verplicht</summary>
        [IproxProperty(SequenceId = 1)]
        public BooleanField Verplicht {
          get {
            return this.fieldVerplicht;
          }
          
          set {
            this.fieldVerplicht.Value = (value ?? new BooleanField()).Value;
          }
        }
      }
      
      /// <content>BijlageCluster can be used for IElement</content>
      public partial class BijlageCluster : IElement {
      }
      
      /// <summary>Datum (class)</summary>
      [IproxCluster(Alias = "Datum")]
      public partial class DatumCluster : IproxCluster {
        /// <summary>Label (singular)</summary>
        [IproxField(Alias = "Label")]
        private readonly PlainField fieldLabel = new PlainField();
        
        /// <summary>Verplicht (singular)</summary>
        [IproxField(Alias = "Verplicht")]
        private readonly BooleanField fieldVerplicht = new BooleanField();
        
        /// <summary>Gets or sets Label</summary>
        [IproxProperty(SequenceId = 0)]
        public PlainField Label {
          get {
            return this.fieldLabel;
          }
          
          set {
            this.fieldLabel.Value = (value ?? new PlainField()).Value;
          }
        }
        
        /// <summary>Gets or sets Verplicht</summary>
        [IproxProperty(SequenceId = 1)]
        public BooleanField Verplicht {
          get {
            return this.fieldVerplicht;
          }
          
          set {
            this.fieldVerplicht.Value = (value ?? new BooleanField()).Value;
          }
        }
      }
      
      /// <content>DatumCluster can be used for IElement</content>
      public partial class DatumCluster : IElement {
      }
      
      /// <summary>Tijd (class)</summary>
      [IproxCluster(Alias = "Tijd")]
      public partial class TijdCluster : IproxCluster {
        /// <summary>Label (singular)</summary>
        [IproxField(Alias = "Label")]
        private readonly PlainField fieldLabel = new PlainField();
        
        /// <summary>Verplicht (singular)</summary>
        [IproxField(Alias = "Verplicht")]
        private readonly BooleanField fieldVerplicht = new BooleanField();
        
        /// <summary>Gets or sets Label</summary>
        [IproxProperty(SequenceId = 0)]
        public PlainField Label {
          get {
            return this.fieldLabel;
          }
          
          set {
            this.fieldLabel.Value = (value ?? new PlainField()).Value;
          }
        }
        
        /// <summary>Gets or sets Verplicht</summary>
        [IproxProperty(SequenceId = 1)]
        public BooleanField Verplicht {
          get {
            return this.fieldVerplicht;
          }
          
          set {
            this.fieldVerplicht.Value = (value ?? new BooleanField()).Value;
          }
        }
      }
      
      /// <content>TijdCluster can be used for IElement</content>
      public partial class TijdCluster : IElement {
      }
      
      /// <summary>Editor (class)</summary>
      [IproxCluster(Alias = "Editor")]
      public partial class EditorCluster : IproxCluster {
        /// <summary>Label (singular)</summary>
        [IproxField(Alias = "Label")]
        private readonly PlainField fieldLabel = new PlainField();
        
        /// <summary>Rijen (singular)</summary>
        [IproxField(Alias = "Rijen")]
        private readonly PlainField fieldRijen = new PlainField();
        
        /// <summary>Gets or sets Label</summary>
        [IproxProperty(SequenceId = 0)]
        public PlainField Label {
          get {
            return this.fieldLabel;
          }
          
          set {
            this.fieldLabel.Value = (value ?? new PlainField()).Value;
          }
        }
        
        /// <summary>Gets or sets Rijen</summary>
        [IproxProperty(SequenceId = 1)]
        public PlainField Rijen {
          get {
            return this.fieldRijen;
          }
          
          set {
            this.fieldRijen.Value = (value ?? new PlainField()).Value;
          }
        }
      }
      
      /// <content>EditorCluster can be used for IElement</content>
      public partial class EditorCluster : IElement {
      }
    }
  }
  
  /// <summary>Selectie (class)</summary>
  [IproxPagetype(Alias = "selectie", Prototype = true)]
  public partial class Selectie : IproxCluster {
    /// <summary>Selectie instellingen (singular)</summary>
    [IproxCluster(Alias = "Selectie instellingen")]
    private readonly Selectie.SelectieInstellingenCluster fieldSelectieInstellingen = new Selectie.SelectieInstellingenCluster();
    
    /// <summary>Inleiding (singular)</summary>
    [IproxCluster(Alias = "Inhoud")]
    [IproxField(Alias = "Inleiding", SequenceId = 1)]
    private readonly HtmlField fieldInleiding = new HtmlField();
    
    /// <summary>Gets Selectie instellingen</summary>
    [IproxProperty(SequenceId = 0)]
    public Selectie.SelectieInstellingenCluster SelectieInstellingen {
      get {
        return this.fieldSelectieInstellingen;
      }
    }
    
    /// <summary>Gets or sets Inleiding</summary>
    [IproxProperty(SequenceId = 1)]
    public HtmlField Inleiding {
      get {
        return this.fieldInleiding;
      }
      
      set {
        this.fieldInleiding.Value = (value ?? new HtmlField()).Value;
      }
    }
    
    /// <summary>Selectie instellingen (class)</summary>
    [IproxCluster(Alias = "Selectie instellingen")]
    public partial class SelectieInstellingenCluster : IproxCluster {
      /// <summary>Eigenschappen (singular)</summary>
      [IproxCluster(Alias = "Eigenschappen")]
      private readonly Selectie.SelectieInstellingenCluster.EigenschappenCluster fieldEigenschappen = new Selectie.SelectieInstellingenCluster.EigenschappenCluster();
      
      /// <summary>Zoekopties (singular)</summary>
      [IproxCluster(Alias = "Zoekopties")]
      private readonly Selectie.SelectieInstellingenCluster.ZoekoptiesCluster fieldZoekopties = new Selectie.SelectieInstellingenCluster.ZoekoptiesCluster();
      
      /// <summary>Link (collection)</summary>
      [IproxCluster(Alias = "Startpunt")]
      [IproxField(Alias = "Link", SequenceId = 1)]
      private readonly IproxFieldsCollection<LinkField> fieldLink = new IproxFieldsCollection<LinkField>();
      
      /// <summary>Tekst bij niets gevonden (singular)</summary>
      [IproxCluster(Alias = "Algemeen")]
      [IproxField(Alias = "Tekst bij niets gevonden", SequenceId = 1)]
      private readonly HtmlField fieldTekstBijNietsGevonden = new HtmlField();
      
      /// <summary>Gets Eigenschappen</summary>
      [IproxProperty(SequenceId = 0)]
      public Selectie.SelectieInstellingenCluster.EigenschappenCluster Eigenschappen {
        get {
          return this.fieldEigenschappen;
        }
      }
      
      /// <summary>Gets Zoekopties</summary>
      [IproxProperty(SequenceId = 1)]
      public Selectie.SelectieInstellingenCluster.ZoekoptiesCluster Zoekopties {
        get {
          return this.fieldZoekopties;
        }
      }
      
      /// <summary>Gets collection of Link</summary>
      [IproxProperty(SequenceId = 2)]
      public IproxFieldsCollection<LinkField> Link {
        get {
          return this.fieldLink;
        }
      }
      
      /// <summary>Gets or sets Tekst bij niets gevonden</summary>
      [IproxProperty(SequenceId = 3)]
      public HtmlField TekstBijNietsGevonden {
        get {
          return this.fieldTekstBijNietsGevonden;
        }
        
        set {
          this.fieldTekstBijNietsGevonden.Value = (value ?? new HtmlField()).Value;
        }
      }
      
      /// <summary>Eigenschappen (class)</summary>
      [IproxCluster(Alias = "Eigenschappen")]
      public partial class EigenschappenCluster : IproxCluster {
        /// <summary>Samenvatting tonen (singular)</summary>
        [IproxField(Alias = "Samenvatting tonen")]
        private readonly BooleanField fieldSamenvattingTonen = new BooleanField();
        
        /// <summary>Trefwoord tonen (singular)</summary>
        [IproxField(Alias = "Trefwoord tonen")]
        private readonly BooleanField fieldTrefwoordTonen = new BooleanField();
        
        /// <summary>Afbeelding tonen (singular)</summary>
        [IproxField(Alias = "Afbeelding tonen")]
        private readonly BooleanField fieldAfbeeldingTonen = new BooleanField();
        
        /// <summary>Datum tonen (singular)</summary>
        [IproxField(Alias = "Datum tonen")]
        private readonly SelectionField fieldDatumTonen = new SelectionField();
        
        /// <summary>Ordening (singular)</summary>
        [IproxField(Alias = "Ordening")]
        private readonly SelectionField fieldOrdening = new SelectionField();
        
        /// <summary>Maximale ouderdom (singular)</summary>
        [IproxField(Alias = "Maximale ouderdom")]
        private readonly SelectionField fieldMaximaleOuderdom = new SelectionField();
        
        /// <summary>Selectiefilter (singular)</summary>
        [IproxField(Alias = "Selectiefilter")]
        private readonly PicklistField fieldSelectiefilter = new PicklistField();
        
        /// <summary>Trefwoordfilter (singular)</summary>
        [IproxField(Alias = "Trefwoordfilter")]
        private readonly KeywordsField fieldTrefwoordfilter = new KeywordsField();
        
        /// <summary>Trefwoordfilter AND operator (singular)</summary>
        [IproxField(Alias = "Trefwoordfilter AND operator")]
        private readonly BooleanField fieldTrefwoordfilterANDOperator = new BooleanField();
        
        /// <summary>Paginatypefilter (singular)</summary>
        [IproxField(Alias = "Paginatypefilter")]
        private readonly PicklistField fieldPaginatypefilter = new PicklistField();
        
        /// <summary>Aantal niveaus onder startpunt (singular)</summary>
        [IproxField(Alias = "Aantal niveaus onder startpunt")]
        private readonly PlainField fieldAantalNiveausOnderStartpunt = new PlainField();
        
        /// <summary>Items per pagina (singular)</summary>
        [IproxField(Alias = "Items per pagina")]
        private readonly PlainField fieldItemsPerPagina = new PlainField();
        
        /// <summary>Niet bladeren (singular)</summary>
        [IproxField(Alias = "Niet bladeren")]
        private readonly BooleanField fieldNietBladeren = new BooleanField();
        
        /// <summary>Indeling in maanden (singular)</summary>
        [IproxField(Alias = "Indeling in maanden")]
        private readonly BooleanField fieldIndelingInMaanden = new BooleanField();
        
        /// <summary>Toon op basis van begin- en einddatum (singular)</summary>
        [IproxField(Alias = "Toon op basis van begin- en einddatum")]
        private readonly BooleanField fieldToonOpBasisVanBeginEnEinddatum = new BooleanField();
        
        /// <summary>Zoekopdracht (singular)</summary>
        [IproxField(Alias = "Zoekopdracht")]
        private readonly PlainField fieldZoekopdracht = new PlainField();
        
        /// <summary>Categorie (singular)</summary>
        [IproxField(Alias = "Categorie")]
        private readonly SelectionField fieldCategorie = new SelectionField();
        
        /// <summary>Weergave (singular)</summary>
        [IproxField(Alias = "Weergave")]
        private readonly SelectionField fieldWeergave = new SelectionField();
        
        /// <summary>Facetten (singular)</summary>
        [IproxField(Alias = "Facetten")]
        private readonly PicklistField fieldFacetten = new PicklistField();
        
        /// <summary>Gets or sets Samenvatting tonen</summary>
        [IproxProperty(SequenceId = 0)]
        public BooleanField SamenvattingTonen {
          get {
            return this.fieldSamenvattingTonen;
          }
          
          set {
            this.fieldSamenvattingTonen.Value = (value ?? new BooleanField()).Value;
          }
        }
        
        /// <summary>Gets or sets Trefwoord tonen</summary>
        [IproxProperty(SequenceId = 1)]
        public BooleanField TrefwoordTonen {
          get {
            return this.fieldTrefwoordTonen;
          }
          
          set {
            this.fieldTrefwoordTonen.Value = (value ?? new BooleanField()).Value;
          }
        }
        
        /// <summary>Gets or sets Afbeelding tonen</summary>
        [IproxProperty(SequenceId = 2)]
        public BooleanField AfbeeldingTonen {
          get {
            return this.fieldAfbeeldingTonen;
          }
          
          set {
            this.fieldAfbeeldingTonen.Value = (value ?? new BooleanField()).Value;
          }
        }
        
        /// <summary>Gets or sets Datum tonen</summary>
        [IproxProperty(SequenceId = 3)]
        public SelectionField DatumTonen {
          get {
            return this.fieldDatumTonen;
          }
          
          set {
            this.fieldDatumTonen.Value = (value ?? new SelectionField()).Value;
          }
        }
        
        /// <summary>Gets or sets Ordening</summary>
        [IproxProperty(SequenceId = 4)]
        public SelectionField Ordening {
          get {
            return this.fieldOrdening;
          }
          
          set {
            this.fieldOrdening.Value = (value ?? new SelectionField()).Value;
          }
        }
        
        /// <summary>Gets or sets Maximale ouderdom</summary>
        [IproxProperty(SequenceId = 5)]
        public SelectionField MaximaleOuderdom {
          get {
            return this.fieldMaximaleOuderdom;
          }
          
          set {
            this.fieldMaximaleOuderdom.Value = (value ?? new SelectionField()).Value;
          }
        }
        
        /// <summary>Gets or sets Selectiefilter</summary>
        [IproxProperty(SequenceId = 6)]
        public PicklistField Selectiefilter {
          get {
            return this.fieldSelectiefilter;
          }
          
          set {
            this.fieldSelectiefilter.Value = (value ?? new PicklistField()).Value;
          }
        }
        
        /// <summary>Gets or sets Trefwoordfilter</summary>
        [IproxProperty(SequenceId = 7)]
        public KeywordsField Trefwoordfilter {
          get {
            return this.fieldTrefwoordfilter;
          }
          
          set {
            this.fieldTrefwoordfilter.Value = (value ?? new KeywordsField()).Value;
          }
        }
        
        /// <summary>Gets or sets Trefwoordfilter AND operator</summary>
        [IproxProperty(SequenceId = 8)]
        public BooleanField TrefwoordfilterANDOperator {
          get {
            return this.fieldTrefwoordfilterANDOperator;
          }
          
          set {
            this.fieldTrefwoordfilterANDOperator.Value = (value ?? new BooleanField()).Value;
          }
        }
        
        /// <summary>Gets or sets Paginatypefilter</summary>
        [IproxProperty(SequenceId = 9)]
        public PicklistField Paginatypefilter {
          get {
            return this.fieldPaginatypefilter;
          }
          
          set {
            this.fieldPaginatypefilter.Value = (value ?? new PicklistField()).Value;
          }
        }
        
        /// <summary>Gets or sets Aantal niveaus onder startpunt</summary>
        [IproxProperty(SequenceId = 10)]
        public PlainField AantalNiveausOnderStartpunt {
          get {
            return this.fieldAantalNiveausOnderStartpunt;
          }
          
          set {
            this.fieldAantalNiveausOnderStartpunt.Value = (value ?? new PlainField()).Value;
          }
        }
        
        /// <summary>Gets or sets Items per pagina</summary>
        [IproxProperty(SequenceId = 11)]
        public PlainField ItemsPerPagina {
          get {
            return this.fieldItemsPerPagina;
          }
          
          set {
            this.fieldItemsPerPagina.Value = (value ?? new PlainField()).Value;
          }
        }
        
        /// <summary>Gets or sets Niet bladeren</summary>
        /// <remarks>AAN: veld "Items per pagina" wordt genegeerd</remarks>
        [IproxProperty(SequenceId = 12)]
        public BooleanField NietBladeren {
          get {
            return this.fieldNietBladeren;
          }
          
          set {
            this.fieldNietBladeren.Value = (value ?? new BooleanField()).Value;
          }
        }
        
        /// <summary>Gets or sets Indeling in maanden</summary>
        [IproxProperty(SequenceId = 13)]
        public BooleanField IndelingInMaanden {
          get {
            return this.fieldIndelingInMaanden;
          }
          
          set {
            this.fieldIndelingInMaanden.Value = (value ?? new BooleanField()).Value;
          }
        }
        
        /// <summary>Gets or sets Toon op basis van begin- en einddatum</summary>
        [IproxProperty(SequenceId = 14)]
        public BooleanField ToonOpBasisVanBeginEnEinddatum {
          get {
            return this.fieldToonOpBasisVanBeginEnEinddatum;
          }
          
          set {
            this.fieldToonOpBasisVanBeginEnEinddatum.Value = (value ?? new BooleanField()).Value;
          }
        }
        
        /// <summary>Gets or sets Zoekopdracht</summary>
        [IproxProperty(SequenceId = 15)]
        public PlainField Zoekopdracht {
          get {
            return this.fieldZoekopdracht;
          }
          
          set {
            this.fieldZoekopdracht.Value = (value ?? new PlainField()).Value;
          }
        }
        
        /// <summary>Gets or sets Categorie</summary>
        [IproxProperty(SequenceId = 16)]
        public SelectionField Categorie {
          get {
            return this.fieldCategorie;
          }
          
          set {
            this.fieldCategorie.Value = (value ?? new SelectionField()).Value;
          }
        }
        
        /// <summary>Gets or sets Weergave</summary>
        [IproxProperty(SequenceId = 17)]
        public SelectionField Weergave {
          get {
            return this.fieldWeergave;
          }
          
          set {
            this.fieldWeergave.Value = (value ?? new SelectionField()).Value;
          }
        }
        
        /// <summary>Gets or sets Facetten</summary>
        [IproxProperty(SequenceId = 18)]
        public PicklistField Facetten {
          get {
            return this.fieldFacetten;
          }
          
          set {
            this.fieldFacetten.Value = (value ?? new PicklistField()).Value;
          }
        }
      }
      
      /// <summary>Zoekopties (class)</summary>
      [IproxCluster(Alias = "Zoekopties")]
      public partial class ZoekoptiesCluster : IproxCluster {
        /// <summary>Vrij zoeken (singular)</summary>
        [IproxField(Alias = "Vrij zoeken")]
        private readonly BooleanField fieldVrijZoeken = new BooleanField();
        
        /// <summary>Zoekmethode (singular)</summary>
        [IproxField(Alias = "Zoekmethode")]
        private readonly BooleanField fieldZoekmethode = new BooleanField();
        
        /// <summary>Zoekbereik (singular)</summary>
        [IproxField(Alias = "Zoekbereik")]
        private readonly BooleanField fieldZoekbereik = new BooleanField();
        
        /// <summary>Datum (singular)</summary>
        [IproxField(Alias = "Datum")]
        private readonly BooleanField fieldDatum = new BooleanField();
        
        /// <summary>Trefwoorden (singular)</summary>
        [IproxField(Alias = "Trefwoorden")]
        private readonly BooleanField fieldTrefwoorden = new BooleanField();
        
        /// <summary>Paginatypefilter (singular)</summary>
        [IproxField(Alias = "Paginatypefilter")]
        private readonly BooleanField fieldPaginatypefilter = new BooleanField();
        
        /// <summary>Toon resultaten na zoekactie (singular)</summary>
        [IproxField(Alias = "Toon resultaten na zoekactie")]
        private readonly BooleanField fieldToonResultatenNaZoekactie = new BooleanField();
        
        /// <summary>Zoekformulier persistent (singular)</summary>
        [IproxField(Alias = "Zoekformulier persistent")]
        private readonly BooleanField fieldZoekformulierPersistent = new BooleanField();
        
        /// <summary>Ordening (singular)</summary>
        [IproxField(Alias = "Ordening")]
        private readonly PicklistField fieldOrdening = new PicklistField();
        
        /// <summary>Facetten (singular)</summary>
        [IproxField(Alias = "Facetten")]
        private readonly PicklistField fieldFacetten = new PicklistField();
        
        /// <summary>Filteren bij typen (singular)</summary>
        [IproxField(Alias = "Filteren bij typen")]
        private readonly BooleanField fieldFilterenBijTypen = new BooleanField();
        
        /// <summary>Gets or sets Vrij zoeken</summary>
        [IproxProperty(SequenceId = 0)]
        public BooleanField VrijZoeken {
          get {
            return this.fieldVrijZoeken;
          }
          
          set {
            this.fieldVrijZoeken.Value = (value ?? new BooleanField()).Value;
          }
        }
        
        /// <summary>Gets or sets Zoekmethode</summary>
        [IproxProperty(SequenceId = 1)]
        public BooleanField Zoekmethode {
          get {
            return this.fieldZoekmethode;
          }
          
          set {
            this.fieldZoekmethode.Value = (value ?? new BooleanField()).Value;
          }
        }
        
        /// <summary>Gets or sets Zoekbereik</summary>
        [IproxProperty(SequenceId = 2)]
        public BooleanField Zoekbereik {
          get {
            return this.fieldZoekbereik;
          }
          
          set {
            this.fieldZoekbereik.Value = (value ?? new BooleanField()).Value;
          }
        }
        
        /// <summary>Gets or sets Datum</summary>
        /// <remarks>toon filter (maand/jaar)</remarks>
        [IproxProperty(SequenceId = 3)]
        public BooleanField Datum {
          get {
            return this.fieldDatum;
          }
          
          set {
            this.fieldDatum.Value = (value ?? new BooleanField()).Value;
          }
        }
        
        /// <summary>Gets or sets Trefwoorden</summary>
        [IproxProperty(SequenceId = 4)]
        public BooleanField Trefwoorden {
          get {
            return this.fieldTrefwoorden;
          }
          
          set {
            this.fieldTrefwoorden.Value = (value ?? new BooleanField()).Value;
          }
        }
        
        /// <summary>Gets or sets Paginatypefilter</summary>
        [IproxProperty(SequenceId = 5)]
        public BooleanField Paginatypefilter {
          get {
            return this.fieldPaginatypefilter;
          }
          
          set {
            this.fieldPaginatypefilter.Value = (value ?? new BooleanField()).Value;
          }
        }
        
        /// <summary>Gets or sets Toon resultaten na zoekactie</summary>
        [IproxProperty(SequenceId = 6)]
        public BooleanField ToonResultatenNaZoekactie {
          get {
            return this.fieldToonResultatenNaZoekactie;
          }
          
          set {
            this.fieldToonResultatenNaZoekactie.Value = (value ?? new BooleanField()).Value;
          }
        }
        
        /// <summary>Gets or sets Zoekformulier persistent</summary>
        [IproxProperty(SequenceId = 7)]
        public BooleanField ZoekformulierPersistent {
          get {
            return this.fieldZoekformulierPersistent;
          }
          
          set {
            this.fieldZoekformulierPersistent.Value = (value ?? new BooleanField()).Value;
          }
        }
        
        /// <summary>Gets or sets Ordening</summary>
        [IproxProperty(SequenceId = 8)]
        public PicklistField Ordening {
          get {
            return this.fieldOrdening;
          }
          
          set {
            this.fieldOrdening.Value = (value ?? new PicklistField()).Value;
          }
        }
        
        /// <summary>Gets or sets Facetten</summary>
        [IproxProperty(SequenceId = 9)]
        public PicklistField Facetten {
          get {
            return this.fieldFacetten;
          }
          
          set {
            this.fieldFacetten.Value = (value ?? new PicklistField()).Value;
          }
        }
        
        /// <summary>Gets or sets Filteren bij typen</summary>
        [IproxProperty(SequenceId = 10)]
        public BooleanField FilterenBijTypen {
          get {
            return this.fieldFilterenBijTypen;
          }
          
          set {
            this.fieldFilterenBijTypen.Value = (value ?? new BooleanField()).Value;
          }
        }
      }
    }
  }
  
  /// <summary>Zie ook (interface)</summary>
  /// <see cref="InhoudCluster" />
  /// <see cref="MediawidgetCluster" />
  /// <see cref="FotoCluster" />
  /// <see cref="VraagEnAntwoordIndexCluster" />
  /// <see cref="TrefwoordCluster" />
  /// <see cref="OpiniepeilingCluster" />
  /// <see cref="FotoalbumCluster" />
  /// <see cref="NavigatieCluster" />
  /// <see cref="MediaCluster" />
  [IproxPagetype(Alias = "seealso", Prototype = true)]
  [IproxCluster(Alias = "Blokken", SequenceId = 1)]
  public partial interface ISeealso : IIproxCluster {
  }
  
  /// <summary>Zie ook (static class)</summary>
  [IproxPagetype(Alias = "seealso", Prototype = true)]
  [IproxCluster(Alias = "Blokken", SequenceId = 1)]
  public static class SeealsoClusters {
    /// <summary>Inhoud (class)</summary>
    [IproxCluster(Alias = "Inhoud", Prototype = true)]
    public partial class InhoudCluster : Inhoud {
    }
    
    /// <content>InhoudCluster can be used for ISeealso</content>
    public partial class InhoudCluster : ISeealso {
    }
    
    /// <summary>Mediawidget (class)</summary>
    [IproxCluster(Alias = "Mediawidget", Prototype = true)]
    public partial class MediawidgetCluster : Mediawidget {
    }
    
    /// <content>MediawidgetCluster can be used for ISeealso</content>
    public partial class MediawidgetCluster : ISeealso {
    }
    
    /// <summary>Foto (class)</summary>
    [IproxCluster(Alias = "Foto", Prototype = true)]
    public partial class FotoCluster : Foto {
    }
    
    /// <content>FotoCluster can be used for ISeealso</content>
    public partial class FotoCluster : ISeealso {
    }
    
    /// <summary>Vraag en antwoord index (class)</summary>
    [IproxCluster(Alias = "Vraag en antwoord index", Prototype = true)]
    public partial class VraagEnAntwoordIndexCluster : SocialQuestions {
    }
    
    /// <content>VraagEnAntwoordIndexCluster can be used for ISeealso</content>
    public partial class VraagEnAntwoordIndexCluster : ISeealso {
    }
    
    /// <summary>Trefwoord (class)</summary>
    [IproxCluster(Alias = "Trefwoord", Prototype = true)]
    public partial class TrefwoordCluster : Trefwoord {
    }
    
    /// <content>TrefwoordCluster can be used for ISeealso</content>
    public partial class TrefwoordCluster : ISeealso {
    }
    
    /// <summary>Opiniepeiling (class)</summary>
    [IproxCluster(Alias = "Opiniepeiling", Prototype = true)]
    public partial class OpiniepeilingCluster : OpiniepeilingBlok {
    }
    
    /// <content>OpiniepeilingCluster can be used for ISeealso</content>
    public partial class OpiniepeilingCluster : ISeealso {
    }
    
    /// <summary>Fotoalbum (class)</summary>
    [IproxCluster(Alias = "Fotoalbum", Prototype = true)]
    public partial class FotoalbumCluster : FotoalbumPrototype {
    }
    
    /// <content>FotoalbumCluster can be used for ISeealso</content>
    public partial class FotoalbumCluster : ISeealso {
    }
    
    /// <summary>Navigatie (class)</summary>
    [IproxCluster(Alias = "Navigatie", Prototype = true)]
    public partial class NavigatieCluster : Navigatie {
    }
    
    /// <content>NavigatieCluster can be used for ISeealso</content>
    public partial class NavigatieCluster : ISeealso {
    }
    
    /// <summary>Media (class)</summary>
    [IproxCluster(Alias = "Media", Prototype = true)]
    public partial class MediaCluster : MediaBlok {
    }
    
    /// <content>MediaCluster can be used for ISeealso</content>
    public partial class MediaCluster : ISeealso {
    }
  }
  
  /// <summary>Applicatielink (class)</summary>
  [IproxPagetype(Alias = "applicatielink", Prototype = true)]
  public partial class ApplicatielinkPrototype : IproxCluster {
    /// <summary>Instellingen (singular)</summary>
    [IproxCluster(Alias = "Instellingen")]
    private readonly ApplicatielinkPrototype.InstellingenCluster fieldInstellingen = new ApplicatielinkPrototype.InstellingenCluster();
    
    /// <summary>Inhoud (singular)</summary>
    [IproxCluster(Alias = "Inhoud")]
    private readonly ApplicatielinkPrototype.InhoudCluster fieldInhoud = new ApplicatielinkPrototype.InhoudCluster();
    
    /// <summary>Gets Instellingen</summary>
    [IproxProperty(SequenceId = 0)]
    public ApplicatielinkPrototype.InstellingenCluster Instellingen {
      get {
        return this.fieldInstellingen;
      }
    }
    
    /// <summary>Gets Inhoud</summary>
    [IproxProperty(SequenceId = 1)]
    public ApplicatielinkPrototype.InhoudCluster Inhoud {
      get {
        return this.fieldInhoud;
      }
    }
    
    /// <summary>Instellingen (class)</summary>
    [IproxCluster(Alias = "Instellingen")]
    public partial class InstellingenCluster : IproxCluster {
      /// <summary>Nieuw venster (singular)</summary>
      [IproxField(Alias = "Nieuw venster")]
      private readonly BooleanField fieldNieuwVenster = new BooleanField();
      
      /// <summary>Hoogte (singular)</summary>
      [IproxField(Alias = "Hoogte")]
      private readonly PlainField fieldHoogte = new PlainField();
      
      /// <summary>Gets or sets Nieuw venster</summary>
      /// <remarks>Aan = de applicatie verschijnt in een nieuw browserwindow.</remarks>
      [IproxProperty(SequenceId = 0)]
      public BooleanField NieuwVenster {
        get {
          return this.fieldNieuwVenster;
        }
        
        set {
          this.fieldNieuwVenster.Value = (value ?? new BooleanField()).Value;
        }
      }
      
      /// <summary>Gets or sets Hoogte</summary>
      [IproxProperty(SequenceId = 1)]
      public PlainField Hoogte {
        get {
          return this.fieldHoogte;
        }
        
        set {
          this.fieldHoogte.Value = (value ?? new PlainField()).Value;
        }
      }
    }
    
    /// <summary>Inhoud (class)</summary>
    [IproxCluster(Alias = "Inhoud")]
    public partial class InhoudCluster : IproxCluster {
      /// <summary>Inleiding (singular)</summary>
      [IproxField(Alias = "Inleiding")]
      private readonly HtmlField fieldInleiding = new HtmlField();
      
      /// <summary>Naam (singular)</summary>
      [IproxField(Alias = "Naam")]
      private readonly PlainField fieldNaam = new PlainField();
      
      /// <summary>Internet adres (singular)</summary>
      [IproxField(Alias = "Internet adres")]
      private readonly AddressField fieldInternetAdres = new AddressField();
      
      /// <summary>Gets or sets Inleiding</summary>
      [IproxProperty(SequenceId = 0)]
      public HtmlField Inleiding {
        get {
          return this.fieldInleiding;
        }
        
        set {
          this.fieldInleiding.Value = (value ?? new HtmlField()).Value;
        }
      }
      
      /// <summary>Gets or sets Naam</summary>
      /// <remarks>De naam van de applicatie, dit wordt het linklabel als er voor een popup wordt gekozen.</remarks>
      [IproxProperty(SequenceId = 1)]
      public PlainField Naam {
        get {
          return this.fieldNaam;
        }
        
        set {
          this.fieldNaam.Value = (value ?? new PlainField()).Value;
        }
      }
      
      /// <summary>Gets or sets Internet adres</summary>
      /// <remarks>URL van de webapplicatie</remarks>
      [IproxProperty(SequenceId = 2)]
      public AddressField InternetAdres {
        get {
          return this.fieldInternetAdres;
        }
        
        set {
          this.fieldInternetAdres.Value = (value ?? new AddressField()).Value;
        }
      }
    }
  }
  
  /// <summary>Opiniepeiling (class)</summary>
  [IproxPagetype(Alias = "opiniepeiling", Prototype = true)]
  public partial class OpiniepeilingPrototype : IproxCluster {
    /// <summary>Instellingen (singular)</summary>
    [IproxCluster(Alias = "Instellingen")]
    private readonly OpiniepeilingPrototype.InstellingenCluster fieldInstellingen = new OpiniepeilingPrototype.InstellingenCluster();
    
    /// <summary>Tekst (singular)</summary>
    [IproxCluster(Alias = "Conclusie")]
    [IproxField(Alias = "Tekst", SequenceId = 1)]
    private readonly HtmlField fieldTekst = new HtmlField();
    
    /// <summary>Inhoud (singular)</summary>
    [IproxCluster(Alias = "Inhoud")]
    private readonly OpiniepeilingPrototype.InhoudCluster fieldInhoud = new OpiniepeilingPrototype.InhoudCluster();
    
    /// <summary>Antwoord (collection)</summary>
    [IproxCluster(Alias = "Antwoord")]
    private readonly IproxClustersCollection<OpiniepeilingPrototype.AntwoordCluster> fieldAntwoord = new IproxClustersCollection<OpiniepeilingPrototype.AntwoordCluster>();
    
    /// <summary>Gets Instellingen</summary>
    [IproxProperty(SequenceId = 0)]
    public OpiniepeilingPrototype.InstellingenCluster Instellingen {
      get {
        return this.fieldInstellingen;
      }
    }
    
    /// <summary>Gets or sets Tekst</summary>
    [IproxProperty(SequenceId = 1)]
    public HtmlField Tekst {
      get {
        return this.fieldTekst;
      }
      
      set {
        this.fieldTekst.Value = (value ?? new HtmlField()).Value;
      }
    }
    
    /// <summary>Gets Inhoud</summary>
    [IproxProperty(SequenceId = 2)]
    public OpiniepeilingPrototype.InhoudCluster Inhoud {
      get {
        return this.fieldInhoud;
      }
    }
    
    /// <summary>Gets collection of Antwoord</summary>
    [IproxProperty(SequenceId = 3)]
    public IproxClustersCollection<OpiniepeilingPrototype.AntwoordCluster> Antwoord {
      get {
        return this.fieldAntwoord;
      }
    }
    
    /// <summary>Instellingen (class)</summary>
    [IproxCluster(Alias = "Instellingen")]
    public partial class InstellingenCluster : IproxCluster {
      /// <summary>Actief (singular)</summary>
      [IproxField(Alias = "Actief")]
      private readonly BooleanField fieldActief = new BooleanField();
      
      /// <summary>Stemmen verplicht (singular)</summary>
      [IproxField(Alias = "Stemmen verplicht")]
      private readonly BooleanField fieldStemmenVerplicht = new BooleanField();
      
      /// <summary>Cookie plaatsen (singular)</summary>
      [IproxField(Alias = "Cookie plaatsen")]
      private readonly BooleanField fieldCookiePlaatsen = new BooleanField();
      
      /// <summary>Aantallen tonen (singular)</summary>
      [IproxField(Alias = "Aantallen tonen")]
      private readonly BooleanField fieldAantallenTonen = new BooleanField();
      
      /// <summary>Bericht na verzending (singular)</summary>
      [IproxField(Alias = "Bericht na verzending")]
      private readonly HtmlField fieldBerichtNaVerzending = new HtmlField();
      
      /// <summary>Resultaat openen in nieuw venster (singular)</summary>
      [IproxField(Alias = "Resultaat openen in nieuw venster")]
      private readonly BooleanField fieldResultaatOpenenInNieuwVenster = new BooleanField();
      
      /// <summary>Gets or sets Actief</summary>
      [IproxProperty(SequenceId = 0)]
      public BooleanField Actief {
        get {
          return this.fieldActief;
        }
        
        set {
          this.fieldActief.Value = (value ?? new BooleanField()).Value;
        }
      }
      
      /// <summary>Gets or sets Stemmen verplicht</summary>
      [IproxProperty(SequenceId = 1)]
      public BooleanField StemmenVerplicht {
        get {
          return this.fieldStemmenVerplicht;
        }
        
        set {
          this.fieldStemmenVerplicht.Value = (value ?? new BooleanField()).Value;
        }
      }
      
      /// <summary>Gets or sets Cookie plaatsen</summary>
      [IproxProperty(SequenceId = 2)]
      public BooleanField CookiePlaatsen {
        get {
          return this.fieldCookiePlaatsen;
        }
        
        set {
          this.fieldCookiePlaatsen.Value = (value ?? new BooleanField()).Value;
        }
      }
      
      /// <summary>Gets or sets Aantallen tonen</summary>
      [IproxProperty(SequenceId = 3)]
      public BooleanField AantallenTonen {
        get {
          return this.fieldAantallenTonen;
        }
        
        set {
          this.fieldAantallenTonen.Value = (value ?? new BooleanField()).Value;
        }
      }
      
      /// <summary>Gets or sets Bericht na verzending</summary>
      [IproxProperty(SequenceId = 4)]
      public HtmlField BerichtNaVerzending {
        get {
          return this.fieldBerichtNaVerzending;
        }
        
        set {
          this.fieldBerichtNaVerzending.Value = (value ?? new HtmlField()).Value;
        }
      }
      
      /// <summary>Gets or sets Resultaat openen in nieuw venster</summary>
      [IproxProperty(SequenceId = 5)]
      public BooleanField ResultaatOpenenInNieuwVenster {
        get {
          return this.fieldResultaatOpenenInNieuwVenster;
        }
        
        set {
          this.fieldResultaatOpenenInNieuwVenster.Value = (value ?? new BooleanField()).Value;
        }
      }
    }
    
    /// <summary>Inhoud (class)</summary>
    [IproxCluster(Alias = "Inhoud")]
    public partial class InhoudCluster : IproxCluster {
      /// <summary>Stelling (singular)</summary>
      [IproxField(Alias = "Stelling")]
      private readonly HtmlField fieldStelling = new HtmlField();
      
      /// <summary>Inleiding resultaten (singular)</summary>
      [IproxField(Alias = "Inleiding resultaten")]
      private readonly HtmlField fieldInleidingResultaten = new HtmlField();
      
      /// <summary>Gets or sets Stelling</summary>
      [IproxProperty(SequenceId = 0)]
      public HtmlField Stelling {
        get {
          return this.fieldStelling;
        }
        
        set {
          this.fieldStelling.Value = (value ?? new HtmlField()).Value;
        }
      }
      
      /// <summary>Gets or sets Inleiding resultaten</summary>
      [IproxProperty(SequenceId = 1)]
      public HtmlField InleidingResultaten {
        get {
          return this.fieldInleidingResultaten;
        }
        
        set {
          this.fieldInleidingResultaten.Value = (value ?? new HtmlField()).Value;
        }
      }
    }
    
    /// <summary>Antwoord (class)</summary>
    [IproxCluster(Alias = "Antwoord")]
    public partial class AntwoordCluster : IproxCluster {
      /// <summary>Tekst (singular)</summary>
      [IproxField(Alias = "Tekst")]
      private readonly PlainField fieldTekst = new PlainField();
      
      /// <summary>Gets or sets Tekst</summary>
      [IproxProperty(SequenceId = 0)]
      public PlainField Tekst {
        get {
          return this.fieldTekst;
        }
        
        set {
          this.fieldTekst.Value = (value ?? new PlainField()).Value;
        }
      }
    }
  }
  
  /// <summary>Social - Evenementen (class)</summary>
  /// <remarks>grid-blok</remarks>
  [IproxPagetype(Alias = "social-calendar", Prototype = true)]
  public partial class SocialCalendar : IproxCluster {
    /// <summary>title (singular)</summary>
    [IproxCluster(Alias = "Blok")]
    [IproxField(Alias = "title", SequenceId = 1)]
    private readonly PlainField fieldTitle = new PlainField();
    
    /// <summary>itemsPerPage (singular)</summary>
    [IproxCluster(Alias = "Blok")]
    [IproxField(Alias = "itemsPerPage", SequenceId = 1)]
    private readonly PlainField fieldItemsPerPage = new PlainField();
    
    /// <summary>Liken (singular)</summary>
    [IproxCluster(Alias = "Blok")]
    [IproxField(Alias = "Liken", SequenceId = 1)]
    private readonly BooleanField fieldLiken = new BooleanField();
    
    /// <summary>enableTagging (singular)</summary>
    [IproxCluster(Alias = "Blok")]
    [IproxField(Alias = "enableTagging", SequenceId = 1)]
    private readonly BooleanField fieldEnableTagging = new BooleanField();
    
    /// <summary>Reageren (singular)</summary>
    [IproxCluster(Alias = "Blok")]
    [IproxField(Alias = "Reageren", SequenceId = 1)]
    private readonly BooleanField fieldReageren = new BooleanField();
    
    /// <summary>Blokrol (singular)</summary>
    [IproxCluster(Alias = "Blok")]
    [IproxField(Alias = "Blokrol", SequenceId = 1)]
    private readonly SelectionField fieldBlokrol = new SelectionField();
    
    /// <summary>allowCommentAsUser (singular)</summary>
    [IproxCluster(Alias = "Blok")]
    [IproxField(Alias = "allowCommentAsUser", SequenceId = 1)]
    private readonly BooleanField fieldAllowCommentAsUser = new BooleanField();
    
    /// <summary>Gets or sets title</summary>
    [IproxProperty(SequenceId = 0)]
    public PlainField Title {
      get {
        return this.fieldTitle;
      }
      
      set {
        this.fieldTitle.Value = (value ?? new PlainField()).Value;
      }
    }
    
    /// <summary>Gets or sets itemsPerPage</summary>
    [IproxProperty(SequenceId = 1)]
    public PlainField ItemsPerPage {
      get {
        return this.fieldItemsPerPage;
      }
      
      set {
        this.fieldItemsPerPage.Value = (value ?? new PlainField()).Value;
      }
    }
    
    /// <summary>Gets or sets Liken</summary>
    [IproxProperty(SequenceId = 2)]
    public BooleanField Liken {
      get {
        return this.fieldLiken;
      }
      
      set {
        this.fieldLiken.Value = (value ?? new BooleanField()).Value;
      }
    }
    
    /// <summary>Gets or sets enableTagging</summary>
    [IproxProperty(SequenceId = 3)]
    public BooleanField EnableTagging {
      get {
        return this.fieldEnableTagging;
      }
      
      set {
        this.fieldEnableTagging.Value = (value ?? new BooleanField()).Value;
      }
    }
    
    /// <summary>Gets or sets Reageren</summary>
    [IproxProperty(SequenceId = 4)]
    public BooleanField Reageren {
      get {
        return this.fieldReageren;
      }
      
      set {
        this.fieldReageren.Value = (value ?? new BooleanField()).Value;
      }
    }
    
    /// <summary>Gets or sets Blokrol</summary>
    [IproxProperty(SequenceId = 5)]
    public SelectionField Blokrol {
      get {
        return this.fieldBlokrol;
      }
      
      set {
        this.fieldBlokrol.Value = (value ?? new SelectionField()).Value;
      }
    }
    
    /// <summary>Gets or sets allowCommentAsUser</summary>
    [IproxProperty(SequenceId = 6)]
    public BooleanField AllowCommentAsUser {
      get {
        return this.fieldAllowCommentAsUser;
      }
      
      set {
        this.fieldAllowCommentAsUser.Value = (value ?? new BooleanField()).Value;
      }
    }
  }
  
  /// <summary>Vraag en antwoord (class)</summary>
  /// <remarks>Module gebruikt prototype Vraag en antwoord</remarks>
  [IproxPagetype(Alias = "faq", Prototype = true)]
  public partial class FaqPrototype : IproxCluster {
    /// <summary>Antwoord (singular)</summary>
    [IproxCluster(Alias = "Inhoud")]
    [IproxField(Alias = "Antwoord", SequenceId = 1)]
    private readonly HtmlField fieldAntwoord = new HtmlField();
    
    /// <summary>Gets or sets Antwoord</summary>
    [IproxProperty(SequenceId = 0)]
    public HtmlField Antwoord {
      get {
        return this.fieldAntwoord;
      }
      
      set {
        this.fieldAntwoord.Value = (value ?? new HtmlField()).Value;
      }
    }
  }
  
  /// <summary>Applicatielink (class)</summary>
  /// <remarks>grid-blok</remarks>
  [IproxPagetype(Alias = "applicatielink-blok", Prototype = true)]
  public partial class ApplicatielinkBlok : IproxCluster {
    /// <summary>Titel (singular)</summary>
    [IproxCluster(Alias = "Blok")]
    [IproxField(Alias = "Titel", SequenceId = 1)]
    private readonly PlainField fieldTitel = new PlainField();
    
    /// <summary>Tekst (singular)</summary>
    [IproxCluster(Alias = "Blok")]
    [IproxField(Alias = "Tekst", SequenceId = 1)]
    private readonly HtmlField fieldTekst = new HtmlField();
    
    /// <summary>Blokrol (singular)</summary>
    [IproxCluster(Alias = "Blok")]
    [IproxField(Alias = "Blokrol", SequenceId = 1)]
    private readonly SelectionField fieldBlokrol = new SelectionField();
    
    /// <summary>Adres applicatie (singular)</summary>
    [IproxCluster(Alias = "Blok")]
    [IproxField(Alias = "Adres applicatie", SequenceId = 1)]
    private readonly AddressField fieldAdresApplicatie = new AddressField();
    
    /// <summary>Hoogte (singular)</summary>
    [IproxCluster(Alias = "Blok")]
    [IproxField(Alias = "Hoogte", SequenceId = 1)]
    private readonly PlainField fieldHoogte = new PlainField();
    
    /// <summary>Gets or sets Titel</summary>
    [IproxProperty(SequenceId = 0)]
    public PlainField Titel {
      get {
        return this.fieldTitel;
      }
      
      set {
        this.fieldTitel.Value = (value ?? new PlainField()).Value;
      }
    }
    
    /// <summary>Gets or sets Tekst</summary>
    [IproxProperty(SequenceId = 1)]
    public HtmlField Tekst {
      get {
        return this.fieldTekst;
      }
      
      set {
        this.fieldTekst.Value = (value ?? new HtmlField()).Value;
      }
    }
    
    /// <summary>Gets or sets Blokrol</summary>
    [IproxProperty(SequenceId = 2)]
    public SelectionField Blokrol {
      get {
        return this.fieldBlokrol;
      }
      
      set {
        this.fieldBlokrol.Value = (value ?? new SelectionField()).Value;
      }
    }
    
    /// <summary>Gets or sets Adres applicatie</summary>
    /// <remarks>http://verwijzing naar de applicatie</remarks>
    [IproxProperty(SequenceId = 3)]
    public AddressField AdresApplicatie {
      get {
        return this.fieldAdresApplicatie;
      }
      
      set {
        this.fieldAdresApplicatie.Value = (value ?? new AddressField()).Value;
      }
    }
    
    /// <summary>Gets or sets Hoogte</summary>
    /// <remarks>Hoogte van de applicatie in pixels</remarks>
    [IproxProperty(SequenceId = 4)]
    public PlainField Hoogte {
      get {
        return this.fieldHoogte;
      }
      
      set {
        this.fieldHoogte.Value = (value ?? new PlainField()).Value;
      }
    }
  }
  
  /// <summary>Formulier (class)</summary>
  /// <remarks>TODO, baseline blok</remarks>
  [IproxPagetype(Alias = "formulier-blok", Prototype = true)]
  public partial class FormulierBlok : IproxCluster {
  }
  
  /// <summary>Mailinglist (class)</summary>
  /// <remarks>grid-blok</remarks>
  [IproxPagetype(Alias = "mailinglist-blok", Prototype = true)]
  public partial class MailinglistBlok : IproxCluster {
    /// <summary>Titel (singular)</summary>
    [IproxCluster(Alias = "Blok")]
    [IproxField(Alias = "Titel", SequenceId = 1)]
    private readonly PlainField fieldTitel = new PlainField();
    
    /// <summary>Tekst (singular)</summary>
    [IproxCluster(Alias = "Blok")]
    [IproxField(Alias = "Tekst", SequenceId = 1)]
    private readonly HtmlField fieldTekst = new HtmlField();
    
    /// <summary>Mailinglist (singular)</summary>
    [IproxCluster(Alias = "Blok")]
    [IproxField(Alias = "Mailinglist", SequenceId = 1)]
    private readonly LinkField fieldMailinglist = new LinkField();
    
    /// <summary>Blokrol (singular)</summary>
    [IproxCluster(Alias = "Blok")]
    [IproxField(Alias = "Blokrol", SequenceId = 1)]
    private readonly SelectionField fieldBlokrol = new SelectionField();
    
    /// <summary>Gets or sets Titel</summary>
    [IproxProperty(SequenceId = 0)]
    public PlainField Titel {
      get {
        return this.fieldTitel;
      }
      
      set {
        this.fieldTitel.Value = (value ?? new PlainField()).Value;
      }
    }
    
    /// <summary>Gets or sets Tekst</summary>
    [IproxProperty(SequenceId = 1)]
    public HtmlField Tekst {
      get {
        return this.fieldTekst;
      }
      
      set {
        this.fieldTekst.Value = (value ?? new HtmlField()).Value;
      }
    }
    
    /// <summary>Gets or sets Mailinglist</summary>
    [IproxProperty(SequenceId = 2)]
    public LinkField Mailinglist {
      get {
        return this.fieldMailinglist;
      }
      
      set {
        this.fieldMailinglist.Value = (value ?? new LinkField()).Value;
      }
    }
    
    /// <summary>Gets or sets Blokrol</summary>
    [IproxProperty(SequenceId = 3)]
    public SelectionField Blokrol {
      get {
        return this.fieldBlokrol;
      }
      
      set {
        this.fieldBlokrol.Value = (value ?? new SelectionField()).Value;
      }
    }
  }
  
  /// <summary>RSS (class)</summary>
  /// <remarks>grid-blok</remarks>
  [IproxPagetype(Alias = "rss-blok", Prototype = true)]
  public partial class RssBlok : IproxCluster {
    /// <summary>Algemeen (singular)</summary>
    [IproxCluster(Alias = "Blok")]
    [IproxCluster(Alias = "Algemeen", SequenceId = 1)]
    private readonly RssBlok.AlgemeenCluster fieldAlgemeen = new RssBlok.AlgemeenCluster();
    
    /// <summary>Instellingen (singular)</summary>
    [IproxCluster(Alias = "Blok")]
    [IproxCluster(Alias = "Instellingen", SequenceId = 1)]
    private readonly RssBlok.InstellingenCluster fieldInstellingen = new RssBlok.InstellingenCluster();
    
    /// <summary>Feed (collection)</summary>
    [IproxCluster(Alias = "Blok")]
    [IproxCluster(Alias = "Feed", SequenceId = 1)]
    private readonly IproxClustersCollection<RssBlok.FeedCluster> fieldFeed = new IproxClustersCollection<RssBlok.FeedCluster>();
    
    /// <summary>Meer-link (collection)</summary>
    [IproxCluster(Alias = "Blok")]
    [IproxCluster(Alias = "Meer-link", Prototype = true, SequenceId = 1)]
    [IproxPagetype(Alias = "verwijzing", Prototype = true, SequenceId = 2)]
    [IproxCluster(Alias = "Verwijzing", SequenceId = 3)]
    private readonly IproxClustersCollection<IVerwijzing> fieldMeerLink = new IproxClustersCollection<IVerwijzing>();
    
    /// <summary>Gets Algemeen</summary>
    [IproxProperty(SequenceId = 0)]
    public RssBlok.AlgemeenCluster Algemeen {
      get {
        return this.fieldAlgemeen;
      }
    }
    
    /// <summary>Gets Instellingen</summary>
    [IproxProperty(SequenceId = 1)]
    public RssBlok.InstellingenCluster Instellingen {
      get {
        return this.fieldInstellingen;
      }
    }
    
    /// <summary>Gets collection of Feed</summary>
    [IproxProperty(SequenceId = 2)]
    public IproxClustersCollection<RssBlok.FeedCluster> Feed {
      get {
        return this.fieldFeed;
      }
    }
    
    /// <summary>Gets collection of Meer-link</summary>
    [IproxProperty(SequenceId = 3)]
    public IproxClustersCollection<IVerwijzing> MeerLink {
      get {
        return this.fieldMeerLink;
      }
    }
    
    /// <summary>Algemeen (class)</summary>
    [IproxCluster(Alias = "Blok")]
    [IproxCluster(Alias = "Algemeen", SequenceId = 1)]
    public partial class AlgemeenCluster : IproxCluster {
      /// <summary>Titel (singular)</summary>
      [IproxField(Alias = "Titel")]
      private readonly PlainField fieldTitel = new PlainField();
      
      /// <summary>Foto (singular)</summary>
      [IproxField(Alias = "Foto")]
      private readonly ImageField fieldFoto = new ImageField();
      
      /// <summary>Tekst (singular)</summary>
      [IproxField(Alias = "Tekst")]
      private readonly HtmlField fieldTekst = new HtmlField();
      
      /// <summary>Blokrol (singular)</summary>
      [IproxField(Alias = "Blokrol")]
      private readonly SelectionField fieldBlokrol = new SelectionField();
      
      /// <summary>Gets or sets Titel</summary>
      [IproxProperty(SequenceId = 0)]
      public PlainField Titel {
        get {
          return this.fieldTitel;
        }
        
        set {
          this.fieldTitel.Value = (value ?? new PlainField()).Value;
        }
      }
      
      /// <summary>Gets or sets Foto</summary>
      [IproxProperty(SequenceId = 1)]
      public ImageField Foto {
        get {
          return this.fieldFoto;
        }
        
        set {
          this.fieldFoto.Value = (value ?? new ImageField()).Value;
        }
      }
      
      /// <summary>Gets or sets Tekst</summary>
      [IproxProperty(SequenceId = 2)]
      public HtmlField Tekst {
        get {
          return this.fieldTekst;
        }
        
        set {
          this.fieldTekst.Value = (value ?? new HtmlField()).Value;
        }
      }
      
      /// <summary>Gets or sets Blokrol</summary>
      [IproxProperty(SequenceId = 3)]
      public SelectionField Blokrol {
        get {
          return this.fieldBlokrol;
        }
        
        set {
          this.fieldBlokrol.Value = (value ?? new SelectionField()).Value;
        }
      }
    }
    
    /// <summary>Instellingen (class)</summary>
    [IproxCluster(Alias = "Blok")]
    [IproxCluster(Alias = "Instellingen", SequenceId = 1)]
    public partial class InstellingenCluster : IproxCluster {
      /// <summary>Range (singular)</summary>
      [IproxField(Alias = "Range")]
      private readonly PlainField fieldRange = new PlainField();
      
      /// <summary>Samenvatting tonen (singular)</summary>
      [IproxField(Alias = "Samenvatting tonen")]
      private readonly BooleanField fieldSamenvattingTonen = new BooleanField();
      
      /// <summary>Datum tonen (singular)</summary>
      [IproxField(Alias = "Datum tonen")]
      private readonly BooleanField fieldDatumTonen = new BooleanField();
      
      /// <summary>Bron tonen (singular)</summary>
      [IproxField(Alias = "Bron tonen")]
      private readonly BooleanField fieldBronTonen = new BooleanField();
      
      /// <summary>Gets or sets Range</summary>
      /// <remarks>Aantal of verzameling van items uit de feed(s)</remarks>
      [IproxProperty(SequenceId = 0)]
      public PlainField Range {
        get {
          return this.fieldRange;
        }
        
        set {
          this.fieldRange.Value = (value ?? new PlainField()).Value;
        }
      }
      
      /// <summary>Gets or sets Samenvatting tonen</summary>
      [IproxProperty(SequenceId = 1)]
      public BooleanField SamenvattingTonen {
        get {
          return this.fieldSamenvattingTonen;
        }
        
        set {
          this.fieldSamenvattingTonen.Value = (value ?? new BooleanField()).Value;
        }
      }
      
      /// <summary>Gets or sets Datum tonen</summary>
      [IproxProperty(SequenceId = 2)]
      public BooleanField DatumTonen {
        get {
          return this.fieldDatumTonen;
        }
        
        set {
          this.fieldDatumTonen.Value = (value ?? new BooleanField()).Value;
        }
      }
      
      /// <summary>Gets or sets Bron tonen</summary>
      [IproxProperty(SequenceId = 3)]
      public BooleanField BronTonen {
        get {
          return this.fieldBronTonen;
        }
        
        set {
          this.fieldBronTonen.Value = (value ?? new BooleanField()).Value;
        }
      }
    }
    
    /// <summary>Feed (class)</summary>
    [IproxCluster(Alias = "Blok")]
    [IproxCluster(Alias = "Feed", SequenceId = 1)]
    public partial class FeedCluster : IproxCluster {
      /// <summary>URL (singular)</summary>
      [IproxField(Alias = "URL")]
      private readonly AddressField fieldURL = new AddressField();
      
      /// <summary>Bevat (singular)</summary>
      [IproxField(Alias = "Bevat")]
      private readonly PlainField fieldBevat = new PlainField();
      
      /// <summary>Bevat niet (singular)</summary>
      [IproxField(Alias = "Bevat niet")]
      private readonly PlainField fieldBevatNiet = new PlainField();
      
      /// <summary>Gets or sets URL</summary>
      [IproxProperty(SequenceId = 0)]
      public AddressField URL {
        get {
          return this.fieldURL;
        }
        
        set {
          this.fieldURL.Value = (value ?? new AddressField()).Value;
        }
      }
      
      /// <summary>Gets or sets Bevat</summary>
      /// <remarks>Komma-gescheiden lijst met termen welke het item moet bevatten</remarks>
      [IproxProperty(SequenceId = 1)]
      public PlainField Bevat {
        get {
          return this.fieldBevat;
        }
        
        set {
          this.fieldBevat.Value = (value ?? new PlainField()).Value;
        }
      }
      
      /// <summary>Gets or sets Bevat niet</summary>
      /// <remarks>Komma-gescheiden lijst met termen welke het item niet mag bevatten</remarks>
      [IproxProperty(SequenceId = 2)]
      public PlainField BevatNiet {
        get {
          return this.fieldBevatNiet;
        }
        
        set {
          this.fieldBevatNiet.Value = (value ?? new PlainField()).Value;
        }
      }
    }
  }
  
  /// <summary>Verwijzingen-plus (interface)</summary>
  /// <see cref="InterneVerwijzingCluster" />
  /// <see cref="ExterneVerwijzingCluster" />
  /// <see cref="DownloadCluster" />
  [IproxPagetype(Alias = "verwijzingen_plus", Prototype = true)]
  [IproxCluster(Alias = "Verwijzing", SequenceId = 1)]
  public partial interface IVerwijzingenPlus : IIproxCluster {
  }
  
  /// <summary>Verwijzingen-plus (static class)</summary>
  [IproxPagetype(Alias = "verwijzingen_plus", Prototype = true)]
  [IproxCluster(Alias = "Verwijzing", SequenceId = 1)]
  public static class VerwijzingenPlusClusters {
    /// <summary>Interne verwijzing (class)</summary>
    [IproxCluster(Alias = "Interne verwijzing")]
    public partial class InterneVerwijzingCluster : IproxCluster {
      /// <summary>Link (singular)</summary>
      [IproxField(Alias = "Link")]
      private readonly LinkField fieldLink = new LinkField();
      
      /// <summary>Openen in nieuw venster (singular)</summary>
      [IproxField(Alias = "Openen in nieuw venster")]
      private readonly BooleanField fieldOpenenInNieuwVenster = new BooleanField();
      
      /// <summary>Pictogram (singular)</summary>
      [IproxField(Alias = "Pictogram")]
      private readonly ImageField fieldPictogram = new ImageField();
      
      /// <summary>Gets or sets Link</summary>
      [IproxProperty(SequenceId = 0)]
      public LinkField Link {
        get {
          return this.fieldLink;
        }
        
        set {
          this.fieldLink.Value = (value ?? new LinkField()).Value;
        }
      }
      
      /// <summary>Gets or sets Openen in nieuw venster</summary>
      [IproxProperty(SequenceId = 1)]
      public BooleanField OpenenInNieuwVenster {
        get {
          return this.fieldOpenenInNieuwVenster;
        }
        
        set {
          this.fieldOpenenInNieuwVenster.Value = (value ?? new BooleanField()).Value;
        }
      }
      
      /// <summary>Gets or sets Pictogram</summary>
      [IproxProperty(SequenceId = 2)]
      public ImageField Pictogram {
        get {
          return this.fieldPictogram;
        }
        
        set {
          this.fieldPictogram.Value = (value ?? new ImageField()).Value;
        }
      }
    }
    
    /// <content>InterneVerwijzingCluster can be used for IVerwijzingenPlus</content>
    public partial class InterneVerwijzingCluster : IVerwijzingenPlus {
    }
    
    /// <summary>Externe verwijzing (class)</summary>
    [IproxCluster(Alias = "Externe verwijzing")]
    public partial class ExterneVerwijzingCluster : IproxCluster {
      /// <summary>Link (singular)</summary>
      [IproxField(Alias = "Link")]
      private readonly AddressField fieldLink = new AddressField();
      
      /// <summary>Openen in nieuw venster (singular)</summary>
      [IproxField(Alias = "Openen in nieuw venster")]
      private readonly BooleanField fieldOpenenInNieuwVenster = new BooleanField();
      
      /// <summary>Pictogram (singular)</summary>
      [IproxField(Alias = "Pictogram")]
      private readonly ImageField fieldPictogram = new ImageField();
      
      /// <summary>Gets or sets Link</summary>
      [IproxProperty(SequenceId = 0)]
      public AddressField Link {
        get {
          return this.fieldLink;
        }
        
        set {
          this.fieldLink.Value = (value ?? new AddressField()).Value;
        }
      }
      
      /// <summary>Gets or sets Openen in nieuw venster</summary>
      [IproxProperty(SequenceId = 1)]
      public BooleanField OpenenInNieuwVenster {
        get {
          return this.fieldOpenenInNieuwVenster;
        }
        
        set {
          this.fieldOpenenInNieuwVenster.Value = (value ?? new BooleanField()).Value;
        }
      }
      
      /// <summary>Gets or sets Pictogram</summary>
      [IproxProperty(SequenceId = 2)]
      public ImageField Pictogram {
        get {
          return this.fieldPictogram;
        }
        
        set {
          this.fieldPictogram.Value = (value ?? new ImageField()).Value;
        }
      }
    }
    
    /// <content>ExterneVerwijzingCluster can be used for IVerwijzingenPlus</content>
    public partial class ExterneVerwijzingCluster : IVerwijzingenPlus {
    }
    
    /// <summary>Download (class)</summary>
    [IproxCluster(Alias = "Download")]
    public partial class DownloadCluster : IproxCluster {
      /// <summary>Bestand (singular)</summary>
      [IproxField(Alias = "Bestand")]
      private readonly DocumentField fieldBestand = new DocumentField();
      
      /// <summary>Document (singular)</summary>
      [IproxField(Alias = "Document")]
      private readonly UnknownField fieldDocument = new UnknownField();
      
      /// <summary>Gets or sets Bestand</summary>
      [IproxProperty(SequenceId = 0)]
      public DocumentField Bestand {
        get {
          return this.fieldBestand;
        }
        
        set {
          this.fieldBestand.Value = (value ?? new DocumentField()).Value;
        }
      }
      
      /// <summary>Gets or sets Document</summary>
      [IproxProperty(SequenceId = 1)]
      public UnknownField Document {
        get {
          return this.fieldDocument;
        }
        
        set {
          this.fieldDocument.Value = (value ?? new UnknownField()).Value;
        }
      }
    }
    
    /// <content>DownloadCluster can be used for IVerwijzingenPlus</content>
    public partial class DownloadCluster : IVerwijzingenPlus {
    }
  }
  
  /// <summary>Kaart (class)</summary>
  /// <remarks>WEG grid-blok</remarks>
  [IproxPagetype(Alias = "kaart-blok", Prototype = true)]
  public partial class KaartBlok : IproxCluster {
    /// <summary>Titel (singular)</summary>
    [IproxCluster(Alias = "Blok")]
    [IproxField(Alias = "Titel", SequenceId = 1)]
    private readonly PlainField fieldTitel = new PlainField();
    
    /// <summary>Kaart (singular)</summary>
    [IproxCluster(Alias = "Blok")]
    [IproxField(Alias = "Kaart", SequenceId = 1)]
    private readonly LinkField fieldKaart = new LinkField();
    
    /// <summary>Gets or sets Titel</summary>
    [IproxProperty(SequenceId = 0)]
    public PlainField Titel {
      get {
        return this.fieldTitel;
      }
      
      set {
        this.fieldTitel.Value = (value ?? new PlainField()).Value;
      }
    }
    
    /// <summary>Gets or sets Kaart</summary>
    [IproxProperty(SequenceId = 1)]
    public LinkField Kaart {
      get {
        return this.fieldKaart;
      }
      
      set {
        this.fieldKaart.Value = (value ?? new LinkField()).Value;
      }
    }
  }
  
  /// <summary>Dashboard (class)</summary>
  /// <remarks>grid-blok</remarks>
  [IproxPagetype(Alias = "dashboard", Prototype = true)]
  public partial class Dashboard : IproxCluster {
    /// <summary>Titel (singular)</summary>
    [IproxCluster(Alias = "Blok")]
    [IproxField(Alias = "Titel", SequenceId = 1)]
    private readonly PlainField fieldTitel = new PlainField();
    
    /// <summary>Gets or sets Titel</summary>
    [IproxProperty(SequenceId = 0)]
    public PlainField Titel {
      get {
        return this.fieldTitel;
      }
      
      set {
        this.fieldTitel.Value = (value ?? new PlainField()).Value;
      }
    }
  }
  
  /// <summary>Social - Groep leden (class)</summary>
  [IproxPagetype(Alias = "social-group-members", Prototype = true)]
  public partial class SocialGroupMembers : IproxCluster {
    /// <summary>Blokrol (singular)</summary>
    [IproxCluster(Alias = "Blok")]
    [IproxCluster(Alias = "general", SequenceId = 1)]
    [IproxField(Alias = "Blokrol", SequenceId = 2)]
    private readonly SelectionField fieldBlokrol = new SelectionField();
    
    /// <summary>owners (singular)</summary>
    [IproxCluster(Alias = "Blok")]
    [IproxCluster(Alias = "owners", SequenceId = 1)]
    private readonly SocialGroupMembers.OwnersCluster fieldOwners = new SocialGroupMembers.OwnersCluster();
    
    /// <summary>members (singular)</summary>
    [IproxCluster(Alias = "Blok")]
    [IproxCluster(Alias = "members", SequenceId = 1)]
    private readonly SocialGroupMembers.MembersCluster fieldMembers = new SocialGroupMembers.MembersCluster();
    
    /// <summary>Gets or sets Blokrol</summary>
    [IproxProperty(SequenceId = 0)]
    public SelectionField Blokrol {
      get {
        return this.fieldBlokrol;
      }
      
      set {
        this.fieldBlokrol.Value = (value ?? new SelectionField()).Value;
      }
    }
    
    /// <summary>Gets owners</summary>
    [IproxProperty(SequenceId = 1)]
    public SocialGroupMembers.OwnersCluster Owners {
      get {
        return this.fieldOwners;
      }
    }
    
    /// <summary>Gets members</summary>
    [IproxProperty(SequenceId = 2)]
    public SocialGroupMembers.MembersCluster Members {
      get {
        return this.fieldMembers;
      }
    }
    
    /// <summary>owners (class)</summary>
    [IproxCluster(Alias = "Blok")]
    [IproxCluster(Alias = "owners", SequenceId = 1)]
    public partial class OwnersCluster : IproxCluster {
      /// <summary>show (singular)</summary>
      [IproxField(Alias = "show")]
      private readonly BooleanField fieldShow = new BooleanField();
      
      /// <summary>Blokrol (singular)</summary>
      [IproxField(Alias = "Blokrol")]
      private readonly SelectionField fieldBlokrol = new SelectionField();
      
      /// <summary>Gets or sets show</summary>
      [IproxProperty(SequenceId = 0)]
      public BooleanField Show {
        get {
          return this.fieldShow;
        }
        
        set {
          this.fieldShow.Value = (value ?? new BooleanField()).Value;
        }
      }
      
      /// <summary>Gets or sets Blokrol</summary>
      [IproxProperty(SequenceId = 1)]
      public SelectionField Blokrol {
        get {
          return this.fieldBlokrol;
        }
        
        set {
          this.fieldBlokrol.Value = (value ?? new SelectionField()).Value;
        }
      }
    }
    
    /// <summary>members (class)</summary>
    [IproxCluster(Alias = "Blok")]
    [IproxCluster(Alias = "members", SequenceId = 1)]
    public partial class MembersCluster : IproxCluster {
      /// <summary>show (singular)</summary>
      [IproxField(Alias = "show")]
      private readonly BooleanField fieldShow = new BooleanField();
      
      /// <summary>Blokrol (singular)</summary>
      [IproxField(Alias = "Blokrol")]
      private readonly SelectionField fieldBlokrol = new SelectionField();
      
      /// <summary>Gets or sets show</summary>
      [IproxProperty(SequenceId = 0)]
      public BooleanField Show {
        get {
          return this.fieldShow;
        }
        
        set {
          this.fieldShow.Value = (value ?? new BooleanField()).Value;
        }
      }
      
      /// <summary>Gets or sets Blokrol</summary>
      [IproxProperty(SequenceId = 1)]
      public SelectionField Blokrol {
        get {
          return this.fieldBlokrol;
        }
        
        set {
          this.fieldBlokrol.Value = (value ?? new SelectionField()).Value;
        }
      }
    }
  }
  
  /// <summary>Social - Groep meta (class)</summary>
  [IproxPagetype(Alias = "social-group-meta", Prototype = true)]
  public partial class SocialGroupMeta : IproxCluster {
    /// <summary>Blokrol (singular)</summary>
    [IproxCluster(Alias = "Blok")]
    [IproxField(Alias = "Blokrol", SequenceId = 1)]
    private readonly SelectionField fieldBlokrol = new SelectionField();
    
    /// <summary>E-mail (singular)</summary>
    [IproxCluster(Alias = "E-mail")]
    private readonly SocialGroupMeta.EMailCluster fieldEMail = new SocialGroupMeta.EMailCluster();
    
    /// <summary>Gets or sets Blokrol</summary>
    [IproxProperty(SequenceId = 0)]
    public SelectionField Blokrol {
      get {
        return this.fieldBlokrol;
      }
      
      set {
        this.fieldBlokrol.Value = (value ?? new SelectionField()).Value;
      }
    }
    
    /// <summary>Gets E-mail</summary>
    [IproxProperty(SequenceId = 1)]
    public SocialGroupMeta.EMailCluster EMail {
      get {
        return this.fieldEMail;
      }
    }
    
    /// <summary>E-mail (class)</summary>
    [IproxCluster(Alias = "E-mail")]
    public partial class EMailCluster : IproxCluster {
      /// <summary>emailOwners (singular)</summary>
      [IproxField(Alias = "emailOwners")]
      private readonly BooleanField fieldEmailOwners = new BooleanField();
      
      /// <summary>emailMembers (singular)</summary>
      [IproxField(Alias = "emailMembers")]
      private readonly BooleanField fieldEmailMembers = new BooleanField();
      
      /// <summary>emailOwnersAndMembers (singular)</summary>
      [IproxField(Alias = "emailOwnersAndMembers")]
      private readonly BooleanField fieldEmailOwnersAndMembers = new BooleanField();
      
      /// <summary>emailFollowers (singular)</summary>
      [IproxField(Alias = "emailFollowers")]
      private readonly BooleanField fieldEmailFollowers = new BooleanField();
      
      /// <summary>emailAll (singular)</summary>
      [IproxField(Alias = "emailAll")]
      private readonly BooleanField fieldEmailAll = new BooleanField();
      
      /// <summary>Gets or sets emailOwners</summary>
      [IproxProperty(SequenceId = 0)]
      public BooleanField EmailOwners {
        get {
          return this.fieldEmailOwners;
        }
        
        set {
          this.fieldEmailOwners.Value = (value ?? new BooleanField()).Value;
        }
      }
      
      /// <summary>Gets or sets emailMembers</summary>
      [IproxProperty(SequenceId = 1)]
      public BooleanField EmailMembers {
        get {
          return this.fieldEmailMembers;
        }
        
        set {
          this.fieldEmailMembers.Value = (value ?? new BooleanField()).Value;
        }
      }
      
      /// <summary>Gets or sets emailOwnersAndMembers</summary>
      [IproxProperty(SequenceId = 2)]
      public BooleanField EmailOwnersAndMembers {
        get {
          return this.fieldEmailOwnersAndMembers;
        }
        
        set {
          this.fieldEmailOwnersAndMembers.Value = (value ?? new BooleanField()).Value;
        }
      }
      
      /// <summary>Gets or sets emailFollowers</summary>
      /// <remarks>Kan alleen gebruikt worden als er een custom mailform is ingesteld</remarks>
      [IproxProperty(SequenceId = 3)]
      public BooleanField EmailFollowers {
        get {
          return this.fieldEmailFollowers;
        }
        
        set {
          this.fieldEmailFollowers.Value = (value ?? new BooleanField()).Value;
        }
      }
      
      /// <summary>Gets or sets emailAll</summary>
      /// <remarks>Kan alleen gebruikt worden als er een custom mailform is ingesteld</remarks>
      [IproxProperty(SequenceId = 4)]
      public BooleanField EmailAll {
        get {
          return this.fieldEmailAll;
        }
        
        set {
          this.fieldEmailAll.Value = (value ?? new BooleanField()).Value;
        }
      }
    }
  }
  
  /// <summary>Social - Groep blog lijst (class)</summary>
  /// <remarks>grid-blok</remarks>
  [IproxPagetype(Alias = "social-group-blog-list", Prototype = true)]
  public partial class SocialGroupBlogList : IproxCluster {
    /// <summary>title (singular)</summary>
    [IproxCluster(Alias = "Blok")]
    [IproxField(Alias = "title", SequenceId = 1)]
    private readonly PlainField fieldTitle = new PlainField();
    
    /// <summary>Blokrol (singular)</summary>
    [IproxCluster(Alias = "Blok")]
    [IproxField(Alias = "Blokrol", SequenceId = 1)]
    private readonly SelectionField fieldBlokrol = new SelectionField();
    
    /// <summary>Gets or sets title</summary>
    [IproxProperty(SequenceId = 0)]
    public PlainField Title {
      get {
        return this.fieldTitle;
      }
      
      set {
        this.fieldTitle.Value = (value ?? new PlainField()).Value;
      }
    }
    
    /// <summary>Gets or sets Blokrol</summary>
    [IproxProperty(SequenceId = 1)]
    public SelectionField Blokrol {
      get {
        return this.fieldBlokrol;
      }
      
      set {
        this.fieldBlokrol.Value = (value ?? new SelectionField()).Value;
      }
    }
  }
  
  /// <summary>Social - Activiteiten (class)</summary>
  [IproxPagetype(Alias = "social-activities", Prototype = true)]
  public partial class SocialActivities : IproxCluster {
    /// <summary>title (singular)</summary>
    [IproxCluster(Alias = "Blok")]
    [IproxField(Alias = "title", SequenceId = 1)]
    private readonly PlainField fieldTitle = new PlainField();
    
    /// <summary>type (singular)</summary>
    [IproxCluster(Alias = "Blok")]
    [IproxField(Alias = "type", SequenceId = 1)]
    private readonly SelectionField fieldType = new SelectionField();
    
    /// <summary>Blokrol (singular)</summary>
    [IproxCluster(Alias = "Blok")]
    [IproxField(Alias = "Blokrol", SequenceId = 1)]
    private readonly SelectionField fieldBlokrol = new SelectionField();
    
    /// <summary>linkToPage (singular)</summary>
    [IproxCluster(Alias = "Blok")]
    [IproxField(Alias = "linkToPage", SequenceId = 1)]
    private readonly LinkField fieldLinkToPage = new LinkField();
    
    /// <summary>itemsPerPage (singular)</summary>
    [IproxCluster(Alias = "Blok")]
    [IproxField(Alias = "itemsPerPage", SequenceId = 1)]
    private readonly PlainField fieldItemsPerPage = new PlainField();
    
    /// <summary>maxAge (singular)</summary>
    [IproxCluster(Alias = "Blok")]
    [IproxField(Alias = "maxAge", SequenceId = 1)]
    private readonly PlainField fieldMaxAge = new PlainField();
    
    /// <summary>maxItems (singular)</summary>
    [IproxCluster(Alias = "Blok")]
    [IproxField(Alias = "maxItems", SequenceId = 1)]
    private readonly PlainField fieldMaxItems = new PlainField();
    
    /// <summary>Gets or sets title</summary>
    [IproxProperty(SequenceId = 0)]
    public PlainField Title {
      get {
        return this.fieldTitle;
      }
      
      set {
        this.fieldTitle.Value = (value ?? new PlainField()).Value;
      }
    }
    
    /// <summary>Gets or sets type</summary>
    [IproxProperty(SequenceId = 1)]
    public SelectionField Type {
      get {
        return this.fieldType;
      }
      
      set {
        this.fieldType.Value = (value ?? new SelectionField()).Value;
      }
    }
    
    /// <summary>Gets or sets Blokrol</summary>
    [IproxProperty(SequenceId = 2)]
    public SelectionField Blokrol {
      get {
        return this.fieldBlokrol;
      }
      
      set {
        this.fieldBlokrol.Value = (value ?? new SelectionField()).Value;
      }
    }
    
    /// <summary>Gets or sets linkToPage</summary>
    [IproxProperty(SequenceId = 3)]
    public LinkField LinkToPage {
      get {
        return this.fieldLinkToPage;
      }
      
      set {
        this.fieldLinkToPage.Value = (value ?? new LinkField()).Value;
      }
    }
    
    /// <summary>Gets or sets itemsPerPage</summary>
    [IproxProperty(SequenceId = 4)]
    public PlainField ItemsPerPage {
      get {
        return this.fieldItemsPerPage;
      }
      
      set {
        this.fieldItemsPerPage.Value = (value ?? new PlainField()).Value;
      }
    }
    
    /// <summary>Gets or sets maxAge</summary>
    /// <remarks>in dagen</remarks>
    [IproxProperty(SequenceId = 5)]
    public PlainField MaxAge {
      get {
        return this.fieldMaxAge;
      }
      
      set {
        this.fieldMaxAge.Value = (value ?? new PlainField()).Value;
      }
    }
    
    /// <summary>Gets or sets maxItems</summary>
    [IproxProperty(SequenceId = 6)]
    public PlainField MaxItems {
      get {
        return this.fieldMaxItems;
      }
      
      set {
        this.fieldMaxItems.Value = (value ?? new PlainField()).Value;
      }
    }
  }
  
  /// <summary>Carrousel (class)</summary>
  /// <remarks>grid-blok</remarks>
  [IproxPagetype(Alias = "carrousel-blok", Prototype = true)]
  public partial class CarrouselBlok : IproxCluster {
    /// <summary>Algemeen (singular)</summary>
    [IproxCluster(Alias = "Blok")]
    [IproxCluster(Alias = "Algemeen", SequenceId = 1)]
    private readonly CarrouselBlok.AlgemeenCluster fieldAlgemeen = new CarrouselBlok.AlgemeenCluster();
    
    /// <summary>Instellingen (singular)</summary>
    [IproxCluster(Alias = "Blok")]
    [IproxCluster(Alias = "Instellingen", SequenceId = 1)]
    private readonly CarrouselBlok.InstellingenCluster fieldInstellingen = new CarrouselBlok.InstellingenCluster();
    
    /// <summary>Foto's (collection)</summary>
    [IproxCluster(Alias = "Blok")]
    [IproxCluster(Alias = "Foto's", SequenceId = 1)]
    private readonly IproxClustersCollection<CarrouselBlok.FotoSCluster> fieldFotoS = new IproxClustersCollection<CarrouselBlok.FotoSCluster>();
    
    /// <summary>Gets Algemeen</summary>
    [IproxProperty(SequenceId = 0)]
    public CarrouselBlok.AlgemeenCluster Algemeen {
      get {
        return this.fieldAlgemeen;
      }
    }
    
    /// <summary>Gets Instellingen</summary>
    [IproxProperty(SequenceId = 1)]
    public CarrouselBlok.InstellingenCluster Instellingen {
      get {
        return this.fieldInstellingen;
      }
    }
    
    /// <summary>Gets collection of Foto's</summary>
    [IproxProperty(SequenceId = 2)]
    public IproxClustersCollection<CarrouselBlok.FotoSCluster> FotoS {
      get {
        return this.fieldFotoS;
      }
    }
    
    /// <summary>Algemeen (class)</summary>
    [IproxCluster(Alias = "Blok")]
    [IproxCluster(Alias = "Algemeen", SequenceId = 1)]
    public partial class AlgemeenCluster : IproxCluster {
      /// <summary>Titel (singular)</summary>
      [IproxField(Alias = "Titel")]
      private readonly PlainField fieldTitel = new PlainField();
      
      /// <summary>Blokrol (singular)</summary>
      [IproxField(Alias = "Blokrol")]
      private readonly SelectionField fieldBlokrol = new SelectionField();
      
      /// <summary>Gets or sets Titel</summary>
      [IproxProperty(SequenceId = 0)]
      public PlainField Titel {
        get {
          return this.fieldTitel;
        }
        
        set {
          this.fieldTitel.Value = (value ?? new PlainField()).Value;
        }
      }
      
      /// <summary>Gets or sets Blokrol</summary>
      [IproxProperty(SequenceId = 1)]
      public SelectionField Blokrol {
        get {
          return this.fieldBlokrol;
        }
        
        set {
          this.fieldBlokrol.Value = (value ?? new SelectionField()).Value;
        }
      }
    }
    
    /// <summary>Instellingen (class)</summary>
    [IproxCluster(Alias = "Blok")]
    [IproxCluster(Alias = "Instellingen", SequenceId = 1)]
    public partial class InstellingenCluster : IproxCluster {
      /// <summary>Duur overgang (singular)</summary>
      [IproxField(Alias = "Duur overgang")]
      private readonly SelectionField fieldDuurOvergang = new SelectionField();
      
      /// <summary>Duur tonen (singular)</summary>
      [IproxField(Alias = "Duur tonen")]
      private readonly SelectionField fieldDuurTonen = new SelectionField();
      
      /// <summary>Automatisch starten (singular)</summary>
      [IproxField(Alias = "Automatisch starten")]
      private readonly BooleanField fieldAutomatischStarten = new BooleanField();
      
      /// <summary>Willekeurige volgorde (singular)</summary>
      [IproxField(Alias = "Willekeurige volgorde")]
      private readonly BooleanField fieldWillekeurigeVolgorde = new BooleanField();
      
      /// <summary>Type overgang (singular)</summary>
      [IproxField(Alias = "Type overgang")]
      private readonly SelectionField fieldTypeOvergang = new SelectionField();
      
      /// <summary>Gets or sets Duur overgang</summary>
      [IproxProperty(SequenceId = 0)]
      public SelectionField DuurOvergang {
        get {
          return this.fieldDuurOvergang;
        }
        
        set {
          this.fieldDuurOvergang.Value = (value ?? new SelectionField()).Value;
        }
      }
      
      /// <summary>Gets or sets Duur tonen</summary>
      [IproxProperty(SequenceId = 1)]
      public SelectionField DuurTonen {
        get {
          return this.fieldDuurTonen;
        }
        
        set {
          this.fieldDuurTonen.Value = (value ?? new SelectionField()).Value;
        }
      }
      
      /// <summary>Gets or sets Automatisch starten</summary>
      [IproxProperty(SequenceId = 2)]
      public BooleanField AutomatischStarten {
        get {
          return this.fieldAutomatischStarten;
        }
        
        set {
          this.fieldAutomatischStarten.Value = (value ?? new BooleanField()).Value;
        }
      }
      
      /// <summary>Gets or sets Willekeurige volgorde</summary>
      [IproxProperty(SequenceId = 3)]
      public BooleanField WillekeurigeVolgorde {
        get {
          return this.fieldWillekeurigeVolgorde;
        }
        
        set {
          this.fieldWillekeurigeVolgorde.Value = (value ?? new BooleanField()).Value;
        }
      }
      
      /// <summary>Gets or sets Type overgang</summary>
      [IproxProperty(SequenceId = 4)]
      public SelectionField TypeOvergang {
        get {
          return this.fieldTypeOvergang;
        }
        
        set {
          this.fieldTypeOvergang.Value = (value ?? new SelectionField()).Value;
        }
      }
    }
    
    /// <summary>Foto's (class)</summary>
    [IproxCluster(Alias = "Blok")]
    [IproxCluster(Alias = "Foto's", SequenceId = 1)]
    public partial class FotoSCluster : IproxCluster {
      /// <summary>Afbeelding (singular)</summary>
      [IproxCluster(Alias = "Foto")]
      [IproxField(Alias = "Afbeelding", SequenceId = 1)]
      private readonly ImageField fieldAfbeelding = new ImageField();
      
      /// <summary>Verwijzing (collection)</summary>
      [IproxCluster(Alias = "Verwijzing", Prototype = true)]
      [IproxPagetype(Alias = "verwijzing", Prototype = true, SequenceId = 1)]
      [IproxCluster(Alias = "Verwijzing", SequenceId = 2)]
      private readonly IproxClustersCollection<IVerwijzing> fieldVerwijzing = new IproxClustersCollection<IVerwijzing>();
      
      /// <summary>Gets or sets Afbeelding</summary>
      [IproxProperty(SequenceId = 0)]
      public ImageField Afbeelding {
        get {
          return this.fieldAfbeelding;
        }
        
        set {
          this.fieldAfbeelding.Value = (value ?? new ImageField()).Value;
        }
      }
      
      /// <summary>Gets collection of Verwijzing</summary>
      [IproxProperty(SequenceId = 1)]
      public IproxClustersCollection<IVerwijzing> Verwijzing {
        get {
          return this.fieldVerwijzing;
        }
      }
    }
  }
  
  /// <summary>Social - Dashboard: Personen (class)</summary>
  [IproxPagetype(Alias = "social-person-finder", Prototype = true)]
  public partial class SocialPersonFinder : IproxCluster {
    /// <summary>general (singular)</summary>
    [IproxCluster(Alias = "Blok")]
    [IproxCluster(Alias = "general", SequenceId = 1)]
    private readonly SocialPersonFinder.GeneralCluster fieldGeneral = new SocialPersonFinder.GeneralCluster();
    
    /// <summary>Blokrol (singular)</summary>
    [IproxCluster(Alias = "Blok")]
    [IproxCluster(Alias = "cards", SequenceId = 1)]
    [IproxField(Alias = "Blokrol", SequenceId = 2)]
    private readonly SelectionField fieldBlokrol = new SelectionField();
    
    /// <summary>Gets general</summary>
    [IproxProperty(SequenceId = 0)]
    public SocialPersonFinder.GeneralCluster General {
      get {
        return this.fieldGeneral;
      }
    }
    
    /// <summary>Gets or sets Blokrol</summary>
    [IproxProperty(SequenceId = 1)]
    public SelectionField Blokrol {
      get {
        return this.fieldBlokrol;
      }
      
      set {
        this.fieldBlokrol.Value = (value ?? new SelectionField()).Value;
      }
    }
    
    /// <summary>general (class)</summary>
    [IproxCluster(Alias = "Blok")]
    [IproxCluster(Alias = "general", SequenceId = 1)]
    public partial class GeneralCluster : IproxCluster {
      /// <summary>title (singular)</summary>
      [IproxField(Alias = "title")]
      private readonly PlainField fieldTitle = new PlainField();
      
      /// <summary>Blokrol (singular)</summary>
      [IproxField(Alias = "Blokrol")]
      private readonly SelectionField fieldBlokrol = new SelectionField();
      
      /// <summary>description (singular)</summary>
      [IproxField(Alias = "description")]
      private readonly PlainField fieldDescription = new PlainField();
      
      /// <summary>rowsPerView (singular)</summary>
      [IproxField(Alias = "rowsPerView")]
      private readonly PlainField fieldRowsPerView = new PlainField();
      
      /// <summary>type (singular)</summary>
      [IproxField(Alias = "type")]
      private readonly SelectionField fieldType = new SelectionField();
      
      /// <summary>Gets or sets title</summary>
      [IproxProperty(SequenceId = 0)]
      public PlainField Title {
        get {
          return this.fieldTitle;
        }
        
        set {
          this.fieldTitle.Value = (value ?? new PlainField()).Value;
        }
      }
      
      /// <summary>Gets or sets Blokrol</summary>
      [IproxProperty(SequenceId = 1)]
      public SelectionField Blokrol {
        get {
          return this.fieldBlokrol;
        }
        
        set {
          this.fieldBlokrol.Value = (value ?? new SelectionField()).Value;
        }
      }
      
      /// <summary>Gets or sets description</summary>
      [IproxProperty(SequenceId = 2)]
      public PlainField Description {
        get {
          return this.fieldDescription;
        }
        
        set {
          this.fieldDescription.Value = (value ?? new PlainField()).Value;
        }
      }
      
      /// <summary>Gets or sets rowsPerView</summary>
      [IproxProperty(SequenceId = 3)]
      public PlainField RowsPerView {
        get {
          return this.fieldRowsPerView;
        }
        
        set {
          this.fieldRowsPerView.Value = (value ?? new PlainField()).Value;
        }
      }
      
      /// <summary>Gets or sets type</summary>
      [IproxProperty(SequenceId = 4)]
      public SelectionField Type {
        get {
          return this.fieldType;
        }
        
        set {
          this.fieldType.Value = (value ?? new SelectionField()).Value;
        }
      }
    }
  }
  
  /// <summary>Social - Dashboard: Mijn volgers (class)</summary>
  [IproxPagetype(Alias = "social-user-following", Prototype = true)]
  public partial class SocialUserFollowing : IproxCluster {
    /// <summary>general (singular)</summary>
    [IproxCluster(Alias = "Blok")]
    [IproxCluster(Alias = "general", SequenceId = 1)]
    private readonly SocialUserFollowing.GeneralCluster fieldGeneral = new SocialUserFollowing.GeneralCluster();
    
    /// <summary>Blokrol (singular)</summary>
    [IproxCluster(Alias = "Blok")]
    [IproxCluster(Alias = "cards", SequenceId = 1)]
    [IproxField(Alias = "Blokrol", SequenceId = 2)]
    private readonly SelectionField fieldBlokrol = new SelectionField();
    
    /// <summary>Gets general</summary>
    [IproxProperty(SequenceId = 0)]
    public SocialUserFollowing.GeneralCluster General {
      get {
        return this.fieldGeneral;
      }
    }
    
    /// <summary>Gets or sets Blokrol</summary>
    [IproxProperty(SequenceId = 1)]
    public SelectionField Blokrol {
      get {
        return this.fieldBlokrol;
      }
      
      set {
        this.fieldBlokrol.Value = (value ?? new SelectionField()).Value;
      }
    }
    
    /// <summary>general (class)</summary>
    [IproxCluster(Alias = "Blok")]
    [IproxCluster(Alias = "general", SequenceId = 1)]
    public partial class GeneralCluster : IproxCluster {
      /// <summary>title (singular)</summary>
      [IproxField(Alias = "title")]
      private readonly PlainField fieldTitle = new PlainField();
      
      /// <summary>Blokrol (singular)</summary>
      [IproxField(Alias = "Blokrol")]
      private readonly SelectionField fieldBlokrol = new SelectionField();
      
      /// <summary>Gets or sets title</summary>
      [IproxProperty(SequenceId = 0)]
      public PlainField Title {
        get {
          return this.fieldTitle;
        }
        
        set {
          this.fieldTitle.Value = (value ?? new PlainField()).Value;
        }
      }
      
      /// <summary>Gets or sets Blokrol</summary>
      [IproxProperty(SequenceId = 1)]
      public SelectionField Blokrol {
        get {
          return this.fieldBlokrol;
        }
        
        set {
          this.fieldBlokrol.Value = (value ?? new SelectionField()).Value;
        }
      }
    }
  }
  
  /// <summary>Social - Mijn mededelingen (class)</summary>
  /// <remarks>grid-blok NIEUW september 2017</remarks>
  [IproxPagetype(Alias = "social-user-alerts", Prototype = true)]
  public partial class SocialUserAlerts : IproxCluster {
    /// <summary>Blokrol (singular)</summary>
    [IproxCluster(Alias = "Blok")]
    [IproxField(Alias = "Blokrol", SequenceId = 1)]
    private readonly SelectionField fieldBlokrol = new SelectionField();
    
    /// <summary>url (singular)</summary>
    [IproxCluster(Alias = "Blok")]
    [IproxField(Alias = "url", SequenceId = 1)]
    private readonly LinkField fieldUrl = new LinkField();
    
    /// <summary>autoRefresh (singular)</summary>
    [IproxCluster(Alias = "Blok")]
    [IproxField(Alias = "autoRefresh", SequenceId = 1)]
    private readonly PlainField fieldAutoRefresh = new PlainField();
    
    /// <summary>snoozeDays (singular)</summary>
    [IproxCluster(Alias = "Blok")]
    [IproxField(Alias = "snoozeDays", SequenceId = 1)]
    private readonly PlainField fieldSnoozeDays = new PlainField();
    
    /// <summary>deleteAlerts (singular)</summary>
    [IproxCluster(Alias = "Blok")]
    [IproxField(Alias = "deleteAlerts", SequenceId = 1)]
    private readonly BooleanField fieldDeleteAlerts = new BooleanField();
    
    /// <summary>nextAlertButton (singular)</summary>
    [IproxCluster(Alias = "Blok")]
    [IproxField(Alias = "nextAlertButton", SequenceId = 1)]
    private readonly BooleanField fieldNextAlertButton = new BooleanField();
    
    /// <summary>profileEditButton (singular)</summary>
    [IproxCluster(Alias = "Blok")]
    [IproxField(Alias = "profileEditButton", SequenceId = 1)]
    private readonly BooleanField fieldProfileEditButton = new BooleanField();
    
    /// <summary>zoneAliasImportant (singular)</summary>
    [IproxCluster(Alias = "Blok")]
    [IproxField(Alias = "zoneAliasImportant", SequenceId = 1)]
    private readonly SelectionField fieldZoneAliasImportant = new SelectionField();
    
    /// <summary>zoneAliasPersonal (singular)</summary>
    [IproxCluster(Alias = "Blok")]
    [IproxField(Alias = "zoneAliasPersonal", SequenceId = 1)]
    private readonly SelectionField fieldZoneAliasPersonal = new SelectionField();
    
    /// <summary>Gets or sets Blokrol</summary>
    [IproxProperty(SequenceId = 0)]
    public SelectionField Blokrol {
      get {
        return this.fieldBlokrol;
      }
      
      set {
        this.fieldBlokrol.Value = (value ?? new SelectionField()).Value;
      }
    }
    
    /// <summary>Gets or sets url</summary>
    [IproxProperty(SequenceId = 1)]
    public LinkField Url {
      get {
        return this.fieldUrl;
      }
      
      set {
        this.fieldUrl.Value = (value ?? new LinkField()).Value;
      }
    }
    
    /// <summary>Gets or sets autoRefresh</summary>
    [IproxProperty(SequenceId = 2)]
    public PlainField AutoRefresh {
      get {
        return this.fieldAutoRefresh;
      }
      
      set {
        this.fieldAutoRefresh.Value = (value ?? new PlainField()).Value;
      }
    }
    
    /// <summary>Gets or sets snoozeDays</summary>
    [IproxProperty(SequenceId = 3)]
    public PlainField SnoozeDays {
      get {
        return this.fieldSnoozeDays;
      }
      
      set {
        this.fieldSnoozeDays.Value = (value ?? new PlainField()).Value;
      }
    }
    
    /// <summary>Gets or sets deleteAlerts</summary>
    [IproxProperty(SequenceId = 4)]
    public BooleanField DeleteAlerts {
      get {
        return this.fieldDeleteAlerts;
      }
      
      set {
        this.fieldDeleteAlerts.Value = (value ?? new BooleanField()).Value;
      }
    }
    
    /// <summary>Gets or sets nextAlertButton</summary>
    [IproxProperty(SequenceId = 5)]
    public BooleanField NextAlertButton {
      get {
        return this.fieldNextAlertButton;
      }
      
      set {
        this.fieldNextAlertButton.Value = (value ?? new BooleanField()).Value;
      }
    }
    
    /// <summary>Gets or sets profileEditButton</summary>
    [IproxProperty(SequenceId = 6)]
    public BooleanField ProfileEditButton {
      get {
        return this.fieldProfileEditButton;
      }
      
      set {
        this.fieldProfileEditButton.Value = (value ?? new BooleanField()).Value;
      }
    }
    
    /// <summary>Gets or sets zoneAliasImportant</summary>
    [IproxProperty(SequenceId = 7)]
    public SelectionField ZoneAliasImportant {
      get {
        return this.fieldZoneAliasImportant;
      }
      
      set {
        this.fieldZoneAliasImportant.Value = (value ?? new SelectionField()).Value;
      }
    }
    
    /// <summary>Gets or sets zoneAliasPersonal</summary>
    [IproxProperty(SequenceId = 8)]
    public SelectionField ZoneAliasPersonal {
      get {
        return this.fieldZoneAliasPersonal;
      }
      
      set {
        this.fieldZoneAliasPersonal.Value = (value ?? new SelectionField()).Value;
      }
    }
  }
  
  /// <summary>Social - Favorieten: Favorietenknop (class)</summary>
  [IproxPagetype(Alias = "social-bookmarks-button", Prototype = true)]
  public partial class SocialBookmarksButton : IproxCluster {
    /// <summary>Blokrol (singular)</summary>
    [IproxCluster(Alias = "Blok")]
    [IproxField(Alias = "Blokrol", SequenceId = 1)]
    private readonly SelectionField fieldBlokrol = new SelectionField();
    
    /// <summary>Gets or sets Blokrol</summary>
    [IproxProperty(SequenceId = 0)]
    public SelectionField Blokrol {
      get {
        return this.fieldBlokrol;
      }
      
      set {
        this.fieldBlokrol.Value = (value ?? new SelectionField()).Value;
      }
    }
  }
  
  /// <summary>Social - Dashboard: Mijn groepen (class)</summary>
  [IproxPagetype(Alias = "social-user-groups", Prototype = true)]
  public partial class SocialUserGroups : IproxCluster {
    /// <summary>general (singular)</summary>
    [IproxCluster(Alias = "Blok")]
    [IproxCluster(Alias = "general", SequenceId = 1)]
    private readonly SocialUserGroups.GeneralCluster fieldGeneral = new SocialUserGroups.GeneralCluster();
    
    /// <summary>Blokrol (singular)</summary>
    [IproxCluster(Alias = "Blok")]
    [IproxCluster(Alias = "cardContainer", SequenceId = 1)]
    [IproxField(Alias = "Blokrol", SequenceId = 2)]
    private readonly SelectionField fieldBlokrol = new SelectionField();
    
    /// <summary>cards (singular)</summary>
    [IproxCluster(Alias = "Blok")]
    [IproxCluster(Alias = "cards", SequenceId = 1)]
    private readonly SocialUserGroups.CardsCluster fieldCards = new SocialUserGroups.CardsCluster();
    
    /// <summary>Gets general</summary>
    [IproxProperty(SequenceId = 0)]
    public SocialUserGroups.GeneralCluster General {
      get {
        return this.fieldGeneral;
      }
    }
    
    /// <summary>Gets or sets Blokrol</summary>
    [IproxProperty(SequenceId = 1)]
    public SelectionField Blokrol {
      get {
        return this.fieldBlokrol;
      }
      
      set {
        this.fieldBlokrol.Value = (value ?? new SelectionField()).Value;
      }
    }
    
    /// <summary>Gets cards</summary>
    [IproxProperty(SequenceId = 2)]
    public SocialUserGroups.CardsCluster Cards {
      get {
        return this.fieldCards;
      }
    }
    
    /// <summary>general (class)</summary>
    [IproxCluster(Alias = "Blok")]
    [IproxCluster(Alias = "general", SequenceId = 1)]
    public partial class GeneralCluster : IproxCluster {
      /// <summary>title (singular)</summary>
      [IproxField(Alias = "title")]
      private readonly PlainField fieldTitle = new PlainField();
      
      /// <summary>Blokrol (singular)</summary>
      [IproxField(Alias = "Blokrol")]
      private readonly SelectionField fieldBlokrol = new SelectionField();
      
      /// <summary>rowsPerView (singular)</summary>
      [IproxField(Alias = "rowsPerView")]
      private readonly PlainField fieldRowsPerView = new PlainField();
      
      /// <summary>Gets or sets title</summary>
      [IproxProperty(SequenceId = 0)]
      public PlainField Title {
        get {
          return this.fieldTitle;
        }
        
        set {
          this.fieldTitle.Value = (value ?? new PlainField()).Value;
        }
      }
      
      /// <summary>Gets or sets Blokrol</summary>
      [IproxProperty(SequenceId = 1)]
      public SelectionField Blokrol {
        get {
          return this.fieldBlokrol;
        }
        
        set {
          this.fieldBlokrol.Value = (value ?? new SelectionField()).Value;
        }
      }
      
      /// <summary>Gets or sets rowsPerView</summary>
      [IproxProperty(SequenceId = 2)]
      public PlainField RowsPerView {
        get {
          return this.fieldRowsPerView;
        }
        
        set {
          this.fieldRowsPerView.Value = (value ?? new PlainField()).Value;
        }
      }
    }
    
    /// <summary>cards (class)</summary>
    [IproxCluster(Alias = "Blok")]
    [IproxCluster(Alias = "cards", SequenceId = 1)]
    public partial class CardsCluster : IproxCluster {
      /// <summary>Blokrol (singular)</summary>
      [IproxField(Alias = "Blokrol")]
      private readonly SelectionField fieldBlokrol = new SelectionField();
      
      /// <summary>Gets or sets Blokrol</summary>
      [IproxProperty(SequenceId = 0)]
      public SelectionField Blokrol {
        get {
          return this.fieldBlokrol;
        }
        
        set {
          this.fieldBlokrol.Value = (value ?? new SelectionField()).Value;
        }
      }
    }
  }
  
  /// <summary>Social - Dashboard: Recent gepubliceerde blogposts (class)</summary>
  [IproxPagetype(Alias = "social-recently-published-blog", Prototype = true)]
  public partial class SocialRecentlyPublishedBlog : IproxCluster {
    /// <summary>general (singular)</summary>
    [IproxCluster(Alias = "Blok")]
    [IproxCluster(Alias = "general", SequenceId = 1)]
    private readonly SocialRecentlyPublishedBlog.GeneralCluster fieldGeneral = new SocialRecentlyPublishedBlog.GeneralCluster();
    
    /// <summary>cards (singular)</summary>
    [IproxCluster(Alias = "Blok")]
    [IproxCluster(Alias = "cards", SequenceId = 1)]
    private readonly SocialRecentlyPublishedBlog.CardsCluster fieldCards = new SocialRecentlyPublishedBlog.CardsCluster();
    
    /// <summary>Gets general</summary>
    [IproxProperty(SequenceId = 0)]
    public SocialRecentlyPublishedBlog.GeneralCluster General {
      get {
        return this.fieldGeneral;
      }
    }
    
    /// <summary>Gets cards</summary>
    [IproxProperty(SequenceId = 1)]
    public SocialRecentlyPublishedBlog.CardsCluster Cards {
      get {
        return this.fieldCards;
      }
    }
    
    /// <summary>general (class)</summary>
    [IproxCluster(Alias = "Blok")]
    [IproxCluster(Alias = "general", SequenceId = 1)]
    public partial class GeneralCluster : IproxCluster {
      /// <summary>title (singular)</summary>
      [IproxField(Alias = "title")]
      private readonly PlainField fieldTitle = new PlainField();
      
      /// <summary>Blokrol (singular)</summary>
      [IproxField(Alias = "Blokrol")]
      private readonly SelectionField fieldBlokrol = new SelectionField();
      
      /// <summary>rowsPerView (singular)</summary>
      [IproxField(Alias = "rowsPerView")]
      private readonly PlainField fieldRowsPerView = new PlainField();
      
      /// <summary>Gets or sets title</summary>
      [IproxProperty(SequenceId = 0)]
      public PlainField Title {
        get {
          return this.fieldTitle;
        }
        
        set {
          this.fieldTitle.Value = (value ?? new PlainField()).Value;
        }
      }
      
      /// <summary>Gets or sets Blokrol</summary>
      [IproxProperty(SequenceId = 1)]
      public SelectionField Blokrol {
        get {
          return this.fieldBlokrol;
        }
        
        set {
          this.fieldBlokrol.Value = (value ?? new SelectionField()).Value;
        }
      }
      
      /// <summary>Gets or sets rowsPerView</summary>
      [IproxProperty(SequenceId = 2)]
      public PlainField RowsPerView {
        get {
          return this.fieldRowsPerView;
        }
        
        set {
          this.fieldRowsPerView.Value = (value ?? new PlainField()).Value;
        }
      }
    }
    
    /// <summary>cards (class)</summary>
    [IproxCluster(Alias = "Blok")]
    [IproxCluster(Alias = "cards", SequenceId = 1)]
    public partial class CardsCluster : IproxCluster {
      /// <summary>Blokrol (singular)</summary>
      [IproxField(Alias = "Blokrol")]
      private readonly SelectionField fieldBlokrol = new SelectionField();
      
      /// <summary>showStats (singular)</summary>
      [IproxField(Alias = "showStats")]
      private readonly BooleanField fieldShowStats = new BooleanField();
      
      /// <summary>Gets or sets Blokrol</summary>
      [IproxProperty(SequenceId = 0)]
      public SelectionField Blokrol {
        get {
          return this.fieldBlokrol;
        }
        
        set {
          this.fieldBlokrol.Value = (value ?? new SelectionField()).Value;
        }
      }
      
      /// <summary>Gets or sets showStats</summary>
      [IproxProperty(SequenceId = 1)]
      public BooleanField ShowStats {
        get {
          return this.fieldShowStats;
        }
        
        set {
          this.fieldShowStats.Value = (value ?? new BooleanField()).Value;
        }
      }
    }
  }
  
  /// <summary>Social - Microblog (class)</summary>
  [IproxPagetype(Alias = "social-microblog", Prototype = true)]
  public partial class SocialMicroblog : IproxCluster {
    /// <summary>title (singular)</summary>
    [IproxCluster(Alias = "Blok")]
    [IproxField(Alias = "title", SequenceId = 1)]
    private readonly PlainField fieldTitle = new PlainField();
    
    /// <summary>Blokrol (singular)</summary>
    [IproxCluster(Alias = "Blok")]
    [IproxField(Alias = "Blokrol", SequenceId = 1)]
    private readonly SelectionField fieldBlokrol = new SelectionField();
    
    /// <summary>access (singular)</summary>
    [IproxCluster(Alias = "Blok")]
    [IproxField(Alias = "access", SequenceId = 1)]
    private readonly SelectionField fieldAccess = new SelectionField();
    
    /// <summary>itemsPerPage (singular)</summary>
    [IproxCluster(Alias = "Blok")]
    [IproxField(Alias = "itemsPerPage", SequenceId = 1)]
    private readonly PlainField fieldItemsPerPage = new PlainField();
    
    /// <summary>maxAge (singular)</summary>
    [IproxCluster(Alias = "Blok")]
    [IproxField(Alias = "maxAge", SequenceId = 1)]
    private readonly PlainField fieldMaxAge = new PlainField();
    
    /// <summary>postsMinlength (singular)</summary>
    [IproxCluster(Alias = "Blok")]
    [IproxField(Alias = "postsMinlength", SequenceId = 1)]
    private readonly PlainField fieldPostsMinlength = new PlainField();
    
    /// <summary>postsMaxlength (singular)</summary>
    [IproxCluster(Alias = "Blok")]
    [IproxField(Alias = "postsMaxlength", SequenceId = 1)]
    private readonly PlainField fieldPostsMaxlength = new PlainField();
    
    /// <summary>height (singular)</summary>
    [IproxCluster(Alias = "Blok")]
    [IproxField(Alias = "height", SequenceId = 1)]
    private readonly PlainField fieldHeight = new PlainField();
    
    /// <summary>Gets or sets title</summary>
    [IproxProperty(SequenceId = 0)]
    public PlainField Title {
      get {
        return this.fieldTitle;
      }
      
      set {
        this.fieldTitle.Value = (value ?? new PlainField()).Value;
      }
    }
    
    /// <summary>Gets or sets Blokrol</summary>
    [IproxProperty(SequenceId = 1)]
    public SelectionField Blokrol {
      get {
        return this.fieldBlokrol;
      }
      
      set {
        this.fieldBlokrol.Value = (value ?? new SelectionField()).Value;
      }
    }
    
    /// <summary>Gets or sets access</summary>
    [IproxProperty(SequenceId = 2)]
    public SelectionField Access {
      get {
        return this.fieldAccess;
      }
      
      set {
        this.fieldAccess.Value = (value ?? new SelectionField()).Value;
      }
    }
    
    /// <summary>Gets or sets itemsPerPage</summary>
    [IproxProperty(SequenceId = 3)]
    public PlainField ItemsPerPage {
      get {
        return this.fieldItemsPerPage;
      }
      
      set {
        this.fieldItemsPerPage.Value = (value ?? new PlainField()).Value;
      }
    }
    
    /// <summary>Gets or sets maxAge</summary>
    /// <remarks>In dagen</remarks>
    [IproxProperty(SequenceId = 4)]
    public PlainField MaxAge {
      get {
        return this.fieldMaxAge;
      }
      
      set {
        this.fieldMaxAge.Value = (value ?? new PlainField()).Value;
      }
    }
    
    /// <summary>Gets or sets postsMinlength</summary>
    /// <remarks>-1 voor onbeperkt</remarks>
    [IproxProperty(SequenceId = 5)]
    public PlainField PostsMinlength {
      get {
        return this.fieldPostsMinlength;
      }
      
      set {
        this.fieldPostsMinlength.Value = (value ?? new PlainField()).Value;
      }
    }
    
    /// <summary>Gets or sets postsMaxlength</summary>
    /// <remarks>-1 voor onbeperkt</remarks>
    [IproxProperty(SequenceId = 6)]
    public PlainField PostsMaxlength {
      get {
        return this.fieldPostsMaxlength;
      }
      
      set {
        this.fieldPostsMaxlength.Value = (value ?? new PlainField()).Value;
      }
    }
    
    /// <summary>Gets or sets height</summary>
    /// <remarks>In pixels</remarks>
    [IproxProperty(SequenceId = 7)]
    public PlainField Height {
      get {
        return this.fieldHeight;
      }
      
      set {
        this.fieldHeight.Value = (value ?? new PlainField()).Value;
      }
    }
  }
  
  /// <summary>Social - Gebruikersmenu (class)</summary>
  [IproxPagetype(Alias = "social-user-menu", Prototype = true)]
  public partial class SocialUserMenu : IproxCluster {
    /// <summary>allowLogin (singular)</summary>
    [IproxCluster(Alias = "Blok")]
    [IproxField(Alias = "allowLogin", SequenceId = 1)]
    private readonly BooleanField fieldAllowLogin = new BooleanField();
    
    /// <summary>allowLogout (singular)</summary>
    [IproxCluster(Alias = "Blok")]
    [IproxField(Alias = "allowLogout", SequenceId = 1)]
    private readonly BooleanField fieldAllowLogout = new BooleanField();
    
    /// <summary>Blokrol (singular)</summary>
    [IproxCluster(Alias = "Blok")]
    [IproxField(Alias = "Blokrol", SequenceId = 1)]
    private readonly SelectionField fieldBlokrol = new SelectionField();
    
    /// <summary>Gets or sets allowLogin</summary>
    [IproxProperty(SequenceId = 0)]
    public BooleanField AllowLogin {
      get {
        return this.fieldAllowLogin;
      }
      
      set {
        this.fieldAllowLogin.Value = (value ?? new BooleanField()).Value;
      }
    }
    
    /// <summary>Gets or sets allowLogout</summary>
    [IproxProperty(SequenceId = 1)]
    public BooleanField AllowLogout {
      get {
        return this.fieldAllowLogout;
      }
      
      set {
        this.fieldAllowLogout.Value = (value ?? new BooleanField()).Value;
      }
    }
    
    /// <summary>Gets or sets Blokrol</summary>
    [IproxProperty(SequenceId = 2)]
    public SelectionField Blokrol {
      get {
        return this.fieldBlokrol;
      }
      
      set {
        this.fieldBlokrol.Value = (value ?? new SelectionField()).Value;
      }
    }
  }
  
  /// <summary>Social - Favorieten (class)</summary>
  [IproxPagetype(Alias = "social-bookmarks", Prototype = true)]
  public partial class SocialBookmarks : IproxCluster {
    /// <summary>title (singular)</summary>
    [IproxCluster(Alias = "Blok")]
    [IproxField(Alias = "title", SequenceId = 1)]
    private readonly PlainField fieldTitle = new PlainField();
    
    /// <summary>Blokrol (singular)</summary>
    [IproxCluster(Alias = "Blok")]
    [IproxField(Alias = "Blokrol", SequenceId = 1)]
    private readonly SelectionField fieldBlokrol = new SelectionField();
    
    /// <summary>Gets or sets title</summary>
    [IproxProperty(SequenceId = 0)]
    public PlainField Title {
      get {
        return this.fieldTitle;
      }
      
      set {
        this.fieldTitle.Value = (value ?? new PlainField()).Value;
      }
    }
    
    /// <summary>Gets or sets Blokrol</summary>
    [IproxProperty(SequenceId = 1)]
    public SelectionField Blokrol {
      get {
        return this.fieldBlokrol;
      }
      
      set {
        this.fieldBlokrol.Value = (value ?? new SelectionField()).Value;
      }
    }
  }
  
  /// <summary>Social - Pagina aanvullingen (class)</summary>
  [IproxPagetype(Alias = "social-enhancements", Prototype = true)]
  public partial class SocialEnhancements : IproxCluster {
    /// <summary>enabled (singular)</summary>
    [IproxCluster(Alias = "like")]
    [IproxField(Alias = "enabled", SequenceId = 1)]
    private readonly BooleanField fieldEnabled = new BooleanField();
    
    /// <summary>pageviews (singular)</summary>
    [IproxCluster(Alias = "pageviews")]
    private readonly SocialEnhancements.PageviewsCluster fieldPageviews = new SocialEnhancements.PageviewsCluster();
    
    /// <summary>comments (singular)</summary>
    [IproxCluster(Alias = "comments")]
    private readonly SocialEnhancements.CommentsCluster fieldComments = new SocialEnhancements.CommentsCluster();
    
    /// <summary>Gets or sets enabled</summary>
    [IproxProperty(SequenceId = 0)]
    public BooleanField Enabled {
      get {
        return this.fieldEnabled;
      }
      
      set {
        this.fieldEnabled.Value = (value ?? new BooleanField()).Value;
      }
    }
    
    /// <summary>Gets pageviews</summary>
    [IproxProperty(SequenceId = 1)]
    public SocialEnhancements.PageviewsCluster Pageviews {
      get {
        return this.fieldPageviews;
      }
    }
    
    /// <summary>Gets comments</summary>
    [IproxProperty(SequenceId = 2)]
    public SocialEnhancements.CommentsCluster Comments {
      get {
        return this.fieldComments;
      }
    }
    
    /// <summary>pageviews (class)</summary>
    [IproxCluster(Alias = "pageviews")]
    public partial class PageviewsCluster : IproxCluster {
      /// <summary>enabled (singular)</summary>
      [IproxField(Alias = "enabled")]
      private readonly BooleanField fieldEnabled = new BooleanField();
      
      /// <summary>Gets or sets enabled</summary>
      [IproxProperty(SequenceId = 0)]
      public BooleanField Enabled {
        get {
          return this.fieldEnabled;
        }
        
        set {
          this.fieldEnabled.Value = (value ?? new BooleanField()).Value;
        }
      }
    }
    
    /// <summary>comments (class)</summary>
    [IproxCluster(Alias = "comments")]
    public partial class CommentsCluster : IproxCluster {
      /// <summary>enabled (singular)</summary>
      [IproxField(Alias = "enabled")]
      private readonly BooleanField fieldEnabled = new BooleanField();
      
      /// <summary>Gets or sets enabled</summary>
      [IproxProperty(SequenceId = 0)]
      public BooleanField Enabled {
        get {
          return this.fieldEnabled;
        }
        
        set {
          this.fieldEnabled.Value = (value ?? new BooleanField()).Value;
        }
      }
    }
  }
  
  /// <summary>Index (class)</summary>
  /// <remarks>grid-blok</remarks>
  [IproxPagetype(Alias = "index", Prototype = true)]
  public partial class IndexPrototype : IproxCluster {
    /// <summary>Algemeen (singular)</summary>
    [IproxCluster(Alias = "Blok")]
    [IproxCluster(Alias = "Algemeen", SequenceId = 1)]
    private readonly IndexPrototype.AlgemeenCluster fieldAlgemeen = new IndexPrototype.AlgemeenCluster();
    
    /// <summary>Instellingen (singular)</summary>
    [IproxCluster(Alias = "Blok")]
    [IproxCluster(Alias = "Instellingen", SequenceId = 1)]
    private readonly IndexPrototype.InstellingenCluster fieldInstellingen = new IndexPrototype.InstellingenCluster();
    
    /// <summary>Index (singular)</summary>
    [IproxCluster(Alias = "Blok")]
    [IproxCluster(Alias = "Koppeling", SequenceId = 1)]
    [IproxField(Alias = "Index", SequenceId = 2)]
    private readonly LinkField fieldIndex = new LinkField();
    
    /// <summary>Gets Algemeen</summary>
    [IproxProperty(SequenceId = 0)]
    public IndexPrototype.AlgemeenCluster Algemeen {
      get {
        return this.fieldAlgemeen;
      }
    }
    
    /// <summary>Gets Instellingen</summary>
    [IproxProperty(SequenceId = 1)]
    public IndexPrototype.InstellingenCluster Instellingen {
      get {
        return this.fieldInstellingen;
      }
    }
    
    /// <summary>Gets or sets Index</summary>
    [IproxProperty(SequenceId = 2)]
    public LinkField Index {
      get {
        return this.fieldIndex;
      }
      
      set {
        this.fieldIndex.Value = (value ?? new LinkField()).Value;
      }
    }
    
    /// <summary>Algemeen (class)</summary>
    [IproxCluster(Alias = "Blok")]
    [IproxCluster(Alias = "Algemeen", SequenceId = 1)]
    public partial class AlgemeenCluster : IproxCluster {
      /// <summary>Titel (singular)</summary>
      [IproxField(Alias = "Titel")]
      private readonly PlainField fieldTitel = new PlainField();
      
      /// <summary>Foto (singular)</summary>
      [IproxField(Alias = "Foto")]
      private readonly ImageField fieldFoto = new ImageField();
      
      /// <summary>Tekst (singular)</summary>
      [IproxField(Alias = "Tekst")]
      private readonly HtmlField fieldTekst = new HtmlField();
      
      /// <summary>Blokrol (singular)</summary>
      [IproxField(Alias = "Blokrol")]
      private readonly SelectionField fieldBlokrol = new SelectionField();
      
      /// <summary>Gets or sets Titel</summary>
      [IproxProperty(SequenceId = 0)]
      public PlainField Titel {
        get {
          return this.fieldTitel;
        }
        
        set {
          this.fieldTitel.Value = (value ?? new PlainField()).Value;
        }
      }
      
      /// <summary>Gets or sets Foto</summary>
      [IproxProperty(SequenceId = 1)]
      public ImageField Foto {
        get {
          return this.fieldFoto;
        }
        
        set {
          this.fieldFoto.Value = (value ?? new ImageField()).Value;
        }
      }
      
      /// <summary>Gets or sets Tekst</summary>
      [IproxProperty(SequenceId = 2)]
      public HtmlField Tekst {
        get {
          return this.fieldTekst;
        }
        
        set {
          this.fieldTekst.Value = (value ?? new HtmlField()).Value;
        }
      }
      
      /// <summary>Gets or sets Blokrol</summary>
      [IproxProperty(SequenceId = 3)]
      public SelectionField Blokrol {
        get {
          return this.fieldBlokrol;
        }
        
        set {
          this.fieldBlokrol.Value = (value ?? new SelectionField()).Value;
        }
      }
    }
    
    /// <summary>Instellingen (class)</summary>
    [IproxCluster(Alias = "Blok")]
    [IproxCluster(Alias = "Instellingen", SequenceId = 1)]
    public partial class InstellingenCluster : IproxCluster {
      /// <summary>Range (singular)</summary>
      [IproxField(Alias = "Range")]
      private readonly PlainField fieldRange = new PlainField();
      
      /// <summary>Samenvatting tonen (singular)</summary>
      [IproxField(Alias = "Samenvatting tonen")]
      private readonly BooleanField fieldSamenvattingTonen = new BooleanField();
      
      /// <summary>Datum tonen (singular)</summary>
      [IproxField(Alias = "Datum tonen")]
      private readonly BooleanField fieldDatumTonen = new BooleanField();
      
      /// <summary>Afbeelding tonen (singular)</summary>
      [IproxField(Alias = "Afbeelding tonen")]
      private readonly BooleanField fieldAfbeeldingTonen = new BooleanField();
      
      /// <summary>Meer-link verbergen (singular)</summary>
      [IproxField(Alias = "Meer-link verbergen")]
      private readonly BooleanField fieldMeerLinkVerbergen = new BooleanField();
      
      /// <summary>Gets or sets Range</summary>
      /// <remarks>Aantal of verzameling van items uit de index</remarks>
      [IproxProperty(SequenceId = 0)]
      public PlainField Range {
        get {
          return this.fieldRange;
        }
        
        set {
          this.fieldRange.Value = (value ?? new PlainField()).Value;
        }
      }
      
      /// <summary>Gets or sets Samenvatting tonen</summary>
      [IproxProperty(SequenceId = 1)]
      public BooleanField SamenvattingTonen {
        get {
          return this.fieldSamenvattingTonen;
        }
        
        set {
          this.fieldSamenvattingTonen.Value = (value ?? new BooleanField()).Value;
        }
      }
      
      /// <summary>Gets or sets Datum tonen</summary>
      [IproxProperty(SequenceId = 2)]
      public BooleanField DatumTonen {
        get {
          return this.fieldDatumTonen;
        }
        
        set {
          this.fieldDatumTonen.Value = (value ?? new BooleanField()).Value;
        }
      }
      
      /// <summary>Gets or sets Afbeelding tonen</summary>
      [IproxProperty(SequenceId = 3)]
      public BooleanField AfbeeldingTonen {
        get {
          return this.fieldAfbeeldingTonen;
        }
        
        set {
          this.fieldAfbeeldingTonen.Value = (value ?? new BooleanField()).Value;
        }
      }
      
      /// <summary>Gets or sets Meer-link verbergen</summary>
      [IproxProperty(SequenceId = 4)]
      public BooleanField MeerLinkVerbergen {
        get {
          return this.fieldMeerLinkVerbergen;
        }
        
        set {
          this.fieldMeerLinkVerbergen.Value = (value ?? new BooleanField()).Value;
        }
      }
    }
  }
  
  /// <summary>Vraag en antwoord index (class)</summary>
  /// <remarks>grid-blok</remarks>
  [IproxPagetype(Alias = "faqindex-blok", Prototype = true)]
  public partial class FaqindexBlok : IproxCluster {
    /// <summary>Titel (singular)</summary>
    [IproxCluster(Alias = "Blok")]
    [IproxField(Alias = "Titel", SequenceId = 1)]
    private readonly PlainField fieldTitel = new PlainField();
    
    /// <summary>Blokrol (singular)</summary>
    [IproxCluster(Alias = "Blok")]
    [IproxField(Alias = "Blokrol", SequenceId = 1)]
    private readonly SelectionField fieldBlokrol = new SelectionField();
    
    /// <summary>Vraag en antwoord index (singular)</summary>
    [IproxCluster(Alias = "Blok")]
    [IproxField(Alias = "Vraag en antwoord index", SequenceId = 1)]
    private readonly LinkField fieldVraagEnAntwoordIndex = new LinkField();
    
    /// <summary>Range (singular)</summary>
    [IproxCluster(Alias = "Blok")]
    [IproxField(Alias = "Range", SequenceId = 1)]
    private readonly PlainField fieldRange = new PlainField();
    
    /// <summary>Antwoord tonen binnen blok (singular)</summary>
    [IproxCluster(Alias = "Blok")]
    [IproxField(Alias = "Antwoord tonen binnen blok", SequenceId = 1)]
    private readonly BooleanField fieldAntwoordTonenBinnenBlok = new BooleanField();
    
    /// <summary>Gets or sets Titel</summary>
    [IproxProperty(SequenceId = 0)]
    public PlainField Titel {
      get {
        return this.fieldTitel;
      }
      
      set {
        this.fieldTitel.Value = (value ?? new PlainField()).Value;
      }
    }
    
    /// <summary>Gets or sets Blokrol</summary>
    [IproxProperty(SequenceId = 1)]
    public SelectionField Blokrol {
      get {
        return this.fieldBlokrol;
      }
      
      set {
        this.fieldBlokrol.Value = (value ?? new SelectionField()).Value;
      }
    }
    
    /// <summary>Gets or sets Vraag en antwoord index</summary>
    [IproxProperty(SequenceId = 2)]
    public LinkField VraagEnAntwoordIndex {
      get {
        return this.fieldVraagEnAntwoordIndex;
      }
      
      set {
        this.fieldVraagEnAntwoordIndex.Value = (value ?? new LinkField()).Value;
      }
    }
    
    /// <summary>Gets or sets Range</summary>
    [IproxProperty(SequenceId = 3)]
    public PlainField Range {
      get {
        return this.fieldRange;
      }
      
      set {
        this.fieldRange.Value = (value ?? new PlainField()).Value;
      }
    }
    
    /// <summary>Gets or sets Antwoord tonen binnen blok</summary>
    [IproxProperty(SequenceId = 4)]
    public BooleanField AntwoordTonenBinnenBlok {
      get {
        return this.fieldAntwoordTonenBinnenBlok;
      }
      
      set {
        this.fieldAntwoordTonenBinnenBlok.Value = (value ?? new BooleanField()).Value;
      }
    }
  }
  
  /// <summary>Evenementenagenda (class)</summary>
  /// <remarks>grid-blok</remarks>
  [IproxPagetype(Alias = "evenementenagenda", Prototype = true)]
  public partial class EvenementenagendaPrototype : IproxCluster {
    /// <summary>Algemeen (singular)</summary>
    [IproxCluster(Alias = "Blok")]
    [IproxCluster(Alias = "Algemeen", SequenceId = 1)]
    private readonly EvenementenagendaPrototype.AlgemeenCluster fieldAlgemeen = new EvenementenagendaPrototype.AlgemeenCluster();
    
    /// <summary>Instellingen (singular)</summary>
    [IproxCluster(Alias = "Blok")]
    [IproxCluster(Alias = "Instellingen", SequenceId = 1)]
    private readonly EvenementenagendaPrototype.InstellingenCluster fieldInstellingen = new EvenementenagendaPrototype.InstellingenCluster();
    
    /// <summary>Evenementenagenda (singular)</summary>
    [IproxCluster(Alias = "Blok")]
    [IproxCluster(Alias = "Koppeling", SequenceId = 1)]
    [IproxField(Alias = "Evenementenagenda", SequenceId = 2)]
    private readonly LinkField fieldEvenementenagenda = new LinkField();
    
    /// <summary>Gets Algemeen</summary>
    [IproxProperty(SequenceId = 0)]
    public EvenementenagendaPrototype.AlgemeenCluster Algemeen {
      get {
        return this.fieldAlgemeen;
      }
    }
    
    /// <summary>Gets Instellingen</summary>
    [IproxProperty(SequenceId = 1)]
    public EvenementenagendaPrototype.InstellingenCluster Instellingen {
      get {
        return this.fieldInstellingen;
      }
    }
    
    /// <summary>Gets or sets Evenementenagenda</summary>
    [IproxProperty(SequenceId = 2)]
    public LinkField Evenementenagenda {
      get {
        return this.fieldEvenementenagenda;
      }
      
      set {
        this.fieldEvenementenagenda.Value = (value ?? new LinkField()).Value;
      }
    }
    
    /// <summary>Algemeen (class)</summary>
    [IproxCluster(Alias = "Blok")]
    [IproxCluster(Alias = "Algemeen", SequenceId = 1)]
    public partial class AlgemeenCluster : IproxCluster {
      /// <summary>Titel (singular)</summary>
      [IproxField(Alias = "Titel")]
      private readonly PlainField fieldTitel = new PlainField();
      
      /// <summary>Foto (singular)</summary>
      [IproxField(Alias = "Foto")]
      private readonly ImageField fieldFoto = new ImageField();
      
      /// <summary>Tekst (singular)</summary>
      [IproxField(Alias = "Tekst")]
      private readonly HtmlField fieldTekst = new HtmlField();
      
      /// <summary>Blokrol (singular)</summary>
      [IproxField(Alias = "Blokrol")]
      private readonly SelectionField fieldBlokrol = new SelectionField();
      
      /// <summary>Gets or sets Titel</summary>
      [IproxProperty(SequenceId = 0)]
      public PlainField Titel {
        get {
          return this.fieldTitel;
        }
        
        set {
          this.fieldTitel.Value = (value ?? new PlainField()).Value;
        }
      }
      
      /// <summary>Gets or sets Foto</summary>
      [IproxProperty(SequenceId = 1)]
      public ImageField Foto {
        get {
          return this.fieldFoto;
        }
        
        set {
          this.fieldFoto.Value = (value ?? new ImageField()).Value;
        }
      }
      
      /// <summary>Gets or sets Tekst</summary>
      [IproxProperty(SequenceId = 2)]
      public HtmlField Tekst {
        get {
          return this.fieldTekst;
        }
        
        set {
          this.fieldTekst.Value = (value ?? new HtmlField()).Value;
        }
      }
      
      /// <summary>Gets or sets Blokrol</summary>
      [IproxProperty(SequenceId = 3)]
      public SelectionField Blokrol {
        get {
          return this.fieldBlokrol;
        }
        
        set {
          this.fieldBlokrol.Value = (value ?? new SelectionField()).Value;
        }
      }
    }
    
    /// <summary>Instellingen (class)</summary>
    [IproxCluster(Alias = "Blok")]
    [IproxCluster(Alias = "Instellingen", SequenceId = 1)]
    public partial class InstellingenCluster : IproxCluster {
      /// <summary>Range (singular)</summary>
      [IproxField(Alias = "Range")]
      private readonly PlainField fieldRange = new PlainField();
      
      /// <summary>Samenvatting tonen (singular)</summary>
      [IproxField(Alias = "Samenvatting tonen")]
      private readonly BooleanField fieldSamenvattingTonen = new BooleanField();
      
      /// <summary>Datum tonen (singular)</summary>
      [IproxField(Alias = "Datum tonen")]
      private readonly BooleanField fieldDatumTonen = new BooleanField();
      
      /// <summary>Afbeelding tonen (singular)</summary>
      [IproxField(Alias = "Afbeelding tonen")]
      private readonly BooleanField fieldAfbeeldingTonen = new BooleanField();
      
      /// <summary>Gets or sets Range</summary>
      /// <remarks>Aantal of verzameling van items uit de index</remarks>
      [IproxProperty(SequenceId = 0)]
      public PlainField Range {
        get {
          return this.fieldRange;
        }
        
        set {
          this.fieldRange.Value = (value ?? new PlainField()).Value;
        }
      }
      
      /// <summary>Gets or sets Samenvatting tonen</summary>
      [IproxProperty(SequenceId = 1)]
      public BooleanField SamenvattingTonen {
        get {
          return this.fieldSamenvattingTonen;
        }
        
        set {
          this.fieldSamenvattingTonen.Value = (value ?? new BooleanField()).Value;
        }
      }
      
      /// <summary>Gets or sets Datum tonen</summary>
      [IproxProperty(SequenceId = 2)]
      public BooleanField DatumTonen {
        get {
          return this.fieldDatumTonen;
        }
        
        set {
          this.fieldDatumTonen.Value = (value ?? new BooleanField()).Value;
        }
      }
      
      /// <summary>Gets or sets Afbeelding tonen</summary>
      /// <remarks>toont Metaclusterveld Afbeelding voor index</remarks>
      [IproxProperty(SequenceId = 3)]
      public BooleanField AfbeeldingTonen {
        get {
          return this.fieldAfbeeldingTonen;
        }
        
        set {
          this.fieldAfbeeldingTonen.Value = (value ?? new BooleanField()).Value;
        }
      }
    }
  }
  
  /// <summary>Social - Groep volgers (class)</summary>
  [IproxPagetype(Alias = "social-group-followers", Prototype = true)]
  public partial class SocialGroupFollowers : IproxCluster {
    /// <summary>general (singular)</summary>
    [IproxCluster(Alias = "Blok")]
    [IproxCluster(Alias = "general", SequenceId = 1)]
    private readonly SocialGroupFollowers.GeneralCluster fieldGeneral = new SocialGroupFollowers.GeneralCluster();
    
    /// <summary>Blokrol (singular)</summary>
    [IproxCluster(Alias = "Blok")]
    [IproxCluster(Alias = "cards", SequenceId = 1)]
    [IproxField(Alias = "Blokrol", SequenceId = 2)]
    private readonly SelectionField fieldBlokrol = new SelectionField();
    
    /// <summary>Gets general</summary>
    [IproxProperty(SequenceId = 0)]
    public SocialGroupFollowers.GeneralCluster General {
      get {
        return this.fieldGeneral;
      }
    }
    
    /// <summary>Gets or sets Blokrol</summary>
    [IproxProperty(SequenceId = 1)]
    public SelectionField Blokrol {
      get {
        return this.fieldBlokrol;
      }
      
      set {
        this.fieldBlokrol.Value = (value ?? new SelectionField()).Value;
      }
    }
    
    /// <summary>general (class)</summary>
    [IproxCluster(Alias = "Blok")]
    [IproxCluster(Alias = "general", SequenceId = 1)]
    public partial class GeneralCluster : IproxCluster {
      /// <summary>title (singular)</summary>
      [IproxField(Alias = "title")]
      private readonly PlainField fieldTitle = new PlainField();
      
      /// <summary>Blokrol (singular)</summary>
      [IproxField(Alias = "Blokrol")]
      private readonly SelectionField fieldBlokrol = new SelectionField();
      
      /// <summary>rowsPerView (singular)</summary>
      [IproxField(Alias = "rowsPerView")]
      private readonly PlainField fieldRowsPerView = new PlainField();
      
      /// <summary>Gets or sets title</summary>
      [IproxProperty(SequenceId = 0)]
      public PlainField Title {
        get {
          return this.fieldTitle;
        }
        
        set {
          this.fieldTitle.Value = (value ?? new PlainField()).Value;
        }
      }
      
      /// <summary>Gets or sets Blokrol</summary>
      [IproxProperty(SequenceId = 1)]
      public SelectionField Blokrol {
        get {
          return this.fieldBlokrol;
        }
        
        set {
          this.fieldBlokrol.Value = (value ?? new SelectionField()).Value;
        }
      }
      
      /// <summary>Gets or sets rowsPerView</summary>
      [IproxProperty(SequenceId = 2)]
      public PlainField RowsPerView {
        get {
          return this.fieldRowsPerView;
        }
        
        set {
          this.fieldRowsPerView.Value = (value ?? new PlainField()).Value;
        }
      }
    }
  }
  
  /// <summary>Media (class)</summary>
  [IproxPagetype(Alias = "media", Prototype = true)]
  public partial class MediaPrototype : IproxCluster {
    /// <summary>Instellingen (singular)</summary>
    [IproxCluster(Alias = "Instellingen")]
    private readonly MediaPrototype.InstellingenCluster fieldInstellingen = new MediaPrototype.InstellingenCluster();
    
    /// <summary>Inleiding (singular)</summary>
    [IproxCluster(Alias = "Inhoud")]
    [IproxField(Alias = "Inleiding", SequenceId = 1)]
    private readonly HtmlField fieldInleiding = new HtmlField();
    
    /// <summary>Media (collection)</summary>
    [IproxCluster(Alias = "Media")]
    private readonly IproxClustersCollection<MediaPrototype.IMedia> fieldMedia = new IproxClustersCollection<MediaPrototype.IMedia>();
    
    /// <summary>Ondertitels (collection)</summary>
    [IproxCluster(Alias = "Ondertitels")]
    private readonly IproxClustersCollection<MediaPrototype.IOndertitels> fieldOndertitels = new IproxClustersCollection<MediaPrototype.IOndertitels>();
    
    /// <summary>Audiodescriptie (collection)</summary>
    [IproxCluster(Alias = "Audiodescriptie")]
    private readonly IproxClustersCollection<MediaPrototype.IAudiodescriptie> fieldAudiodescriptie = new IproxClustersCollection<MediaPrototype.IAudiodescriptie>();
    
    /// <summary>Download (collection)</summary>
    [IproxCluster(Alias = "Downloads")]
    [IproxCluster(Alias = "Download", SequenceId = 1)]
    private readonly IproxClustersCollection<MediaPrototype.IDownload> fieldDownload = new IproxClustersCollection<MediaPrototype.IDownload>();
    
    /// <summary>Gets Instellingen</summary>
    [IproxProperty(SequenceId = 0)]
    public MediaPrototype.InstellingenCluster Instellingen {
      get {
        return this.fieldInstellingen;
      }
    }
    
    /// <summary>Gets or sets Inleiding</summary>
    [IproxProperty(SequenceId = 1)]
    public HtmlField Inleiding {
      get {
        return this.fieldInleiding;
      }
      
      set {
        this.fieldInleiding.Value = (value ?? new HtmlField()).Value;
      }
    }
    
    /// <summary>Gets collection of Media</summary>
    [IproxProperty(SequenceId = 2)]
    public IproxClustersCollection<MediaPrototype.IMedia> Media {
      get {
        return this.fieldMedia;
      }
    }
    
    /// <summary>Gets collection of Ondertitels</summary>
    [IproxProperty(SequenceId = 3)]
    public IproxClustersCollection<MediaPrototype.IOndertitels> Ondertitels {
      get {
        return this.fieldOndertitels;
      }
    }
    
    /// <summary>Gets collection of Audiodescriptie</summary>
    [IproxProperty(SequenceId = 4)]
    public IproxClustersCollection<MediaPrototype.IAudiodescriptie> Audiodescriptie {
      get {
        return this.fieldAudiodescriptie;
      }
    }
    
    /// <summary>Gets collection of Download</summary>
    [IproxProperty(SequenceId = 5)]
    public IproxClustersCollection<MediaPrototype.IDownload> Download {
      get {
        return this.fieldDownload;
      }
    }
    
    /// <summary>Instellingen (class)</summary>
    [IproxCluster(Alias = "Instellingen")]
    public partial class InstellingenCluster : IproxCluster {
      /// <summary>Breedte (singular)</summary>
      [IproxField(Alias = "Breedte")]
      private readonly PlainField fieldBreedte = new PlainField();
      
      /// <summary>Hoogte (singular)</summary>
      [IproxField(Alias = "Hoogte")]
      private readonly PlainField fieldHoogte = new PlainField();
      
      /// <summary>Uitgeschreven tekst (singular)</summary>
      [IproxField(Alias = "Uitgeschreven tekst")]
      private readonly HtmlField fieldUitgeschrevenTekst = new HtmlField();
      
      /// <summary>Start afbeelding (singular)</summary>
      [IproxField(Alias = "Start afbeelding")]
      private readonly ImageField fieldStartAfbeelding = new ImageField();
      
      /// <summary>Gets or sets Breedte</summary>
      [IproxProperty(SequenceId = 0)]
      public PlainField Breedte {
        get {
          return this.fieldBreedte;
        }
        
        set {
          this.fieldBreedte.Value = (value ?? new PlainField()).Value;
        }
      }
      
      /// <summary>Gets or sets Hoogte</summary>
      [IproxProperty(SequenceId = 1)]
      public PlainField Hoogte {
        get {
          return this.fieldHoogte;
        }
        
        set {
          this.fieldHoogte.Value = (value ?? new PlainField()).Value;
        }
      }
      
      /// <summary>Gets or sets Uitgeschreven tekst</summary>
      [IproxProperty(SequenceId = 2)]
      public HtmlField UitgeschrevenTekst {
        get {
          return this.fieldUitgeschrevenTekst;
        }
        
        set {
          this.fieldUitgeschrevenTekst.Value = (value ?? new HtmlField()).Value;
        }
      }
      
      /// <summary>Gets or sets Start afbeelding</summary>
      [IproxProperty(SequenceId = 3)]
      public ImageField StartAfbeelding {
        get {
          return this.fieldStartAfbeelding;
        }
        
        set {
          this.fieldStartAfbeelding.Value = (value ?? new ImageField()).Value;
        }
      }
    }
    
    /// <summary>Media (interface)</summary>
    /// <see cref="LokaalCluster" />
    /// <see cref="ExternCluster" />
    [IproxCluster(Alias = "Media")]
    public partial interface IMedia : IIproxCluster {
    }
    
    /// <summary>Media (static class)</summary>
    [IproxCluster(Alias = "Media")]
    public static class MediaClusters {
      /// <summary>Lokaal (class)</summary>
      [IproxCluster(Alias = "Lokaal")]
      public partial class LokaalCluster : IproxCluster {
        /// <summary>Bestand (singular)</summary>
        [IproxField(Alias = "Bestand")]
        private readonly DocumentField fieldBestand = new DocumentField();
        
        /// <summary>Download optie (singular)</summary>
        [IproxField(Alias = "Download optie")]
        private readonly BooleanField fieldDownloadOptie = new BooleanField();
        
        /// <summary>Lengte (singular)</summary>
        [IproxField(Alias = "Lengte")]
        private readonly PlainField fieldLengte = new PlainField();
        
        /// <summary>Gets or sets Bestand</summary>
        [IproxProperty(SequenceId = 0)]
        public DocumentField Bestand {
          get {
            return this.fieldBestand;
          }
          
          set {
            this.fieldBestand.Value = (value ?? new DocumentField()).Value;
          }
        }
        
        /// <summary>Gets or sets Download optie</summary>
        [IproxProperty(SequenceId = 1)]
        public BooleanField DownloadOptie {
          get {
            return this.fieldDownloadOptie;
          }
          
          set {
            this.fieldDownloadOptie.Value = (value ?? new BooleanField()).Value;
          }
        }
        
        /// <summary>Gets or sets Lengte</summary>
        [IproxProperty(SequenceId = 2)]
        public PlainField Lengte {
          get {
            return this.fieldLengte;
          }
          
          set {
            this.fieldLengte.Value = (value ?? new PlainField()).Value;
          }
        }
      }
      
      /// <content>LokaalCluster can be used for IMedia</content>
      public partial class LokaalCluster : IMedia {
      }
      
      /// <summary>Extern (class)</summary>
      [IproxCluster(Alias = "Extern")]
      public partial class ExternCluster : IproxCluster {
        /// <summary>Link (singular)</summary>
        [IproxField(Alias = "Link")]
        private readonly AddressField fieldLink = new AddressField();
        
        /// <summary>Download optie (singular)</summary>
        [IproxField(Alias = "Download optie")]
        private readonly BooleanField fieldDownloadOptie = new BooleanField();
        
        /// <summary>Lengte (singular)</summary>
        [IproxField(Alias = "Lengte")]
        private readonly PlainField fieldLengte = new PlainField();
        
        /// <summary>Bestandsgrootte (singular)</summary>
        [IproxField(Alias = "Bestandsgrootte")]
        private readonly PlainField fieldBestandsgrootte = new PlainField();
        
        /// <summary>Gets or sets Link</summary>
        [IproxProperty(SequenceId = 0)]
        public AddressField Link {
          get {
            return this.fieldLink;
          }
          
          set {
            this.fieldLink.Value = (value ?? new AddressField()).Value;
          }
        }
        
        /// <summary>Gets or sets Download optie</summary>
        [IproxProperty(SequenceId = 1)]
        public BooleanField DownloadOptie {
          get {
            return this.fieldDownloadOptie;
          }
          
          set {
            this.fieldDownloadOptie.Value = (value ?? new BooleanField()).Value;
          }
        }
        
        /// <summary>Gets or sets Lengte</summary>
        [IproxProperty(SequenceId = 2)]
        public PlainField Lengte {
          get {
            return this.fieldLengte;
          }
          
          set {
            this.fieldLengte.Value = (value ?? new PlainField()).Value;
          }
        }
        
        /// <summary>Gets or sets Bestandsgrootte</summary>
        [IproxProperty(SequenceId = 3)]
        public PlainField Bestandsgrootte {
          get {
            return this.fieldBestandsgrootte;
          }
          
          set {
            this.fieldBestandsgrootte.Value = (value ?? new PlainField()).Value;
          }
        }
      }
      
      /// <content>ExternCluster can be used for IMedia</content>
      public partial class ExternCluster : IMedia {
      }
    }
    
    /// <summary>Ondertitels (interface)</summary>
    /// <see cref="LokaalCluster" />
    /// <see cref="ExternCluster" />
    [IproxCluster(Alias = "Ondertitels")]
    public partial interface IOndertitels : IIproxCluster {
    }
    
    /// <summary>Ondertitels (static class)</summary>
    [IproxCluster(Alias = "Ondertitels")]
    public static class OndertitelsClusters {
      /// <summary>Lokaal (class)</summary>
      [IproxCluster(Alias = "Lokaal")]
      public partial class LokaalCluster : IproxCluster {
        /// <summary>Bestand (singular)</summary>
        [IproxField(Alias = "Bestand")]
        private readonly DocumentField fieldBestand = new DocumentField();
        
        /// <summary>Download optie (singular)</summary>
        [IproxField(Alias = "Download optie")]
        private readonly BooleanField fieldDownloadOptie = new BooleanField();
        
        /// <summary>Gets or sets Bestand</summary>
        [IproxProperty(SequenceId = 0)]
        public DocumentField Bestand {
          get {
            return this.fieldBestand;
          }
          
          set {
            this.fieldBestand.Value = (value ?? new DocumentField()).Value;
          }
        }
        
        /// <summary>Gets or sets Download optie</summary>
        [IproxProperty(SequenceId = 1)]
        public BooleanField DownloadOptie {
          get {
            return this.fieldDownloadOptie;
          }
          
          set {
            this.fieldDownloadOptie.Value = (value ?? new BooleanField()).Value;
          }
        }
      }
      
      /// <content>LokaalCluster can be used for IOndertitels</content>
      public partial class LokaalCluster : IOndertitels {
      }
      
      /// <summary>Extern (class)</summary>
      [IproxCluster(Alias = "Extern")]
      public partial class ExternCluster : IproxCluster {
        /// <summary>Link (singular)</summary>
        [IproxField(Alias = "Link")]
        private readonly AddressField fieldLink = new AddressField();
        
        /// <summary>Download optie (singular)</summary>
        [IproxField(Alias = "Download optie")]
        private readonly BooleanField fieldDownloadOptie = new BooleanField();
        
        /// <summary>Gets or sets Link</summary>
        [IproxProperty(SequenceId = 0)]
        public AddressField Link {
          get {
            return this.fieldLink;
          }
          
          set {
            this.fieldLink.Value = (value ?? new AddressField()).Value;
          }
        }
        
        /// <summary>Gets or sets Download optie</summary>
        [IproxProperty(SequenceId = 1)]
        public BooleanField DownloadOptie {
          get {
            return this.fieldDownloadOptie;
          }
          
          set {
            this.fieldDownloadOptie.Value = (value ?? new BooleanField()).Value;
          }
        }
      }
      
      /// <content>ExternCluster can be used for IOndertitels</content>
      public partial class ExternCluster : IOndertitels {
      }
    }
    
    /// <summary>Audiodescriptie (interface)</summary>
    /// <see cref="LokaalCluster" />
    /// <see cref="ExternCluster" />
    [IproxCluster(Alias = "Audiodescriptie")]
    public partial interface IAudiodescriptie : IIproxCluster {
    }
    
    /// <summary>Audiodescriptie (static class)</summary>
    [IproxCluster(Alias = "Audiodescriptie")]
    public static class AudiodescriptieClusters {
      /// <summary>Lokaal (class)</summary>
      [IproxCluster(Alias = "Lokaal")]
      public partial class LokaalCluster : IproxCluster {
        /// <summary>Bestand (singular)</summary>
        [IproxField(Alias = "Bestand")]
        private readonly DocumentField fieldBestand = new DocumentField();
        
        /// <summary>Download optie (singular)</summary>
        [IproxField(Alias = "Download optie")]
        private readonly BooleanField fieldDownloadOptie = new BooleanField();
        
        /// <summary>Gets or sets Bestand</summary>
        [IproxProperty(SequenceId = 0)]
        public DocumentField Bestand {
          get {
            return this.fieldBestand;
          }
          
          set {
            this.fieldBestand.Value = (value ?? new DocumentField()).Value;
          }
        }
        
        /// <summary>Gets or sets Download optie</summary>
        [IproxProperty(SequenceId = 1)]
        public BooleanField DownloadOptie {
          get {
            return this.fieldDownloadOptie;
          }
          
          set {
            this.fieldDownloadOptie.Value = (value ?? new BooleanField()).Value;
          }
        }
      }
      
      /// <content>LokaalCluster can be used for IAudiodescriptie</content>
      public partial class LokaalCluster : IAudiodescriptie {
      }
      
      /// <summary>Extern (class)</summary>
      [IproxCluster(Alias = "Extern")]
      public partial class ExternCluster : IproxCluster {
        /// <summary>Link (singular)</summary>
        [IproxField(Alias = "Link")]
        private readonly AddressField fieldLink = new AddressField();
        
        /// <summary>Download optie (singular)</summary>
        [IproxField(Alias = "Download optie")]
        private readonly BooleanField fieldDownloadOptie = new BooleanField();
        
        /// <summary>Gets or sets Link</summary>
        [IproxProperty(SequenceId = 0)]
        public AddressField Link {
          get {
            return this.fieldLink;
          }
          
          set {
            this.fieldLink.Value = (value ?? new AddressField()).Value;
          }
        }
        
        /// <summary>Gets or sets Download optie</summary>
        [IproxProperty(SequenceId = 1)]
        public BooleanField DownloadOptie {
          get {
            return this.fieldDownloadOptie;
          }
          
          set {
            this.fieldDownloadOptie.Value = (value ?? new BooleanField()).Value;
          }
        }
      }
      
      /// <content>ExternCluster can be used for IAudiodescriptie</content>
      public partial class ExternCluster : IAudiodescriptie {
      }
    }
    
    /// <summary>Download (interface)</summary>
    /// <see cref="LokaalCluster" />
    /// <see cref="ExternCluster" />
    [IproxCluster(Alias = "Downloads")]
    [IproxCluster(Alias = "Download", SequenceId = 1)]
    public partial interface IDownload : IIproxCluster {
    }
    
    /// <summary>Download (static class)</summary>
    [IproxCluster(Alias = "Downloads")]
    [IproxCluster(Alias = "Download", SequenceId = 1)]
    public static class DownloadClusters {
      /// <summary>Lokaal (class)</summary>
      [IproxCluster(Alias = "Lokaal")]
      public partial class LokaalCluster : IproxCluster {
        /// <summary>Bestand (singular)</summary>
        [IproxField(Alias = "Bestand")]
        private readonly DocumentField fieldBestand = new DocumentField();
        
        /// <summary>Lengte (singular)</summary>
        [IproxField(Alias = "Lengte")]
        private readonly PlainField fieldLengte = new PlainField();
        
        /// <summary>Gets or sets Bestand</summary>
        [IproxProperty(SequenceId = 0)]
        public DocumentField Bestand {
          get {
            return this.fieldBestand;
          }
          
          set {
            this.fieldBestand.Value = (value ?? new DocumentField()).Value;
          }
        }
        
        /// <summary>Gets or sets Lengte</summary>
        [IproxProperty(SequenceId = 1)]
        public PlainField Lengte {
          get {
            return this.fieldLengte;
          }
          
          set {
            this.fieldLengte.Value = (value ?? new PlainField()).Value;
          }
        }
      }
      
      /// <content>LokaalCluster can be used for IDownload</content>
      public partial class LokaalCluster : IDownload {
      }
      
      /// <summary>Extern (class)</summary>
      [IproxCluster(Alias = "Extern")]
      public partial class ExternCluster : IproxCluster {
        /// <summary>Link (singular)</summary>
        [IproxField(Alias = "Link")]
        private readonly AddressField fieldLink = new AddressField();
        
        /// <summary>Lengte (singular)</summary>
        [IproxField(Alias = "Lengte")]
        private readonly PlainField fieldLengte = new PlainField();
        
        /// <summary>Bestandsgrootte (singular)</summary>
        [IproxField(Alias = "Bestandsgrootte")]
        private readonly PlainField fieldBestandsgrootte = new PlainField();
        
        /// <summary>Gets or sets Link</summary>
        [IproxProperty(SequenceId = 0)]
        public AddressField Link {
          get {
            return this.fieldLink;
          }
          
          set {
            this.fieldLink.Value = (value ?? new AddressField()).Value;
          }
        }
        
        /// <summary>Gets or sets Lengte</summary>
        [IproxProperty(SequenceId = 1)]
        public PlainField Lengte {
          get {
            return this.fieldLengte;
          }
          
          set {
            this.fieldLengte.Value = (value ?? new PlainField()).Value;
          }
        }
        
        /// <summary>Gets or sets Bestandsgrootte</summary>
        [IproxProperty(SequenceId = 2)]
        public PlainField Bestandsgrootte {
          get {
            return this.fieldBestandsgrootte;
          }
          
          set {
            this.fieldBestandsgrootte.Value = (value ?? new PlainField()).Value;
          }
        }
      }
      
      /// <content>ExternCluster can be used for IDownload</content>
      public partial class ExternCluster : IDownload {
      }
    }
  }
  
  /// <summary>Media blok (class)</summary>
  /// <remarks>grid-blok</remarks>
  [IproxPagetype(Alias = "media-blok", Prototype = true)]
  public partial class MediaBlok : IproxCluster {
    /// <summary>Titel (singular)</summary>
    [IproxCluster(Alias = "Blok")]
    [IproxField(Alias = "Titel", SequenceId = 1)]
    private readonly PlainField fieldTitel = new PlainField();
    
    /// <summary>Tekst (singular)</summary>
    [IproxCluster(Alias = "Blok")]
    [IproxField(Alias = "Tekst", SequenceId = 1)]
    private readonly HtmlField fieldTekst = new HtmlField();
    
    /// <summary>Blokrol (singular)</summary>
    [IproxCluster(Alias = "Blok")]
    [IproxField(Alias = "Blokrol", SequenceId = 1)]
    private readonly SelectionField fieldBlokrol = new SelectionField();
    
    /// <summary>Media (singular)</summary>
    [IproxCluster(Alias = "Blok")]
    [IproxField(Alias = "Media", SequenceId = 1)]
    private readonly LinkField fieldMedia = new LinkField();
    
    /// <summary>Gets or sets Titel</summary>
    [IproxProperty(SequenceId = 0)]
    public PlainField Titel {
      get {
        return this.fieldTitel;
      }
      
      set {
        this.fieldTitel.Value = (value ?? new PlainField()).Value;
      }
    }
    
    /// <summary>Gets or sets Tekst</summary>
    [IproxProperty(SequenceId = 1)]
    public HtmlField Tekst {
      get {
        return this.fieldTekst;
      }
      
      set {
        this.fieldTekst.Value = (value ?? new HtmlField()).Value;
      }
    }
    
    /// <summary>Gets or sets Blokrol</summary>
    [IproxProperty(SequenceId = 2)]
    public SelectionField Blokrol {
      get {
        return this.fieldBlokrol;
      }
      
      set {
        this.fieldBlokrol.Value = (value ?? new SelectionField()).Value;
      }
    }
    
    /// <summary>Gets or sets Media</summary>
    [IproxProperty(SequenceId = 3)]
    public LinkField Media {
      get {
        return this.fieldMedia;
      }
      
      set {
        this.fieldMedia.Value = (value ?? new LinkField()).Value;
      }
    }
  }
  
  /// <summary>Zoeken (class)</summary>
  [IproxPagetype(Alias = "zoeken")]
  public partial class Zoeken : Page {
    /// <summary>Meta (singular)</summary>
    [IproxCluster(Alias = "Meta", Prototype = true)]
    [IproxPagetype(Alias = "meta", Prototype = true, SequenceId = 1)]
    private readonly Meta fieldMeta = new Meta();
    
    /// <summary>Stramien (singular)</summary>
    [IproxCluster(Alias = "Layout")]
    [IproxField(Alias = "Stramien", SequenceId = 1)]
    private readonly LayoutField fieldStramien = new LayoutField();
    
    /// <summary>Instellingen (singular)</summary>
    [IproxCluster(Alias = "Instellingen")]
    private readonly Zoeken.InstellingenCluster fieldInstellingen = new Zoeken.InstellingenCluster();
    
    /// <summary>Link (collection)</summary>
    [IproxCluster(Alias = "Startpunt")]
    [IproxField(Alias = "Link", SequenceId = 1)]
    private readonly IproxFieldsCollection<LinkField> fieldLink = new IproxFieldsCollection<LinkField>();
    
    /// <summary>Inhoud (singular)</summary>
    [IproxCluster(Alias = "Inhoud")]
    private readonly Zoeken.InhoudCluster fieldInhoud = new Zoeken.InhoudCluster();
    
    /// <summary>Zie ook (collection)</summary>
    [IproxCluster(Alias = "Zie ook", Prototype = true)]
    [IproxPagetype(Alias = "seealso", Prototype = true, SequenceId = 1)]
    [IproxCluster(Alias = "Blokken", SequenceId = 2)]
    private readonly IproxClustersCollection<ISeealso> fieldZieOok = new IproxClustersCollection<ISeealso>();
    
    /// <summary>Initializes a new instance of the Zoeken class.</summary>
    /// <param name="item">Containing item</param>
    public Zoeken(Item item)
      : base(item) {
    }
    
    /// <summary>Prevents a default instance of the Zoeken class from being created.</summary>
    private Zoeken() {
    }
    
    /// <summary>Gets Meta</summary>
    [IproxProperty(SequenceId = 0)]
    public Meta Meta {
      get {
        return this.fieldMeta;
      }
    }
    
    /// <summary>Gets or sets Stramien</summary>
    [IproxProperty(SequenceId = 1)]
    public LayoutField Stramien {
      get {
        return this.fieldStramien;
      }
      
      set {
        this.fieldStramien.Value = (value ?? new LayoutField()).Value;
      }
    }
    
    /// <summary>Gets Instellingen</summary>
    [IproxProperty(SequenceId = 2)]
    public Zoeken.InstellingenCluster Instellingen {
      get {
        return this.fieldInstellingen;
      }
    }
    
    /// <summary>Gets collection of Link</summary>
    [IproxProperty(SequenceId = 3)]
    public IproxFieldsCollection<LinkField> Link {
      get {
        return this.fieldLink;
      }
    }
    
    /// <summary>Gets Inhoud</summary>
    [IproxProperty(SequenceId = 4)]
    public Zoeken.InhoudCluster Inhoud {
      get {
        return this.fieldInhoud;
      }
    }
    
    /// <summary>Gets collection of Zie ook</summary>
    [IproxProperty(SequenceId = 5)]
    public IproxClustersCollection<ISeealso> ZieOok {
      get {
        return this.fieldZieOok;
      }
    }
    
    /// <summary>Instellingen (class)</summary>
    [IproxCluster(Alias = "Instellingen")]
    public partial class InstellingenCluster : IproxCluster {
      /// <summary>Zoekbereik (singular)</summary>
      [IproxField(Alias = "Zoekbereik")]
      private readonly BooleanField fieldZoekbereik = new BooleanField();
      
      /// <summary>Datum (singular)</summary>
      [IproxField(Alias = "Datum")]
      private readonly BooleanField fieldDatum = new BooleanField();
      
      /// <summary>Zoekformulier persistent (singular)</summary>
      [IproxField(Alias = "Zoekformulier persistent")]
      private readonly BooleanField fieldZoekformulierPersistent = new BooleanField();
      
      /// <summary>Paginatypefilter (singular)</summary>
      [IproxField(Alias = "Paginatypefilter")]
      private readonly PicklistField fieldPaginatypefilter = new PicklistField();
      
      /// <summary>Items per pagina (singular)</summary>
      [IproxField(Alias = "Items per pagina")]
      private readonly PlainField fieldItemsPerPagina = new PlainField();
      
      /// <summary>Facetten (singular)</summary>
      [IproxField(Alias = "Facetten")]
      private readonly PicklistField fieldFacetten = new PicklistField();
      
      /// <summary>Gets or sets Zoekbereik</summary>
      [IproxProperty(SequenceId = 0)]
      public BooleanField Zoekbereik {
        get {
          return this.fieldZoekbereik;
        }
        
        set {
          this.fieldZoekbereik.Value = (value ?? new BooleanField()).Value;
        }
      }
      
      /// <summary>Gets or sets Datum</summary>
      /// <remarks>tussen 2 data zoeken</remarks>
      [IproxProperty(SequenceId = 1)]
      public BooleanField Datum {
        get {
          return this.fieldDatum;
        }
        
        set {
          this.fieldDatum.Value = (value ?? new BooleanField()).Value;
        }
      }
      
      /// <summary>Gets or sets Zoekformulier persistent</summary>
      [IproxProperty(SequenceId = 2)]
      public BooleanField ZoekformulierPersistent {
        get {
          return this.fieldZoekformulierPersistent;
        }
        
        set {
          this.fieldZoekformulierPersistent.Value = (value ?? new BooleanField()).Value;
        }
      }
      
      /// <summary>Gets or sets Paginatypefilter</summary>
      [IproxProperty(SequenceId = 3)]
      public PicklistField Paginatypefilter {
        get {
          return this.fieldPaginatypefilter;
        }
        
        set {
          this.fieldPaginatypefilter.Value = (value ?? new PicklistField()).Value;
        }
      }
      
      /// <summary>Gets or sets Items per pagina</summary>
      [IproxProperty(SequenceId = 4)]
      public PlainField ItemsPerPagina {
        get {
          return this.fieldItemsPerPagina;
        }
        
        set {
          this.fieldItemsPerPagina.Value = (value ?? new PlainField()).Value;
        }
      }
      
      /// <summary>Gets or sets Facetten</summary>
      [IproxProperty(SequenceId = 5)]
      public PicklistField Facetten {
        get {
          return this.fieldFacetten;
        }
        
        set {
          this.fieldFacetten.Value = (value ?? new PicklistField()).Value;
        }
      }
    }
    
    /// <summary>Inhoud (class)</summary>
    [IproxCluster(Alias = "Inhoud")]
    public partial class InhoudCluster : IproxCluster {
      /// <summary>Inleiding formulier (singular)</summary>
      [IproxField(Alias = "Inleiding formulier")]
      private readonly HtmlField fieldInleidingFormulier = new HtmlField();
      
      /// <summary>Tekst bij geen resultaten (singular)</summary>
      [IproxField(Alias = "Tekst bij geen resultaten")]
      private readonly HtmlField fieldTekstBijGeenResultaten = new HtmlField();
      
      /// <summary>Gets or sets Inleiding formulier</summary>
      [IproxProperty(SequenceId = 0)]
      public HtmlField InleidingFormulier {
        get {
          return this.fieldInleidingFormulier;
        }
        
        set {
          this.fieldInleidingFormulier.Value = (value ?? new HtmlField()).Value;
        }
      }
      
      /// <summary>Gets or sets Tekst bij geen resultaten</summary>
      [IproxProperty(SequenceId = 1)]
      public HtmlField TekstBijGeenResultaten {
        get {
          return this.fieldTekstBijGeenResultaten;
        }
        
        set {
          this.fieldTekstBijGeenResultaten.Value = (value ?? new HtmlField()).Value;
        }
      }
    }
  }
  
  /// <summary>Landingspagina (class)</summary>
  [IproxPagetype(Alias = "landingspagina")]
  public partial class Landingspagina : Page {
    /// <summary>Meta (singular)</summary>
    [IproxCluster(Alias = "Meta", Prototype = true)]
    [IproxPagetype(Alias = "meta", Prototype = true, SequenceId = 1)]
    private readonly Meta fieldMeta = new Meta();
    
    /// <summary>Stramien (singular)</summary>
    [IproxCluster(Alias = "Layout")]
    [IproxField(Alias = "Stramien", SequenceId = 1)]
    private readonly LayoutField fieldStramien = new LayoutField();
    
    /// <summary>Blokken (collection)</summary>
    [IproxCluster(Alias = "Blokken")]
    private readonly IproxClustersCollection<Landingspagina.IBlokken> fieldBlokken = new IproxClustersCollection<Landingspagina.IBlokken>();
    
    /// <summary>Initializes a new instance of the Landingspagina class.</summary>
    /// <param name="item">Containing item</param>
    public Landingspagina(Item item)
      : base(item) {
    }
    
    /// <summary>Prevents a default instance of the Landingspagina class from being created.</summary>
    private Landingspagina() {
    }
    
    /// <summary>Gets Meta</summary>
    [IproxProperty(SequenceId = 0)]
    public Meta Meta {
      get {
        return this.fieldMeta;
      }
    }
    
    /// <summary>Gets or sets Stramien</summary>
    [IproxProperty(SequenceId = 1)]
    public LayoutField Stramien {
      get {
        return this.fieldStramien;
      }
      
      set {
        this.fieldStramien.Value = (value ?? new LayoutField()).Value;
      }
    }
    
    /// <summary>Gets collection of Blokken</summary>
    [IproxProperty(SequenceId = 2)]
    public IproxClustersCollection<Landingspagina.IBlokken> Blokken {
      get {
        return this.fieldBlokken;
      }
    }
    
    /// <summary>Blokken (interface)</summary>
    /// <see cref="InhoudCluster" />
    /// <see cref="MediawidgetCluster" />
    /// <see cref="BannerCluster" />
    /// <see cref="FotoCluster" />
    /// <see cref="FotoalbumCluster" />
    /// <see cref="RSSCluster" />
    /// <see cref="NavigatieCluster" />
    /// <see cref="OpiniepeilingCluster" />
    /// <see cref="ApplicatielinkCluster" />
    /// <see cref="DashboardCluster" />
    /// <see cref="TrefwoordCluster" />
    /// <see cref="CarrouselCluster" />
    /// <see cref="SocialDashboardRecentGepubliceerdeBlogpostsCluster" />
    /// <see cref="SocialDashboardPersonenCluster" />
    /// <see cref="SocialDashboardMijnVolgersCluster" />
    /// <see cref="SocialDashboardMijnGroepenCluster" />
    /// <see cref="SocialMicroblogCluster" />
    /// <see cref="SocialFavorietenCluster" />
    /// <see cref="EvenementenagendaCluster" />
    /// <see cref="VraagEnAntwoordIndexCluster" />
    /// <see cref="IndexCluster" />
    /// <see cref="SocialActiviteitenCluster" />
    /// <see cref="SocialEvenementenCluster" />
    /// <see cref="MediaCluster" />
    [IproxCluster(Alias = "Blokken")]
    public partial interface IBlokken : IIproxCluster {
    }
    
    /// <summary>Blokken (static class)</summary>
    [IproxCluster(Alias = "Blokken")]
    public static class BlokkenClusters {
      /// <summary>Inhoud (class)</summary>
      [IproxCluster(Alias = "Inhoud", Prototype = true)]
      public partial class InhoudCluster : Inhoud {
      }
      
      /// <content>InhoudCluster can be used for IBlokken</content>
      public partial class InhoudCluster : IBlokken {
      }
      
      /// <summary>Mediawidget (class)</summary>
      [IproxCluster(Alias = "Mediawidget", Prototype = true)]
      public partial class MediawidgetCluster : Mediawidget {
      }
      
      /// <content>MediawidgetCluster can be used for IBlokken</content>
      public partial class MediawidgetCluster : IBlokken {
      }
      
      /// <summary>Banner (class)</summary>
      [IproxCluster(Alias = "Banner", Prototype = true)]
      public partial class BannerCluster : Banner {
      }
      
      /// <content>BannerCluster can be used for IBlokken</content>
      public partial class BannerCluster : IBlokken {
      }
      
      /// <summary>Foto (class)</summary>
      [IproxCluster(Alias = "Foto", Prototype = true)]
      public partial class FotoCluster : Foto {
      }
      
      /// <content>FotoCluster can be used for IBlokken</content>
      public partial class FotoCluster : IBlokken {
      }
      
      /// <summary>Fotoalbum (class)</summary>
      [IproxCluster(Alias = "Fotoalbum", Prototype = true)]
      public partial class FotoalbumCluster : FotoalbumPrototype {
      }
      
      /// <content>FotoalbumCluster can be used for IBlokken</content>
      public partial class FotoalbumCluster : IBlokken {
      }
      
      /// <summary>RSS (class)</summary>
      [IproxCluster(Alias = "RSS", Prototype = true)]
      public partial class RSSCluster : RssBlok {
      }
      
      /// <content>RSSCluster can be used for IBlokken</content>
      public partial class RSSCluster : IBlokken {
      }
      
      /// <summary>Navigatie (class)</summary>
      [IproxCluster(Alias = "Navigatie", Prototype = true)]
      public partial class NavigatieCluster : Navigatie {
      }
      
      /// <content>NavigatieCluster can be used for IBlokken</content>
      public partial class NavigatieCluster : IBlokken {
      }
      
      /// <summary>Opiniepeiling (class)</summary>
      [IproxCluster(Alias = "Opiniepeiling", Prototype = true)]
      public partial class OpiniepeilingCluster : OpiniepeilingBlok {
      }
      
      /// <content>OpiniepeilingCluster can be used for IBlokken</content>
      public partial class OpiniepeilingCluster : IBlokken {
      }
      
      /// <summary>Applicatielink (class)</summary>
      [IproxCluster(Alias = "Applicatielink", Prototype = true)]
      public partial class ApplicatielinkCluster : ApplicatielinkBlok {
      }
      
      /// <content>ApplicatielinkCluster can be used for IBlokken</content>
      public partial class ApplicatielinkCluster : IBlokken {
      }
      
      /// <summary>Dashboard (class)</summary>
      [IproxCluster(Alias = "Dashboard", Prototype = true)]
      public partial class DashboardCluster : Dashboard {
      }
      
      /// <content>DashboardCluster can be used for IBlokken</content>
      public partial class DashboardCluster : IBlokken {
      }
      
      /// <summary>Trefwoord (class)</summary>
      [IproxCluster(Alias = "Trefwoord", Prototype = true)]
      public partial class TrefwoordCluster : Trefwoord {
      }
      
      /// <content>TrefwoordCluster can be used for IBlokken</content>
      public partial class TrefwoordCluster : IBlokken {
      }
      
      /// <summary>Carrousel (class)</summary>
      [IproxCluster(Alias = "Carrousel", Prototype = true)]
      public partial class CarrouselCluster : CarrouselBlok {
      }
      
      /// <content>CarrouselCluster can be used for IBlokken</content>
      public partial class CarrouselCluster : IBlokken {
      }
      
      /// <summary>Social - Dashboard: Recent gepubliceerde blogposts (class)</summary>
      [IproxCluster(Alias = "Social - Dashboard: Recent gepubliceerde blogposts", Prototype = true)]
      public partial class SocialDashboardRecentGepubliceerdeBlogpostsCluster : SocialRecentlyPublishedBlog {
      }
      
      /// <content>SocialDashboardRecentGepubliceerdeBlogpostsCluster can be used for IBlokken</content>
      public partial class SocialDashboardRecentGepubliceerdeBlogpostsCluster : IBlokken {
      }
      
      /// <summary>Social - Dashboard: Personen (class)</summary>
      [IproxCluster(Alias = "Social - Dashboard: Personen", Prototype = true)]
      public partial class SocialDashboardPersonenCluster : SocialPersonFinder {
      }
      
      /// <content>SocialDashboardPersonenCluster can be used for IBlokken</content>
      public partial class SocialDashboardPersonenCluster : IBlokken {
      }
      
      /// <summary>Social - Dashboard: Mijn volgers (class)</summary>
      [IproxCluster(Alias = "Social - Dashboard: Mijn volgers", Prototype = true)]
      public partial class SocialDashboardMijnVolgersCluster : SocialUserFollowing {
      }
      
      /// <content>SocialDashboardMijnVolgersCluster can be used for IBlokken</content>
      public partial class SocialDashboardMijnVolgersCluster : IBlokken {
      }
      
      /// <summary>Social - Dashboard: Mijn groepen (class)</summary>
      [IproxCluster(Alias = "Social - Dashboard: Mijn groepen", Prototype = true)]
      public partial class SocialDashboardMijnGroepenCluster : SocialUserGroups {
      }
      
      /// <content>SocialDashboardMijnGroepenCluster can be used for IBlokken</content>
      public partial class SocialDashboardMijnGroepenCluster : IBlokken {
      }
      
      /// <summary>Social - Microblog (class)</summary>
      [IproxCluster(Alias = "Social - Microblog", Prototype = true)]
      public partial class SocialMicroblogCluster : SocialMicroblog {
      }
      
      /// <content>SocialMicroblogCluster can be used for IBlokken</content>
      public partial class SocialMicroblogCluster : IBlokken {
      }
      
      /// <summary>Social - Favorieten (class)</summary>
      [IproxCluster(Alias = "Social - Favorieten", Prototype = true)]
      public partial class SocialFavorietenCluster : SocialBookmarks {
      }
      
      /// <content>SocialFavorietenCluster can be used for IBlokken</content>
      public partial class SocialFavorietenCluster : IBlokken {
      }
      
      /// <summary>Evenementenagenda (class)</summary>
      [IproxCluster(Alias = "Evenementenagenda", Prototype = true)]
      public partial class EvenementenagendaCluster : EvenementenagendaPrototype {
      }
      
      /// <content>EvenementenagendaCluster can be used for IBlokken</content>
      public partial class EvenementenagendaCluster : IBlokken {
      }
      
      /// <summary>Vraag en antwoord index (class)</summary>
      [IproxCluster(Alias = "Vraag en antwoord index", Prototype = true)]
      public partial class VraagEnAntwoordIndexCluster : FaqindexBlok {
      }
      
      /// <content>VraagEnAntwoordIndexCluster can be used for IBlokken</content>
      public partial class VraagEnAntwoordIndexCluster : IBlokken {
      }
      
      /// <summary>Index (class)</summary>
      [IproxCluster(Alias = "Index", Prototype = true)]
      public partial class IndexCluster : IndexPrototype {
      }
      
      /// <content>IndexCluster can be used for IBlokken</content>
      public partial class IndexCluster : IBlokken {
      }
      
      /// <summary>Social - Activiteiten (class)</summary>
      [IproxCluster(Alias = "Social - Activiteiten", Prototype = true)]
      public partial class SocialActiviteitenCluster : SocialActivities {
      }
      
      /// <content>SocialActiviteitenCluster can be used for IBlokken</content>
      public partial class SocialActiviteitenCluster : IBlokken {
      }
      
      /// <summary>Social - Evenementen (class)</summary>
      [IproxCluster(Alias = "Social - Evenementen", Prototype = true)]
      public partial class SocialEvenementenCluster : SocialCalendar {
      }
      
      /// <content>SocialEvenementenCluster can be used for IBlokken</content>
      public partial class SocialEvenementenCluster : IBlokken {
      }
      
      /// <summary>Media (class)</summary>
      [IproxCluster(Alias = "Media", Prototype = true)]
      public partial class MediaCluster : MediaBlok {
      }
      
      /// <content>MediaCluster can be used for IBlokken</content>
      public partial class MediaCluster : IBlokken {
      }
    }
  }
  
  /// <summary>Header (class)</summary>
  [IproxPagetype(Alias = "header")]
  public partial class Header : Page {
    /// <summary>Blokken (collection)</summary>
    [IproxCluster(Alias = "Blokken")]
    private readonly IproxClustersCollection<Header.IBlokken> fieldBlokken = new IproxClustersCollection<Header.IBlokken>();
    
    /// <summary>Stramien (singular)</summary>
    [IproxCluster(Alias = "Layout")]
    [IproxField(Alias = "Stramien", SequenceId = 1)]
    private readonly LayoutField fieldStramien = new LayoutField();
    
    /// <summary>Initializes a new instance of the Header class.</summary>
    /// <param name="item">Containing item</param>
    public Header(Item item)
      : base(item) {
    }
    
    /// <summary>Prevents a default instance of the Header class from being created.</summary>
    private Header() {
    }
    
    /// <summary>Gets collection of Blokken</summary>
    [IproxProperty(SequenceId = 0)]
    public IproxClustersCollection<Header.IBlokken> Blokken {
      get {
        return this.fieldBlokken;
      }
    }
    
    /// <summary>Gets or sets Stramien</summary>
    [IproxProperty(SequenceId = 1)]
    public LayoutField Stramien {
      get {
        return this.fieldStramien;
      }
      
      set {
        this.fieldStramien.Value = (value ?? new LayoutField()).Value;
      }
    }
    
    /// <summary>Blokken (interface)</summary>
    /// <see cref="LogoCluster" />
    /// <see cref="PrimaireNavigatieCluster" />
    /// <see cref="SecundaireNavigatieCluster" />
    /// <see cref="ZoekenCluster" />
    /// <see cref="FotoCluster" />
    /// <see cref="KaartCluster" />
    /// <see cref="SocialGebruikersmenuCluster" />
    /// <see cref="MijnMededelingenCluster" />
    /// <see cref="InhoudCluster" />
    [IproxCluster(Alias = "Blokken")]
    public partial interface IBlokken : IIproxCluster {
    }
    
    /// <summary>Blokken (static class)</summary>
    [IproxCluster(Alias = "Blokken")]
    public static class BlokkenClusters {
      /// <summary>Logo (class)</summary>
      [IproxCluster(Alias = "Logo", Prototype = true)]
      public partial class LogoCluster : Logo {
      }
      
      /// <content>LogoCluster can be used for IBlokken</content>
      public partial class LogoCluster : IBlokken {
      }
      
      /// <summary>Primaire navigatie (class)</summary>
      [IproxCluster(Alias = "Primaire navigatie", Prototype = true)]
      public partial class PrimaireNavigatieCluster : PrimaireNavigatie {
      }
      
      /// <content>PrimaireNavigatieCluster can be used for IBlokken</content>
      public partial class PrimaireNavigatieCluster : IBlokken {
      }
      
      /// <summary>Secundaire navigatie (class)</summary>
      [IproxCluster(Alias = "Secundaire navigatie", Prototype = true)]
      public partial class SecundaireNavigatieCluster : SecundaireNavigatie {
      }
      
      /// <content>SecundaireNavigatieCluster can be used for IBlokken</content>
      public partial class SecundaireNavigatieCluster : IBlokken {
      }
      
      /// <summary>Zoeken (class)</summary>
      [IproxCluster(Alias = "Zoeken", Prototype = true)]
      public partial class ZoekenCluster : ZoekenPrototype {
      }
      
      /// <content>ZoekenCluster can be used for IBlokken</content>
      public partial class ZoekenCluster : IBlokken {
      }
      
      /// <summary>Foto (class)</summary>
      [IproxCluster(Alias = "Foto", Prototype = true)]
      public partial class FotoCluster : Foto {
      }
      
      /// <content>FotoCluster can be used for IBlokken</content>
      public partial class FotoCluster : IBlokken {
      }
      
      /// <summary>Kaart (class)</summary>
      [IproxCluster(Alias = "Kaart", Prototype = true)]
      public partial class KaartCluster : KaartBlok {
      }
      
      /// <content>KaartCluster can be used for IBlokken</content>
      public partial class KaartCluster : IBlokken {
      }
      
      /// <summary>Social - Gebruikersmenu (class)</summary>
      [IproxCluster(Alias = "Social - Gebruikersmenu", Prototype = true)]
      public partial class SocialGebruikersmenuCluster : SocialUserMenu {
      }
      
      /// <content>SocialGebruikersmenuCluster can be used for IBlokken</content>
      public partial class SocialGebruikersmenuCluster : IBlokken {
      }
      
      /// <summary>Mijn mededelingen (class)</summary>
      [IproxCluster(Alias = "Mijn mededelingen", Prototype = true)]
      public partial class MijnMededelingenCluster : SocialUserAlerts {
      }
      
      /// <content>MijnMededelingenCluster can be used for IBlokken</content>
      public partial class MijnMededelingenCluster : IBlokken {
      }
      
      /// <summary>Inhoud (class)</summary>
      [IproxCluster(Alias = "Inhoud", Prototype = true)]
      public partial class InhoudCluster : Inhoud {
      }
      
      /// <content>InhoudCluster can be used for IBlokken</content>
      public partial class InhoudCluster : IBlokken {
      }
    }
  }
  
  /// <summary>Footer (class)</summary>
  [IproxPagetype(Alias = "footer")]
  public partial class Footer : Page {
    /// <summary>Blokken (collection)</summary>
    [IproxCluster(Alias = "Blokken")]
    private readonly IproxClustersCollection<Footer.IBlokken> fieldBlokken = new IproxClustersCollection<Footer.IBlokken>();
    
    /// <summary>Stramien (singular)</summary>
    [IproxCluster(Alias = "Layout")]
    [IproxField(Alias = "Stramien", SequenceId = 1)]
    private readonly LayoutField fieldStramien = new LayoutField();
    
    /// <summary>Initializes a new instance of the Footer class.</summary>
    /// <param name="item">Containing item</param>
    public Footer(Item item)
      : base(item) {
    }
    
    /// <summary>Prevents a default instance of the Footer class from being created.</summary>
    private Footer() {
    }
    
    /// <summary>Gets collection of Blokken</summary>
    [IproxProperty(SequenceId = 0)]
    public IproxClustersCollection<Footer.IBlokken> Blokken {
      get {
        return this.fieldBlokken;
      }
    }
    
    /// <summary>Gets or sets Stramien</summary>
    [IproxProperty(SequenceId = 1)]
    public LayoutField Stramien {
      get {
        return this.fieldStramien;
      }
      
      set {
        this.fieldStramien.Value = (value ?? new LayoutField()).Value;
      }
    }
    
    /// <summary>Blokken (interface)</summary>
    /// <see cref="InhoudCluster" />
    /// <see cref="SitepadCluster" />
    /// <see cref="ZoekenCluster" />
    /// <see cref="NavigatieCluster" />
    /// <see cref="FooterSpecialsCluster" />
    /// <see cref="SocialFavorietenFavorietenknopCluster" />
    [IproxCluster(Alias = "Blokken")]
    public partial interface IBlokken : IIproxCluster {
    }
    
    /// <summary>Blokken (static class)</summary>
    [IproxCluster(Alias = "Blokken")]
    public static class BlokkenClusters {
      /// <summary>Inhoud (class)</summary>
      [IproxCluster(Alias = "Inhoud", Prototype = true)]
      public partial class InhoudCluster : Inhoud {
      }
      
      /// <content>InhoudCluster can be used for IBlokken</content>
      public partial class InhoudCluster : IBlokken {
      }
      
      /// <summary>Sitepad (class)</summary>
      [IproxCluster(Alias = "Sitepad", Prototype = true)]
      public partial class SitepadCluster : Sitepad {
      }
      
      /// <content>SitepadCluster can be used for IBlokken</content>
      public partial class SitepadCluster : IBlokken {
      }
      
      /// <summary>Zoeken (class)</summary>
      [IproxCluster(Alias = "Zoeken", Prototype = true)]
      public partial class ZoekenCluster : ZoekenPrototype {
      }
      
      /// <content>ZoekenCluster can be used for IBlokken</content>
      public partial class ZoekenCluster : IBlokken {
      }
      
      /// <summary>Navigatie (class)</summary>
      [IproxCluster(Alias = "Navigatie", Prototype = true)]
      public partial class NavigatieCluster : Navigatie {
      }
      
      /// <content>NavigatieCluster can be used for IBlokken</content>
      public partial class NavigatieCluster : IBlokken {
      }
      
      /// <summary>Footer specials (class)</summary>
      [IproxCluster(Alias = "Footer specials")]
      public partial class FooterSpecialsCluster : IproxCluster {
        /// <summary>Titel (singular)</summary>
        [IproxField(Alias = "Titel")]
        private readonly PlainField fieldTitel = new PlainField();
        
        /// <summary>Blokrol (singular)</summary>
        [IproxField(Alias = "Blokrol")]
        private readonly SelectionField fieldBlokrol = new SelectionField();
        
        /// <summary>Gets or sets Titel</summary>
        [IproxProperty(SequenceId = 0)]
        public PlainField Titel {
          get {
            return this.fieldTitel;
          }
          
          set {
            this.fieldTitel.Value = (value ?? new PlainField()).Value;
          }
        }
        
        /// <summary>Gets or sets Blokrol</summary>
        [IproxProperty(SequenceId = 1)]
        public SelectionField Blokrol {
          get {
            return this.fieldBlokrol;
          }
          
          set {
            this.fieldBlokrol.Value = (value ?? new SelectionField()).Value;
          }
        }
      }
      
      /// <content>FooterSpecialsCluster can be used for IBlokken</content>
      public partial class FooterSpecialsCluster : IBlokken {
      }
      
      /// <summary>Social - Favorieten: Favorietenknop (class)</summary>
      [IproxCluster(Alias = "Social - Favorieten: Favorietenknop", Prototype = true)]
      public partial class SocialFavorietenFavorietenknopCluster : SocialBookmarksButton {
      }
      
      /// <content>SocialFavorietenFavorietenknopCluster can be used for IBlokken</content>
      public partial class SocialFavorietenFavorietenknopCluster : IBlokken {
      }
    }
  }
  
  /// <summary>Artikel (class)</summary>
  [IproxPagetype(Alias = "artikel")]
  public partial class Artikel : Page {
    /// <summary>Meta (singular)</summary>
    [IproxCluster(Alias = "Meta", Prototype = true)]
    [IproxPagetype(Alias = "meta", Prototype = true, SequenceId = 1)]
    private readonly Meta fieldMeta = new Meta();
    
    /// <summary>Stramien (singular)</summary>
    [IproxCluster(Alias = "Layout")]
    [IproxField(Alias = "Stramien", SequenceId = 1)]
    private readonly LayoutField fieldStramien = new LayoutField();
    
    /// <summary>Visual (collection)</summary>
    [IproxCluster(Alias = "Visual")]
    [IproxCluster(Alias = "Visual", SequenceId = 1)]
    private readonly IproxClustersCollection<Artikel.IVisual> fieldVisual = new IproxClustersCollection<Artikel.IVisual>();
    
    /// <summary>Inleiding (singular)</summary>
    [IproxCluster(Alias = "Artikel", Prototype = true)]
    [IproxPagetype(Alias = "artikel", Prototype = true, SequenceId = 1)]
    [IproxCluster(Alias = "Inhoud", SequenceId = 2)]
    [IproxField(Alias = "Inleiding", SequenceId = 3)]
    private readonly HtmlField fieldInleiding = new HtmlField();
    
    /// <summary>Inhoud (singular)</summary>
    [IproxCluster(Alias = "Artikel", Prototype = true)]
    [IproxPagetype(Alias = "artikel", Prototype = true, SequenceId = 1)]
    [IproxCluster(Alias = "Inhoud", SequenceId = 2)]
    [IproxField(Alias = "Inhoud", SequenceId = 3)]
    private readonly HtmlField fieldInhoud = new HtmlField();
    
    /// <summary>Imagemap hotspot toont bij onclick (singular)</summary>
    [IproxCluster(Alias = "Artikel", Prototype = true)]
    [IproxPagetype(Alias = "artikel", Prototype = true, SequenceId = 1)]
    [IproxCluster(Alias = "Inhoud", SequenceId = 2)]
    [IproxField(Alias = "Imagemap hotspot toont bij onclick", SequenceId = 3)]
    private readonly BooleanField fieldImagemapHotspotToontBijOnclick = new BooleanField();
    
    /// <summary>Gedrag imagemap (singular)</summary>
    [IproxCluster(Alias = "Artikel", Prototype = true)]
    [IproxPagetype(Alias = "artikel", Prototype = true, SequenceId = 1)]
    [IproxCluster(Alias = "Inhoud", SequenceId = 2)]
    [IproxField(Alias = "Gedrag imagemap", SequenceId = 3)]
    private readonly SelectionField fieldGedragImagemap = new SelectionField();
    
    /// <summary>Imagemap (singular)</summary>
    [IproxCluster(Alias = "Artikel", Prototype = true)]
    [IproxPagetype(Alias = "artikel", Prototype = true, SequenceId = 1)]
    [IproxCluster(Alias = "Inhoud", SequenceId = 2)]
    [IproxField(Alias = "Imagemap", SequenceId = 3)]
    private readonly HtmlField fieldImagemap = new HtmlField();
    
    /// <summary>Zie ook (collection)</summary>
    [IproxCluster(Alias = "Zie ook", Prototype = true)]
    [IproxPagetype(Alias = "seealso", Prototype = true, SequenceId = 1)]
    [IproxCluster(Alias = "Blokken", SequenceId = 2)]
    private readonly IproxClustersCollection<ISeealso> fieldZieOok = new IproxClustersCollection<ISeealso>();
    
    /// <summary>Reactiemogelijkheid (singular)</summary>
    [IproxCluster(Alias = "Reactiemogelijkheid", Prototype = true)]
    [IproxPagetype(Alias = "feedback", Prototype = true, SequenceId = 1)]
    private readonly Feedback fieldReactiemogelijkheid = new Feedback();
    
    /// <summary>Social (singular)</summary>
    [IproxCluster(Alias = "Social", Prototype = true)]
    [IproxPagetype(Alias = "social-enhancements", Prototype = true, SequenceId = 1)]
    private readonly SocialEnhancements fieldSocial = new SocialEnhancements();
    
    /// <summary>Initializes a new instance of the Artikel class.</summary>
    /// <param name="item">Containing item</param>
    public Artikel(Item item)
      : base(item) {
    }
    
    /// <summary>Prevents a default instance of the Artikel class from being created.</summary>
    private Artikel() {
    }
    
    /// <summary>Gets Meta</summary>
    [IproxProperty(SequenceId = 0)]
    public Meta Meta {
      get {
        return this.fieldMeta;
      }
    }
    
    /// <summary>Gets or sets Stramien</summary>
    [IproxProperty(SequenceId = 1)]
    public LayoutField Stramien {
      get {
        return this.fieldStramien;
      }
      
      set {
        this.fieldStramien.Value = (value ?? new LayoutField()).Value;
      }
    }
    
    /// <summary>Gets collection of Visual</summary>
    [IproxProperty(SequenceId = 2)]
    public IproxClustersCollection<Artikel.IVisual> Visual {
      get {
        return this.fieldVisual;
      }
    }
    
    /// <summary>Gets or sets Inleiding</summary>
    [IproxProperty(SequenceId = 3)]
    public HtmlField Inleiding {
      get {
        return this.fieldInleiding;
      }
      
      set {
        this.fieldInleiding.Value = (value ?? new HtmlField()).Value;
      }
    }
    
    /// <summary>Gets or sets Inhoud</summary>
    [IproxProperty(SequenceId = 4)]
    public HtmlField Inhoud {
      get {
        return this.fieldInhoud;
      }
      
      set {
        this.fieldInhoud.Value = (value ?? new HtmlField()).Value;
      }
    }
    
    /// <summary>Gets or sets Imagemap hotspot toont bij onclick</summary>
    /// <remarks>false: hotspot toont bij onmouseover</remarks>
    [IproxProperty(SequenceId = 5)]
    public BooleanField ImagemapHotspotToontBijOnclick {
      get {
        return this.fieldImagemapHotspotToontBijOnclick;
      }
      
      set {
        this.fieldImagemapHotspotToontBijOnclick.Value = (value ?? new BooleanField()).Value;
      }
    }
    
    /// <summary>Gets or sets Gedrag imagemap</summary>
    [IproxProperty(SequenceId = 6)]
    public SelectionField GedragImagemap {
      get {
        return this.fieldGedragImagemap;
      }
      
      set {
        this.fieldGedragImagemap.Value = (value ?? new SelectionField()).Value;
      }
    }
    
    /// <summary>Gets or sets Imagemap</summary>
    [IproxProperty(SequenceId = 7)]
    public HtmlField Imagemap {
      get {
        return this.fieldImagemap;
      }
      
      set {
        this.fieldImagemap.Value = (value ?? new HtmlField()).Value;
      }
    }
    
    /// <summary>Gets collection of Zie ook</summary>
    [IproxProperty(SequenceId = 8)]
    public IproxClustersCollection<ISeealso> ZieOok {
      get {
        return this.fieldZieOok;
      }
    }
    
    /// <summary>Gets Reactiemogelijkheid</summary>
    /// <remarks>Feedback mogelijkheid toevoegen aan paginatypen</remarks>
    [IproxProperty(SequenceId = 9)]
    public Feedback Reactiemogelijkheid {
      get {
        return this.fieldReactiemogelijkheid;
      }
    }
    
    /// <summary>Gets Social</summary>
    [IproxProperty(SequenceId = 10)]
    public SocialEnhancements Social {
      get {
        return this.fieldSocial;
      }
    }
    
    /// <summary>Visual (interface)</summary>
    /// <see cref="FotoCluster" />
    /// <see cref="WidgetCluster" />
    /// <see cref="FotoalbumCluster" />
    [IproxCluster(Alias = "Visual")]
    [IproxCluster(Alias = "Visual", SequenceId = 1)]
    public partial interface IVisual : IIproxCluster {
    }
    
    /// <summary>Visual (static class)</summary>
    [IproxCluster(Alias = "Visual")]
    [IproxCluster(Alias = "Visual", SequenceId = 1)]
    public static class VisualClusters {
      /// <summary>Foto (class)</summary>
      [IproxCluster(Alias = "Foto")]
      public partial class FotoCluster : IproxCluster {
        /// <summary>Foto (singular)</summary>
        [IproxField(Alias = "Foto")]
        private readonly ImageField fieldFoto = new ImageField();
        
        /// <summary>Gets or sets Foto</summary>
        /// <remarks>maat: 940 X 220</remarks>
        [IproxProperty(SequenceId = 0)]
        public ImageField Foto {
          get {
            return this.fieldFoto;
          }
          
          set {
            this.fieldFoto.Value = (value ?? new ImageField()).Value;
          }
        }
      }
      
      /// <content>FotoCluster can be used for IVisual</content>
      public partial class FotoCluster : IVisual {
      }
      
      /// <summary>Widget (class)</summary>
      [IproxCluster(Alias = "Widget")]
      public partial class WidgetCluster : IproxCluster {
        /// <summary>Widget (singular)</summary>
        [IproxField(Alias = "Widget")]
        private readonly UnknownField fieldWidget = new UnknownField();
        
        /// <summary>Gets or sets Widget</summary>
        [IproxProperty(SequenceId = 0)]
        public UnknownField Widget {
          get {
            return this.fieldWidget;
          }
          
          set {
            this.fieldWidget.Value = (value ?? new UnknownField()).Value;
          }
        }
      }
      
      /// <content>WidgetCluster can be used for IVisual</content>
      public partial class WidgetCluster : IVisual {
      }
      
      /// <summary>Fotoalbum (class)</summary>
      [IproxCluster(Alias = "Fotoalbum", Prototype = true)]
      public partial class FotoalbumCluster : FotoalbumPrototype {
      }
      
      /// <content>FotoalbumCluster can be used for IVisual</content>
      public partial class FotoalbumCluster : IVisual {
      }
    }
  }
  
  /// <summary>Evenement (class)</summary>
  [IproxPagetype(Alias = "evenement")]
  public partial class Evenement : Page {
    /// <summary>Meta (singular)</summary>
    [IproxCluster(Alias = "Meta", Prototype = true)]
    [IproxPagetype(Alias = "meta", Prototype = true, SequenceId = 1)]
    private readonly Meta fieldMeta = new Meta();
    
    /// <summary>Inhoud (singular)</summary>
    [IproxCluster(Alias = "Inhoud")]
    private readonly Evenement.InhoudCluster fieldInhoud = new Evenement.InhoudCluster();
    
    /// <summary>Stramien (singular)</summary>
    [IproxCluster(Alias = "Layout")]
    [IproxField(Alias = "Stramien", SequenceId = 1)]
    private readonly LayoutField fieldStramien = new LayoutField();
    
    /// <summary>Initializes a new instance of the Evenement class.</summary>
    /// <param name="item">Containing item</param>
    public Evenement(Item item)
      : base(item) {
    }
    
    /// <summary>Prevents a default instance of the Evenement class from being created.</summary>
    private Evenement() {
    }
    
    /// <summary>Gets Meta</summary>
    [IproxProperty(SequenceId = 0)]
    public Meta Meta {
      get {
        return this.fieldMeta;
      }
    }
    
    /// <summary>Gets Inhoud</summary>
    [IproxProperty(SequenceId = 1)]
    public Evenement.InhoudCluster Inhoud {
      get {
        return this.fieldInhoud;
      }
    }
    
    /// <summary>Gets or sets Stramien</summary>
    [IproxProperty(SequenceId = 2)]
    public LayoutField Stramien {
      get {
        return this.fieldStramien;
      }
      
      set {
        this.fieldStramien.Value = (value ?? new LayoutField()).Value;
      }
    }
    
    /// <summary>Inhoud (class)</summary>
    [IproxCluster(Alias = "Inhoud")]
    public partial class InhoudCluster : IproxCluster {
      /// <summary>Type (singular)</summary>
      [IproxField(Alias = "Type")]
      private readonly SelectionField fieldType = new SelectionField();
      
      /// <summary>Startdatum (singular)</summary>
      [IproxField(Alias = "Startdatum")]
      private readonly DateField fieldStartdatum = new DateField();
      
      /// <summary>Einddatum (singular)</summary>
      [IproxField(Alias = "Einddatum")]
      private readonly DateField fieldEinddatum = new DateField();
      
      /// <summary>Starttijd (singular)</summary>
      [IproxField(Alias = "Starttijd")]
      private readonly TimeField fieldStarttijd = new TimeField();
      
      /// <summary>Eindtijd (singular)</summary>
      [IproxField(Alias = "Eindtijd")]
      private readonly TimeField fieldEindtijd = new TimeField();
      
      /// <summary>Locatie (singular)</summary>
      [IproxField(Alias = "Locatie")]
      private readonly PlainField fieldLocatie = new PlainField();
      
      /// <summary>Doelgroep (singular)</summary>
      [IproxField(Alias = "Doelgroep")]
      private readonly HtmlField fieldDoelgroep = new HtmlField();
      
      /// <summary>Omschrijving (singular)</summary>
      [IproxField(Alias = "Omschrijving")]
      private readonly HtmlField fieldOmschrijving = new HtmlField();
      
      /// <summary>Kosten (singular)</summary>
      [IproxField(Alias = "Kosten")]
      private readonly HtmlField fieldKosten = new HtmlField();
      
      /// <summary>Meer informatie (singular)</summary>
      [IproxField(Alias = "Meer informatie")]
      private readonly HtmlField fieldMeerInformatie = new HtmlField();
      
      /// <summary>Aanmelden (singular)</summary>
      [IproxField(Alias = "Aanmelden")]
      private readonly HtmlField fieldAanmelden = new HtmlField();
      
      /// <summary>Bijzonderheden (singular)</summary>
      [IproxField(Alias = "Bijzonderheden")]
      private readonly HtmlField fieldBijzonderheden = new HtmlField();
      
      /// <summary>Gets or sets Type</summary>
      [IproxProperty(SequenceId = 0)]
      public SelectionField Type {
        get {
          return this.fieldType;
        }
        
        set {
          this.fieldType.Value = (value ?? new SelectionField()).Value;
        }
      }
      
      /// <summary>Gets or sets Startdatum</summary>
      [IproxProperty(SequenceId = 1)]
      public DateField Startdatum {
        get {
          return this.fieldStartdatum;
        }
        
        set {
          this.fieldStartdatum.Value = (value ?? new DateField()).Value;
        }
      }
      
      /// <summary>Gets or sets Einddatum</summary>
      [IproxProperty(SequenceId = 2)]
      public DateField Einddatum {
        get {
          return this.fieldEinddatum;
        }
        
        set {
          this.fieldEinddatum.Value = (value ?? new DateField()).Value;
        }
      }
      
      /// <summary>Gets or sets Starttijd</summary>
      [IproxProperty(SequenceId = 3)]
      public TimeField Starttijd {
        get {
          return this.fieldStarttijd;
        }
        
        set {
          this.fieldStarttijd.Value = (value ?? new TimeField()).Value;
        }
      }
      
      /// <summary>Gets or sets Eindtijd</summary>
      [IproxProperty(SequenceId = 4)]
      public TimeField Eindtijd {
        get {
          return this.fieldEindtijd;
        }
        
        set {
          this.fieldEindtijd.Value = (value ?? new TimeField()).Value;
        }
      }
      
      /// <summary>Gets or sets Locatie</summary>
      [IproxProperty(SequenceId = 5)]
      public PlainField Locatie {
        get {
          return this.fieldLocatie;
        }
        
        set {
          this.fieldLocatie.Value = (value ?? new PlainField()).Value;
        }
      }
      
      /// <summary>Gets or sets Doelgroep</summary>
      [IproxProperty(SequenceId = 6)]
      public HtmlField Doelgroep {
        get {
          return this.fieldDoelgroep;
        }
        
        set {
          this.fieldDoelgroep.Value = (value ?? new HtmlField()).Value;
        }
      }
      
      /// <summary>Gets or sets Omschrijving</summary>
      [IproxProperty(SequenceId = 7)]
      public HtmlField Omschrijving {
        get {
          return this.fieldOmschrijving;
        }
        
        set {
          this.fieldOmschrijving.Value = (value ?? new HtmlField()).Value;
        }
      }
      
      /// <summary>Gets or sets Kosten</summary>
      [IproxProperty(SequenceId = 8)]
      public HtmlField Kosten {
        get {
          return this.fieldKosten;
        }
        
        set {
          this.fieldKosten.Value = (value ?? new HtmlField()).Value;
        }
      }
      
      /// <summary>Gets or sets Meer informatie</summary>
      [IproxProperty(SequenceId = 9)]
      public HtmlField MeerInformatie {
        get {
          return this.fieldMeerInformatie;
        }
        
        set {
          this.fieldMeerInformatie.Value = (value ?? new HtmlField()).Value;
        }
      }
      
      /// <summary>Gets or sets Aanmelden</summary>
      [IproxProperty(SequenceId = 10)]
      public HtmlField Aanmelden {
        get {
          return this.fieldAanmelden;
        }
        
        set {
          this.fieldAanmelden.Value = (value ?? new HtmlField()).Value;
        }
      }
      
      /// <summary>Gets or sets Bijzonderheden</summary>
      [IproxProperty(SequenceId = 11)]
      public HtmlField Bijzonderheden {
        get {
          return this.fieldBijzonderheden;
        }
        
        set {
          this.fieldBijzonderheden.Value = (value ?? new HtmlField()).Value;
        }
      }
    }
  }
  
  /// <summary>Evenementenagenda (class)</summary>
  [IproxPagetype(Alias = "evenementenagenda")]
  public partial class Evenementenagenda : Page {
    /// <summary>Meta (singular)</summary>
    [IproxCluster(Alias = "Meta", Prototype = true)]
    [IproxPagetype(Alias = "meta", Prototype = true, SequenceId = 1)]
    private readonly Meta fieldMeta = new Meta();
    
    /// <summary>Stramien (singular)</summary>
    [IproxCluster(Alias = "Layout")]
    [IproxField(Alias = "Stramien", SequenceId = 1)]
    private readonly LayoutField fieldStramien = new LayoutField();
    
    /// <summary>Instellingen (singular)</summary>
    [IproxCluster(Alias = "Instellingen")]
    private readonly Evenementenagenda.InstellingenCluster fieldInstellingen = new Evenementenagenda.InstellingenCluster();
    
    /// <summary>Inhoud (singular)</summary>
    [IproxCluster(Alias = "Inhoud")]
    private readonly Evenementenagenda.InhoudCluster fieldInhoud = new Evenementenagenda.InhoudCluster();
    
    /// <summary>Zie ook (collection)</summary>
    [IproxCluster(Alias = "Zie ook", Prototype = true)]
    [IproxPagetype(Alias = "seealso", Prototype = true, SequenceId = 1)]
    [IproxCluster(Alias = "Blokken", SequenceId = 2)]
    private readonly IproxClustersCollection<ISeealso> fieldZieOok = new IproxClustersCollection<ISeealso>();
    
    /// <summary>Initializes a new instance of the Evenementenagenda class.</summary>
    /// <param name="item">Containing item</param>
    public Evenementenagenda(Item item)
      : base(item) {
    }
    
    /// <summary>Prevents a default instance of the Evenementenagenda class from being created.</summary>
    private Evenementenagenda() {
    }
    
    /// <summary>Gets Meta</summary>
    [IproxProperty(SequenceId = 0)]
    public Meta Meta {
      get {
        return this.fieldMeta;
      }
    }
    
    /// <summary>Gets or sets Stramien</summary>
    [IproxProperty(SequenceId = 1)]
    public LayoutField Stramien {
      get {
        return this.fieldStramien;
      }
      
      set {
        this.fieldStramien.Value = (value ?? new LayoutField()).Value;
      }
    }
    
    /// <summary>Gets Instellingen</summary>
    [IproxProperty(SequenceId = 2)]
    public Evenementenagenda.InstellingenCluster Instellingen {
      get {
        return this.fieldInstellingen;
      }
    }
    
    /// <summary>Gets Inhoud</summary>
    [IproxProperty(SequenceId = 3)]
    public Evenementenagenda.InhoudCluster Inhoud {
      get {
        return this.fieldInhoud;
      }
    }
    
    /// <summary>Gets collection of Zie ook</summary>
    [IproxProperty(SequenceId = 4)]
    public IproxClustersCollection<ISeealso> ZieOok {
      get {
        return this.fieldZieOok;
      }
    }
    
    /// <summary>Instellingen (class)</summary>
    [IproxCluster(Alias = "Instellingen")]
    public partial class InstellingenCluster : IproxCluster {
      /// <summary>Link (collection)</summary>
      [IproxCluster(Alias = "Startpunt")]
      [IproxField(Alias = "Link", SequenceId = 1)]
      private readonly IproxFieldsCollection<LinkField> fieldLink = new IproxFieldsCollection<LinkField>();
      
      /// <summary>Selectiefilter (singular)</summary>
      [IproxCluster(Alias = "Selectiefilter")]
      [IproxField(Alias = "Selectiefilter", SequenceId = 1)]
      private readonly PicklistField fieldSelectiefilter = new PicklistField();
      
      /// <summary>Eigenschappen (singular)</summary>
      [IproxCluster(Alias = "Eigenschappen")]
      private readonly Evenementenagenda.InstellingenCluster.EigenschappenCluster fieldEigenschappen = new Evenementenagenda.InstellingenCluster.EigenschappenCluster();
      
      /// <summary>Gets collection of Link</summary>
      [IproxProperty(SequenceId = 0)]
      public IproxFieldsCollection<LinkField> Link {
        get {
          return this.fieldLink;
        }
      }
      
      /// <summary>Gets or sets Selectiefilter</summary>
      [IproxProperty(SequenceId = 1)]
      public PicklistField Selectiefilter {
        get {
          return this.fieldSelectiefilter;
        }
        
        set {
          this.fieldSelectiefilter.Value = (value ?? new PicklistField()).Value;
        }
      }
      
      /// <summary>Gets Eigenschappen</summary>
      [IproxProperty(SequenceId = 2)]
      public Evenementenagenda.InstellingenCluster.EigenschappenCluster Eigenschappen {
        get {
          return this.fieldEigenschappen;
        }
      }
      
      /// <summary>Eigenschappen (class)</summary>
      [IproxCluster(Alias = "Eigenschappen")]
      public partial class EigenschappenCluster : IproxCluster {
        /// <summary>Getoonde velden (singular)</summary>
        [IproxField(Alias = "Getoonde velden")]
        private readonly PicklistField fieldGetoondeVelden = new PicklistField();
        
        /// <summary>Evenementen uit het verleden tonen (singular)</summary>
        [IproxField(Alias = "Evenementen uit het verleden tonen")]
        private readonly BooleanField fieldEvenementenUitHetVerledenTonen = new BooleanField();
        
        /// <summary>Direct alle toekomstige evenementen tonen (singular)</summary>
        [IproxField(Alias = "Direct alle toekomstige evenementen tonen")]
        private readonly BooleanField fieldDirectAlleToekomstigeEvenementenTonen = new BooleanField();
        
        /// <summary>Maand pager tonen (singular)</summary>
        [IproxField(Alias = "Maand pager tonen")]
        private readonly BooleanField fieldMaandPagerTonen = new BooleanField();
        
        /// <summary>Zoekformulier tonen (singular)</summary>
        [IproxField(Alias = "Zoekformulier tonen")]
        private readonly BooleanField fieldZoekformulierTonen = new BooleanField();
        
        /// <summary>Kalender tonen (singular)</summary>
        [IproxField(Alias = "Kalender tonen")]
        private readonly BooleanField fieldKalenderTonen = new BooleanField();
        
        /// <summary>Items per pagina (singular)</summary>
        [IproxField(Alias = "Items per pagina")]
        private readonly PlainField fieldItemsPerPagina = new PlainField();
        
        /// <summary>Gets or sets Getoonde velden</summary>
        [IproxProperty(SequenceId = 0)]
        public PicklistField GetoondeVelden {
          get {
            return this.fieldGetoondeVelden;
          }
          
          set {
            this.fieldGetoondeVelden.Value = (value ?? new PicklistField()).Value;
          }
        }
        
        /// <summary>Gets or sets Evenementen uit het verleden tonen</summary>
        [IproxProperty(SequenceId = 1)]
        public BooleanField EvenementenUitHetVerledenTonen {
          get {
            return this.fieldEvenementenUitHetVerledenTonen;
          }
          
          set {
            this.fieldEvenementenUitHetVerledenTonen.Value = (value ?? new BooleanField()).Value;
          }
        }
        
        /// <summary>Gets or sets Direct alle toekomstige evenementen tonen</summary>
        [IproxProperty(SequenceId = 2)]
        public BooleanField DirectAlleToekomstigeEvenementenTonen {
          get {
            return this.fieldDirectAlleToekomstigeEvenementenTonen;
          }
          
          set {
            this.fieldDirectAlleToekomstigeEvenementenTonen.Value = (value ?? new BooleanField()).Value;
          }
        }
        
        /// <summary>Gets or sets Maand pager tonen</summary>
        [IproxProperty(SequenceId = 3)]
        public BooleanField MaandPagerTonen {
          get {
            return this.fieldMaandPagerTonen;
          }
          
          set {
            this.fieldMaandPagerTonen.Value = (value ?? new BooleanField()).Value;
          }
        }
        
        /// <summary>Gets or sets Zoekformulier tonen</summary>
        [IproxProperty(SequenceId = 4)]
        public BooleanField ZoekformulierTonen {
          get {
            return this.fieldZoekformulierTonen;
          }
          
          set {
            this.fieldZoekformulierTonen.Value = (value ?? new BooleanField()).Value;
          }
        }
        
        /// <summary>Gets or sets Kalender tonen</summary>
        [IproxProperty(SequenceId = 5)]
        public BooleanField KalenderTonen {
          get {
            return this.fieldKalenderTonen;
          }
          
          set {
            this.fieldKalenderTonen.Value = (value ?? new BooleanField()).Value;
          }
        }
        
        /// <summary>Gets or sets Items per pagina</summary>
        [IproxProperty(SequenceId = 6)]
        public PlainField ItemsPerPagina {
          get {
            return this.fieldItemsPerPagina;
          }
          
          set {
            this.fieldItemsPerPagina.Value = (value ?? new PlainField()).Value;
          }
        }
      }
    }
    
    /// <summary>Inhoud (class)</summary>
    [IproxCluster(Alias = "Inhoud")]
    public partial class InhoudCluster : IproxCluster {
      /// <summary>Inleiding (singular)</summary>
      [IproxField(Alias = "Inleiding")]
      private readonly HtmlField fieldInleiding = new HtmlField();
      
      /// <summary>Tekst bij lege agenda (singular)</summary>
      [IproxField(Alias = "Tekst bij lege agenda")]
      private readonly HtmlField fieldTekstBijLegeAgenda = new HtmlField();
      
      /// <summary>Tekst bij niets gevonden (singular)</summary>
      [IproxField(Alias = "Tekst bij niets gevonden")]
      private readonly HtmlField fieldTekstBijNietsGevonden = new HtmlField();
      
      /// <summary>Gets or sets Inleiding</summary>
      [IproxProperty(SequenceId = 0)]
      public HtmlField Inleiding {
        get {
          return this.fieldInleiding;
        }
        
        set {
          this.fieldInleiding.Value = (value ?? new HtmlField()).Value;
        }
      }
      
      /// <summary>Gets or sets Tekst bij lege agenda</summary>
      [IproxProperty(SequenceId = 1)]
      public HtmlField TekstBijLegeAgenda {
        get {
          return this.fieldTekstBijLegeAgenda;
        }
        
        set {
          this.fieldTekstBijLegeAgenda.Value = (value ?? new HtmlField()).Value;
        }
      }
      
      /// <summary>Gets or sets Tekst bij niets gevonden</summary>
      [IproxProperty(SequenceId = 2)]
      public HtmlField TekstBijNietsGevonden {
        get {
          return this.fieldTekstBijNietsGevonden;
        }
        
        set {
          this.fieldTekstBijNietsGevonden.Value = (value ?? new HtmlField()).Value;
        }
      }
    }
  }
  
  /// <summary>Vraag en antwoord (class)</summary>
  [IproxPagetype(Alias = "faq")]
  public partial class Faq : Page {
    /// <summary>Meta (singular)</summary>
    [IproxCluster(Alias = "Meta", Prototype = true)]
    [IproxPagetype(Alias = "meta", Prototype = true, SequenceId = 1)]
    private readonly Meta fieldMeta = new Meta();
    
    /// <summary>Stramien (singular)</summary>
    [IproxCluster(Alias = "Layout")]
    [IproxField(Alias = "Stramien", SequenceId = 1)]
    private readonly LayoutField fieldStramien = new LayoutField();
    
    /// <summary>Vraag en antwoord (singular)</summary>
    [IproxCluster(Alias = "Vraag en antwoord", Prototype = true)]
    [IproxPagetype(Alias = "faq", Prototype = true, SequenceId = 1)]
    private readonly FaqPrototype fieldVraagEnAntwoord = new FaqPrototype();
    
    /// <summary>Initializes a new instance of the Faq class.</summary>
    /// <param name="item">Containing item</param>
    public Faq(Item item)
      : base(item) {
    }
    
    /// <summary>Prevents a default instance of the Faq class from being created.</summary>
    private Faq() {
    }
    
    /// <summary>Gets Meta</summary>
    [IproxProperty(SequenceId = 0)]
    public Meta Meta {
      get {
        return this.fieldMeta;
      }
    }
    
    /// <summary>Gets or sets Stramien</summary>
    [IproxProperty(SequenceId = 1)]
    public LayoutField Stramien {
      get {
        return this.fieldStramien;
      }
      
      set {
        this.fieldStramien.Value = (value ?? new LayoutField()).Value;
      }
    }
    
    /// <summary>Gets Vraag en antwoord</summary>
    /// <remarks>Module gebruikt prototype Vraag en antwoord</remarks>
    [IproxProperty(SequenceId = 2)]
    public FaqPrototype VraagEnAntwoord {
      get {
        return this.fieldVraagEnAntwoord;
      }
    }
  }
  
  /// <summary>Vraag en antwoord index (class)</summary>
  [IproxPagetype(Alias = "faqindex")]
  public partial class Faqindex : Page {
    /// <summary>Meta (singular)</summary>
    [IproxCluster(Alias = "Meta", Prototype = true)]
    [IproxPagetype(Alias = "meta", Prototype = true, SequenceId = 1)]
    private readonly Meta fieldMeta = new Meta();
    
    /// <summary>Stramien (singular)</summary>
    [IproxCluster(Alias = "Layout")]
    [IproxField(Alias = "Stramien", SequenceId = 1)]
    private readonly LayoutField fieldStramien = new LayoutField();
    
    /// <summary>Vraag en antwoord index (singular)</summary>
    [IproxCluster(Alias = "Vraag en antwoord index", Prototype = true)]
    [IproxPagetype(Alias = "selectie", Prototype = true, SequenceId = 1)]
    private readonly Selectie fieldVraagEnAntwoordIndex = new Selectie();
    
    /// <summary>Zie ook (collection)</summary>
    [IproxCluster(Alias = "Zie ook", Prototype = true)]
    [IproxPagetype(Alias = "seealso", Prototype = true, SequenceId = 1)]
    [IproxCluster(Alias = "Blokken", SequenceId = 2)]
    private readonly IproxClustersCollection<ISeealso> fieldZieOok = new IproxClustersCollection<ISeealso>();
    
    /// <summary>Initializes a new instance of the Faqindex class.</summary>
    /// <param name="item">Containing item</param>
    public Faqindex(Item item)
      : base(item) {
    }
    
    /// <summary>Prevents a default instance of the Faqindex class from being created.</summary>
    private Faqindex() {
    }
    
    /// <summary>Gets Meta</summary>
    [IproxProperty(SequenceId = 0)]
    public Meta Meta {
      get {
        return this.fieldMeta;
      }
    }
    
    /// <summary>Gets or sets Stramien</summary>
    [IproxProperty(SequenceId = 1)]
    public LayoutField Stramien {
      get {
        return this.fieldStramien;
      }
      
      set {
        this.fieldStramien.Value = (value ?? new LayoutField()).Value;
      }
    }
    
    /// <summary>Gets Vraag en antwoord index</summary>
    [IproxProperty(SequenceId = 2)]
    public Selectie VraagEnAntwoordIndex {
      get {
        return this.fieldVraagEnAntwoordIndex;
      }
    }
    
    /// <summary>Gets collection of Zie ook</summary>
    [IproxProperty(SequenceId = 3)]
    public IproxClustersCollection<ISeealso> ZieOok {
      get {
        return this.fieldZieOok;
      }
    }
  }
  
  /// <summary>Applicatielink (class)</summary>
  [IproxPagetype(Alias = "applicatielink")]
  public partial class Applicatielink : Page {
    /// <summary>Meta (singular)</summary>
    [IproxCluster(Alias = "Meta", Prototype = true)]
    [IproxPagetype(Alias = "meta", Prototype = true, SequenceId = 1)]
    private readonly Meta fieldMeta = new Meta();
    
    /// <summary>Stramien (singular)</summary>
    [IproxCluster(Alias = "Layout")]
    [IproxField(Alias = "Stramien", SequenceId = 1)]
    private readonly LayoutField fieldStramien = new LayoutField();
    
    /// <summary>Applicatielink (singular)</summary>
    [IproxCluster(Alias = "Applicatielink", Prototype = true)]
    [IproxPagetype(Alias = "applicatielink", Prototype = true, SequenceId = 1)]
    private readonly ApplicatielinkPrototype fieldValue = new ApplicatielinkPrototype();
    
    /// <summary>Zie ook (collection)</summary>
    [IproxCluster(Alias = "Zie ook", Prototype = true)]
    [IproxPagetype(Alias = "seealso", Prototype = true, SequenceId = 1)]
    [IproxCluster(Alias = "Blokken", SequenceId = 2)]
    private readonly IproxClustersCollection<ISeealso> fieldZieOok = new IproxClustersCollection<ISeealso>();
    
    /// <summary>Initializes a new instance of the Applicatielink class.</summary>
    /// <param name="item">Containing item</param>
    public Applicatielink(Item item)
      : base(item) {
    }
    
    /// <summary>Prevents a default instance of the Applicatielink class from being created.</summary>
    private Applicatielink() {
    }
    
    /// <summary>Gets Meta</summary>
    [IproxProperty(SequenceId = 0)]
    public Meta Meta {
      get {
        return this.fieldMeta;
      }
    }
    
    /// <summary>Gets or sets Stramien</summary>
    [IproxProperty(SequenceId = 1)]
    public LayoutField Stramien {
      get {
        return this.fieldStramien;
      }
      
      set {
        this.fieldStramien.Value = (value ?? new LayoutField()).Value;
      }
    }
    
    /// <summary>Gets Applicatielink</summary>
    [IproxProperty(SequenceId = 2)]
    public ApplicatielinkPrototype Value {
      get {
        return this.fieldValue;
      }
    }
    
    /// <summary>Gets collection of Zie ook</summary>
    [IproxProperty(SequenceId = 3)]
    public IproxClustersCollection<ISeealso> ZieOok {
      get {
        return this.fieldZieOok;
      }
    }
  }
  
  /// <summary>Formulier (class)</summary>
  [IproxPagetype(Alias = "enquete")]
  public partial class Enquete : Page {
    /// <summary>Meta (singular)</summary>
    [IproxCluster(Alias = "Meta", Prototype = true)]
    [IproxPagetype(Alias = "meta", Prototype = true, SequenceId = 1)]
    private readonly Meta fieldMeta = new Meta();
    
    /// <summary>Stramien (singular)</summary>
    [IproxCluster(Alias = "Layout")]
    [IproxField(Alias = "Stramien", SequenceId = 1)]
    private readonly LayoutField fieldStramien = new LayoutField();
    
    /// <summary>Instellingen (singular)</summary>
    [IproxCluster(Alias = "Formulier", Prototype = true)]
    [IproxPagetype(Alias = "formulier", Prototype = true, SequenceId = 1)]
    [IproxCluster(Alias = "Instellingen", SequenceId = 2)]
    private readonly Formulier.InstellingenCluster fieldInstellingen = new Formulier.InstellingenCluster();
    
    /// <summary>Inhoud (singular)</summary>
    [IproxCluster(Alias = "Formulier", Prototype = true)]
    [IproxPagetype(Alias = "formulier", Prototype = true, SequenceId = 1)]
    [IproxCluster(Alias = "Inhoud", SequenceId = 2)]
    private readonly Formulier.InhoudCluster fieldInhoud = new Formulier.InhoudCluster();
    
    /// <summary>Standaardvelden (singular)</summary>
    [IproxCluster(Alias = "Formulier", Prototype = true)]
    [IproxPagetype(Alias = "formulier", Prototype = true, SequenceId = 1)]
    [IproxCluster(Alias = "Standaardvelden", SequenceId = 2)]
    private readonly Formulier.StandaardveldenCluster fieldStandaardvelden = new Formulier.StandaardveldenCluster();
    
    /// <summary>Element (collection)</summary>
    [IproxCluster(Alias = "Formulier", Prototype = true)]
    [IproxPagetype(Alias = "formulier", Prototype = true, SequenceId = 1)]
    [IproxCluster(Alias = "Element", SequenceId = 2)]
    private readonly IproxClustersCollection<Formulier.IElement> fieldElement = new IproxClustersCollection<Formulier.IElement>();
    
    /// <summary>Zie ook (collection)</summary>
    [IproxCluster(Alias = "Zie ook", Prototype = true)]
    [IproxPagetype(Alias = "seealso", Prototype = true, SequenceId = 1)]
    [IproxCluster(Alias = "Blokken", SequenceId = 2)]
    private readonly IproxClustersCollection<ISeealso> fieldZieOok = new IproxClustersCollection<ISeealso>();
    
    /// <summary>Initializes a new instance of the Enquete class.</summary>
    /// <param name="item">Containing item</param>
    public Enquete(Item item)
      : base(item) {
    }
    
    /// <summary>Prevents a default instance of the Enquete class from being created.</summary>
    private Enquete() {
    }
    
    /// <summary>Gets Meta</summary>
    [IproxProperty(SequenceId = 0)]
    public Meta Meta {
      get {
        return this.fieldMeta;
      }
    }
    
    /// <summary>Gets or sets Stramien</summary>
    [IproxProperty(SequenceId = 1)]
    public LayoutField Stramien {
      get {
        return this.fieldStramien;
      }
      
      set {
        this.fieldStramien.Value = (value ?? new LayoutField()).Value;
      }
    }
    
    /// <summary>Gets Instellingen</summary>
    [IproxProperty(SequenceId = 2)]
    public Formulier.InstellingenCluster Instellingen {
      get {
        return this.fieldInstellingen;
      }
    }
    
    /// <summary>Gets Inhoud</summary>
    [IproxProperty(SequenceId = 3)]
    public Formulier.InhoudCluster Inhoud {
      get {
        return this.fieldInhoud;
      }
    }
    
    /// <summary>Gets Standaardvelden</summary>
    [IproxProperty(SequenceId = 4)]
    public Formulier.StandaardveldenCluster Standaardvelden {
      get {
        return this.fieldStandaardvelden;
      }
    }
    
    /// <summary>Gets collection of Element</summary>
    [IproxProperty(SequenceId = 5)]
    public IproxClustersCollection<Formulier.IElement> Element {
      get {
        return this.fieldElement;
      }
    }
    
    /// <summary>Gets collection of Zie ook</summary>
    [IproxProperty(SequenceId = 6)]
    public IproxClustersCollection<ISeealso> ZieOok {
      get {
        return this.fieldZieOok;
      }
    }
  }
  
  /// <summary>Index (class)</summary>
  [IproxPagetype(Alias = "index")]
  public partial class Index : Page {
    /// <summary>Meta (singular)</summary>
    [IproxCluster(Alias = "Meta", Prototype = true)]
    [IproxPagetype(Alias = "meta", Prototype = true, SequenceId = 1)]
    private readonly Meta fieldMeta = new Meta();
    
    /// <summary>Stramien (singular)</summary>
    [IproxCluster(Alias = "Layout")]
    [IproxField(Alias = "Stramien", SequenceId = 1)]
    private readonly LayoutField fieldStramien = new LayoutField();
    
    /// <summary>Selectie (singular)</summary>
    [IproxCluster(Alias = "Selectie", Prototype = true)]
    [IproxPagetype(Alias = "selectie", Prototype = true, SequenceId = 1)]
    private readonly Selectie fieldSelectie = new Selectie();
    
    /// <summary>Zie ook (collection)</summary>
    [IproxCluster(Alias = "Zie ook", Prototype = true)]
    [IproxPagetype(Alias = "seealso", Prototype = true, SequenceId = 1)]
    [IproxCluster(Alias = "Blokken", SequenceId = 2)]
    private readonly IproxClustersCollection<ISeealso> fieldZieOok = new IproxClustersCollection<ISeealso>();
    
    /// <summary>Initializes a new instance of the Index class.</summary>
    /// <param name="item">Containing item</param>
    public Index(Item item)
      : base(item) {
    }
    
    /// <summary>Prevents a default instance of the Index class from being created.</summary>
    private Index() {
    }
    
    /// <summary>Gets Meta</summary>
    [IproxProperty(SequenceId = 0)]
    public Meta Meta {
      get {
        return this.fieldMeta;
      }
    }
    
    /// <summary>Gets or sets Stramien</summary>
    [IproxProperty(SequenceId = 1)]
    public LayoutField Stramien {
      get {
        return this.fieldStramien;
      }
      
      set {
        this.fieldStramien.Value = (value ?? new LayoutField()).Value;
      }
    }
    
    /// <summary>Gets Selectie</summary>
    [IproxProperty(SequenceId = 2)]
    public Selectie Selectie {
      get {
        return this.fieldSelectie;
      }
    }
    
    /// <summary>Gets collection of Zie ook</summary>
    [IproxProperty(SequenceId = 3)]
    public IproxClustersCollection<ISeealso> ZieOok {
      get {
        return this.fieldZieOok;
      }
    }
  }
  
  /// <summary>Opiniepeiling (class)</summary>
  [IproxPagetype(Alias = "opiniepeiling")]
  public partial class Opiniepeiling : Page {
    /// <summary>Opiniepeiling (singular)</summary>
    [IproxCluster(Alias = "Opiniepeiling", Prototype = true)]
    [IproxPagetype(Alias = "opiniepeiling", Prototype = true, SequenceId = 1)]
    private readonly OpiniepeilingPrototype fieldValue = new OpiniepeilingPrototype();
    
    /// <summary>Initializes a new instance of the Opiniepeiling class.</summary>
    /// <param name="item">Containing item</param>
    public Opiniepeiling(Item item)
      : base(item) {
    }
    
    /// <summary>Prevents a default instance of the Opiniepeiling class from being created.</summary>
    private Opiniepeiling() {
    }
    
    /// <summary>Gets Opiniepeiling</summary>
    [IproxProperty(SequenceId = 0)]
    public OpiniepeilingPrototype Value {
      get {
        return this.fieldValue;
      }
    }
  }
  
  /// <summary>Fotoalbum (class)</summary>
  [IproxPagetype(Alias = "fotoalbum")]
  public partial class Fotoalbum : Page {
    /// <summary>Meta (singular)</summary>
    [IproxCluster(Alias = "Meta", Prototype = true)]
    [IproxPagetype(Alias = "meta", Prototype = true, SequenceId = 1)]
    private readonly Meta fieldMeta = new Meta();
    
    /// <summary>Lay-out (singular)</summary>
    [IproxCluster(Alias = "Lay-out")]
    [IproxField(Alias = "Lay-out", SequenceId = 1)]
    private readonly LayoutField fieldLayOut = new LayoutField();
    
    /// <summary>Instellingen (singular)</summary>
    [IproxCluster(Alias = "Instellingen")]
    private readonly Fotoalbum.InstellingenCluster fieldInstellingen = new Fotoalbum.InstellingenCluster();
    
    /// <summary>Inhoud (singular)</summary>
    [IproxCluster(Alias = "Inhoud")]
    private readonly Fotoalbum.InhoudCluster fieldInhoud = new Fotoalbum.InhoudCluster();
    
    /// <summary>Foto (collection)</summary>
    [IproxCluster(Alias = "Fotos")]
    [IproxCluster(Alias = "Foto", SequenceId = 1)]
    private readonly IproxClustersCollection<Fotoalbum.FotoCluster> fieldFoto = new IproxClustersCollection<Fotoalbum.FotoCluster>();
    
    /// <summary>Zie ook (collection)</summary>
    [IproxCluster(Alias = "Zie ook", Prototype = true)]
    [IproxPagetype(Alias = "seealso", Prototype = true, SequenceId = 1)]
    [IproxCluster(Alias = "Blokken", SequenceId = 2)]
    private readonly IproxClustersCollection<ISeealso> fieldZieOok = new IproxClustersCollection<ISeealso>();
    
    /// <summary>Initializes a new instance of the Fotoalbum class.</summary>
    /// <param name="item">Containing item</param>
    public Fotoalbum(Item item)
      : base(item) {
    }
    
    /// <summary>Prevents a default instance of the Fotoalbum class from being created.</summary>
    private Fotoalbum() {
    }
    
    /// <summary>Gets Meta</summary>
    [IproxProperty(SequenceId = 0)]
    public Meta Meta {
      get {
        return this.fieldMeta;
      }
    }
    
    /// <summary>Gets or sets Lay-out</summary>
    [IproxProperty(SequenceId = 1)]
    public LayoutField LayOut {
      get {
        return this.fieldLayOut;
      }
      
      set {
        this.fieldLayOut.Value = (value ?? new LayoutField()).Value;
      }
    }
    
    /// <summary>Gets Instellingen</summary>
    [IproxProperty(SequenceId = 2)]
    public Fotoalbum.InstellingenCluster Instellingen {
      get {
        return this.fieldInstellingen;
      }
    }
    
    /// <summary>Gets Inhoud</summary>
    [IproxProperty(SequenceId = 3)]
    public Fotoalbum.InhoudCluster Inhoud {
      get {
        return this.fieldInhoud;
      }
    }
    
    /// <summary>Gets collection of Foto</summary>
    [IproxProperty(SequenceId = 4)]
    public IproxClustersCollection<Fotoalbum.FotoCluster> Foto {
      get {
        return this.fieldFoto;
      }
    }
    
    /// <summary>Gets collection of Zie ook</summary>
    [IproxProperty(SequenceId = 5)]
    public IproxClustersCollection<ISeealso> ZieOok {
      get {
        return this.fieldZieOok;
      }
    }
    
    /// <summary>Instellingen (class)</summary>
    [IproxCluster(Alias = "Instellingen")]
    public partial class InstellingenCluster : IproxCluster {
      /// <summary>Titel tonen (singular)</summary>
      [IproxField(Alias = "Titel tonen")]
      private readonly BooleanField fieldTitelTonen = new BooleanField();
      
      /// <summary>Rijen per pagina (singular)</summary>
      [IproxField(Alias = "Rijen per pagina")]
      private readonly PlainField fieldRijenPerPagina = new PlainField();
      
      /// <summary>Slideshow interval (singular)</summary>
      [IproxField(Alias = "Slideshow interval")]
      private readonly PlainField fieldSlideshowInterval = new PlainField();
      
      /// <summary>Maximale breedte afbeelding (singular)</summary>
      [IproxField(Alias = "Maximale breedte afbeelding")]
      private readonly PlainField fieldMaximaleBreedteAfbeelding = new PlainField();
      
      /// <summary>Maximale hoogte afbeelding (singular)</summary>
      [IproxField(Alias = "Maximale hoogte afbeelding")]
      private readonly PlainField fieldMaximaleHoogteAfbeelding = new PlainField();
      
      /// <summary>Gets or sets Titel tonen</summary>
      [IproxProperty(SequenceId = 0)]
      public BooleanField TitelTonen {
        get {
          return this.fieldTitelTonen;
        }
        
        set {
          this.fieldTitelTonen.Value = (value ?? new BooleanField()).Value;
        }
      }
      
      /// <summary>Gets or sets Rijen per pagina</summary>
      [IproxProperty(SequenceId = 1)]
      public PlainField RijenPerPagina {
        get {
          return this.fieldRijenPerPagina;
        }
        
        set {
          this.fieldRijenPerPagina.Value = (value ?? new PlainField()).Value;
        }
      }
      
      /// <summary>Gets or sets Slideshow interval</summary>
      /// <remarks>tijd in milliseconde voor tonen volgende foto (1000 = 1 seconde)</remarks>
      [IproxProperty(SequenceId = 2)]
      public PlainField SlideshowInterval {
        get {
          return this.fieldSlideshowInterval;
        }
        
        set {
          this.fieldSlideshowInterval.Value = (value ?? new PlainField()).Value;
        }
      }
      
      /// <summary>Gets or sets Maximale breedte afbeelding</summary>
      [IproxProperty(SequenceId = 3)]
      public PlainField MaximaleBreedteAfbeelding {
        get {
          return this.fieldMaximaleBreedteAfbeelding;
        }
        
        set {
          this.fieldMaximaleBreedteAfbeelding.Value = (value ?? new PlainField()).Value;
        }
      }
      
      /// <summary>Gets or sets Maximale hoogte afbeelding</summary>
      [IproxProperty(SequenceId = 4)]
      public PlainField MaximaleHoogteAfbeelding {
        get {
          return this.fieldMaximaleHoogteAfbeelding;
        }
        
        set {
          this.fieldMaximaleHoogteAfbeelding.Value = (value ?? new PlainField()).Value;
        }
      }
    }
    
    /// <summary>Inhoud (class)</summary>
    [IproxCluster(Alias = "Inhoud")]
    public partial class InhoudCluster : IproxCluster {
      /// <summary>Inleiding (singular)</summary>
      [IproxField(Alias = "Inleiding")]
      private readonly HtmlField fieldInleiding = new HtmlField();
      
      /// <summary>Foto bestand (singular)</summary>
      [IproxField(Alias = "Foto bestand")]
      private readonly UnknownField fieldFotoBestand = new UnknownField();
      
      /// <summary>Gets or sets Inleiding</summary>
      [IproxProperty(SequenceId = 0)]
      public HtmlField Inleiding {
        get {
          return this.fieldInleiding;
        }
        
        set {
          this.fieldInleiding.Value = (value ?? new HtmlField()).Value;
        }
      }
      
      /// <summary>Gets or sets Foto bestand</summary>
      /// <remarks>ZIP bestand met foto's</remarks>
      [IproxProperty(SequenceId = 1)]
      public UnknownField FotoBestand {
        get {
          return this.fieldFotoBestand;
        }
        
        set {
          this.fieldFotoBestand.Value = (value ?? new UnknownField()).Value;
        }
      }
    }
    
    /// <summary>Foto (class)</summary>
    [IproxCluster(Alias = "Fotos")]
    [IproxCluster(Alias = "Foto", SequenceId = 1)]
    public partial class FotoCluster : IproxCluster {
      /// <summary>Titel (singular)</summary>
      [IproxField(Alias = "Titel")]
      private readonly PlainField fieldTitel = new PlainField();
      
      /// <summary>Foto (singular)</summary>
      [IproxField(Alias = "Foto")]
      private readonly ImageField fieldFoto = new ImageField();
      
      /// <summary>Beschrijving (singular)</summary>
      [IproxField(Alias = "Beschrijving")]
      private readonly HtmlField fieldBeschrijving = new HtmlField();
      
      /// <summary>Copyright (singular)</summary>
      [IproxField(Alias = "Copyright")]
      private readonly HtmlField fieldCopyright = new HtmlField();
      
      /// <summary>Gets or sets Titel</summary>
      [IproxProperty(SequenceId = 0)]
      public PlainField Titel {
        get {
          return this.fieldTitel;
        }
        
        set {
          this.fieldTitel.Value = (value ?? new PlainField()).Value;
        }
      }
      
      /// <summary>Gets or sets Foto</summary>
      [IproxProperty(SequenceId = 1)]
      public ImageField Foto {
        get {
          return this.fieldFoto;
        }
        
        set {
          this.fieldFoto.Value = (value ?? new ImageField()).Value;
        }
      }
      
      /// <summary>Gets or sets Beschrijving</summary>
      [IproxProperty(SequenceId = 2)]
      public HtmlField Beschrijving {
        get {
          return this.fieldBeschrijving;
        }
        
        set {
          this.fieldBeschrijving.Value = (value ?? new HtmlField()).Value;
        }
      }
      
      /// <summary>Gets or sets Copyright</summary>
      [IproxProperty(SequenceId = 3)]
      public HtmlField Copyright {
        get {
          return this.fieldCopyright;
        }
        
        set {
          this.fieldCopyright.Value = (value ?? new HtmlField()).Value;
        }
      }
    }
  }
  
  /// <summary>Google search (class)</summary>
  [IproxPagetype(Alias = "gsa")]
  public partial class Gsa : Page {
    /// <summary>Meta (singular)</summary>
    [IproxCluster(Alias = "Meta", Prototype = true)]
    [IproxPagetype(Alias = "meta", Prototype = true, SequenceId = 1)]
    private readonly Meta fieldMeta = new Meta();
    
    /// <summary>Instellingen (singular)</summary>
    [IproxCluster(Alias = "Instellingen")]
    private readonly Gsa.InstellingenCluster fieldInstellingen = new Gsa.InstellingenCluster();
    
    /// <summary>Initializes a new instance of the Gsa class.</summary>
    /// <param name="item">Containing item</param>
    public Gsa(Item item)
      : base(item) {
    }
    
    /// <summary>Prevents a default instance of the Gsa class from being created.</summary>
    private Gsa() {
    }
    
    /// <summary>Gets Meta</summary>
    [IproxProperty(SequenceId = 0)]
    public Meta Meta {
      get {
        return this.fieldMeta;
      }
    }
    
    /// <summary>Gets Instellingen</summary>
    [IproxProperty(SequenceId = 1)]
    public Gsa.InstellingenCluster Instellingen {
      get {
        return this.fieldInstellingen;
      }
    }
    
    /// <summary>Instellingen (class)</summary>
    [IproxCluster(Alias = "Instellingen")]
    public partial class InstellingenCluster : IproxCluster {
      /// <summary>Host (singular)</summary>
      [IproxField(Alias = "Host")]
      private readonly AddressField fieldHost = new AddressField();
      
      /// <summary>Site (singular)</summary>
      [IproxField(Alias = "Site")]
      private readonly PlainField fieldSite = new PlainField();
      
      /// <summary>Stylesheet (singular)</summary>
      [IproxField(Alias = "Stylesheet")]
      private readonly PlainField fieldStylesheet = new PlainField();
      
      /// <summary>Gets or sets Host</summary>
      [IproxProperty(SequenceId = 0)]
      public AddressField Host {
        get {
          return this.fieldHost;
        }
        
        set {
          this.fieldHost.Value = (value ?? new AddressField()).Value;
        }
      }
      
      /// <summary>Gets or sets Site</summary>
      [IproxProperty(SequenceId = 1)]
      public PlainField Site {
        get {
          return this.fieldSite;
        }
        
        set {
          this.fieldSite.Value = (value ?? new PlainField()).Value;
        }
      }
      
      /// <summary>Gets or sets Stylesheet</summary>
      [IproxProperty(SequenceId = 2)]
      public PlainField Stylesheet {
        get {
          return this.fieldStylesheet;
        }
        
        set {
          this.fieldStylesheet.Value = (value ?? new PlainField()).Value;
        }
      }
    }
  }
  
  /// <summary>Product (class)</summary>
  [IproxPagetype(Alias = "product")]
  public partial class Product : Page {
    /// <summary>Meta (singular)</summary>
    [IproxCluster(Alias = "Meta", Prototype = true)]
    [IproxPagetype(Alias = "meta", Prototype = true, SequenceId = 1)]
    private readonly Meta fieldMeta = new Meta();
    
    /// <summary>Stramien (singular)</summary>
    [IproxCluster(Alias = "Layout")]
    [IproxField(Alias = "Stramien", SequenceId = 1)]
    private readonly LayoutField fieldStramien = new LayoutField();
    
    /// <summary>Product (singular)</summary>
    [IproxCluster(Alias = "Product")]
    private readonly Product.ProductCluster fieldValue = new Product.ProductCluster();
    
    /// <summary>Zie ook (collection)</summary>
    [IproxCluster(Alias = "Zie ook", Prototype = true)]
    [IproxPagetype(Alias = "seealso", Prototype = true, SequenceId = 1)]
    [IproxCluster(Alias = "Blokken", SequenceId = 2)]
    private readonly IproxClustersCollection<ISeealso> fieldZieOok = new IproxClustersCollection<ISeealso>();
    
    /// <summary>Initializes a new instance of the Product class.</summary>
    /// <param name="item">Containing item</param>
    public Product(Item item)
      : base(item) {
    }
    
    /// <summary>Prevents a default instance of the Product class from being created.</summary>
    private Product() {
    }
    
    /// <summary>Gets Meta</summary>
    [IproxProperty(SequenceId = 0)]
    public Meta Meta {
      get {
        return this.fieldMeta;
      }
    }
    
    /// <summary>Gets or sets Stramien</summary>
    [IproxProperty(SequenceId = 1)]
    public LayoutField Stramien {
      get {
        return this.fieldStramien;
      }
      
      set {
        this.fieldStramien.Value = (value ?? new LayoutField()).Value;
      }
    }
    
    /// <summary>Gets Product</summary>
    [IproxProperty(SequenceId = 2)]
    public Product.ProductCluster Value {
      get {
        return this.fieldValue;
      }
    }
    
    /// <summary>Gets collection of Zie ook</summary>
    [IproxProperty(SequenceId = 3)]
    public IproxClustersCollection<ISeealso> ZieOok {
      get {
        return this.fieldZieOok;
      }
    }
    
    /// <summary>Product (class)</summary>
    [IproxCluster(Alias = "Product")]
    public partial class ProductCluster : IproxCluster {
      /// <summary>Beschrijving (singular)</summary>
      [IproxCluster(Alias = "Product")]
      [IproxField(Alias = "Beschrijving", SequenceId = 1)]
      private readonly HtmlField fieldBeschrijving = new HtmlField();
      
      /// <summary>Regel nu (collection)</summary>
      [IproxCluster(Alias = "Regel nu", Prototype = true)]
      [IproxPagetype(Alias = "verwijzing", Prototype = true, SequenceId = 1)]
      [IproxCluster(Alias = "Verwijzing", SequenceId = 2)]
      private readonly IproxClustersCollection<IVerwijzing> fieldRegelNu = new IproxClustersCollection<IVerwijzing>();
      
      /// <summary>Gets or sets Beschrijving</summary>
      [IproxProperty(SequenceId = 0)]
      public HtmlField Beschrijving {
        get {
          return this.fieldBeschrijving;
        }
        
        set {
          this.fieldBeschrijving.Value = (value ?? new HtmlField()).Value;
        }
      }
      
      /// <summary>Gets collection of Regel nu</summary>
      [IproxProperty(SequenceId = 1)]
      public IproxClustersCollection<IVerwijzing> RegelNu {
        get {
          return this.fieldRegelNu;
        }
      }
    }
  }
  
  /// <summary>Groep (class)</summary>
  [IproxPagetype(Alias = "workgroup")]
  public partial class Workgroup : Page {
    /// <summary>Meta (singular)</summary>
    [IproxCluster(Alias = "Meta", Prototype = true)]
    [IproxPagetype(Alias = "meta", Prototype = true, SequenceId = 1)]
    private readonly Meta fieldMeta = new Meta();
    
    /// <summary>Stramien (singular)</summary>
    [IproxCluster(Alias = "Layout")]
    [IproxField(Alias = "Stramien", SequenceId = 1)]
    private readonly LayoutField fieldStramien = new LayoutField();
    
    /// <summary>Blokken (collection)</summary>
    [IproxCluster(Alias = "Blokken")]
    private readonly IproxClustersCollection<Workgroup.IBlokken> fieldBlokken = new IproxClustersCollection<Workgroup.IBlokken>();
    
    /// <summary>Initializes a new instance of the Workgroup class.</summary>
    /// <param name="item">Containing item</param>
    public Workgroup(Item item)
      : base(item) {
    }
    
    /// <summary>Prevents a default instance of the Workgroup class from being created.</summary>
    private Workgroup() {
    }
    
    /// <summary>Gets Meta</summary>
    [IproxProperty(SequenceId = 0)]
    public Meta Meta {
      get {
        return this.fieldMeta;
      }
    }
    
    /// <summary>Gets or sets Stramien</summary>
    [IproxProperty(SequenceId = 1)]
    public LayoutField Stramien {
      get {
        return this.fieldStramien;
      }
      
      set {
        this.fieldStramien.Value = (value ?? new LayoutField()).Value;
      }
    }
    
    /// <summary>Gets collection of Blokken</summary>
    [IproxProperty(SequenceId = 2)]
    public IproxClustersCollection<Workgroup.IBlokken> Blokken {
      get {
        return this.fieldBlokken;
      }
    }
    
    /// <summary>Blokken (interface)</summary>
    /// <see cref="InhoudCluster" />
    /// <see cref="MediawidgetCluster" />
    /// <see cref="ApplicatielinkCluster" />
    /// <see cref="OpiniepeilingCluster" />
    /// <see cref="FotoCluster" />
    /// <see cref="FotoalbumCluster" />
    /// <see cref="SocialGroepMetaCluster" />
    /// <see cref="SocialNieuwsCluster" />
    /// <see cref="SocialVraagEnAntwoordCluster" />
    /// <see cref="SocialEvenementenCluster" />
    /// <see cref="SocialGroepLedenCluster" />
    /// <see cref="SocialGroepVolgersCluster" />
    /// <see cref="SocialBlogsCluster" />
    /// <see cref="SocialMicroblogCluster" />
    /// <see cref="SocialActiviteitenCluster" />
    /// <see cref="MediaCluster" />
    [IproxCluster(Alias = "Blokken")]
    public partial interface IBlokken : IIproxCluster {
    }
    
    /// <summary>Blokken (static class)</summary>
    [IproxCluster(Alias = "Blokken")]
    public static class BlokkenClusters {
      /// <summary>Inhoud (class)</summary>
      [IproxCluster(Alias = "Inhoud", Prototype = true)]
      public partial class InhoudCluster : Inhoud {
      }
      
      /// <content>InhoudCluster can be used for IBlokken</content>
      public partial class InhoudCluster : IBlokken {
      }
      
      /// <summary>Mediawidget (class)</summary>
      [IproxCluster(Alias = "Mediawidget", Prototype = true)]
      public partial class MediawidgetCluster : Mediawidget {
      }
      
      /// <content>MediawidgetCluster can be used for IBlokken</content>
      public partial class MediawidgetCluster : IBlokken {
      }
      
      /// <summary>Applicatielink (class)</summary>
      [IproxCluster(Alias = "Applicatielink", Prototype = true)]
      public partial class ApplicatielinkCluster : ApplicatielinkBlok {
      }
      
      /// <content>ApplicatielinkCluster can be used for IBlokken</content>
      public partial class ApplicatielinkCluster : IBlokken {
      }
      
      /// <summary>Opiniepeiling (class)</summary>
      [IproxCluster(Alias = "Opiniepeiling", Prototype = true)]
      public partial class OpiniepeilingCluster : OpiniepeilingBlok {
      }
      
      /// <content>OpiniepeilingCluster can be used for IBlokken</content>
      public partial class OpiniepeilingCluster : IBlokken {
      }
      
      /// <summary>Foto (class)</summary>
      [IproxCluster(Alias = "Foto", Prototype = true)]
      public partial class FotoCluster : Foto {
      }
      
      /// <content>FotoCluster can be used for IBlokken</content>
      public partial class FotoCluster : IBlokken {
      }
      
      /// <summary>Fotoalbum (class)</summary>
      [IproxCluster(Alias = "Fotoalbum", Prototype = true)]
      public partial class FotoalbumCluster : FotoalbumPrototype {
      }
      
      /// <content>FotoalbumCluster can be used for IBlokken</content>
      public partial class FotoalbumCluster : IBlokken {
      }
      
      /// <summary>Social - Groep meta (class)</summary>
      [IproxCluster(Alias = "Social - Groep meta", Prototype = true)]
      public partial class SocialGroepMetaCluster : SocialGroupMeta {
      }
      
      /// <content>SocialGroepMetaCluster can be used for IBlokken</content>
      public partial class SocialGroepMetaCluster : IBlokken {
      }
      
      /// <summary>Social - Nieuws (class)</summary>
      [IproxCluster(Alias = "Social - Nieuws", Prototype = true)]
      public partial class SocialNieuwsCluster : SocialPosts {
      }
      
      /// <content>SocialNieuwsCluster can be used for IBlokken</content>
      public partial class SocialNieuwsCluster : IBlokken {
      }
      
      /// <summary>Social - Vraag en Antwoord (class)</summary>
      [IproxCluster(Alias = "Social - Vraag en Antwoord", Prototype = true)]
      public partial class SocialVraagEnAntwoordCluster : SocialQuestions {
      }
      
      /// <content>SocialVraagEnAntwoordCluster can be used for IBlokken</content>
      public partial class SocialVraagEnAntwoordCluster : IBlokken {
      }
      
      /// <summary>Social - Evenementen (class)</summary>
      [IproxCluster(Alias = "Social - Evenementen", Prototype = true)]
      public partial class SocialEvenementenCluster : SocialCalendar {
      }
      
      /// <content>SocialEvenementenCluster can be used for IBlokken</content>
      public partial class SocialEvenementenCluster : IBlokken {
      }
      
      /// <summary>Social - Groep leden (class)</summary>
      [IproxCluster(Alias = "Social - Groep leden", Prototype = true)]
      public partial class SocialGroepLedenCluster : SocialGroupMembers {
      }
      
      /// <content>SocialGroepLedenCluster can be used for IBlokken</content>
      public partial class SocialGroepLedenCluster : IBlokken {
      }
      
      /// <summary>Social - Groep volgers (class)</summary>
      [IproxCluster(Alias = "Social - Groep volgers", Prototype = true)]
      public partial class SocialGroepVolgersCluster : SocialGroupFollowers {
      }
      
      /// <content>SocialGroepVolgersCluster can be used for IBlokken</content>
      public partial class SocialGroepVolgersCluster : IBlokken {
      }
      
      /// <summary>Social - Blogs (class)</summary>
      [IproxCluster(Alias = "Social - Blogs", Prototype = true)]
      public partial class SocialBlogsCluster : SocialGroupBlogList {
      }
      
      /// <content>SocialBlogsCluster can be used for IBlokken</content>
      public partial class SocialBlogsCluster : IBlokken {
      }
      
      /// <summary>Social - Microblog (class)</summary>
      [IproxCluster(Alias = "Social - Microblog", Prototype = true)]
      public partial class SocialMicroblogCluster : SocialMicroblog {
      }
      
      /// <content>SocialMicroblogCluster can be used for IBlokken</content>
      public partial class SocialMicroblogCluster : IBlokken {
      }
      
      /// <summary>Social - Activiteiten (class)</summary>
      [IproxCluster(Alias = "Social - Activiteiten", Prototype = true)]
      public partial class SocialActiviteitenCluster : SocialActivities {
      }
      
      /// <content>SocialActiviteitenCluster can be used for IBlokken</content>
      public partial class SocialActiviteitenCluster : IBlokken {
      }
      
      /// <summary>Media (class)</summary>
      [IproxCluster(Alias = "Media", Prototype = true)]
      public partial class MediaCluster : MediaBlok {
      }
      
      /// <content>MediaCluster can be used for IBlokken</content>
      public partial class MediaCluster : IBlokken {
      }
    }
  }
  
  /// <summary>Prikbord (class)</summary>
  [IproxPagetype(Alias = "prikbord")]
  public partial class Prikbord : Page {
    /// <summary>Meta (singular)</summary>
    [IproxCluster(Alias = "Meta", Prototype = true)]
    [IproxPagetype(Alias = "meta", Prototype = true, SequenceId = 1)]
    private readonly Meta fieldMeta = new Meta();
    
    /// <summary>Instellingen (singular)</summary>
    [IproxCluster(Alias = "Instellingen")]
    private readonly Prikbord.InstellingenCluster fieldInstellingen = new Prikbord.InstellingenCluster();
    
    /// <summary>Naam (collection)</summary>
    [IproxCluster(Alias = "Rubriek")]
    [IproxField(Alias = "Naam", SequenceId = 1)]
    private readonly IproxFieldsCollection<PlainField> fieldNaam = new IproxFieldsCollection<PlainField>();
    
    /// <summary>Inhoud (singular)</summary>
    [IproxCluster(Alias = "Inhoud")]
    private readonly Prikbord.InhoudCluster fieldInhoud = new Prikbord.InhoudCluster();
    
    /// <summary>Standaardvelden (singular)</summary>
    [IproxCluster(Alias = "Standaardvelden")]
    private readonly Prikbord.StandaardveldenCluster fieldStandaardvelden = new Prikbord.StandaardveldenCluster();
    
    /// <summary>Element (collection)</summary>
    [IproxCluster(Alias = "Element")]
    private readonly IproxClustersCollection<Prikbord.ElementCluster> fieldElement = new IproxClustersCollection<Prikbord.ElementCluster>();
    
    /// <summary>Stramien (singular)</summary>
    [IproxCluster(Alias = "Layout")]
    [IproxField(Alias = "Stramien", SequenceId = 1)]
    private readonly LayoutField fieldStramien = new LayoutField();
    
    /// <summary>Initializes a new instance of the Prikbord class.</summary>
    /// <param name="item">Containing item</param>
    public Prikbord(Item item)
      : base(item) {
    }
    
    /// <summary>Prevents a default instance of the Prikbord class from being created.</summary>
    private Prikbord() {
    }
    
    /// <summary>Gets Meta</summary>
    [IproxProperty(SequenceId = 0)]
    public Meta Meta {
      get {
        return this.fieldMeta;
      }
    }
    
    /// <summary>Gets Instellingen</summary>
    [IproxProperty(SequenceId = 1)]
    public Prikbord.InstellingenCluster Instellingen {
      get {
        return this.fieldInstellingen;
      }
    }
    
    /// <summary>Gets collection of Naam</summary>
    [IproxProperty(SequenceId = 2)]
    public IproxFieldsCollection<PlainField> Naam {
      get {
        return this.fieldNaam;
      }
    }
    
    /// <summary>Gets Inhoud</summary>
    [IproxProperty(SequenceId = 3)]
    public Prikbord.InhoudCluster Inhoud {
      get {
        return this.fieldInhoud;
      }
    }
    
    /// <summary>Gets Standaardvelden</summary>
    [IproxProperty(SequenceId = 4)]
    public Prikbord.StandaardveldenCluster Standaardvelden {
      get {
        return this.fieldStandaardvelden;
      }
    }
    
    /// <summary>Gets collection of Element</summary>
    /// <remarks>Extra veld in het formulier</remarks>
    [IproxProperty(SequenceId = 5)]
    public IproxClustersCollection<Prikbord.ElementCluster> Element {
      get {
        return this.fieldElement;
      }
    }
    
    /// <summary>Gets or sets Stramien</summary>
    [IproxProperty(SequenceId = 6)]
    public LayoutField Stramien {
      get {
        return this.fieldStramien;
      }
      
      set {
        this.fieldStramien.Value = (value ?? new LayoutField()).Value;
      }
    }
    
    /// <summary>Instellingen (class)</summary>
    [IproxCluster(Alias = "Instellingen")]
    public partial class InstellingenCluster : IproxCluster {
      /// <summary>Vervalperiode (singular)</summary>
      [IproxField(Alias = "Vervalperiode")]
      private readonly PlainField fieldVervalperiode = new PlainField();
      
      /// <summary>Items per pagina per rubriek (singular)</summary>
      [IproxField(Alias = "Items per pagina per rubriek")]
      private readonly PlainField fieldItemsPerPaginaPerRubriek = new PlainField();
      
      /// <summary>Maximaal aantal rubrieken per bijdrage (singular)</summary>
      [IproxField(Alias = "Maximaal aantal rubrieken per bijdrage")]
      private readonly PlainField fieldMaximaalAantalRubriekenPerBijdrage = new PlainField();
      
      /// <summary>Eigen bijdrage mag aangepast worden (singular)</summary>
      [IproxField(Alias = "Eigen bijdrage mag aangepast worden")]
      private readonly BooleanField fieldEigenBijdrageMagAangepastWorden = new BooleanField();
      
      /// <summary>Toon lege rubrieken (singular)</summary>
      [IproxField(Alias = "Toon lege rubrieken")]
      private readonly BooleanField fieldToonLegeRubrieken = new BooleanField();
      
      /// <summary>Toon categorieën (singular)</summary>
      [IproxField(Alias = "Toon categorieën")]
      private readonly BooleanField fieldToonCategorieN = new BooleanField();
      
      /// <summary>Breedte thumbnail (singular)</summary>
      [IproxField(Alias = "Breedte thumbnail")]
      private readonly PlainField fieldBreedteThumbnail = new PlainField();
      
      /// <summary>Hoogte thumbnail (singular)</summary>
      [IproxField(Alias = "Hoogte thumbnail")]
      private readonly PlainField fieldHoogteThumbnail = new PlainField();
      
      /// <summary>Maximale breedte afbeelding (singular)</summary>
      [IproxField(Alias = "Maximale breedte afbeelding")]
      private readonly PlainField fieldMaximaleBreedteAfbeelding = new PlainField();
      
      /// <summary>Maximale hoogte afbeelding (singular)</summary>
      [IproxField(Alias = "Maximale hoogte afbeelding")]
      private readonly PlainField fieldMaximaleHoogteAfbeelding = new PlainField();
      
      /// <summary>Captcha gebruiken (singular)</summary>
      [IproxField(Alias = "Captcha gebruiken")]
      private readonly BooleanField fieldCaptchaGebruiken = new BooleanField();
      
      /// <summary>Velden in bijdrage (singular)</summary>
      [IproxField(Alias = "Velden in bijdrage")]
      private readonly PicklistField fieldVeldenInBijdrage = new PicklistField();
      
      /// <summary>Gets or sets Vervalperiode</summary>
      [IproxProperty(SequenceId = 0)]
      public PlainField Vervalperiode {
        get {
          return this.fieldVervalperiode;
        }
        
        set {
          this.fieldVervalperiode.Value = (value ?? new PlainField()).Value;
        }
      }
      
      /// <summary>Gets or sets Items per pagina per rubriek</summary>
      [IproxProperty(SequenceId = 1)]
      public PlainField ItemsPerPaginaPerRubriek {
        get {
          return this.fieldItemsPerPaginaPerRubriek;
        }
        
        set {
          this.fieldItemsPerPaginaPerRubriek.Value = (value ?? new PlainField()).Value;
        }
      }
      
      /// <summary>Gets or sets Maximaal aantal rubrieken per bijdrage</summary>
      [IproxProperty(SequenceId = 2)]
      public PlainField MaximaalAantalRubriekenPerBijdrage {
        get {
          return this.fieldMaximaalAantalRubriekenPerBijdrage;
        }
        
        set {
          this.fieldMaximaalAantalRubriekenPerBijdrage.Value = (value ?? new PlainField()).Value;
        }
      }
      
      /// <summary>Gets or sets Eigen bijdrage mag aangepast worden</summary>
      /// <remarks>aast verwijderen kan de bijdrage ook aangepast worden</remarks>
      [IproxProperty(SequenceId = 3)]
      public BooleanField EigenBijdrageMagAangepastWorden {
        get {
          return this.fieldEigenBijdrageMagAangepastWorden;
        }
        
        set {
          this.fieldEigenBijdrageMagAangepastWorden.Value = (value ?? new BooleanField()).Value;
        }
      }
      
      /// <summary>Gets or sets Toon lege rubrieken</summary>
      [IproxProperty(SequenceId = 4)]
      public BooleanField ToonLegeRubrieken {
        get {
          return this.fieldToonLegeRubrieken;
        }
        
        set {
          this.fieldToonLegeRubrieken.Value = (value ?? new BooleanField()).Value;
        }
      }
      
      /// <summary>Gets or sets Toon categorieën</summary>
      [IproxProperty(SequenceId = 5)]
      public BooleanField ToonCategorieN {
        get {
          return this.fieldToonCategorieN;
        }
        
        set {
          this.fieldToonCategorieN.Value = (value ?? new BooleanField()).Value;
        }
      }
      
      /// <summary>Gets or sets Breedte thumbnail</summary>
      /// <remarks>de gegenereerde thumbnail krijgt een vaste breedte</remarks>
      [IproxProperty(SequenceId = 6)]
      public PlainField BreedteThumbnail {
        get {
          return this.fieldBreedteThumbnail;
        }
        
        set {
          this.fieldBreedteThumbnail.Value = (value ?? new PlainField()).Value;
        }
      }
      
      /// <summary>Gets or sets Hoogte thumbnail</summary>
      /// <remarks>de gegenereerde thumbnail krijgt een vaste hoogte</remarks>
      [IproxProperty(SequenceId = 7)]
      public PlainField HoogteThumbnail {
        get {
          return this.fieldHoogteThumbnail;
        }
        
        set {
          this.fieldHoogteThumbnail.Value = (value ?? new PlainField()).Value;
        }
      }
      
      /// <summary>Gets or sets Maximale breedte afbeelding</summary>
      [IproxProperty(SequenceId = 8)]
      public PlainField MaximaleBreedteAfbeelding {
        get {
          return this.fieldMaximaleBreedteAfbeelding;
        }
        
        set {
          this.fieldMaximaleBreedteAfbeelding.Value = (value ?? new PlainField()).Value;
        }
      }
      
      /// <summary>Gets or sets Maximale hoogte afbeelding</summary>
      [IproxProperty(SequenceId = 9)]
      public PlainField MaximaleHoogteAfbeelding {
        get {
          return this.fieldMaximaleHoogteAfbeelding;
        }
        
        set {
          this.fieldMaximaleHoogteAfbeelding.Value = (value ?? new PlainField()).Value;
        }
      }
      
      /// <summary>Gets or sets Captcha gebruiken</summary>
      [IproxProperty(SequenceId = 10)]
      public BooleanField CaptchaGebruiken {
        get {
          return this.fieldCaptchaGebruiken;
        }
        
        set {
          this.fieldCaptchaGebruiken.Value = (value ?? new BooleanField()).Value;
        }
      }
      
      /// <summary>Gets or sets Velden in bijdrage</summary>
      [IproxProperty(SequenceId = 11)]
      public PicklistField VeldenInBijdrage {
        get {
          return this.fieldVeldenInBijdrage;
        }
        
        set {
          this.fieldVeldenInBijdrage.Value = (value ?? new PicklistField()).Value;
        }
      }
    }
    
    /// <summary>Inhoud (class)</summary>
    [IproxCluster(Alias = "Inhoud")]
    public partial class InhoudCluster : IproxCluster {
      /// <summary>Inleiding (singular)</summary>
      [IproxField(Alias = "Inleiding")]
      private readonly HtmlField fieldInleiding = new HtmlField();
      
      /// <summary>Inleiding formulier (singular)</summary>
      [IproxField(Alias = "Inleiding formulier")]
      private readonly HtmlField fieldInleidingFormulier = new HtmlField();
      
      /// <summary>Inleiding aanpassen formulier (singular)</summary>
      [IproxField(Alias = "Inleiding aanpassen formulier")]
      private readonly HtmlField fieldInleidingAanpassenFormulier = new HtmlField();
      
      /// <summary>Inleiding verwijderen formulier (singular)</summary>
      [IproxField(Alias = "Inleiding verwijderen formulier")]
      private readonly HtmlField fieldInleidingVerwijderenFormulier = new HtmlField();
      
      /// <summary>Tekst na verzending (singular)</summary>
      [IproxField(Alias = "Tekst na verzending")]
      private readonly HtmlField fieldTekstNaVerzending = new HtmlField();
      
      /// <summary>Gets or sets Inleiding</summary>
      [IproxProperty(SequenceId = 0)]
      public HtmlField Inleiding {
        get {
          return this.fieldInleiding;
        }
        
        set {
          this.fieldInleiding.Value = (value ?? new HtmlField()).Value;
        }
      }
      
      /// <summary>Gets or sets Inleiding formulier</summary>
      [IproxProperty(SequenceId = 1)]
      public HtmlField InleidingFormulier {
        get {
          return this.fieldInleidingFormulier;
        }
        
        set {
          this.fieldInleidingFormulier.Value = (value ?? new HtmlField()).Value;
        }
      }
      
      /// <summary>Gets or sets Inleiding aanpassen formulier</summary>
      [IproxProperty(SequenceId = 2)]
      public HtmlField InleidingAanpassenFormulier {
        get {
          return this.fieldInleidingAanpassenFormulier;
        }
        
        set {
          this.fieldInleidingAanpassenFormulier.Value = (value ?? new HtmlField()).Value;
        }
      }
      
      /// <summary>Gets or sets Inleiding verwijderen formulier</summary>
      [IproxProperty(SequenceId = 3)]
      public HtmlField InleidingVerwijderenFormulier {
        get {
          return this.fieldInleidingVerwijderenFormulier;
        }
        
        set {
          this.fieldInleidingVerwijderenFormulier.Value = (value ?? new HtmlField()).Value;
        }
      }
      
      /// <summary>Gets or sets Tekst na verzending</summary>
      [IproxProperty(SequenceId = 4)]
      public HtmlField TekstNaVerzending {
        get {
          return this.fieldTekstNaVerzending;
        }
        
        set {
          this.fieldTekstNaVerzending.Value = (value ?? new HtmlField()).Value;
        }
      }
    }
    
    /// <summary>Standaardvelden (class)</summary>
    [IproxCluster(Alias = "Standaardvelden")]
    public partial class StandaardveldenCluster : IproxCluster {
      /// <summary>Plaatsen (singular)</summary>
      [IproxCluster(Alias = "Externe link")]
      [IproxField(Alias = "Plaatsen", SequenceId = 1)]
      private readonly BooleanField fieldPlaatsen = new BooleanField();
      
      /// <summary>Verplicht (singular)</summary>
      [IproxCluster(Alias = "Externe link")]
      [IproxField(Alias = "Verplicht", SequenceId = 1)]
      private readonly BooleanField fieldVerplicht = new BooleanField();
      
      /// <summary>Gets or sets Plaatsen</summary>
      [IproxProperty(SequenceId = 0)]
      public BooleanField Plaatsen {
        get {
          return this.fieldPlaatsen;
        }
        
        set {
          this.fieldPlaatsen.Value = (value ?? new BooleanField()).Value;
        }
      }
      
      /// <summary>Gets or sets Verplicht</summary>
      [IproxProperty(SequenceId = 1)]
      public BooleanField Verplicht {
        get {
          return this.fieldVerplicht;
        }
        
        set {
          this.fieldVerplicht.Value = (value ?? new BooleanField()).Value;
        }
      }
    }
    
    /// <summary>Element (class)</summary>
    /// <remarks>Extra veld in het formulier</remarks>
    [IproxCluster(Alias = "Element")]
    public partial class ElementCluster : IproxCluster {
      /// <summary>Naam (singular)</summary>
      [IproxField(Alias = "Naam")]
      private readonly PlainField fieldNaam = new PlainField();
      
      /// <summary>Verplicht (singular)</summary>
      [IproxField(Alias = "Verplicht")]
      private readonly BooleanField fieldVerplicht = new BooleanField();
      
      /// <summary>Gets or sets Naam</summary>
      [IproxProperty(SequenceId = 0)]
      public PlainField Naam {
        get {
          return this.fieldNaam;
        }
        
        set {
          this.fieldNaam.Value = (value ?? new PlainField()).Value;
        }
      }
      
      /// <summary>Gets or sets Verplicht</summary>
      [IproxProperty(SequenceId = 1)]
      public BooleanField Verplicht {
        get {
          return this.fieldVerplicht;
        }
        
        set {
          this.fieldVerplicht.Value = (value ?? new BooleanField()).Value;
        }
      }
    }
  }
  
  /// <summary>Mijn mededelingen (class)</summary>
  [IproxPagetype(Alias = "mijnmededelingen")]
  public partial class Mijnmededelingen : Page {
    /// <summary>Belangrijk (singular)</summary>
    [IproxCluster(Alias = "Belangrijk")]
    private readonly Mijnmededelingen.BelangrijkCluster fieldBelangrijk = new Mijnmededelingen.BelangrijkCluster();
    
    /// <summary>Algemeen (collection)</summary>
    [IproxCluster(Alias = "Algemeen")]
    private readonly IproxClustersCollection<Mijnmededelingen.AlgemeenCluster> fieldAlgemeen = new IproxClustersCollection<Mijnmededelingen.AlgemeenCluster>();
    
    /// <summary>Persoonlijk (collection)</summary>
    [IproxCluster(Alias = "Persoonlijk")]
    private readonly IproxClustersCollection<Mijnmededelingen.PersoonlijkCluster> fieldPersoonlijk = new IproxClustersCollection<Mijnmededelingen.PersoonlijkCluster>();
    
    /// <summary>AlwaysOn (singular)</summary>
    [IproxCluster(Alias = "AlwaysOn")]
    private readonly Mijnmededelingen.AlwaysOnCluster fieldAlwaysOn = new Mijnmededelingen.AlwaysOnCluster();
    
    /// <summary>Initializes a new instance of the Mijnmededelingen class.</summary>
    /// <param name="item">Containing item</param>
    public Mijnmededelingen(Item item)
      : base(item) {
    }
    
    /// <summary>Prevents a default instance of the Mijnmededelingen class from being created.</summary>
    private Mijnmededelingen() {
    }
    
    /// <summary>Gets Belangrijk</summary>
    [IproxProperty(SequenceId = 0)]
    public Mijnmededelingen.BelangrijkCluster Belangrijk {
      get {
        return this.fieldBelangrijk;
      }
    }
    
    /// <summary>Gets collection of Algemeen</summary>
    [IproxProperty(SequenceId = 1)]
    public IproxClustersCollection<Mijnmededelingen.AlgemeenCluster> Algemeen {
      get {
        return this.fieldAlgemeen;
      }
    }
    
    /// <summary>Gets collection of Persoonlijk</summary>
    [IproxProperty(SequenceId = 2)]
    public IproxClustersCollection<Mijnmededelingen.PersoonlijkCluster> Persoonlijk {
      get {
        return this.fieldPersoonlijk;
      }
    }
    
    /// <summary>Gets AlwaysOn</summary>
    [IproxProperty(SequenceId = 3)]
    public Mijnmededelingen.AlwaysOnCluster AlwaysOn {
      get {
        return this.fieldAlwaysOn;
      }
    }
    
    /// <summary>Belangrijk (class)</summary>
    [IproxCluster(Alias = "Belangrijk")]
    public partial class BelangrijkCluster : IproxCluster {
      /// <summary>Tekst (singular)</summary>
      [IproxCluster(Alias = "Mededeling")]
      [IproxCluster(Alias = "Bericht", SequenceId = 1)]
      [IproxField(Alias = "Tekst", SequenceId = 2)]
      private readonly HtmlField fieldTekst = new HtmlField();
      
      /// <summary>Actief (singular)</summary>
      [IproxCluster(Alias = "Mededeling")]
      [IproxCluster(Alias = "Bericht", SequenceId = 1)]
      [IproxField(Alias = "Actief", SequenceId = 2)]
      private readonly BooleanField fieldActief = new BooleanField();
      
      /// <summary>Gets or sets Tekst</summary>
      [IproxProperty(SequenceId = 0)]
      public HtmlField Tekst {
        get {
          return this.fieldTekst;
        }
        
        set {
          this.fieldTekst.Value = (value ?? new HtmlField()).Value;
        }
      }
      
      /// <summary>Gets or sets Actief</summary>
      [IproxProperty(SequenceId = 1)]
      public BooleanField Actief {
        get {
          return this.fieldActief;
        }
        
        set {
          this.fieldActief.Value = (value ?? new BooleanField()).Value;
        }
      }
    }
    
    /// <summary>Algemeen (class)</summary>
    [IproxCluster(Alias = "Algemeen")]
    public partial class AlgemeenCluster : IproxCluster {
      /// <summary>Tekst (singular)</summary>
      [IproxCluster(Alias = "Mededeling")]
      [IproxCluster(Alias = "Bericht", SequenceId = 1)]
      [IproxField(Alias = "Tekst", SequenceId = 2)]
      private readonly HtmlField fieldTekst = new HtmlField();
      
      /// <summary>Waardering (singular)</summary>
      [IproxCluster(Alias = "Mededeling")]
      [IproxCluster(Alias = "Bericht", SequenceId = 1)]
      [IproxField(Alias = "Waardering", SequenceId = 2)]
      private readonly PlainField fieldWaardering = new PlainField();
      
      /// <summary>Actief (singular)</summary>
      [IproxCluster(Alias = "Mededeling")]
      [IproxCluster(Alias = "Bericht", SequenceId = 1)]
      [IproxField(Alias = "Actief", SequenceId = 2)]
      private readonly BooleanField fieldActief = new BooleanField();
      
      /// <summary>Gets or sets Tekst</summary>
      [IproxProperty(SequenceId = 0)]
      public HtmlField Tekst {
        get {
          return this.fieldTekst;
        }
        
        set {
          this.fieldTekst.Value = (value ?? new HtmlField()).Value;
        }
      }
      
      /// <summary>Gets or sets Waardering</summary>
      /// <remarks>Hoe hoger hoe vaker een bericht getoond wordt.</remarks>
      [IproxProperty(SequenceId = 1)]
      public PlainField Waardering {
        get {
          return this.fieldWaardering;
        }
        
        set {
          this.fieldWaardering.Value = (value ?? new PlainField()).Value;
        }
      }
      
      /// <summary>Gets or sets Actief</summary>
      [IproxProperty(SequenceId = 2)]
      public BooleanField Actief {
        get {
          return this.fieldActief;
        }
        
        set {
          this.fieldActief.Value = (value ?? new BooleanField()).Value;
        }
      }
    }
    
    /// <summary>Persoonlijk (class)</summary>
    [IproxCluster(Alias = "Persoonlijk")]
    public partial class PersoonlijkCluster : IproxCluster {
      /// <summary>Tekst (singular)</summary>
      [IproxCluster(Alias = "Mededeling")]
      [IproxCluster(Alias = "Bericht", SequenceId = 1)]
      [IproxField(Alias = "Tekst", SequenceId = 2)]
      private readonly HtmlField fieldTekst = new HtmlField();
      
      /// <summary>Alias (singular)</summary>
      [IproxCluster(Alias = "Mededeling")]
      [IproxCluster(Alias = "Bericht", SequenceId = 1)]
      [IproxField(Alias = "Alias", SequenceId = 2)]
      private readonly PlainField fieldAlias = new PlainField();
      
      /// <summary>Waardering (singular)</summary>
      [IproxCluster(Alias = "Mededeling")]
      [IproxCluster(Alias = "Bericht", SequenceId = 1)]
      [IproxField(Alias = "Waardering", SequenceId = 2)]
      private readonly PlainField fieldWaardering = new PlainField();
      
      /// <summary>Actief (singular)</summary>
      [IproxCluster(Alias = "Mededeling")]
      [IproxCluster(Alias = "Bericht", SequenceId = 1)]
      [IproxField(Alias = "Actief", SequenceId = 2)]
      private readonly BooleanField fieldActief = new BooleanField();
      
      /// <summary>Gets or sets Tekst</summary>
      [IproxProperty(SequenceId = 0)]
      public HtmlField Tekst {
        get {
          return this.fieldTekst;
        }
        
        set {
          this.fieldTekst.Value = (value ?? new HtmlField()).Value;
        }
      }
      
      /// <summary>Gets or sets Alias</summary>
      [IproxProperty(SequenceId = 1)]
      public PlainField Alias {
        get {
          return this.fieldAlias;
        }
        
        set {
          this.fieldAlias.Value = (value ?? new PlainField()).Value;
        }
      }
      
      /// <summary>Gets or sets Waardering</summary>
      /// <remarks>Hoe hoger hoe vaker een bericht getoond wordt.</remarks>
      [IproxProperty(SequenceId = 2)]
      public PlainField Waardering {
        get {
          return this.fieldWaardering;
        }
        
        set {
          this.fieldWaardering.Value = (value ?? new PlainField()).Value;
        }
      }
      
      /// <summary>Gets or sets Actief</summary>
      [IproxProperty(SequenceId = 3)]
      public BooleanField Actief {
        get {
          return this.fieldActief;
        }
        
        set {
          this.fieldActief.Value = (value ?? new BooleanField()).Value;
        }
      }
    }
    
    /// <summary>AlwaysOn (class)</summary>
    [IproxCluster(Alias = "AlwaysOn")]
    public partial class AlwaysOnCluster : IproxCluster {
      /// <summary>Tekst (singular)</summary>
      [IproxCluster(Alias = "Mededeling")]
      [IproxCluster(Alias = "Bericht", SequenceId = 1)]
      [IproxField(Alias = "Tekst", SequenceId = 2)]
      private readonly HtmlField fieldTekst = new HtmlField();
      
      /// <summary>Waardering (singular)</summary>
      [IproxCluster(Alias = "Mededeling")]
      [IproxCluster(Alias = "Bericht", SequenceId = 1)]
      [IproxField(Alias = "Waardering", SequenceId = 2)]
      private readonly PlainField fieldWaardering = new PlainField();
      
      /// <summary>Actief (singular)</summary>
      [IproxCluster(Alias = "Mededeling")]
      [IproxCluster(Alias = "Bericht", SequenceId = 1)]
      [IproxField(Alias = "Actief", SequenceId = 2)]
      private readonly BooleanField fieldActief = new BooleanField();
      
      /// <summary>Gets or sets Tekst</summary>
      [IproxProperty(SequenceId = 0)]
      public HtmlField Tekst {
        get {
          return this.fieldTekst;
        }
        
        set {
          this.fieldTekst.Value = (value ?? new HtmlField()).Value;
        }
      }
      
      /// <summary>Gets or sets Waardering</summary>
      /// <remarks>Hoe hoger hoe vaker een bericht getoond wordt.</remarks>
      [IproxProperty(SequenceId = 1)]
      public PlainField Waardering {
        get {
          return this.fieldWaardering;
        }
        
        set {
          this.fieldWaardering.Value = (value ?? new PlainField()).Value;
        }
      }
      
      /// <summary>Gets or sets Actief</summary>
      [IproxProperty(SequenceId = 2)]
      public BooleanField Actief {
        get {
          return this.fieldActief;
        }
        
        set {
          this.fieldActief.Value = (value ?? new BooleanField()).Value;
        }
      }
    }
  }
  
  /// <summary>Social configuratie (class)</summary>
  /// <remarks>IPSOC-483 Tbv. POC</remarks>
  [IproxPagetype(Alias = "social-config")]
  public partial class SocialConfig : Page {
    /// <summary>config (singular)</summary>
    [IproxCluster(Alias = "config")]
    private readonly SocialConfig.ConfigCluster fieldConfig = new SocialConfig.ConfigCluster();
    
    /// <summary>groupMeta (singular)</summary>
    [IproxCluster(Alias = "groupMeta")]
    private readonly SocialConfig.GroupMetaCluster fieldGroupMeta = new SocialConfig.GroupMetaCluster();
    
    /// <summary>mylexpro (singular)</summary>
    [IproxCluster(Alias = "mylex")]
    [IproxField(Alias = "mylexpro", SequenceId = 1)]
    private readonly BooleanField fieldMylexpro = new BooleanField();
    
    /// <summary>Initializes a new instance of the SocialConfig class.</summary>
    /// <param name="item">Containing item</param>
    public SocialConfig(Item item)
      : base(item) {
    }
    
    /// <summary>Prevents a default instance of the SocialConfig class from being created.</summary>
    private SocialConfig() {
    }
    
    /// <summary>Gets config</summary>
    [IproxProperty(SequenceId = 0)]
    public SocialConfig.ConfigCluster Config {
      get {
        return this.fieldConfig;
      }
    }
    
    /// <summary>Gets groupMeta</summary>
    [IproxProperty(SequenceId = 1)]
    public SocialConfig.GroupMetaCluster GroupMeta {
      get {
        return this.fieldGroupMeta;
      }
    }
    
    /// <summary>Gets or sets mylexpro</summary>
    [IproxProperty(SequenceId = 2)]
    public BooleanField Mylexpro {
      get {
        return this.fieldMylexpro;
      }
      
      set {
        this.fieldMylexpro.Value = (value ?? new BooleanField()).Value;
      }
    }
    
    /// <summary>config (class)</summary>
    [IproxCluster(Alias = "config")]
    public partial class ConfigCluster : IproxCluster {
      /// <summary>socialApiUrl (singular)</summary>
      [IproxField(Alias = "socialApiUrl")]
      private readonly PlainField fieldSocialApiUrl = new PlainField();
      
      /// <summary>downloadUrl (singular)</summary>
      [IproxField(Alias = "downloadUrl")]
      private readonly PlainField fieldDownloadUrl = new PlainField();
      
      /// <summary>enableImpersonation (singular)</summary>
      [IproxField(Alias = "enableImpersonation")]
      private readonly BooleanField fieldEnableImpersonation = new BooleanField();
      
      /// <summary>enableManualVisitTrigger (singular)</summary>
      [IproxField(Alias = "enableManualVisitTrigger")]
      private readonly BooleanField fieldEnableManualVisitTrigger = new BooleanField();
      
      /// <summary>Gets or sets socialApiUrl</summary>
      [IproxProperty(SequenceId = 0)]
      public PlainField SocialApiUrl {
        get {
          return this.fieldSocialApiUrl;
        }
        
        set {
          this.fieldSocialApiUrl.Value = (value ?? new PlainField()).Value;
        }
      }
      
      /// <summary>Gets or sets downloadUrl</summary>
      [IproxProperty(SequenceId = 1)]
      public PlainField DownloadUrl {
        get {
          return this.fieldDownloadUrl;
        }
        
        set {
          this.fieldDownloadUrl.Value = (value ?? new PlainField()).Value;
        }
      }
      
      /// <summary>Gets or sets enableImpersonation</summary>
      [IproxProperty(SequenceId = 2)]
      public BooleanField EnableImpersonation {
        get {
          return this.fieldEnableImpersonation;
        }
        
        set {
          this.fieldEnableImpersonation.Value = (value ?? new BooleanField()).Value;
        }
      }
      
      /// <summary>Gets or sets enableManualVisitTrigger</summary>
      [IproxProperty(SequenceId = 3)]
      public BooleanField EnableManualVisitTrigger {
        get {
          return this.fieldEnableManualVisitTrigger;
        }
        
        set {
          this.fieldEnableManualVisitTrigger.Value = (value ?? new BooleanField()).Value;
        }
      }
    }
    
    /// <summary>groupMeta (class)</summary>
    [IproxCluster(Alias = "groupMeta")]
    public partial class GroupMetaCluster : IproxCluster {
      /// <summary>emailLink (singular)</summary>
      [IproxField(Alias = "emailLink")]
      private readonly LinkField fieldEmailLink = new LinkField();
      
      /// <summary>enableEditLayout (singular)</summary>
      [IproxField(Alias = "enableEditLayout")]
      private readonly BooleanField fieldEnableEditLayout = new BooleanField();
      
      /// <summary>Gets or sets emailLink</summary>
      [IproxProperty(SequenceId = 0)]
      public LinkField EmailLink {
        get {
          return this.fieldEmailLink;
        }
        
        set {
          this.fieldEmailLink.Value = (value ?? new LinkField()).Value;
        }
      }
      
      /// <summary>Gets or sets enableEditLayout</summary>
      [IproxProperty(SequenceId = 1)]
      public BooleanField EnableEditLayout {
        get {
          return this.fieldEnableEditLayout;
        }
        
        set {
          this.fieldEnableEditLayout.Value = (value ?? new BooleanField()).Value;
        }
      }
    }
  }
  
  /// <summary>Zoeken My-Lex (class)</summary>
  [IproxPagetype(Alias = "zoekenmylex")]
  public partial class Zoekenmylex : Page {
    /// <summary>Meta (singular)</summary>
    [IproxCluster(Alias = "Meta", Prototype = true)]
    [IproxPagetype(Alias = "meta", Prototype = true, SequenceId = 1)]
    private readonly Meta fieldMeta = new Meta();
    
    /// <summary>Inhoud (singular)</summary>
    [IproxCluster(Alias = "Inhoud")]
    private readonly Zoekenmylex.InhoudCluster fieldInhoud = new Zoekenmylex.InhoudCluster();
    
    /// <summary>facetName (collection)</summary>
    [IproxCluster(Alias = "facets")]
    [IproxField(Alias = "facetName", SequenceId = 1)]
    private readonly IproxFieldsCollection<PlainField> fieldFacetName = new IproxFieldsCollection<PlainField>();
    
    /// <summary>Initializes a new instance of the Zoekenmylex class.</summary>
    /// <param name="item">Containing item</param>
    public Zoekenmylex(Item item)
      : base(item) {
    }
    
    /// <summary>Prevents a default instance of the Zoekenmylex class from being created.</summary>
    private Zoekenmylex() {
    }
    
    /// <summary>Gets Meta</summary>
    [IproxProperty(SequenceId = 0)]
    public Meta Meta {
      get {
        return this.fieldMeta;
      }
    }
    
    /// <summary>Gets Inhoud</summary>
    [IproxProperty(SequenceId = 1)]
    public Zoekenmylex.InhoudCluster Inhoud {
      get {
        return this.fieldInhoud;
      }
    }
    
    /// <summary>Gets collection of facetName</summary>
    [IproxProperty(SequenceId = 2)]
    public IproxFieldsCollection<PlainField> FacetName {
      get {
        return this.fieldFacetName;
      }
    }
    
    /// <summary>Inhoud (class)</summary>
    [IproxCluster(Alias = "Inhoud")]
    public partial class InhoudCluster : IproxCluster {
      /// <summary>Tekst bij geen resultaten (singular)</summary>
      [IproxField(Alias = "Tekst bij geen resultaten")]
      private readonly HtmlField fieldTekstBijGeenResultaten = new HtmlField();
      
      /// <summary>Mylex zoekportaal URL (singular)</summary>
      [IproxField(Alias = "Mylex zoekportaal URL")]
      private readonly PlainField fieldMylexZoekportaalURL = new PlainField();
      
      /// <summary>Stateninformatie Systeem URL (singular)</summary>
      [IproxField(Alias = "Stateninformatie Systeem URL")]
      private readonly PlainField fieldStateninformatieSysteemURL = new PlainField();
      
      /// <summary>mylexurl (singular)</summary>
      [IproxField(Alias = "mylexurl")]
      private readonly PlainField fieldMylexurl = new PlainField();
      
      /// <summary>Gets or sets Tekst bij geen resultaten</summary>
      [IproxProperty(SequenceId = 0)]
      public HtmlField TekstBijGeenResultaten {
        get {
          return this.fieldTekstBijGeenResultaten;
        }
        
        set {
          this.fieldTekstBijGeenResultaten.Value = (value ?? new HtmlField()).Value;
        }
      }
      
      /// <summary>Gets or sets Mylex zoekportaal URL</summary>
      /// <remarks>Link naar het persoonlijke mylex zoekportaal. (leeg = niet tonen)</remarks>
      [IproxProperty(SequenceId = 1)]
      public PlainField MylexZoekportaalURL {
        get {
          return this.fieldMylexZoekportaalURL;
        }
        
        set {
          this.fieldMylexZoekportaalURL.Value = (value ?? new PlainField()).Value;
        }
      }
      
      /// <summary>Gets or sets Stateninformatie Systeem URL</summary>
      /// <remarks>Link naar het stateninformatie systeem. (leeg = niet tonen)</remarks>
      [IproxProperty(SequenceId = 2)]
      public PlainField StateninformatieSysteemURL {
        get {
          return this.fieldStateninformatieSysteemURL;
        }
        
        set {
          this.fieldStateninformatieSysteemURL.Value = (value ?? new PlainField()).Value;
        }
      }
      
      /// <summary>Gets or sets mylexurl</summary>
      [IproxProperty(SequenceId = 3)]
      public PlainField Mylexurl {
        get {
          return this.fieldMylexurl;
        }
        
        set {
          this.fieldMylexurl.Value = (value ?? new PlainField()).Value;
        }
      }
    }
  }
  
  /// <summary>Mijn links (class)</summary>
  [IproxPagetype(Alias = "mylinks")]
  public partial class Mylinks : Page {
    /// <summary>Applicatie (collection)</summary>
    [IproxCluster(Alias = "Applicatie")]
    private readonly IproxClustersCollection<Mylinks.ApplicatieCluster> fieldApplicatie = new IproxClustersCollection<Mylinks.ApplicatieCluster>();
    
    /// <summary>Initializes a new instance of the Mylinks class.</summary>
    /// <param name="item">Containing item</param>
    public Mylinks(Item item)
      : base(item) {
    }
    
    /// <summary>Prevents a default instance of the Mylinks class from being created.</summary>
    private Mylinks() {
    }
    
    /// <summary>Gets collection of Applicatie</summary>
    [IproxProperty(SequenceId = 0)]
    public IproxClustersCollection<Mylinks.ApplicatieCluster> Applicatie {
      get {
        return this.fieldApplicatie;
      }
    }
    
    /// <summary>Applicatie (class)</summary>
    [IproxCluster(Alias = "Applicatie")]
    public partial class ApplicatieCluster : IproxCluster {
      /// <summary>Naam (singular)</summary>
      [IproxField(Alias = "Naam")]
      private readonly PlainField fieldNaam = new PlainField();
      
      /// <summary>Icoon (singular)</summary>
      [IproxField(Alias = "Icoon")]
      private readonly ImageField fieldIcoon = new ImageField();
      
      /// <summary>Link (singular)</summary>
      [IproxField(Alias = "Link")]
      private readonly AddressField fieldLink = new AddressField();
      
      /// <summary>In basisset (singular)</summary>
      [IproxField(Alias = "In basisset")]
      private readonly BooleanField fieldInBasisset = new BooleanField();
      
      /// <summary>Gets or sets Naam</summary>
      [IproxProperty(SequenceId = 0)]
      public PlainField Naam {
        get {
          return this.fieldNaam;
        }
        
        set {
          this.fieldNaam.Value = (value ?? new PlainField()).Value;
        }
      }
      
      /// <summary>Gets or sets Icoon</summary>
      [IproxProperty(SequenceId = 1)]
      public ImageField Icoon {
        get {
          return this.fieldIcoon;
        }
        
        set {
          this.fieldIcoon.Value = (value ?? new ImageField()).Value;
        }
      }
      
      /// <summary>Gets or sets Link</summary>
      [IproxProperty(SequenceId = 2)]
      public AddressField Link {
        get {
          return this.fieldLink;
        }
        
        set {
          this.fieldLink.Value = (value ?? new AddressField()).Value;
        }
      }
      
      /// <summary>Gets or sets In basisset</summary>
      [IproxProperty(SequenceId = 3)]
      public BooleanField InBasisset {
        get {
          return this.fieldInBasisset;
        }
        
        set {
          this.fieldInBasisset.Value = (value ?? new BooleanField()).Value;
        }
      }
    }
  }
  
  /// <summary>Media (class)</summary>
  [IproxPagetype(Alias = "media")]
  public partial class Media : Page {
    /// <summary>Meta (singular)</summary>
    [IproxCluster(Alias = "Meta", Prototype = true)]
    [IproxPagetype(Alias = "meta", Prototype = true, SequenceId = 1)]
    private readonly Meta fieldMeta = new Meta();
    
    /// <summary>Media (singular)</summary>
    [IproxCluster(Alias = "Media", Prototype = true)]
    [IproxPagetype(Alias = "media", Prototype = true, SequenceId = 1)]
    private readonly MediaPrototype fieldValue = new MediaPrototype();
    
    /// <summary>Stramien (singular)</summary>
    [IproxCluster(Alias = "Lay-out")]
    [IproxField(Alias = "Stramien", SequenceId = 1)]
    private readonly LayoutField fieldStramien = new LayoutField();
    
    /// <summary>Zie ook (collection)</summary>
    [IproxCluster(Alias = "Zie ook", Prototype = true)]
    [IproxPagetype(Alias = "seealso", Prototype = true, SequenceId = 1)]
    [IproxCluster(Alias = "Blokken", SequenceId = 2)]
    private readonly IproxClustersCollection<ISeealso> fieldZieOok = new IproxClustersCollection<ISeealso>();
    
    /// <summary>Initializes a new instance of the Media class.</summary>
    /// <param name="item">Containing item</param>
    public Media(Item item)
      : base(item) {
    }
    
    /// <summary>Prevents a default instance of the Media class from being created.</summary>
    private Media() {
    }
    
    /// <summary>Gets Meta</summary>
    [IproxProperty(SequenceId = 0)]
    public Meta Meta {
      get {
        return this.fieldMeta;
      }
    }
    
    /// <summary>Gets Media</summary>
    [IproxProperty(SequenceId = 1)]
    public MediaPrototype Value {
      get {
        return this.fieldValue;
      }
    }
    
    /// <summary>Gets or sets Stramien</summary>
    [IproxProperty(SequenceId = 2)]
    public LayoutField Stramien {
      get {
        return this.fieldStramien;
      }
      
      set {
        this.fieldStramien.Value = (value ?? new LayoutField()).Value;
      }
    }
    
    /// <summary>Gets collection of Zie ook</summary>
    [IproxProperty(SequenceId = 3)]
    public IproxClustersCollection<ISeealso> ZieOok {
      get {
        return this.fieldZieOok;
      }
    }
  }
}
