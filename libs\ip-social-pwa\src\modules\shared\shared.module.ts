import { CommonModule, DatePipe } from '@angular/common';
import { Injectable, NgModule } from '@angular/core';

import { IonicModule } from '@ionic/angular';
import { FileProgressService, UtilitiesModule } from '@ip/social-core';
import { TranslocoModule } from '@ngneat/transloco';
import { TimeagoCustomFormatter, TimeagoFormatter, TimeagoIntl, TimeagoModule } from 'ngx-timeago';
import { strings as dutch } from 'ngx-timeago/language-strings/nl';

import { CakeSvgComponent } from './components/cake/cake-svg';
import { ErrorPageComponent } from './components/error-page/error-page.component';
import { EventDateComponent } from './components/event-date/event-date.component';
import { EventIcsComponent } from './components/event-ics/event-ics.component';
import { EventComponent } from './components/event/event.component';
import { HtmlContentComponent } from './components/html-content/html-content.component';
import { ContentVisibleDirective } from './directives/content-visible.directive';
import { PressedDirective } from './directives/pressed.directive';
import { ScrollToDirective } from './directives/scroll-to.directive';
import { BypassSecurityHtmlPipe } from './pipes/bypass-security-html.pipe';
import { TruncatePipe } from './pipes/truncate.pipe';
import { IproxEventMapper } from './utilities/iprox-event.mapper';

@Injectable()
export class DutchTimeAgoIntl extends TimeagoIntl {
  strings = dutch;
}

@Injectable()
export class IpsTimeagoFormatter extends TimeagoCustomFormatter {
  private readonly cutOff = 24 * 60 * 60 * 1000;

  constructor(intl: TimeagoIntl, private pipe: DatePipe) {
    super(intl);
  }

  format(then: number): string {
    if (new Date().getTime() - then > this.cutOff) {
      return this.pipe.transform(then, 'd MMMM y - H:mm') ?? '';
    }

    return super.format(then);
  }
}

@NgModule({
  imports: [
    CommonModule,
    IonicModule,
    TimeagoModule.forChild({
      intl: { provide: TimeagoIntl, useClass: DutchTimeAgoIntl },
      formatter: { provide: TimeagoFormatter, useClass: IpsTimeagoFormatter },
    }),
    UtilitiesModule,
    TranslocoModule,
  ],
  declarations: [
    ErrorPageComponent,
    EventComponent,
    EventDateComponent,
    EventIcsComponent,
    HtmlContentComponent,
    ContentVisibleDirective,
    ScrollToDirective,
    BypassSecurityHtmlPipe,
    TruncatePipe,
    PressedDirective,
    CakeSvgComponent,
  ],
  providers: [
    IproxEventMapper,
    DatePipe,
    BypassSecurityHtmlPipe,
    FileProgressService,
  ],
  exports: [
    TimeagoModule,
    TranslocoModule,
    UtilitiesModule,
    ErrorPageComponent,
    EventComponent,
    EventDateComponent,
    HtmlContentComponent,
    ContentVisibleDirective,
    ScrollToDirective,
    BypassSecurityHtmlPipe,
    TruncatePipe,
    PressedDirective,
    CakeSvgComponent,
  ]
})
export class SharedModule { }
