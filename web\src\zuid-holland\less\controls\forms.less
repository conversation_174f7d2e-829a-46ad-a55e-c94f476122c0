.form-border(@border-color, @border-focus-color: @btn-focus-outline-color) {
  border-color: @border-color;
  box-shadow: none;

  &:focus {
    border-color: @border-focus-color;
    border-width: 1px;
    box-shadow: inset 0 0 0 1px @border-focus-color;
  }
}

.form-control {
  .form-border(@input-border-color);

  &::placeholder {
    color: @input-placeholder-color;
    opacity: 1;
  }
}

.form-group .input-group-addon {
  .form-border(@input-border-color);
}

form.ips .ips-field {
  &.ips-error .form-control {
    .form-border(@pzh-yellow);
  }

  &.ips-success .form-control {
    .form-border(@pzh-green);
  }
}
