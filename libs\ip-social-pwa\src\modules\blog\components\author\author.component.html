<ng-container *ngIf="editMode === false; else form;">
  <ips-person [id]="userId"></ips-person>

  <ng-container *ngIf="enableAuthors === true">
    <ips-person *ngFor="let personId of formControl.value" [id]="personId"></ips-person>
  </ng-container>
</ng-container>

<ng-template #form>
  <ng-container *ngIf="enableAuthors === true">
    <ips-person [id]="userId"></ips-person>

    <ips-person-chip
      *ngFor="let personId of formControl.value"
      [id]="personId"
      icon="close-circle"
      (click)="removeAuthor(personId)"
    ></ips-person-chip>

    <ion-chip (click)="openModal()">
      <ion-label>{{ 'blog.addAuthor' | transloco }}</ion-label>
      <ion-icon name="add-circle"></ion-icon>
    </ion-chip>
  </ng-container>
</ng-template>
