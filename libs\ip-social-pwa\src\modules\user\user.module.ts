import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { RouterModule } from '@angular/router';

import { IonicModule } from '@ionic/angular';
import { IpPwaHeaderModule } from '@ip/pwa';
import { CoreModule } from '@ip/social-core';
import { BlogModule } from '../blog/blog.module';

import { PersonModule } from '../person/person.module';
import { ProfileModule } from '../profile/profile.module';
import { SharedModule } from '../shared/shared.module';
import { DeviceNameComponent } from './components/device-name/device-name.component';
import { PushSettingsComponent } from './components/push-settings/push-settings.component';
import { UserNotificationsMockupComponent } from './components/user-notifications-mockup/user-notifications-mockup.component';
import { UserPageComponent } from './components/user-page/user-page.component';
import { UserSettingsComponent } from './components/user-settings/user-settings.component';

@NgModule({
  declarations: [
    DeviceNameComponent,
    PushSettingsComponent,
    UserSettingsComponent,
    UserNotificationsMockupComponent,
    UserPageComponent,
  ],
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    RouterModule,
    IonicModule,
    SharedModule,
    CoreModule,
    PersonModule,
    IpPwaHeaderModule,
    BlogModule,
    // Is ProfileModule wel wenselijk?
    ProfileModule,
  ],
  exports: [
  ]
})
export class UserModule {
}
