{"extends": ["../../.eslintrc.json"], "ignorePatterns": ["!**/*"], "overrides": [{"files": ["*.ts"], "extends": ["@infoprojects/eslint-config/angular-typescript"], "parserOptions": {"project": ["libs/ip-pwa/tsconfig.*?.json"]}, "rules": {"@angular-eslint/directive-selector": ["error", {"type": "attribute", "prefix": "ip", "style": "camelCase"}], "@angular-eslint/component-selector": ["error", {"type": "element", "prefix": "ip", "style": "kebab-case"}]}}, {"files": ["*.html"], "extends": ["plugin:@nrwl/nx/angular-template"], "rules": {}}]}