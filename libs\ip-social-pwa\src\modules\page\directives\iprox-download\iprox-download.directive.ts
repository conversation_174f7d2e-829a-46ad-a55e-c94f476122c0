import { Directive, HostBinding, HostListener, Input, OnChanges, SimpleChanges } from '@angular/core';

import { CoreSettings, ResourceService } from '@ip/social-core';

@Directive({
  // eslint-disable-next-line @angular-eslint/directive-selector
  selector: 'a[ips-iprox-download]',
})
export class IproxFileDownloadDirective implements OnChanges {
  // eslint-disable-next-line @angular-eslint/no-input-rename
  @Input('ips-iprox-download')
  filePath!: string;

  @HostBinding('href')
  href?: string;

  constructor(
    private resourceService: ResourceService,
    private settings: CoreSettings,
  ) {
  }

  @HostListener('click', ['$event'])
  download(event: Event) {
    event.preventDefault();

    this.resourceService.downloadFile(`${this.settings.apiUrl}iprox/content?file=${this.href}`);
  }

  ngOnChanges(changes: SimpleChanges) {
    if (changes.filePath.currentValue !== changes.filePath.previousValue) {
      this.href = changes.filePath.currentValue;
    }
  }
}
