import { Component, Input } from '@angular/core';

import { ModalController } from '@ionic/angular';

import { IAnniversaryPreview } from '../../models/anniversary-preview.model';

@Component({
  selector: 'ips-anniversary-preview-modal',
  templateUrl: './anniversary-preview-modal.component.html',
  styleUrls: ['./anniversary-preview-modal.component.scss']
})
export class AnniversaryPreviewModalComponent {
  @Input()
  anniversaryPreview!: Array<IAnniversaryPreview>;

  @Input()
  startDay!: Date;

  constructor(public modalController: ModalController) { }
}
